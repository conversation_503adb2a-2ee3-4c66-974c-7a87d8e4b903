<?php

namespace App\Models;

use App\Observers\AjuVerifTanamObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

#[ObservedBy([AjuVerifTanamObserver::class])]
class AjuVerifTanam2024 extends Model
{
	use HasFactory, LogsActivity;
	public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
			->logFillable('*')
			->logOnlyDirty();
    }

	public $table = 'avtanams';

	protected $fillable = [
		'npwp',
		'commitment_id',
		'no_ijin',
		'status',
		'note',


		//file upload
		'batanam', //berita acara hasil pemeriksaan realisasi tanam
		'ndhprt', //nota dinas hasil pemeriksaan realisasi tanam

		'check_by',
		'verif_at',
		'metode',
	];

	protected static function booted()
	{
		static::addGlobalScope('npwp', function (Builder $builder) {
			if (Auth::check()) {
				$user = Auth::user();

				if ($user->hasAnyRole(['admin', 'Super Admin'])) {
					// $builder->whereNotIn('status', ['4']);
				} elseif ($user->hasAnyRole(['direktur'])) {
				} elseif ($user->hasAnyRole(['verifikator'])) {
				} else {
					$builder->where('npwp', $user->npwp);
						// ->where('status', '!=', '4');
				}
				
			}
		});
	}

	public function pks()
	{
		return $this->hasMany(Pks2024::class, 'no_ijin', 'no_ijin');
	}

	public function commitment()
	{
		return $this->hasOne(Commitment2024::class, 'no_ijin', 'no_ijin');
	}

	public function datauser()
	{
		return $this->belongsTo(DataUser::class, 'npwp', 'npwp_company');
	}

	public function dataadmin()
	{
		return $this->belongsTo(DataAdministrator::class, 'check_by', 'user_id');
	}
	public function userDocs()
	{
		return $this->hasOne(UserDocs2024::class, 'no_ijin', 'no_ijin');
	}
}
