<?php

namespace App\Observers;

use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;

class UserObserver
{
	/**
	 * Handle the User "created" event.
	 */
	public function created(User $user): void
	{
		$admin = User::whereHas('roles', function ($query) {
			$query->whereIn('name', ['Super Admin', 'admin']);
		})->get();
		
		Notification::make()
			->title('New User')
			->body('new User has been registered')
			->sendToDatabase($admin);
	}

	/**
	 * Handle the User "updated" event.
	 */
	public function updated(User $user): void
	{
		if ($user->wasChanged('status') && $user->status === 'Aktif') {
			$admin = Auth::user();
			$registrar = $user;
			Notification::make()
				->title('Registrasi Pengguna')
				->body('Registrasi akun Anda telah disetujui oleh Administrator. Anda sekarang dapat melakukan tugas-tugas Anda.')
				->sendToDatabase($registrar);
			Notification::make()
				->title('User Approved')
				->body("You have approved {$registrar->name}'s registration.")
				->sendToDatabase($admin);
		}
	}


	/**
	 * Handle the User "deleted" event.
	 */
	public function deleted(User $user): void
	{
		//
	}

	/**
	 * Handle the User "restored" event.
	 */
	public function restored(User $user): void
	{
		//
	}

	/**
	 * Handle the User "force deleted" event.
	 */
	public function forceDeleted(User $user): void
	{
		//
	}
}
