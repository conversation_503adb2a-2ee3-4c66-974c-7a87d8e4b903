# Template Test Case SIMETHRIS

## Test Case Template

### Test Case ID: [TC-XXX-001]
**Test Case Name**: [Nama Test Case]  
**Module**: [Nama Module]  
**Priority**: [High/Medium/Low]  
**Test Type**: [Functional/Non-Functional]  
**Created By**: [<PERSON>a Tester]  
**Date Created**: [DD/MM/YYYY]  
**Last Updated**: [DD/MM/YYYY]

### Test Objective
[Tujuan dari test case ini]

### Preconditions
- [Kondisi yang harus dipenuhi sebelum test]
- [Data yang harus tersedia]
- [User role yang diperlukan]

### Test Data
| Field | Value | Description |
|-------|-------|-------------|
| Username | test_importir | Test user untuk role importir |
| Password | Test123! | Password untuk test user |
| File Upload | test_document.pdf | File dokumen untuk testing |

### Test Steps
| Step | Action | Expected Result |
|------|--------|-----------------|
| 1 | [Aksi yang dilakukan] | [Hasil yang diharapkan] |
| 2 | [Aksi yang dilakukan] | [Hasil yang diharapkan] |
| 3 | [Aksi yang dilakukan] | [Hasil yang diharapkan] |

### Post-conditions
- [Kondisi setelah test selesai]
- [Data yang harus dibersihkan]

### Test Environment
- **Browser**: Chrome v120+
- **OS**: Windows 10/11
- **Database**: MySQL 8.0
- **Environment**: Testing/Staging

---

## Contoh Test Case Lengkap

### Test Case ID: TC-AUTH-001
**Test Case Name**: Login Importir dengan Kredensial Valid  
**Module**: Authentication  
**Priority**: High  
**Test Type**: Functional  
**Created By**: QA Team  
**Date Created**: 01/12/2024  
**Last Updated**: 01/12/2024

### Test Objective
Memastikan importir dapat login ke sistem dengan menggunakan kredensial yang valid dan diarahkan ke dashboard yang sesuai dengan role.

### Preconditions
- Aplikasi SIMETHRIS dapat diakses
- User dengan role "importir" sudah terdaftar di sistem
- Database dalam kondisi normal

### Test Data
| Field | Value | Description |
|-------|-------|-------------|
| Email | <EMAIL> | Email user test |
| Password | Test123! | Password user test |
| Role | importir | Role yang diharapkan |

### Test Steps
| Step | Action | Expected Result |
|------|--------|-----------------|
| 1 | Buka browser dan akses URL SIMETHRIS | Halaman login muncul dengan form email dan password |
| 2 | Masukkan email: <EMAIL> | Email terisi di field email |
| 3 | Masukkan password: Test123! | Password terisi di field password (tersembunyi) |
| 4 | Klik tombol "Masuk" | Loading indicator muncul |
| 5 | Tunggu proses login | Berhasil login dan diarahkan ke dashboard importir |
| 6 | Verifikasi menu yang tersedia | Menu sesuai dengan role importir (Komitmen, Poktan, Realisasi, dll) |
| 7 | Verifikasi nama user di header | Nama user muncul di pojok kanan atas |

### Post-conditions
- User berhasil login dan session aktif
- Dashboard importir dapat diakses
- Menu sesuai dengan role importir

### Test Environment
- **Browser**: Chrome v120+
- **OS**: Windows 10
- **Database**: MySQL 8.0 (Testing)
- **Environment**: Testing Server

---

## Template Bug Report

### Bug ID: BUG-XXX-001
**Bug Title**: [Judul singkat bug]  
**Module**: [Nama Module]  
**Severity**: [Critical/High/Medium/Low]  
**Priority**: [High/Medium/Low]  
**Status**: [Open/In Progress/Fixed/Closed]  
**Reported By**: [Nama Reporter]  
**Date Reported**: [DD/MM/YYYY]  
**Assigned To**: [Developer Name]

### Bug Description
[Deskripsi detail tentang bug yang ditemukan]

### Steps to Reproduce
1. [Langkah 1]
2. [Langkah 2]
3. [Langkah 3]

### Expected Result
[Hasil yang seharusnya terjadi]

### Actual Result
[Hasil yang sebenarnya terjadi]

### Test Environment
- **Browser**: [Browser dan versi]
- **OS**: [Operating System]
- **URL**: [URL tempat bug ditemukan]
- **User Role**: [Role user saat bug terjadi]

### Attachments
- Screenshot: [nama_file.png]
- Video: [nama_file.mp4]
- Log File: [nama_file.log]

### Additional Information
[Informasi tambahan yang relevan]

---

## Contoh Bug Report

### Bug ID: BUG-AUTH-001
**Bug Title**: Login gagal dengan pesan error tidak jelas  
**Module**: Authentication  
**Severity**: High  
**Priority**: High  
**Status**: Open  
**Reported By**: QA Team  
**Date Reported**: 01/12/2024  
**Assigned To**: Developer A

### Bug Description
Ketika user memasukkan kredensial yang salah, sistem menampilkan pesan error "Something went wrong" yang tidak informatif. Seharusnya menampilkan pesan yang lebih spesifik seperti "Email atau password salah".

### Steps to Reproduce
1. Buka halaman login SIMETHRIS
2. Masukkan email yang tidak terdaftar: <EMAIL>
3. Masukkan password sembarang: wrongpass
4. Klik tombol "Masuk"

### Expected Result
Muncul pesan error yang jelas: "Email atau password yang Anda masukkan salah. Silakan coba lagi."

### Actual Result
Muncul pesan error: "Something went wrong"

### Test Environment
- **Browser**: Chrome v120.0.6099.109
- **OS**: Windows 11
- **URL**: https://simethris-test.com/login
- **User Role**: Guest (belum login)

### Attachments
- Screenshot: login_error_message.png
- Console Log: browser_console.log

### Additional Information
Bug ini mempengaruhi user experience karena user tidak tahu apa yang salah dengan input mereka.

---

## Test Execution Report Template

### Test Execution Summary
**Project**: SIMETHRIS v4.0  
**Test Phase**: [System Testing/UAT/Regression]  
**Test Period**: [Start Date] - [End Date]  
**Tested By**: [QA Team Name]  
**Environment**: [Testing/Staging]

### Test Statistics
| Metric | Count | Percentage |
|--------|-------|------------|
| Total Test Cases | 150 | 100% |
| Executed | 145 | 97% |
| Passed | 138 | 95% |
| Failed | 7 | 5% |
| Blocked | 5 | 3% |
| Not Executed | 5 | 3% |

### Test Results by Module
| Module | Total | Passed | Failed | Pass Rate |
|--------|-------|--------|--------|-----------|
| Authentication | 15 | 14 | 1 | 93% |
| Komitmen | 25 | 24 | 1 | 96% |
| Master Data | 20 | 19 | 1 | 95% |
| Realisasi | 30 | 28 | 2 | 93% |
| Verifikasi | 20 | 19 | 1 | 95% |
| SKL | 15 | 14 | 1 | 93% |
| API | 25 | 20 | 0 | 100% |

### Bug Summary
| Severity | Open | Fixed | Total |
|----------|------|-------|-------|
| Critical | 0 | 0 | 0 |
| High | 2 | 3 | 5 |
| Medium | 5 | 8 | 13 |
| Low | 3 | 5 | 8 |
| **Total** | **10** | **16** | **26** |

### Key Findings
1. **Authentication Module**: Satu bug terkait pesan error yang tidak informatif
2. **File Upload**: Beberapa issue dengan validasi format file
3. **Performance**: Response time untuk beberapa report masih di atas target
4. **Mobile Compatibility**: Minor UI issues pada device dengan resolusi kecil

### Recommendations
1. Fix critical dan high severity bugs sebelum release
2. Improve error messaging untuk better user experience
3. Optimize database queries untuk report generation
4. Enhance mobile responsive design

### Test Coverage
- **Functional Coverage**: 95%
- **Code Coverage**: 85%
- **Requirement Coverage**: 98%

### Risk Assessment
- **High Risk**: Performance issues pada peak load
- **Medium Risk**: Mobile compatibility issues
- **Low Risk**: Minor UI inconsistencies

### Sign-off Status
- [ ] QA Team Sign-off
- [ ] Development Team Sign-off
- [ ] Product Owner Sign-off
- [ ] Stakeholder Sign-off

---

## Checklist Pre-Testing

### Environment Setup
- [ ] Testing server accessible
- [ ] Database restored with latest data
- [ ] Test users created for all roles
- [ ] Test data prepared
- [ ] Browser versions updated
- [ ] Testing tools installed

### Test Data Preparation
- [ ] Master data (provinsi, kabupaten, etc.) available
- [ ] Sample komitmen data created
- [ ] Test documents prepared (PDF, images)
- [ ] User accounts for each role ready
- [ ] API test data prepared

### Tool Configuration
- [ ] Test management tool configured
- [ ] Bug tracking system ready
- [ ] Screen recording software installed
- [ ] Performance testing tools configured
- [ ] Security testing tools ready

### Team Preparation
- [ ] Test cases reviewed and approved
- [ ] Testing schedule communicated
- [ ] Roles and responsibilities assigned
- [ ] Communication channels established
- [ ] Escalation process defined

---

## Checklist Post-Testing

### Test Completion
- [ ] All planned test cases executed
- [ ] Test results documented
- [ ] Screenshots/videos captured for failed tests
- [ ] Bug reports created and assigned
- [ ] Test execution report generated

### Quality Gates
- [ ] Pass rate meets minimum threshold (95%)
- [ ] No critical bugs remaining
- [ ] High priority bugs addressed
- [ ] Performance criteria met
- [ ] Security requirements satisfied

### Documentation
- [ ] Test summary report completed
- [ ] Bug summary report generated
- [ ] Test evidence archived
- [ ] Lessons learned documented
- [ ] Recommendations provided

### Sign-off Process
- [ ] QA team review completed
- [ ] Development team acknowledgment
- [ ] Product owner approval
- [ ] Stakeholder sign-off obtained
- [ ] Go/No-go decision documented
