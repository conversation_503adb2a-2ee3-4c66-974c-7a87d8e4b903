<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\CmsCategory;
use App\Models\User;

class CmsCategoryPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any CmsCategory');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, CmsCategory $cmscategory): bool
    {
        return $user->checkPermissionTo('view CmsCategory');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create CmsCategory');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, CmsCategory $cmscategory): bool
    {
        return $user->checkPermissionTo('update CmsCategory');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, CmsCategory $cmscategory): bool
    {
        return $user->checkPermissionTo('delete CmsCategory');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any CmsCategory');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, CmsCategory $cmscategory): bool
    {
        return $user->checkPermissionTo('restore CmsCategory');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any CmsCategory');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, CmsCategory $cmscategory): bool
    {
        return $user->checkPermissionTo('replicate CmsCategory');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder CmsCategory');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, CmsCategory $cmscategory): bool
    {
        return $user->checkPermissionTo('force-delete CmsCategory');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any CmsCategory');
    }
}
