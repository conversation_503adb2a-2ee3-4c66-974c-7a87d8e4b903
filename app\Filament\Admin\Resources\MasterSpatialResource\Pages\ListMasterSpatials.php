<?php

namespace App\Filament\Admin\Resources\MasterSpatialResource\Pages;

use App\Filament\Admin\Pages\SpatialMapPage;
use App\Filament\Admin\Resources\MasterSpatialResource;
use App\Filament\Admin\Resources\MasterSpatialResource\Widgets\SpatialStatWidgets;
use App\Filament\Admin\Resources\MasterSpatialResource\Widgets\SpatialLineChartWidgets;
use App\Filament\Admin\Resources\MasterSpatialResource\Widgets\SpatialTableWidgets;
use App\Models\MasterSpatial;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class ListMasterSpatials extends ListRecords
{
    protected static string $resource = MasterSpatialResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('viewFilamentMap')
				->label('Lihat/Unggah <PERSON>a')
				->icon('heroicon-o-map')
				->color('primary')
				->url(SpatialMapPage::getUrl()),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
			SpatialStatWidgets::class,
            SpatialLineChartWidgets::class,
            SpatialTableWidgets::class,
        ];
    }

	protected function getTableQuery(): Builder
	{
		$user = Auth::user();
		return $user->hasRole('dinas')
			? MasterSpatial::query()->where('kabupaten_id', Auth::user()->dataadmin->kabupaten_id)
			: MasterSpatial::query();
	}
}
