<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\AnnouncementUser;
use App\Models\User;

class AnnouncementUserPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any AnnouncementUser');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, AnnouncementUser $announcementuser): bool
    {
        return $user->checkPermissionTo('view AnnouncementUser');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create AnnouncementUser');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, AnnouncementUser $announcementuser): bool
    {
        return $user->checkPermissionTo('update AnnouncementUser');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, AnnouncementUser $announcementuser): bool
    {
        return $user->checkPermissionTo('delete AnnouncementUser');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any AnnouncementUser');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, AnnouncementUser $announcementuser): bool
    {
        return $user->checkPermissionTo('restore AnnouncementUser');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any AnnouncementUser');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, AnnouncementUser $announcementuser): bool
    {
        return $user->checkPermissionTo('replicate AnnouncementUser');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder AnnouncementUser');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, AnnouncementUser $announcementuser): bool
    {
        return $user->checkPermissionTo('force-delete AnnouncementUser');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any AnnouncementUser');
    }
}
