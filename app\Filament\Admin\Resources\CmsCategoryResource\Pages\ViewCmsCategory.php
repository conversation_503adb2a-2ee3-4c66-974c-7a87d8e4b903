<?php

namespace App\Filament\Admin\Resources\CmsCategoryResource\Pages;

use App\Filament\Admin\Resources\CmsCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewCmsCategory extends ViewRecord
{
    protected static string $resource = CmsCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    public function getTitle(): string
	{
        $category = $this->record ? $this->record->name : '##';
        return 'Kategori ' . $category;
	}

    public function getHeading(): string
	{
        $category = $this->record ? $this->record->name : '##';
        return 'Kategori ' . $category;
	}
}
