<?php

namespace App\Observers;

use App\Models\AjuVerifSkl2024;
use App\Models\User;
use Filament\Notifications\Notification;

class AjuVerifSklObserver
{
    /**
     * Handle the AjuVerifSkl2024 "created" event.
     */
    public function created(AjuVerifSkl2024 $ajuVerifSkl2024): void
    {
        $pusat = User::whereHas('roles', function ($query) {
			$query->whereIn('name', ['Super Admin', 'admin', 'verifikator']);
		})->get();

		$noIjin = $ajuVerifSkl2024->no_ijin;
		$periode = $ajuVerifSkl2024->commitment->periodetahun;
		$company = $ajuVerifSkl2024->commitment->user->datauser->company_name;
		$cleanNoIjin = str_replace(['/', '-','.'], '', $noIjin);

		Notification::make()
			->title('Permohonan SKL')

			->body("<p class='mb-3'><span class='text-info-500 font-bold'>{$company}</span> telah mengajukan Permohonan SKL Komitmen No.: <span class='text-info-500 font-bold'>{$noIjin}</span>, untuk periode {$periode}.</p>
			<p class='mb-3 mt-5'>Segera tindaklanjut.</p>")
			->sendToDatabase($pusat);
    }

    /**
     * Handle the AjuVerifSkl2024 "updated" event.
     */
    public function updated(AjuVerifSkl2024 $ajuVerifSkl2024): void
    {

		$noIjin = $ajuVerifSkl2024->no_ijin;
		$periode = $ajuVerifSkl2024->commitment->periodetahun;
        if ($ajuVerifSkl2024->wasChanged('status') && $ajuVerifSkl2024->status === '1') {
			$company = $ajuVerifSkl2024->commitment->user->datauser->company_name;
			$cleanNoIjin = str_replace(['/', '-','.'], '', $noIjin);
			
			$pusat = User::whereHas('roles', function ($query) {
				$query->whereIn('name', ['Super Admin', 'admin']);
			})->get();

			Notification::make()
				->title('Permohonan SKL')

				->body("<p class='mb-3'><span class='text-info-500 font-bold'>{$company}</span> telah mengajukan Permohonan SKL Komitmen No.: <span class='text-info-500 font-bold'>{$noIjin}</span>, untuk periode {$periode}.</p>
				<p class='mb-3 mt-5'>Segera tindaklanjut.</p>")
				->sendToDatabase($pusat);
		}

        if ($ajuVerifSkl2024->wasChanged('status') && $ajuVerifSkl2024->status === '2') {
			$registrar = $ajuVerifSkl2024->commitment->user;
			$pusat = User::whereHas('roles', function ($query) {
				$query->whereIn('name', ['Super Admin', 'direktur']);
			})->get();

			Notification::make()
				->title('Permohonan SKL')
				->body("<p class='mb-3'>Administrator merekomendasikan penerbitan Surat Keterangan Lunas untuk Komitmen No.: <span class='text-info-500 font-bold'>{$noIjin}</span> periode {$periode}.</p>")
				->sendToDatabase($pusat);
			Notification::make()
				->title('Permohonan SKL')
				->body("<p class='mb-3'>Administrator merekomendasikan penerbitan Surat Keterangan Lunas untuk Komitmen No.: <span class='text-info-500 font-bold'>{$noIjin}</span> periode {$periode}.</p>")
				->sendToDatabase($registrar);
		}

        if ($ajuVerifSkl2024->wasChanged('status') && $ajuVerifSkl2024->status === '4') {
			$registrar = $ajuVerifSkl2024->commitment->user;
			$pusat = User::whereHas('roles', function ($query) {
				$query->whereIn('name', ['Super Admin', 'admin']);
			})->get();

			Notification::make()
				->title('Permohonan SKL')
				->body("<p class='mb-3'>Permohonan SKL Komitmen No.: <span class='text-success-500 font-bold'>{$noIjin}</span> periode {$periode} telah DISETUJUI oleh Pimpinan.</p>
				<p class='mb-3 mt-5'>Administrator akan segera menerbitkan Surat Keterangan Lunas.</p>")
				->sendToDatabase($registrar);

			Notification::make()
				->title('Permohonan SKL')
				->body("<p class='mb-3'>Permohonan Penerbitan SKL Komitmen No.: <span class='text-success-500 font-bold'>{$noIjin}</span> periode {$periode} telah disetujui oleh Pimpinan. Anda dapat segera terbitkan SKL.</p>")
				->sendToDatabase($pusat);
		}
    }

    /**
     * Handle the AjuVerifSkl2024 "deleted" event.
     */
    public function deleted(AjuVerifSkl2024 $ajuVerifSkl2024): void
    {
        //
    }

    /**
     * Handle the AjuVerifSkl2024 "restored" event.
     */
    public function restored(AjuVerifSkl2024 $ajuVerifSkl2024): void
    {
        //
    }

    /**
     * Handle the AjuVerifSkl2024 "force deleted" event.
     */
    public function forceDeleted(AjuVerifSkl2024 $ajuVerifSkl2024): void
    {
        //
    }
}
