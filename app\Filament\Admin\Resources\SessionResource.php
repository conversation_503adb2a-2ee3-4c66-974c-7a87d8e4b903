<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\SessionResource\Pages;
use App\Filament\Admin\Resources\SessionResource\Widgets\ActiveUsersOverview;
use App\Models\Session;
use Carbon\Carbon;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SessionResource extends Resource
{
    protected static ?string $model = Session::class;

    protected static ?string $navigationIcon = 'heroicon-o-key';

    protected static ?string $navigationGroup = 'System';

    protected static ?string $navigationLabel = 'User Sessions';

    protected static ?int $navigationSort = 90;

    public static function canAccess(): bool
    {
        // Hanya Super Admin yang bisa akses
        return Auth::user()->hasRole('Super Admin');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                \Filament\Forms\Components\Section::make('Informasi Session')
                    ->schema([
                        \Filament\Forms\Components\TextInput::make('id')
                            ->label('Session ID')
                            ->disabled(),
                        \Filament\Forms\Components\TextInput::make('user_id')
                            ->label('User ID')
                            ->disabled(),
                        \Filament\Forms\Components\TextInput::make('user_name')
                            ->label('Nama Pengguna')
                            ->disabled(),
                        \Filament\Forms\Components\TextInput::make('user_email')
                            ->label('Email')
                            ->disabled(),
                        \Filament\Forms\Components\TextInput::make('role')
                            ->label('Peran')
                            ->disabled(),
                        \Filament\Forms\Components\TextInput::make('ip_address')
                            ->label('IP Address')
                            ->disabled(),
                    ])->columns(2),

                \Filament\Forms\Components\Section::make('Informasi Perangkat')
                    ->schema([
                        \Filament\Forms\Components\TextInput::make('device')
                            ->label('Perangkat')
                            ->disabled(),
                        \Filament\Forms\Components\TextInput::make('browser')
                            ->label('Browser')
                            ->disabled(),
                        \Filament\Forms\Components\TextInput::make('platform')
                            ->label('Platform')
                            ->disabled(),
                        \Filament\Forms\Components\Textarea::make('user_agent')
                            ->label('User Agent')
                            ->disabled()
                            ->columnSpanFull(),
                        \Filament\Forms\Components\Placeholder::make('device_info_note')
                            ->label('')
                            ->content(function ($record) {
                                if ($record->device === 'Mobile App') {
                                    return 'Informasi di atas diambil dari payload session aplikasi mobile.';
                                }
                                return 'Informasi di atas dideteksi dari user agent browser.';
                            })
                            ->columnSpanFull(),
                    ])->columns(3),

                \Filament\Forms\Components\Section::make('Aktivitas')
                    ->schema([
                        \Filament\Forms\Components\TextInput::make('last_activity')
                            ->label('Timestamp')
                            ->disabled()
                            ->helperText('Nilai timestamp mentah dari database'),
                        \Filament\Forms\Components\Textarea::make('last_activity_formatted')
                            ->label('Aktivitas Terakhir')
                            ->disabled()
                            ->rows(3),
                        \Filament\Forms\Components\TextInput::make('status')
                            ->label('Status')
                            ->disabled(),
                    ])->columns(1),

                \Filament\Forms\Components\Section::make('Payload')
                    ->schema([
                        \Filament\Forms\Components\Textarea::make('payload')
                            ->label('Payload Raw')
                            ->disabled()
                            ->rows(3)
                            ->columnSpanFull()
                            ->helperText('Payload dalam format raw (serialized)'),
                        \Filament\Forms\Components\Textarea::make('payload_formatted')
                            ->label('Informasi Payload')
                            ->disabled()
                            ->rows(15)
                            ->columnSpanFull()
                            ->helperText('Payload yang sudah diproses dan diformat'),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->query(function () {
                return Session::query()
                    ->select('sessions.*', 'users.name as user_name', 'users.email as user_email', 'roles.name as role_name')
                    ->whereNotNull('sessions.user_id') // Hanya pengguna terautentikasi
                    ->join('users', 'sessions.user_id', '=', 'users.id')
                    ->leftJoin('model_has_roles', function($join) {
                        $join->on('users.id', '=', 'model_has_roles.model_id')
                            ->where('model_has_roles.model_type', '=', 'App\\Models\\User');
                    })
                    ->leftJoin('roles', 'model_has_roles.role_id', '=', 'roles.id')
                    ->orderBy('last_activity', 'desc');
            })
            ->defaultSort('last_activity', 'desc')
            ->columns([
                IconColumn::make('is_online')
                    ->label('Status')
                    ->boolean()
                    ->state(function ($record) {
                        // Pengguna dianggap online jika aktivitas terakhir < 5 menit yang lalu
                        return $record->isActive();
                    })
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                TextColumn::make('user_name')
                    ->label('Nama Pengguna')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Guest'),
                // TextColumn::make('user_email')
                //     ->label('Email')
                //     ->searchable()
                //     ->sortable(),
                TextColumn::make('role_name')
                    ->label('Peran')
                    ->badge()
                    ->color(fn ($state) => match ($state) {
                        'Super Admin' => 'danger',
                        'admin' => 'success',
                        'importir' => 'warning',
                        'verifikator' => 'primary',
                        'direktur' => 'danger',
                        default => 'gray',
                    })
                    ->placeholder('Tidak Ada Peran'),
                TextColumn::make('ip_address')
                    ->label('IP Address')
                    ->searchable(),
                TextColumn::make('device')
                    ->label('Perangkat')
                    ->badge()
                    ->size('md')
                    ->searchable()
                    ->sortable()
                    ->color(fn ($state) => match ($state) {
                        'Mobile App' => 'success',
                        'Desktop' => 'info',
                        'Tablet' => 'warning',
                        'Mobile' => 'danger',
                        default => 'gray',
                    }),
                TextColumn::make('browser')
                    ->label('Browser')
                    ->formatStateUsing(function ($record) {
                        $browser = $record->browser;
                        $platform = $record->platform;

                        if ($record->device === 'Mobile App') {
                            return $browser;
                        }

                        return $browser . ' / ' . $platform;
                    })
                    ->wrap()
                    ->size('sm'),
                TextColumn::make('last_activity')
                    ->label('Aktivitas Terakhir')
                    ->formatStateUsing(function ($record) {
                        try {
                            if (!is_numeric($record->last_activity)) {
                                return $record->last_activity;
                            }

                            // Pastikan timezone diatur dengan benar
                            $timestamp = (int) $record->last_activity;
                            $lastActivity = Carbon::createFromTimestamp($timestamp)->setTimezone('Asia/Jakarta');
                            $now = Carbon::now()->setTimezone('Asia/Jakarta');

                            // Debug info
                            $debug = '';
                            if (app()->environment('local')) {
                                $debug .= ' [Debug: ' . $timestamp . ']';
                            }

                            if ($lastActivity->diffInMinutes($now) < 60) {
                                return $lastActivity->diffForHumans();
                            }

                            return $lastActivity->format('d M Y');
                        } catch (\Exception $e) {
                            return 'Invalid timestamp: ' . $e->getMessage();
                        }
                    })
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\Filter::make('online')
                    ->label('Hanya User Online')
                    ->query(fn (Builder $query) => $query->active()),
                Tables\Filters\Filter::make('offline')
                    ->label('Hanya User Offline')
                    ->query(fn (Builder $query) => $query->whereRaw('CAST(last_activity AS UNSIGNED) <= ?', [Carbon::now()->setTimezone('Asia/Jakarta')->subMinutes(5)->getTimestamp()])),
                Tables\Filters\Filter::make('today')
                    ->label('Aktivitas Hari Ini')
                    ->query(fn (Builder $query) => $query->whereRaw('CAST(last_activity AS UNSIGNED) >= ?', [Carbon::today()->setTimezone('Asia/Jakarta')->getTimestamp()])),
                Tables\Filters\Filter::make('yesterday')
                    ->label('Aktivitas Kemarin')
                    ->query(fn (Builder $query) => $query->whereRaw('CAST(last_activity AS UNSIGNED) >= ?', [Carbon::yesterday()->setTimezone('Asia/Jakarta')->getTimestamp()])
                        ->whereRaw('CAST(last_activity AS UNSIGNED) < ?', [Carbon::today()->setTimezone('Asia/Jakarta')->getTimestamp()])),
                Tables\Filters\Filter::make('this_week')
                    ->label('Aktivitas Minggu Ini')
                    ->query(fn (Builder $query) => $query->whereRaw('CAST(last_activity AS UNSIGNED) >= ?', [Carbon::now()->setTimezone('Asia/Jakarta')->startOfWeek()->getTimestamp()])),
                SelectFilter::make('role')
                    ->label('Filter Berdasarkan Peran')
                    ->options(function () {
                        return DB::table('roles')
                            ->pluck('name', 'name')
                            ->toArray();
                    })
                    ->query(function (Builder $query, array $data) {
                        if (isset($data['value'])) {
                            return $query->where('roles.name', $data['value']);
                        }

                        return $query;
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('Lihat Detail')
					->iconButton()
                    ->color('info'),
                Tables\Actions\Action::make('delete')
                    ->label('Hapus Session')
                    ->color('danger')
					->iconButton()
                    ->icon('heroicon-o-trash')
                    ->requiresConfirmation()
                    ->modalHeading('Hapus Session')
                    ->modalDescription('Apakah Anda yakin ingin menghapus session ini? Pengguna akan terlogout dari perangkat tersebut.')
                    ->modalSubmitActionLabel('Ya, Hapus')
                    ->action(function ($record) {
                        $record->delete();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\BulkAction::make('delete')
                    //     ->label('Hapus Session Terpilih')
                    //     ->color('danger')
                    //     ->icon('heroicon-o-trash')
                    //     ->requiresConfirmation()
                    //     ->modalHeading('Hapus Session Terpilih')
                    //     ->modalDescription('Apakah Anda yakin ingin menghapus semua session yang dipilih? Pengguna akan terlogout dari perangkat tersebut.')
                    //     ->modalSubmitActionLabel('Ya, Hapus')
                    //     ->deselectRecordsAfterCompletion()
                    //     ->action(function ($records) {
                    //         foreach ($records as $record) {
                    //             $record->delete();
                    //         }
                    //     }),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSessions::route('/'),
            'view' => Pages\ViewSession::route('/{record:id}'),
        ];
    }

    public static function getWidgets(): array
    {
        return [
            ActiveUsersOverview::class,
        ];
    }

    /**
     * Mendapatkan jumlah pengguna yang sedang online
     */
    public static function getOnlineUserCount()
    {
        return Session::authenticated()->active()->count();
    }

    /**
     * Mendapatkan jumlah total session pengguna terautentikasi
     */
    public static function getTotalSessionCount()
    {
        return Session::authenticated()->count();
    }

    /**
     * Mendapatkan daftar pengguna yang sedang online
     */
    public static function getOnlineUsers()
    {
        return Session::authenticated()
            ->active()
            ->select('sessions.*', 'users.name', 'users.email', 'roles.name as role')
            ->leftJoin('users', 'sessions.user_id', '=', 'users.id')
            ->leftJoin('model_has_roles', function($join) {
                $join->on('users.id', '=', 'model_has_roles.model_id')
                    ->where('model_has_roles.model_type', '=', 'App\\Models\\User');
            })
            ->leftJoin('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->get();
    }
}
