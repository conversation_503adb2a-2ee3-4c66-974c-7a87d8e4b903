@php
    $isStaff = $message->user->hasAnyRole(['admin', 'Super Admin', 'support']);
    $maxDepth = 3; // Maksimal kedalaman reply
@endphp

<div class="message-container {{ $depth > 0 ? 'ml-8 mt-3' : 'mb-6' }}" 
     style="{{ $depth > 0 ? 'border-left: 2px solid #e5e7eb; padding-left: 1rem;' : '' }}">
     
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 shadow-sm">
        <!-- Message Header -->
        <div class="flex items-start justify-between mb-3">
            <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-{{ $isStaff ? 'blue' : 'green' }}-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                        {{ substr($message->user->name, 0, 1) }}
                    </div>
                </div>
                <div>
                    <div class="flex items-center space-x-2">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {{ $message->user->name }}
                        </h4>
                        @if($isStaff)
                            <x-filament::badge color="primary" size="xs">
                                Staff
                            </x-filament::badge>
                        @endif
                    </div>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                        {{ $message->created_at->format('d M Y H:i') }}
                        @if($message->created_at != $message->updated_at)
                            <span class="italic">(diedit)</span>
                        @endif
                    </p>
                </div>
            </div>
        </div>

        <!-- Message Content -->
        <div class="prose dark:prose-invert max-w-none text-sm">
            @if($message->message)
                {!! $message->message !!}
            @else
                <p class="text-gray-500 italic">Pesan kosong</p>
            @endif
        </div>

        <!-- Attachment -->
        @if($message->attachment)
            <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                <div class="flex items-center space-x-2 text-sm">
                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                    </svg>
                    <a href="{{ \Illuminate\Support\Facades\Storage::url($message->attachment) }}"
                       target="_blank"
                       class="text-primary-600 hover:text-primary-500 dark:text-primary-400">
                        {{ basename($message->attachment) }}
                    </a>
                </div>
            </div>
        @endif
    </div>

    <!-- Replies -->
    @if($message->replies->isNotEmpty() && $depth < $maxDepth)
        <div class="mt-3">
            @foreach($message->replies->sortBy('created_at') as $reply)
                @include('filament.admin.widgets.partials.message-thread', [
                    'message' => $reply, 
                    'depth' => $depth + 1
                ])
            @endforeach
        </div>
    @endif
    
    @if($message->replies->isNotEmpty() && $depth >= $maxDepth)
        <div class="mt-3 ml-8">
            <p class="text-xs text-gray-500 italic">
                {{ $message->replies->count() }} balasan lainnya... 
                <span class="text-primary-600 cursor-pointer hover:underline">Lihat semua</span>
            </p>
        </div>
    @endif
</div>
