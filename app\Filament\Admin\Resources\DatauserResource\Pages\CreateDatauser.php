<?php

namespace App\Filament\Admin\Resources\DatauserResource\Pages;

use App\Filament\Admin\Resources\DatauserResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Enums\Alignment;

class CreateDatauser extends CreateRecord
{
    protected static string $resource = DatauserResource::class;
    public static string | Alignment $formActionsAlignment = Alignment::Right;
    protected static ?string $title = 'Penyelarasan Data Pengguna';

    public function getHeading(): string
	{
		// $sales_no = $this->record ? $this->record->sales_no : 'N/A';
        return 'Penyelarasan Profile ';
	}

    protected function getFormActions(): array
	{
		$visible = $this->data['visible'];
		if($visible === 'visible'){
			return [
                $this->getCreateFormAction()->label('Simpan'),
                $this->getCancelFormAction()
			];
		}else{
			return [];
		}
	}

    // public function mount(): void
    // {
    //     // // Periksa jika ada error di session
    //     // if (session()->has('error')) {
    //     //     // Tam<PERSON>lkan notifikasi Filament
    //     //     Notification::make()
    //     //         ->title('Authentication Error')
    //     //         ->body(session('error'))
    //     //         ->danger()
    //     //         ->persistent()
    //     //         ->send();
    //     // }
    // }
}
