<?php

namespace App\Filament\Panel2025\Resources\Commitment2025Resource\Pages;

use App\Filament\Panel2025\Resources\Commitment2025Resource;
use App\Filament\Panel2025\Resources\Commitment2025Resource\RelationManagers;
use App\Models\MasterSpatial;
use App\Models\Pks2025;
use App\Models\Realisasi2025;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;

class LocationConfirm extends EditRecord
{
    protected static string $resource = Commitment2025Resource::class;
    protected static ?string $title = 'Halaman Konfirmasi';

    public function getHeading(): string
	{
        return 'Konfirmasi Lokasi Tanam';
	}

    public function getSubheading(): ?string
    {
        $noIjin = $this->record ? $this->record->no_ijin : '##';
        return 'untuk PPRK No: ' . $noIjin;
    }

    public function form(Form $form): Form
	{
		return $form
		->schema([
            Hidden::make('no_ijin'),
            Placeholder::make('Konfirmasi')
                ->columnSpanFull()
                ->hiddenLabel()
                ->extraAttributes(['class'=>'text-primary-500 text-xs'])
                ->content(function($record) {
                    $createdAt = $record->created_at->format('d F Y'); // format sesuai kebutuhan
                    return new HtmlString("<p class='text-danger-500'>Di bawah ini adalah lokasi-lokasi yang telah Anda pilih di menu sebelumnya. Silahkan Konfirmasi kembali pilihan Anda (Pilih Semua). Segera lakukan Perjanjian Kerjasama dengan Kelompoktani dari lokasi-lokasi yang telah Anda pilih di bawah ini untuk kemudian melampirkan berkas PKS selambat-lambatnya 30 (tiga puluh) hari kalender sejak {$createdAt}.</p>");
                }),

            CheckboxList::make('mySpatials')
                ->columnSpanFull()
                ->required()
                ->label('Lokasi')
                ->bulkToggleable()
                ->searchable()
                ->columns(4)
                ->options(function($record) {
                    return MasterSpatial::where('reserved_by', $record->no_ijin)
                        ->orderBy('kode_spatial')
                        ->get()
                        ->mapWithKeys(function($spatial) {
                            return [$spatial->id => $spatial->kode_spatial];
                        });
                })
                ->getOptionLabelFromRecordUsing(function ($record) {
                    $luas = number_format($record->luas_lahan, 0, ',', '.');
                    $html = "
                    <ul role='list' class='divide-y divide-gray-100'>
                        <li class='flex justify-between gap-x-6'>
                            <p class='text-sm/6 text-gray-500'>Lokasi</p>
                            <div class='flex min-w-0 gap-x-4'>
                                <div class='min-w-0 flex-auto'>
                                    <p class='text-sm/6 font-semibold text-gray-900'>{$record->kode_spatial}</p>
                                </div>
                            </div>
                        </li>
                        <li class='flex justify-between gap-x-6'>
                            <p class='text-sm/6 text-gray-500'>Luas Lahan</p>
                            <div class='flex min-w-0 gap-x-4'>
                                <div class='min-w-0 flex-auto'>
                                    <span>{$luas} m²</span>
                                </div>
                            </div>
                        </li>
                        <li class='flex justify-between gap-x-6'>
                            <p class='text-sm/6 text-gray-500'>Kelompok Tani</p>
                            <div class='flex min-w-0 gap-x-4'>
                                <div class='min-w-0 truncate flex-auto'>
                                    <span>{$record->masterpoktan->nama_kelompok}</span>
                                </div>
                            </div>
                        </li>
                        <li class='flex justify-between gap-x-6'>
                            <p class='text-sm/6 text-gray-500'>Petani</p>
                            <div class='flex min-w-0 gap-x-4'>
                                <div class='min-w-0 flex-auto'>
                                    <span>{$record->anggota->nama_petani}</span>
                                </div>
                            </div>
                        </li>
                    </ul>";

                    return new HtmlString($html);
                })

        ]);
    }

    public function mutateFormDataBeforeSave(array $data): array
    {
        $selectedIds = $data['mySpatials'] ?? [];

        $allIds = MasterSpatial::where('reserved_by', $data['no_ijin'])->pluck('id')->toArray();
        $unselectedIds = array_diff($allIds, $selectedIds);

        MasterSpatial::whereIn('id', $selectedIds)->update(['status' => 2]);

        MasterSpatial::whereIn('id', $unselectedIds)->update([
            'status' => 0,
            'reserved_by' => null,
            'reserved_at' => null,
        ]);

        $selectedSpatials = MasterSpatial::whereIn('id', $selectedIds)->get();

        $groupedByPoktan = $selectedSpatials->groupBy(fn($spatial) => $spatial->masterpoktan->kode_poktan);

        foreach ($groupedByPoktan as $kodePoktan => $spatials) {
            Pks2025::updateOrCreate(
                [
                    'no_ijin' => $data['no_ijin'],
                    'kode_poktan' => $kodePoktan,
                ],
                [
					'deadline_at' => optional(Pks2025::where('no_ijin', $data['no_ijin'])
						->where('kode_poktan', $kodePoktan)
						->first())->deadline_at ?? Carbon::today()->addDays(30),
                    'nama_poktan' => $spatials->first()->masterpoktan->nama_kelompok,
                    'npwp' => Auth::user()->datauser->npwp_company,
                    'luas_rencana' => $spatials->sum('luas_lahan'),
                    'jumlah_anggota' => $spatials->count(),
                    'provinsi_id' => $spatials->first()->masterpoktan->provinsi_id,
                    'kabupaten_id' => $spatials->first()->masterpoktan->kabupaten_id,
                    'kecamatan_id' => $spatials->first()->masterpoktan->kecamatan_id,
                    'kelurahan_id' => $spatials->first()->masterpoktan->kelurahan_id,
                ]
            );
        }

        foreach ($selectedSpatials as $spatial) {
            Realisasi2025::updateOrCreate(
                [
                    'no_ijin' => $spatial->reserved_by,
                    'kode_spatial' => $spatial->kode_spatial,
                ],
                [
                    'luas_lahan' => $spatial->luas_lahan,
                    'ktp_petani' => $spatial->anggota->ktp_petani,
                    'npwp' => Auth::user()->datauser->npwp_company,
                    'kode_poktan' => $spatial->kode_poktan,
                ]
            );
        }

        return $data;
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static string | Alignment $formActionsAlignment = Alignment::Right;
    protected function getFormActions(): array
	{
		// $visible = $this->data['visible'];
		// if($visible === 'visible'){
			return [
                $this->getSaveFormAction()
                    ->label('Konfirmasi')
                    ->color('warning')
                    ->icon('icon-check-all')
                    ->requiresConfirmation()
                    ->modalHeading('Konfirmasi Pilihan')
                    ->modalDescription('Anda yakin dengan pilihan ini?')
                    ->modalSubmitActionLabel('Ya, Konfirmasi.'),
                $this->getCancelFormAction()->icon('icon-x-circle')->color('primary')
			];
		// }else{
		// 	return [];
		// }
	}

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->getRecord()]);
    }
}
