<?php

namespace App\Filament\Admin\Resources\MasterSpatialResource\Widgets;

use App\Models\Commitment2025;
use App\Models\MasterSpatial;
use Filament\Support\Enums\IconPosition;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class SpatialStatWidgets extends BaseWidget
{
    protected function getStats(): array
    {
		$chartCountSpatial = MasterSpatial::query()
            ->where('is_active', 1)
            ->selectRaw('kabupaten_id, COUNT(*) as total')
            ->groupBy('kabupaten_id')
            ->pluck('total', 'kabupaten_id')
            ->toArray();

        // Ambil total luas_lahan per kabupaten yang is_active = 1
        $chartSumLahan = MasterSpatial::query()
            ->where('is_active', 1)
            ->selectRaw('kabupaten_id, SUM(luas_lahan) as total_luas')
            ->groupBy('kabupaten_id')
            ->pluck('total_luas', 'kabupaten_id')
            ->toArray();

        // Konversi ke format array numerik untuk chart
        $chartCountArray = array_values($chartCountSpatial); // [jumlah1, jumlah2, jumlah3, ...]
        $chartSumArray = array_values($chartSumLahan);

        return [
            Stat::make('Lahan', number_format(MasterSpatial::query()->where('is_active', 1)->count(),0,',','.') . ' titik')
				->description('Jumlah s.d Tahun ' . date('Y'))
				->descriptionIcon('icon-geo-alt-fill', IconPosition::Before)
				->chart($chartCountArray)
				->color('danger'),
			Stat::make('Luas', number_format(MasterSpatial::query()->where('is_active', 1)->sum('luas_lahan'),0,',','.').' m2')
				->description('Total luas lahan, setara: ' . number_format(MasterSpatial::query()->where('is_active', 1)->sum('luas_lahan') / 10000, 2, ',', '.') . ' ha')
				->chart($chartSumArray)
				->descriptionIcon('icon-map-fill', IconPosition::Before)
				->color('warning'),
			Stat::make('Sebaran', number_format(MasterSpatial::where('is_active', 1)->distinct()->count('kabupaten_id'),0,',','.').' Kab/Kota')
				->description('Wilayah sebaran lahan')
				->descriptionIcon('icon-signpost-split-fill', IconPosition::Before)
				->chart([7, 2, 10, 3, 15, 4, 17])
				->color('success'),
        ];
    }
}