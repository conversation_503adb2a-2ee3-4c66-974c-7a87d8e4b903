<?php

namespace App\Models;

use App\Observers\CompletedObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

#[ObservedBy([CompletedObserver::class])]
class Completed extends Model
{
	use HasFactory, SoftDeletes, LogsActivity;
	public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
			->logFillable('*')
			->logOnlyDirty();
    }

	public $table = 'completeds';

	protected $dates = [
		'created_at',
		'updated_at',
		'deleted_at',
	];

	protected $fillable = [
		'no_skl',
		'periodetahun',
		'no_ijin',
		'npwp',
		'published_date',
		'luas_tanam',
		'volume',
		'status',
		'skl_upload',
		'url',
	];

	protected static function booted()
	{
		static::addGlobalScope('npwp', function (Builder $builder) {
			if (Auth::check()) {
				$user = Auth::user();

				if ($user->hasAnyRole(['admin', 'direktur', 'Super Admin', 'verifikator','riph'])) {
				}
				else {
					$builder->where('npwp', $user->npwp);
				}
			}
		});
	}

	public function user()
	{
		return $this->belongsTo(User::class, 'npwp', 'npwp');
	}

	public function datauser()
	{
		return $this->belongsTo(DataUser::class, 'npwp', 'npwp_company');
	}

	public function commitment2025()
	{
		return $this->belongsTo(Commitment2025::class, 'no_ijin', 'no_ijin');
	}

	public function commitment2024()
	{
		return $this->belongsTo(Commitment2024::class, 'no_ijin', 'no_ijin');
	}
}
