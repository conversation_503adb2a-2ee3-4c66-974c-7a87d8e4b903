<?php

namespace App\Filament\Panel2025\Resources\VerifberkasResource\Pages;

use App\Filament\Panel2025\Resources\VerifberkasResource;
use App\Models\PengajuanVerifikasi;
use App\Models\Userfile;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\SelectColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class VerififikasiBerkas extends ListRecords
{
    protected static string $resource = VerifberkasResource::class;
	protected static ?string $title = 'Verifikasi Berkas';
    public function getHeading(): string
	{
        return 'Verifikasi Berkas';
	}

    public function getSubheading(): ?string
    {
        $kind = request()->route('kind');
		$pengajuan = request()->route('pengajuan') ?? '';
		$dataPengajuan = PengajuanVerifikasi::where('kind', $kind)
            ->where('no_pengajuan', $pengajuan)
            ->first();

		$noIjin = $dataPengajuan->no_ijin;

        return $pengajuan . ' - PPRK: ' . $noIjin;
    }

	public function getTableQuery(): Builder
    {
        $kind = request()->route('kind');
        $pengajuan = request()->route('pengajuan');

        $dataPengajuan = PengajuanVerifikasi::where('kind', $kind)
            ->where('no_pengajuan', $pengajuan)
            ->first();

        if (!$dataPengajuan) {
            return Userfile::query()->whereRaw('1 = 0'); // Kembali dengan hasil kosong
        }

        $noIjin = $dataPengajuan->no_ijin;

        if ($kind === 'PVT') {
            return Userfile::where('no_ijin', $noIjin)
                ->whereIn('kind', [
                    'spvt', 'sptjmt', 'rta',
                    'spht', 'spdst', 'logbook'
                ]);
        } elseif ($kind === 'PVP') {
            return Userfile::where('no_ijin', $noIjin)
                ->whereIn('kind', [
                    'spvt', 'spvp', 'sptjmt', 'sptjmp', 'rta', 'rpo',
                    'spht', 'sphb', 'spdst', 'spdsp', 'logbook', 'la',
                ]);
        } elseif ($kind === 'PVS') {
            return Userfile::where('no_ijin', $noIjin);
        }

        return Userfile::query()->whereRaw('1 = 0'); // Jika tidak cocok, kosongkan hasil
    }

	public function table(Table $table): Table
    {
		$kindLabels = [
			'spvt' => 'Surat Pengajuan Verifikasi (Tanam)',
			'spvp' => 'Surat Pengajuan Verifikasi (Produksi)',
			'spskl' => 'Surat Pengajuan Penerbitan SKL',
			'sptjmt' => 'Surat Pernyataan Tanggung Jawab Mutlak (Periode Tanam)',
			'sptjmp' => 'Surat Pernyataan Tanggung Jawab Mutlak (Periode Produksi)',
			'rta' => 'Form Realisasi Tanam',
			'rpo' => 'Form Realisasi Produksi',
			'spht' => 'Statistik Pertanian Hortikultura (Periode Tanam)',
			'sphb' => 'Statistik Pertanian Hortikultura (Periode Produksi)',
			'spdst' => 'Surat Pengantar Dinas Telah Selesai Tanam',
			'spdsp' => 'Surat Pengantar Dinas Telah Selesai Produksi',
			'logbook' => 'Logbook (Tanam/Produksi)',
			'la' => 'Laporan Akhir',
		];
        return $table
            ->columns([
                TextColumn::make('kind')
					->label('Berkas')
					->formatStateUsing(fn ($state) => $kindLabels[$state] ?? $state),
				TextColumn::make('verif_by')
					->label('Verifikator')
					->formatStateUsing(fn ($record)=>$record->verifikator->name),
				TextColumn::make('verif_at')
					->label('Tanggal Periksa')
					->date(),
				TextColumn::make('file_url')
					->label('Tautan')
					->alignCenter()
					->extraAttributes(['class'=>'text-start'])
					->formatStateUsing(fn ($state) => new HtmlString(
						'<a class="text-start" href="/'. $state .'" rel="nofollow noreferer" target="_blank">Lihat Berkas</a>'
					)),
				// SelectColumn::make('status')
				// 	->options([
				// 		'Sesuai' => 'Sesuai',
				// 		'Tidak Sesuai' => 'Tidak Sesuai',
				// 	])
				// 	->afterStateUpdated(function ($record){
				// 		$record->verif_at = today();
				// 		$record->verif_by = Auth::user()->id;
				// 		$record->save();
				// 	}),
				TextColumn::make('status')
					->badge()
					->alignCenter()
					->color(fn ($record) => $record->status === 'Sesuai' ? 'success' : 'danger')
            ])
            ->filters([
                //
            ])
            ->actions([
                Action::make('verifikasi')
					->hiddenLabel()
					->tooltip('Verifikasi berkas ini')
					->icon('icon-book-half')
					->url(fn ($record) => route('filament.panel2025.resources.verifberkas.berkas', $record->id)),
            ])
            ->bulkActions([
                // BulkActionGroup::make([
                //     DeleteBulkAction::make(),
                // ]),
            ]);
    }
}
