<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\ProfilAdmin;
use App\Models\User;

class ProfilAdminPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any ProfilAdmin');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ProfilAdmin $profiladmin): bool
    {
        return $user->checkPermissionTo('view ProfilAdmin');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create ProfilAdmin');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ProfilAdmin $profiladmin): bool
    {
        return $user->checkPermissionTo('update ProfilAdmin');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ProfilAdmin $profiladmin): bool
    {
        return $user->checkPermissionTo('delete ProfilAdmin');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any ProfilAdmin');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, ProfilAdmin $profiladmin): bool
    {
        return $user->checkPermissionTo('restore ProfilAdmin');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any ProfilAdmin');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, ProfilAdmin $profiladmin): bool
    {
        return $user->checkPermissionTo('replicate ProfilAdmin');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder ProfilAdmin');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, ProfilAdmin $profiladmin): bool
    {
        return $user->checkPermissionTo('force-delete ProfilAdmin');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any ProfilAdmin');
    }
}
