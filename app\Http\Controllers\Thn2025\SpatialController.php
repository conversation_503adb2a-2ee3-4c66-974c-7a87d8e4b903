<?php

namespace App\Http\Controllers\Thn2025;

use App\Http\Controllers\Controller;
use App\Models\ForeignApi;
use App\Models\MasterAnggota;
use App\Models\MasterKabupaten;
use App\Models\MasterPoktan;
use App\Models\MasterSpatial;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class SpatialController extends Controller
{
	public function uploadSpatials()
	{
		$modul = 'Unggah Peta';
		$pageTitle = 'Panel Pemetaan';
		$page_heading = 'Buat Peta Lokasi Tanam Baru';
		$kabupatens = MasterSpatial::distinct()->pluck('kabupaten_id');
		$indexKabupaten = MasterKabupaten::whereIn('kabupaten_id', $kabupatens)
			->select('kabupaten_id', 'nama_kab')
			->get()
			->toArray();
		$mapKey = ForeignApi::where('status', 1)->select('key')->first();
		return view('velzon.spatial.uploadSpatials', compact('mapKey','modul', 'pageTitle', 'page_heading', 'indexKabupaten'));
	}

	public function storesingle(Request $request)
	{
		DB::beginTransaction();

		try {
			$request->validate([
				'kml_url' => 'required|file|mimes:kml,xml,application/vnd.google-earth.kml+xml|max:2048',
			]);

			if ($request->hasFile('kml_url')) {
				$file = $request->file('kml_url');
				$kdLokasi = $request->input('kode_spatial');

				$filename = $kdLokasi . '.' . $file->getClientOriginalExtension();
				$path = 'uploads/kml';
				$filePath = $file->storeAs($path, $filename, 'public');
			} else {
				throw new \Exception('File not found');
			}

			$kelurahanId = $request->input('kelurahan_id');
			$namaKelompok = $request->input('poktan_name');
			$hashedKodePoktan = md5($kelurahanId . $namaKelompok);
			$kodePoktan = 'poktan_' . $hashedKodePoktan;

			$masterPoktan = MasterPoktan::updateOrCreate(
				[
					'kelurahan_id' => $request->input('kelurahan_id'),
					'nama_kelompok' => $request->input('poktan_name'),
				],
				[
					'kode_poktan' => $kodePoktan,
					'provinsi_id' => $request->input('provinsi_id'),
					'kabupaten_id' => $request->input('kabupaten_id'),
					'kecamatan_id' => $request->input('kecamatan_id'),
					'kelurahan_id' => $request->input('kelurahan_id'),
				],
			);

			MasterAnggota::updateOrCreate(
				[
					'ktp_petani' => $request->input('ktp_petani'),
				],
				[
					'kode_poktan' => $masterPoktan->kode_poktan,
					'nama_petani' => $request->input('nama_petani'),
					'provinsi_id' => $request->input('provinsi_id'),
					'kabupaten_id' => $request->input('kabupaten_id'),
					'kecamatan_id' => $request->input('kecamatan_id'),
					'kelurahan_id' => $request->input('kelurahan_id'),
				],
			);

			MasterSpatial::updateOrCreate(
				['kode_spatial' => $request->input('kode_spatial')],
				[
					'kode_poktan' => $masterPoktan->kode_poktan,
					'ktp_petani' => $request->input('ktp_petani'),
					'nama_petani' => $request->input('nama_petani'),
					'latitude' => $request->input('latitude'),
					'longitude' => $request->input('longitude'),
					'ktp_petani' => $request->input('ktp_petani'),
					'nama_petani' => $request->input('nama_petani'),
					'latitude' => $request->input('latitude'),
					'longitude' => $request->input('longitude'),
					'polygon' => $request->input('polygon'),
					'altitude' => $request->input('altitude'),
					'luas_lahan' => $request->input('luas_lahan'),
					'nama_lahan' => $request->input('nama_lahan'),
					'provinsi_id' => $request->input('provinsi_id'),
					'kabupaten_id' => $request->input('kabupaten_id'),
					'kecamatan_id' => $request->input('kecamatan_id'),
					'kelurahan_id' => $request->input('kelurahan_id'),
					'status' => 0,
					'kml_url' => $filePath,
				]
			);

			DB::commit();

			// Periksa apakah request mengharapkan respons JSON (AJAX request)
			$isAjax = $request->ajax() ||
				$request->wantsJson() ||
				$request->header('X-Requested-With') === 'XMLHttpRequest' ||
				$request->header('Accept') === 'application/json';

			if ($isAjax) {
				return response()->json([
					'success' => true,
					'message' => 'Data berhasil disimpan',
					'file_path' => $filePath
				]);
			}

			// Respons normal untuk request non-AJAX
			return redirect()->back()->with('success', 'Data successfully saved.');
		} catch (\Exception $e) {
			DB::rollBack();

			if (isset($filePath)) {
				Storage::disk('public')->delete($filePath);
			}

			// Periksa apakah request mengharapkan respons JSON (AJAX request)
			$isAjax = $request->ajax() ||
				$request->wantsJson() ||
				$request->header('X-Requested-With') === 'XMLHttpRequest' ||
				$request->header('Accept') === 'application/json';

			if ($isAjax) {
				return response()->json([
					'success' => false,
					'message' => $e->getMessage()
				], 422);
			}

			// Respons normal untuk request non-AJAX
			return redirect()->back()->with('error', $e->getMessage());
		}
	}
}
