<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Completed;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Illuminate\Support\Facades\Auth;

class LeaderboardWidget extends BaseWidget
{
	protected static string $view = 'filament.admin.resources.leaderboard-widget';
	protected static ?int $sort = 1;

	protected int | string | array $columnSpan = [2];
	public static function canView(): bool
	{
		$user = Auth::user();
		return $user->hasAnyRole(['admin', 'Super Admin', 'importir']);
	}
	protected function getViewData(): array
	{
		return [
			'skl' => Completed::query()
				->selectRaw('
					completeds.npwp,
					COUNT(completeds.npwp) as total_completed,
					(
						SELECT COUNT(*) FROM pull_riphs WHERE pull_riphs.npwp = completeds.npwp
					) + (
						SELECT COUNT(*) FROM t2025_commitments WHERE t2025_commitments.npwp = completeds.npwp
					) as total_commitments,
					SUM(completeds.luas_tanam) as total_luas_lahan,
					SUM(completeds.volume) as total_volume,
					CASE 
						WHEN SUM(completeds.luas_tanam) > 0 THEN SUM(completeds.volume) / SUM(completeds.luas_tanam) 
						ELSE 0 
					END as efisiensi,
					COUNT(completeds.npwp) / NULLIF(
						(
							(SELECT COUNT(*) FROM pull_riphs WHERE pull_riphs.npwp = completeds.npwp) +
							(SELECT COUNT(*) FROM t2025_commitments WHERE t2025_commitments.npwp = completeds.npwp)
						), 0
					) as completion_ratio
				')
				->groupBy('completeds.npwp')
				->orderByDesc('completion_ratio')
				->orderByDesc('total_completed')
				->orderByDesc('efisiensi')
				->limit(5)
				->get(),
		];
	}

}
