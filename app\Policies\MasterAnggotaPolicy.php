<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\MasterAnggota;
use App\Models\User;

class MasterAnggotaPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any MasterAnggota');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, MasterAnggota $masteranggota): bool
    {
        return $user->checkPermissionTo('view MasterAnggota');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create MasterAnggota');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, MasterAnggota $masteranggota): bool
    {
        return $user->checkPermissionTo('update MasterAnggota');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, MasterAnggota $masteranggota): bool
    {
        return $user->checkPermissionTo('delete MasterAnggota');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any MasterAnggota');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, MasterAnggota $masteranggota): bool
    {
        return $user->checkPermissionTo('restore MasterAnggota');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any MasterAnggota');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, MasterAnggota $masteranggota): bool
    {
        return $user->checkPermissionTo('replicate MasterAnggota');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder MasterAnggota');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, MasterAnggota $masteranggota): bool
    {
        return $user->checkPermissionTo('force-delete MasterAnggota');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any MasterAnggota');
    }
}
