<?php

namespace App\Filament\Panel2025\Pages;

use App\Models\Commitment2025;
use Filament\Facades\Filament;
use Filament\Pages\Page;
use Filament\Support\Facades\FilamentIcon;
use Filament\Widgets\Widget;
use Filament\Forms\Components\{DatePicker, Section, Select};
use Filament\Forms\Form;
use Filament\Widgets\WidgetConfiguration;
use Filament\Pages\Dashboard as BaseDashboard;
use Filament\Pages\Dashboard\Concerns\HasFiltersForm;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class Dashboard extends BaseDashboard
{
	use HasFiltersForm;
    protected static string $routePath = '/';

    protected static ?int $navigationSort = -98;
	protected static ?string $title = 'Panel 2025';
	public function filtersForm(Form $form): Form
    {
		$periodetahunOptions = Commitment2025::select('periodetahun')
		->distinct()
		->pluck('periodetahun', 'periodetahun')
		->toArray();
	
		// Ambil nilai maksimum periodetahun dari database
		$maxPeriode = Commitment2025::max('periodetahun');
		
		// Tambahkan opsi "All" di awal array
		$periodetahunOptions = ['all' => 'Semua Periode'] + $periodetahunOptions;
		
		// Set default ke nilai maksimum periodetahun
		$defaultPeriode = $maxPeriode ?? 'all';
			return $form
				->schema([
					Select::make('periodeFilter')
						->label('Pilih Periode')
						->inlineLabel()
						->columnStart(3)
						->options($periodetahunOptions)  // Opsi dengan tambahan "All"
						->default($defaultPeriode)  // Set default ke max(periodetahun) atau "All" jika kosong
						->placeholder('Pilih Periode Tahun')
						->live(),
				])->columns(3);
		
	}

    /**
     * @var view-string
     */
    protected static string $view = 'filament-panels::pages.dashboard';

    public static function getNavigationLabel(): string
    {
        return static::$navigationLabel ??
            static::$title ??
            __('filament-panels::pages/dashboard.title');
    }

    public static function getNavigationIcon(): string | Htmlable | null
    {
        return static::$navigationIcon
            ?? FilamentIcon::resolve('panels::pages.dashboard.navigation-item')
            ?? (Filament::hasTopNavigation() ? 'heroicon-m-home' : 'heroicon-o-home');
    }

    public static function getRoutePath(): string
    {
        return static::$routePath;
    }

    /**
     * @return array<class-string<Widget> | WidgetConfiguration>
     */
    public function getWidgets(): array
    {
        return Filament::getWidgets();
    }

    /**
     * @return array<class-string<Widget> | WidgetConfiguration>
     */
    public function getVisibleWidgets(): array
    {
        return $this->filterVisibleWidgets($this->getWidgets());
    }

    /**
     * @return int | string | array<string, int | string | null>
     */
    public function getColumns(): int | string | array
    {
        return 2;
    }

    public function getTitle(): string | Htmlable
    {
        return new HtmlString('Panel 2025');
    }

	public function getSubheading(): string|Htmlable
    {
        return new HtmlString('<span class="text-md">Panel ini digunakan untuk memantau data mulai tahun 2025</span>');
    }
}
