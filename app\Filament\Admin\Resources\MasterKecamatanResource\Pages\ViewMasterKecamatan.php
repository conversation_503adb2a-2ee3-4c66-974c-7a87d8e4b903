<?php

namespace App\Filament\Admin\Resources\MasterKecamatanResource\Pages;

use App\Filament\Admin\Resources\MasterKecamatanResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;

class ViewMasterKecamatan extends ViewRecord
{
    protected static string $resource = MasterKecamatanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

	public function getTitle(): string|Htmlable
	{
		$nama = $this->record ? $this->record->nama_kecamatan : '##';
        return 'Data Kecamatan ' . $nama;
	}

    public function getHeading(): string
	{
        $nama = $this->record ? $this->record->nama_kecamatan : '##';
        return 'Data Kecamatan ' . $nama;
	}
}
