<?php

namespace App\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Observers\Commitment2025Observer;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

#[ObservedBy([Commitment2025Observer::class])]
class Commitment2025 extends Model
{
	use HasFactory;
	use SoftDeletes, LogsActivity;
	public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
			->logFillable('*')
			->logOnlyDirty();
    }

	public $table = 't2025_commitments';

	protected $fillable = [
		'user_id',
		'npwp',
		'keterangan',
		'nama',
		'no_ijin',
		'periodetahun',
		'tgl_ijin',
		'tgl_akhir',
		'no_hs',
		'volume_riph',
		'volume_produksi',
		'luas_wajib_tanam',
        'kebutuhan_benih',
        'beli_penangkar',
		'stok_mandiri',
		'pupuk_organik',
		'npk',
		'dolomit',
		'za',
		'mulsa',
		'status',
		'formRiph',
		'formSptjm',
		'logBook',
		'formRt',
		'formRta',
		'formRpo',
		'formLa',
		'no_doc',
		'poktan_share',
		'importir_share',
		'status',
		'skl',
		'datariph'
	];

	protected $dates = [
		'created_at',
		'updated_at',
		'deleted_at',
	];

	protected static function booted()
	{
		static::addGlobalScope('npwp', function (Builder $builder) {
			if (Auth::check()) {
				$user = Auth::user();

				if ($user->hasAnyRole(['importir'])) {
					$builder->where('npwp', $user->npwp);
				}
			}
		});
	}

	public function getFormattedNoIjinAttribute()
    {
        // return str_replace(['/', '.', ' '], '', $this->no_ijin);
    }

	public function user()
	{
		return $this->belongsTo(User::class, 'npwp', 'npwp');
	}

	public function myRegions(): HasMany
	{
		return $this->hasMany(CommitmentRegion::class, 'no_ijin', 'no_ijin');
	}

	public function myPenangkars(): HasMany
	{
		return $this->hasMany(Penangkar2025::class, 'no_ijin', 'no_ijin');
	}

	public function mySpatials(): HasMany
	{
		return $this->hasMany(MasterSpatial::class, 'reserved_by', 'no_ijin');
	}

	public function pks(): HasMany
	{
		return $this->hasMany(Pks2025::class, 'no_ijin', 'no_ijin');
	}

	public function realisasi(): HasMany
	{
		return $this->hasMany(Realisasi2025::class, 'no_ijin', 'no_ijin');
	}

	public function userfiles(): HasMany
	{
		return $this->hasMany(Userfile::class, 'no_ijin', 'no_ijin');
	}

	public function ajuverif(): HasMany
	{
		return $this->hasMany(PengajuanVerifikasi::class, 'no_ijin', 'no_ijin');
	}

	public function completed(): HasOne
	{
		return $this->hasOne(Completed::class, 'no_ijin', 'no_ijin');
	}

	//customs
	public function detailrealisasitanam(): HasMany{
		return $this->hasMany(DetailRealisasi2025::class, 'no_ijin', 'no_ijin')->where('jenis_keg', 'tanam');
	}

	public function detailrealisasiproduksi(): HasMany{
		return $this->hasMany(DetailRealisasi2025::class, 'no_ijin', 'no_ijin')->where('jenis_keg', 'panen');
	}

	public function detailrealisasidistribusi(): HasMany{
		return $this->hasMany(DetailRealisasi2025::class, 'no_ijin', 'no_ijin')->where('jenis_keg', 'distribusi');
	}

	public function detailrealisasipupuk(): HasMany{
		return $this->hasMany(DetailRealisasi2025::class, 'no_ijin', 'no_ijin')->where('jenis_keg', 'pupuk');
	}

	public function detailrealisasimulsa(): HasMany{
		return $this->hasMany(DetailRealisasi2025::class, 'no_ijin', 'no_ijin')->where('jenis_keg', 'mulsa');
	}
	

	public function dataverifikasitanam(): HasOne{
		return $this->hasOne(PengajuanVerifikasi::class, 'no_ijin', 'no_ijin')
        ->where('kind', 'PVT')
        ->latest();
	}

	public function dataverifikasiproduksi(): HasOne{
		return $this->hasOne(PengajuanVerifikasi::class, 'no_ijin', 'no_ijin')
        ->where('kind', 'PVP')
        ->latest();
	}

	public function dataverifikatortanam(): HasMany {
		$latestPengajuan = PengajuanVerifikasi::where('no_ijin', $this->no_ijin)
			->where('kind', 'PVT')
			->latest()
			->first();
	
		return $this->hasMany(VerificatorAssignment::class, 'no_ijin', 'no_ijin')->where('kode_pengajuan', $latestPengajuan->no_pengajuan);
	}

	public function dataverifikatorproduksi(): HasMany {
		$latestPengajuan = PengajuanVerifikasi::where('no_ijin', $this->no_ijin)
			->where('kind', 'PVP')
			->latest()
			->first();
	
		return $this->hasMany(VerificatorAssignment::class, 'no_ijin', 'no_ijin')->where('kode_pengajuan', $latestPengajuan->no_pengajuan);
	}
}
