<?php

namespace App\Models;

use App\Traits\Auditable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class MasterPoktan2024 extends Model
{
	use HasFactory, LogsActivity;
	public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
			->logFillable('*')
			->logOnlyDirty();
    }

	public $table = 'master_poktans';

	protected $dates = [
		'created_at',
		'updated_at',
		'deleted_at',
	];

	protected $fillable = [
		'id',
		'npwp',
		'poktan_id',
		'id_provinsi',
		'id_kabupaten',
		'id_kecamatan',
		'id_kelurahan',
		'nama_kelompok',
		'nama_pimpinan',
		'hp_pimpinan',
		'status'
	];

	protected static function booted()
	{
		static::addGlobalScope('npwp', function (Builder $builder) {
			if (Auth::check()) {
				$user = Auth::user();

				if ($user->hasAnyRole(['admin', 'direktur', 'Super Admin', 'verifikator'])) {
				}
				else {
					$builder->where('npwp', $user->npwp);
				}
			}
		});
	}

	public function pks()
	{
		return $this->belongsTo(Pks2024::class, 'poktan_id', 'poktan_id');
	}


	public function anggota()
	{
		return $this->hasMany(MasterAnggota2024::class, 'poktan_id', 'poktan_id');
	}

	public function desa()
	{
		return $this->belongsTo(MasterDesa::class, 'id_kelurahan', 'kelurahan_id');
	}
}
