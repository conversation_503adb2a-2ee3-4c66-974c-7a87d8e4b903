@php
    $messages = \App\Models\SupportTicketMessage::where('ticket_id', $ticket->id)
        ->whereNull('parent_id')
        ->with([
            'user',
            'replies.user',
            'replies.replies.user'
        ])
        ->orderBy('created_at', 'asc')
        ->get();
@endphp

<div class="space-y-4">
    @if($messages->isEmpty())
        <div class="text-center py-8 text-gray-500 dark:text-gray-400">
            <p>Belum ada diskusi untuk tiket ini.</p>
        </div>
    @else
        @foreach($messages as $message)
            @include('filament.admin.widgets.partials.message-display', ['message' => $message, 'depth' => 0])
        @endforeach
    @endif
</div>
