<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\AjuVerifSkl2024Resource\Pages;
use App\Filament\Admin\Resources\AjuVerifSkl2024Resource\RelationManagers;
use App\Models\AjuVerifSkl2024;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

class AjuVerifSkl2024Resource extends Resource
{
    protected static ?string $model = AjuVerifSkl2024::class;
	protected static ?string $navigationLabel = 'Pengajuan SKL V3';
    protected static ?string $navigationIcon = 'icon-award-v3';
	protected static ?int $navigationSort = 2;
	public static function getNavigationGroup(): ?string
	{
		return 'SKL';
	}

	public static function shouldRegisterNavigation(): bool
    {
        $user = Auth::user();
        if ($user->hasAnyRole(['admin','Super Admin','direktur', 'importir'])) {
			return true;
        }else{
        	return false;
        }
    }

	public static function getNavigationBadge(): ?string
	{
		if (Auth::user()->hasRole('direktur')) {
			return static::getModel()::where('status', 2)
			->count();
		} else {
			return static::getModel()::whereIn('status', [1, 2, 3,5])
				->orWhere(function ($query) {
					$query->where('status', 4)
						->where(function ($subQuery) {
							$subQuery->whereDoesntHave('completed')
								->orWhereHas('completed', function ($q) {
									$q->whereNull('url')->orWhere('url', '');
								});
						});
				})
				->count();
		}
	}

	public static function getNavigationBadgeColor(): ?string
	{
		return static::getModel()::count() > 0 ? 'warning' : 'danger';
	}

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                //
            ])
            ->filters([
                //
            ])
            ->actions([
                // Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAjuVerifSkl2024s::route('/'),
            'create' => Pages\CreateAjuVerifSkl2024::route('/create'),
            'rekomendasi' => Pages\ApprovalRekomendasi2024::route('/{record}/approval-rekomendasi'),
            'final-verification' => Pages\FinalVerification2024::route('/{record}/final-verification'),
            'view-verification' => Pages\ViewVerification2024::route('/{record}/view-verification'),
            // 'edit' => Pages\EditAjuVerifSkl2024::route('/{record}/edit'),
        ];
    }
}
