<?php

namespace App\Http\Controllers\Thn2025;

use App\Http\Controllers\Controller;
use App\Models\CommitmentRegion;
use App\Models\MasterSpatial;
use App\Models\Realisasi2025;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CommitmentRegionController extends Controller
{
    public function calculateFullfilled($record)
    {
        $commitmentRegion = CommitmentRegion::findOrFail($record);
        $kabId = $commitmentRegion->kabupaten_id;
        $noIjin = $commitmentRegion->no_ijin;
        $spatialCodes = MasterSpatial::where('reserved_by', $noIjin)
            ->where('kabupaten_id', $kabId)
            ->pluck('kode_spatial');

        $realisasis = Realisasi2025::where('no_ijin', $noIjin)
            ->whereIn('kode_spatial', $spatialCodes)
			->with('detailrealisasi')
            ->get();

        $fullfill = $realisasis->flatMap->detailrealisasi
        ->where('jenis_keg', 'tanam')
        ->sum('value');
        $commitmentRegion->fullfilled = $fullfill;
        $commitmentRegion->save();
        return redirect()->back();
    }
}
