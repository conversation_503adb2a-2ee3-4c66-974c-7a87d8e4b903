<?php

namespace App\Filament\Panel2024\Resources\Pks2024Resource\Pages;

use App\Filament\Panel2024\Resources\Pks2024Resource;
use App\Models\Commitment2024;
use App\Models\Varietas;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Forms\Components\{DatePicker, FileUpload, Placeholder, Section, Select, TextInput};
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Support\HtmlString;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class EditPks2024 extends EditRecord
{
    protected static string $resource = Pks2024Resource::class;
    protected static ?string $title = 'PKS';
    public function getHeading(): string
	{
        $noPks = $this->record ? $this->record->masterpoktan->nama_kelompok : '#';
        return 'Poktan '.$noPks;
	}
    public function getSubheading(): ?string
    {
        $noIjin = $this->record ? $this->record->no_ijin : '##';
        return 'untuk RIPH No: ' . $noIjin;
    }

    public static string | Alignment $formActionsAlignment = Alignment::Right;
    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            // Actions\DeleteAction::make(),
        ];
    }

	protected function getFormActions(): array
	{
		return [
			$this->getSaveFormAction()->label('Simpan untuk Pelaporan'),
			Action::make('back')
            ->label('Kembali')
            ->color('success')
            ->url(fn () => route('filament.panel2024.resources.commitment2024s.edit', $this->record->commitment->id)),
			$this->getCancelFormAction(),
		];
	}

	public function form(Form $form): Form
    {
		return $form
            ->schema([
				Section::make('Data Mitra')
					->aside()
					->description('Profil Mitra Kelompok Tani')
					->schema([
						Placeholder::make('Nama Kelompok')
							->inlineLabel()
							->content(fn ($record) => $record->masterpoktan->nama_kelompok),
						Placeholder::make('Pimpinan')
							->inlineLabel()
							->content(fn ($record) => $record->masterpoktan->nama_pimpinan),
						Placeholder::make('Jumlah Anggota')
							->inlineLabel()
							->content(fn ($record) => $record->lokasi->count() . ' orang'),
						Placeholder::make('Jumlah Realisasi Lahan')
							->inlineLabel()
							->content(fn ($record) => $record->datarealisasi->count() . ' petak'),
						Placeholder::make('Total Luas')
							->inlineLabel()
							->content(fn ($record) => number_format($record->lokasi->sum('luas_lahan'),3,',','.') . ' ha / Realisasi: ' . number_format($record->datarealisasi->sum('luas_lahan'),3,',','.') . ' ha'),

						Placeholder::make('Domisili')
							->inlineLabel()
							->content(fn ($record) => new HtmlString('
								<p>'. $record->masterpoktan->desa->nama_desa . ' - '.
								$record->masterpoktan->desa->kecamatan->nama_kecamatan . '</p>'.
								'<p>'. $record->masterpoktan->desa->kecamatan->kabupaten->nama_kab .' - '.
								$record->masterpoktan->desa->kecamatan->kabupaten->provinsi->nama.'</p>')
							),

					]),
				Section::make('Data Perjanjian')
					->aside()
					->description('Lengkapi Data Perjanjian dengan Mitra Poktan.')
					->schema([
						TextInput::make('no_perjanjian')
							->inlineLabel(),
						DatePicker::make('tgl_perjanjian_start')
							->label('Mulai Berlaku')
							->inlineLabel()
							->live()
							->required()
							->closeOnDateSelection(),
						DatePicker::make('tgl_perjanjian_end')
							->label('Sampai dengan')
							->inlineLabel()
							->reactive()
							->required()
							->closeOnDateSelection()
							->minDate(fn ($get) => $get('tgl_perjanjian_start')),

						TextInput::make('periode_tanam')
							->inlineLabel(),

						Select::make('varietas_tanam')
							->inlineLabel()
							->options(
								Varietas::whereNotNull('nama_varietas')
									->where('nama_varietas', '!=', '')
									->pluck('nama_varietas', 'id')
									->filter()
									->toArray()
							),

						FileUpload::make('berkas_pks')
							->openable()
							// ->required()
							->maxSize(2048)
							->inlineLabel()
							->columnSpan(1)
							->downloadable()
							->deletable()
							->label('Berkas PKS')
							->visibility('public')
							->panelAspectRatio('5:1')
							->imagePreviewHeight('50')
							->fetchFileInformation(true)
							->helperText('Maksimal 2MB, format PDF')
							->disk('public')
							->directory(function ($record) {
								$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
								$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
								return "uploads/{$cleanNpwp}/{$cleanNoIjin}/dokumen/pks";
							})
							->rules([
								'file',
								'mimetypes:application/pdf',
								'mimes:pdf',
								'max:2048',
							])
							->validationMessages([
								'mimetypes' => 'Hanya file PDF yang diperbolehkan',
								'mimes' => 'Ekstensi file harus .pdf',
							])
							->getUploadedFileNameForStorageUsing(
								function (TemporaryUploadedFile $file, $get, $record): string {
									$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
									$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

									// Format nama file: [ID]_[NPWP]_[NOIJIN].[ext]
									return 'pks_'.$record->id . '_' . $cleanNpwp . '_' . $cleanNoIjin . '_' . uniqid() .'.' . $file->getClientOriginalExtension();
								}
							),
					])
			]);
	}

	public function getRelations(): array
    {
        return [
            //
        ];
    }

	public function hasCombinedRelationManagerTabsWithContent(): bool
	{
		return true;
	}
}
