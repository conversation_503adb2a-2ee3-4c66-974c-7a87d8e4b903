<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Announcement;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class UnreadAnnouncements extends Widget
{
    protected static string $view = 'filament.admin.resources.unread-announcements';

	protected static ?int $sort = -1;
    protected int|string|array $columnSpan = 'full';

    protected function getData(): array
    {
        $userId = Auth::id();

        $announcements = Announcement::where(function ($query) use ($userId) {
                $query->whereNull('read_by') // Handle jika JSON kosong
                    ->orWhereNotJsonContains('read_by', $userId);
            })
            ->latest()
            ->take(5)
            ->get();
			Log::info($announcements);
        return compact('announcements'); // Kirim data dengan benar ke Blade
    }
	public static function canView(): bool
	{
		$user = Auth::user();
	
		return !$user->hasAnyRole(['admin', 'Super Admin']);
	}
}