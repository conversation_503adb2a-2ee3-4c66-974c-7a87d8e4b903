<?php

namespace App\Filament\Admin\Resources\SupportTicketResource\Pages;

use App\Filament\Admin\Resources\SupportTicketResource;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\Auth;

class SupportTicket extends Page
{
    protected static string $resource = SupportTicketResource::class;
    protected static string $view = 'filament.admin.resources.support-ticket-resource.pages.support-ticket';

	protected static ?string $title = null;

    // public function mount(): mixed
    // {
    //     if (Auth::check() && !Auth::user()->hasRole('Super Admin')) {
    //         return redirect(SupportTicketResource::getUrl('list'));
    //     }
    //     return null;
    // }
}
