<?php

namespace App\Providers\Filament;

use Althinect\FilamentSpatieRolesPermissions\FilamentSpatieRolesPermissionsPlugin;
use Filament\Http\Middleware\Authenticate;
use Laravel\Sanctum\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
// use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use App\Filament\Pages\Auth\Login;
use DiogoGPinto\AuthUIEnhancer\AuthUIEnhancerPlugin;
use Filament\Navigation\NavigationItem;
use Joaopaulolndev\FilamentEditProfile\FilamentEditProfilePlugin;
use Joaopaulolndev\FilamentEditProfile\Pages\EditProfilePage;
Use App\Filament\Admin\Widgets;
use App\Filament\Panel2025\Pages\Dashboard;
use Filament\Navigation\MenuItem;
use Illuminate\Support\Facades\Auth;

class Panel2025PanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('panel2025')
            ->path('panel/2025')
            ->brandName('Simethris v4.0 @2025')
            ->breadcrumbs(false)
            ->brandLogo(asset('assets/img/logo-simet.png'))
            // ->topbar(true)
            ->topNavigation(true)
			->favicon(asset('assets/img/favicon.png'))
			->databaseNotifications()
            ->colors([
                'danger' => Color::Red,
                'gray' => Color::Gray,
                'info' => Color::Blue,
                'primary' => Color::Indigo,
                'success' => Color::Emerald,
                'warning' => Color::Orange,
            ])
            ->discoverResources(in: app_path('Filament/Panel2025/Resources'), for: 'App\\Filament\\Panel2025\\Resources')
            ->discoverPages(in: app_path('Filament/Panel2025/Pages'), for: 'App\\Filament\\Panel2025\\Pages')
            ->pages([
                Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Panel2025/Widgets'), for: 'App\\Filament\\Panel2025\\Widgets')
            ->widgets([
                // Widgets\AccountWidget::class,
                // Widgets\FilamentInfoWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class, // Using Laravel Sanctum's AuthenticateSession
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
			->userMenuItems([
				MenuItem::make()
					->label('Beranda')
					->url('/')
					->icon('heroicon-o-home'),
				MenuItem::make()
					->label('Profil Saya')
					->url(function () {
						if(Auth::user()->hasRole('importir')){
							if(is_null(Auth::user()->datauser))
							{
								return '/admin/datausers/create';
							}
							return '/admin/datausers/' . Auth::user()->datauser->id . '/view';
						}

						return '/admin/users/'. Auth::user()->id .'/myprofile';
					})
					->icon('heroicon-o-user-circle')
					->visible(function () {
						$user = Auth::user();

						// Jika user adalah importir, cek apakah datauser ada
						if ($user->hasRole('importir')) {
							return $user->relationLoaded('datauser')
								? !is_null($user->datauser)
								: $user->datauser()->exists(); // Cek di database jika belum ter-load
						}
						return true;
					}),
			])
            ->navigationItems([
                NavigationItem::make('Panel Utama')
                    ->url('/admin', shouldOpenInNewTab: false)
                    ->icon('heroicon-o-arrow-left-end-on-rectangle')
                    // ->group('Pilih Tahun')
                    ->sort(-99),
            ])
            // ->login(Login::class)
            ->viteTheme('resources/css/filament/panel2025/theme.css')
            // ->plugin(FilamentSpatieRolesPermissionsPlugin::make())
            ->plugins([
                AuthUIEnhancerPlugin::make()
                    ->showEmptyPanelOnMobile(false)
                    ->formPanelPosition('right')
                    ->formPanelWidth('40%')
                    ->emptyPanelBackgroundImageOpacity('90%')
                    ->emptyPanelBackgroundImageUrl(asset('assets/img/simet-bawang-pagi.webp')),

                FilamentEditProfilePlugin::make()
                    ->slug('my-profile')
                    ->setTitle('My Profile')
                    ->setNavigationLabel('My Profile')
                    ->setNavigationGroup('Group Profile')
                    ->setIcon('heroicon-o-user')
                    ->setSort(10)
                 //    ->canAccess(fn () => Auth::user()->id === 1)
                    ->shouldRegisterNavigation(false)
                    ->shouldShowDeleteAccountForm(false)
                 //    ->shouldShowSanctumTokens()
                    ->shouldShowBrowserSessionsForm()
                    ->shouldShowAvatarForm(
                         value: true,
                         directory: '/uploads/avatars', // image will be stored in 'storage/app/public/avatars
                         rules: 'mimes:jpeg,png|max:1024' //only accept jpeg and png files with a maximum size of 1MB
                     )
            ]);
    }
}
