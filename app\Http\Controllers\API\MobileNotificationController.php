<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Notifications\DatabaseNotification;

class MobileNotificationController extends Controller
{
    /**
     * Get user notifications
     */
    public function index(Request $request)
    {
        $user = $request->user();

        // Ambil notifikasi dari database
        $notifications = $user->notifications()
            ->orderBy('created_at', 'desc')
            ->limit(50)
            ->get()
            ->map(function ($notification) {
                // Format data notifikasi untuk mobile
                return [
                    'id' => $notification->id,
                    'type' => class_basename($notification->type),
                    'title' => $this->getNotificationTitle($notification),
                    'message' => $this->getNotificationMessage($notification),
                    'created_at' => $notification->created_at,
                    'read_at' => $notification->read_at,
                    'data' => $notification->data,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $notifications,
            'message' => 'Notifications retrieved successfully'
        ]);
    }

    /**
     * Mark a notification as read
     */
    public function markAsRead(Request $request, $id)
    {
        $user = $request->user();

        // Cari notifikasi berdasarkan ID
        $notification = $user->notifications()->where('id', $id)->first();

        if (!$notification) {
            return response()->json([
                'success' => false,
                'message' => 'Notification not found'
            ], 404);
        }

        // Gunakan metode markAsRead() yang sudah ada
        if ($notification->read_at === null) {
            $notification->markAsRead();
        }

        return response()->json([
            'success' => true,
            'message' => 'Notification marked as read'
        ]);
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead(Request $request)
    {
        $user = $request->user();

        // Gunakan metode markAsRead() untuk semua notifikasi yang belum dibaca
        $user->unreadNotifications->markAsRead();

        return response()->json([
            'success' => true,
            'message' => 'All notifications marked as read'
        ]);
    }

    /**
     * Clear all notifications for the authenticated user
     */
    public function clearAll(Request $request)
    {
        $user = $request->user();

        // Hapus semua notifikasi pengguna
        $user->notifications()->delete();

        return response()->json([
            'success' => true,
            'message' => 'All notifications cleared successfully'
        ]);
    }

    /**
     * Get notification title based on type
     */
    private function getNotificationTitle($notification)
    {
        $type = class_basename($notification->type);

        // Ekstrak judul berdasarkan tipe notifikasi
        if (strpos($type, 'Announcement') !== false) {
            return 'Pengumuman Baru';
        } elseif (strpos($type, 'SklIssued') !== false) {
            return 'SKL TERBIT';
        } elseif (strpos($type, 'SklPublished') !== false) {
            return 'SKL Telah Terbit!!';
        }

        // Default title jika tidak ada yang cocok
        return $notification->data['title'] ?? 'Notifikasi';
    }

    /**
     * Get notification message based on type
     */
    private function getNotificationMessage($notification)
    {
        $type = class_basename($notification->type);
        $data = $notification->data;

        // Ekstrak pesan berdasarkan tipe notifikasi
        if (strpos($type, 'Announcement') !== false) {
            return 'Administrator telah membuat pengumuman baru bersifat ' . ($data['priority'] ?? 'Penting');
        } elseif (strpos($type, 'SklIssued') !== false || strpos($type, 'SklPublished') !== false) {
            if (isset($data['message'])) {
                return $data['message'];
            } elseif (isset($data['skl_no'])) {
                return 'SELAMAT! Surat Keterangan Lunas (SKL) telah diterbitkan.';
            }
        }

        // Default message jika tidak ada yang cocok
        return $data['message'] ?? 'Anda memiliki notifikasi baru';
    }
}
