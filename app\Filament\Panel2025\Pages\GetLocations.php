<?php

namespace App\Filament\Panel2025\Pages;

use App\Models\Commitment2025;
use App\Models\CommitmentRegion;
use App\Models\MasterSpatial;
use App\Models\Realisasi2025;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Facades\Filament;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Resources\Pages\Concerns\InteractsWithRecord;
use Filament\Tables\Actions\{Action as TableAction, DeleteAction};
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\On;

class GetLocations extends Page
{
    // use InteractsWithTable;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static string $view = 'filament.panel2025.pages.get-locations';

    public ?int $record = null;
	public ?Realisasi2025 $realisasi = null;

    public static function getRoutePath(): string
    {
        return 'get-locations/{record}';
    }

    protected static bool $shouldRegisterNavigation = false;

    public function mount(?int $record = null): void
    {
        $this->record = $record;
		if ($record) {
			$this->realisasi = Realisasi2025::with(['commitment', 'poktan', 'spatial', 'anggota'])->find($record);
		}
    }

    protected function loadLocations()
    {
        if (!$this->record) {
            return [];
        }

        $record = Realisasi2025::find($this->record);
        if (!$record) {
            return [];
        }
		$lokasi = $record->spatial->toArray();

		$data = [
			'lokasi' => $lokasi,
		];
        return $data;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
				Section::make('')
					->columnSpan(2)
					->schema([
						Placeholder::make('verifikasi tanam')
						->inlineLabel()
						->content(fn() => view('components.status-badge-verifikasi', ['status' => $this->realisasi->vt_status])),
					]),
                Placeholder::make('map')
                    ->label('Peta')
					->hiddenLabel()
                    ->live(true)
                    ->reactive()
					->columnSpan(4)
                    ->content(fn () => view('components.map', ['data' => $this->loadLocations()])),

                Section::make('Persiapan Lahan')
					->extraAttributes(['class'=>'mt-3'])
					->aside()
					->schema([
						Placeholder::make('Persiapan Lahan')
							// ->hiddenLabel()
							->inlineLabel()
							->content(fn ()=>$this->realisasi->kode_spatial),
						Repeater::make('DataRealisasi')
							->addable(false)
							->deletable(false)
							->schema([

							])
					])
            ])->columns(6);
    }

	public function infolist(Infolist $infolist): Infolist
	{
		return $infolist
			->schema([

			]);
	}

    // protected function getTableQuery(): Builder
    // {
    //     $record = Commitment2025::find($this->record);
    //     $noIjin = $record->no_ijin;
    //     return MasterSpatial::where('reserved_by', $noIjin);
    // }

    // public function table(Table $table): Table
    // {
    //     return $table
    //         ->query(self::getTableQuery())
    //         ->columns([
    //             TextColumn::make('kode_spatial')->label('Kode Spatial'),
    //             TextColumn::make('latitude')->label('Latitude'),
    //             TextColumn::make('longitude')->label('Longitude'),
    //         ])
    //         ->actions([
    //             TableAction::make('unmark')
    //                 ->label('Batal')
    //                 ->visible(fn ()=> Auth::user()->hasRole('importir'))
    //                 ->tooltip('Buang dari daftar')
    //                 ->color('warning')
    //                 ->icon('icon-x-circle')
    //                 ->requiresConfirmation()
	// 				->modalHeading('Batalkan Lokasi ini')
	// 				->modalDescription('Anda akan membatalkan lokasi ini, lanjutkan?')
    //                 ->action(function ($record){
    //                     $record->update([
    //                         'status' => 0,
    //                         'reserved_by' => null,
    //                         'reserved_at' => null,
    //                         'deadline_at' => null,
    //                     ]);
    //                     $this->locations = $this->loadLocations();
    //                     $this->dispatch('refreshMarkers', locations: $this->locations);
    //                     $this->dispatch('reinitMap');
    //                     Notification::make()
    //                         ->title('Berhasil')
    //                         ->body('Lokasi telah dibatalkan.')
    //                         ->success()
    //                         ->send();
    //                 }),
    //             DeleteAction::make(),
    //         ])
    //         ;
    // }
}
