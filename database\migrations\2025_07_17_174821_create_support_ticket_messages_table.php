<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
	/**
	 * Run the migrations.
	 */
	public function up(): void
	{
		Schema::create('support_ticket_messages', function (Blueprint $table) {
			$table->id();
			$table->foreignId('ticket_id')->constrained('support_tickets')->cascadeOnDelete();
			$table->foreignId('user_id')->constrained()->cascadeOnDelete();
			$table->longText('message')->nullable();
			$table->string('attachment')->nullable();

			$table->unsignedBigInteger('parent_id')->nullable();
			$table->foreign('parent_id')->references('id')->on('support_ticket_messages')->nullOnDelete();

			$table->timestamps();

			// Index untuk performa threaded message dan tracking
			$table->index(['ticket_id', 'created_at']);
			$table->index('parent_id');
		});
	}

	/**
	 * Reverse the migrations.
	 */
	public function down(): void
	{
		Schema::dropIfExists('support_ticket_messages');
	}
};
