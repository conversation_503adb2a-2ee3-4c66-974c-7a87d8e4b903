<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\AnnouncementResource\Pages;
use App\Filament\Admin\Resources\AnnouncementResource\RelationManagers;
use App\Models\Announcement;
use Filament\Forms;
use Filament\Forms\Components\{DatePicker, DateTimePicker, Section, Select, Textarea, TextInput, Toggle};
use Filament\Forms\Form;
use Filament\Infolists\Components\TextEntry;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

class AnnouncementResource extends Resource
{
    protected static ?string $model = Announcement::class;

    protected static ?string $navigationIcon = 'heroicon-o-megaphone';
    protected static ?int $navigationSort = -1;

	protected static ?string $title = 'Pengumuman';

    public static function getPluralModelLabel(): string
    {
        return 'Pengumuman';
    }
    protected static ?string $navigationLabel = 'Pengumuman';

	public static function getNavigationBadge(): ?string
	{
		return Announcement::where('role_id', Auth::user()->roles->first()->id)
			->where(fn($query) => $query->whereJsonDoesntContain('read_by', Auth::id())->orWhereNull('read_by'))
			->count();
	}

	public static function getNavigationBadgeColor(): ?string
	{
		return 'warning';
	}

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
				Section::make('Isi Pengumuman')
					->aside()
					->description('Pengumuman hanya akan muncul di dashboard sesuai dengan peran pengguna yang menjadi target pengumuman.')
					->schema([
						Select::make('role_id')
							->relationship('role', 'name')
							->label('Target Pengguna')
							->inlineLabel()
							->required(),
						Select::make('priority')
							->label('Sifat')
							->inlineLabel()
							->required()
							->options([
								'Biasa' => 'Biasa',
								'Penting' => 'Penting',
								'Sangat Penting' => 'Sangat Penting',
								'Kritis' => 'Kritis'
							]),
						TextInput::make('title')
							->required()
							->label('Judul Pengumuman')
							->inlineLabel()
							->maxLength(255),
						Textarea::make('content')
							->required()
							->autosize()
							->label('Isi Pengumuman')
							->inlineLabel()
							->columnSpanFull(),
						DateTimePicker::make('published_at')
							->inlineLabel()
							->label('Tanggal Publikasi'),
						Toggle::make('is_active')
							->inlineLabel(),
					]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
			->striped()
            ->columns([
				IconColumn::make('priority')
					->label('Sifat')
					->tooltip(fn ($state) => 'Pengumuman '.$state)
					->icon(fn (string $state): string => match ($state) {
						'Biasa' => 'icon-exclamation-circle-fill',
						'Penting' => 'icon-exclamation-octagon-fill',
						'Sangat Penting' => 'icon-exclamation-square-fill',
						'Kritis' => 'icon-exclamation-triangle-fill',
					})
					->color(fn (string $state) => match ($state) {
						'Biasa' => 'success',
						'Penting' => 'info',
						'Sangat Penting' => 'warning',
						'Kritis' => 'danger',
						default => 'warning',
					}),
				Tables\Columns\TextColumn::make('role.name')
                    ->numeric()
					->label('Target')
					->visible(fn () => Auth::user()->hasAnyRole(['admin', 'Super Admin']))
					->formatStateUsing(fn (string $state): string => ucwords($state)) //capitalize/uppercase $state
                    ->sortable(),
                Tables\Columns\TextColumn::make('title')
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_active')
					->visible(fn () => Auth::user()->hasAnyRole(['admin', 'Super Admin']))
                    ->boolean(),
                Tables\Columns\TextColumn::make('published_at')
                    ->sortable()
					->since()
    				->dateTooltip(),
				TextColumn::make('read_by')
					->label('Dibaca')
					->badge()
					->icon(fn () => Auth::user()->hasAnyRole(['admin', 'Super Admin']) ? 'heroicon-o-eye' : null)
					->getStateUsing(function ($record) {
						$user = Auth::user();
						if ($user->hasAnyRole(['admin', 'Super Admin'])) {
							return count((array) $record->read_by);
						}
						return $record->isReadBy() ? 'Sudah Dibaca' : 'Belum Dibaca';
					})
					->color(fn (Announcement $record) => $record->isReadBy() ? 'success' : 'warning')
					->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->iconButton(),
                Tables\Actions\EditAction::make()->iconButton(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAnnouncements::route('/'),
            'create' => Pages\CreateAnnouncement::route('/create'),
            'view' => Pages\ViewAnnouncement::route('/{record}'),
            'edit' => Pages\EditAnnouncement::route('/{record}/edit'),
        ];
    }
}
