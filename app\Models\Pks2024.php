<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Pks2024 extends Model
{
	use HasFactory, SoftDeletes, LogsActivity;
	public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
			->logFillable('*')
			->logOnlyDirty();
    }

	public $table = 'pks';

	protected $dates = [
		'created_at',
		'updated_at',
		'deleted_at',
	];

	protected $fillable = [
		'npwp',
		'no_ijin',
		'poktan_id',
		'no_perjanjian',
		'tgl_perjanjian_start',
		'tgl_perjanjian_end',
		'jumlah_anggota',
		'luas_rencana',
		'varietas_tanam',
		'periode_tanam',
		'provinsi_id',
		'kabupaten_id',
		'kecamatan_id',
		'kelurahan_id',
		'status',
		'berkas_pks',
		'note',
	];

	protected static function booted()
	{
		static::addGlobalScope('npwp', function (Builder $builder) {
			if (Auth::check()) {
				$user = Auth::user();
				if ($user->hasRole('importir')) {
					$builder->where('npwp', $user->npwp);
				}
				elseif ($user->hasAnyRole(['admin', 'direktur', 'Super Admin', 'verifikator'])) {
				}
				else {
					$builder->where('npwp', $user->npwp);
				}
			}
		});
	}

	public function lokasi()
	{
		//perbaikan: key sebaiknya menggunakan 2 key sebagai anchor
		return $this->hasMany(Lokasi2024::class, 'poktan_id', 'poktan_id');
	}

	public function datarealisasi()
	{
		return $this->hasMany(DataRealisasi2024::class, 'pks_id', 'id');
	}

	public function masterpoktan()
	{
		return $this->belongsTo(MasterPoktan2024::class, 'poktan_id', 'poktan_id');
	}

	public function anggota()
	{
		return $this->hasMany(MasterAnggota2024::class, 'poktan_id', 'poktan_id');
	}

	public function commitment()
	{
		return $this->belongsTo(Commitment2024::class, 'no_ijin', 'no_ijin');
	}

	public function varietas()
	{
		return $this->belongsTo(Varietas::class, 'varietas_tanam');
	}

	// public function pkscheck()
	// {
	// 	return $this->hasMany(PksCheck::class, 'pks_id');
	// }
}
