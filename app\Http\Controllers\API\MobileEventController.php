<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Post;
use App\Models\EventSpeaker;
use App\Models\EventSchedule;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class MobileEventController extends Controller
{
    /**
     * Mendapatkan daftar event
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEvents()
    {
        $events = Post::where('type', 'Event')
            ->where('is_published', true)
            ->with(['category:id,name,color,slug'])
            ->select(
                'id',
                'title',
                'slug',
                'short_description',
                'feature_image',
                'cover_image',
                'published_at',
                'venue',
                'date_start',
                'date_end',
                'category_id'
            )
            ->orderBy('date_start', 'desc')
            ->get()
            ->map(function ($event) {
                // Transformasi data untuk API
                $event->feature_image = $event->feature_image ? Storage::url($event->feature_image) : null;
                $event->cover_image = $event->cover_image ? Storage::url($event->cover_image) : null;
                return $event;
            });

        return response()->json([
            'status' => 'success',
            'message' => 'Daftar event berhasil diambil',
            'data' => $events
        ]);
    }

    /**
     * Mendapatkan daftar event yang akan datang
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUpcomingEvents()
    {
        $events = Post::where('type', 'Event')
            ->where('is_published', true)
            ->where('date_start', '>', now())
            ->with(['category:id,name,color,slug'])
            ->select(
                'id',
                'title',
                'slug',
                'short_description',
                'feature_image',
                'cover_image',
                'published_at',
                'venue',
                'date_start',
                'date_end',
                'category_id'
            )
            ->orderBy('date_start', 'asc')
            ->get()
            ->map(function ($event) {
                // Transformasi data untuk API
                $event->feature_image = $event->feature_image ? Storage::url($event->feature_image) : null;
                $event->cover_image = $event->cover_image ? Storage::url($event->cover_image) : null;
                return $event;
            });

        return response()->json([
            'status' => 'success',
            'message' => 'Daftar event yang akan datang berhasil diambil',
            'data' => $events
        ]);
    }

    /**
     * Mendapatkan detail event berdasarkan slug
     * 
     * @param string $slug
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEventDetail($slug)
    {
        $event = Post::where('type', 'Event')
            ->where('slug', $slug)
            ->where('is_published', true)
            ->with([
                'category:id,name,color,slug',
                'author:id,name',
                'eventSpeaker',
                'eventSchedule' => function ($query) {
                    $query->orderBy('scheduled_date', 'asc')
                          ->orderBy('scheduled_time', 'asc');
                }
            ])
            ->first();

        if (!$event) {
            return response()->json([
                'status' => 'error',
                'message' => 'Event tidak ditemukan'
            ], 404);
        }

        // Transformasi data untuk API
        $event->feature_image = $event->feature_image ? Storage::url($event->feature_image) : null;
        $event->cover_image = $event->cover_image ? Storage::url($event->cover_image) : null;
        
        // Transformasi data speaker
        $event->eventSpeaker->transform(function ($speaker) {
            $speaker->avatar_url = $speaker->avatar_url ? Storage::url($speaker->avatar_url) : null;
            return $speaker;
        });

        // Kelompokkan jadwal berdasarkan tanggal
        $scheduledDates = $event->eventSchedule->groupBy('scheduled_date')->map(function ($schedules) {
            return $schedules->map(function ($schedule) {
                return [
                    'id' => $schedule->id,
                    'time' => $schedule->scheduled_time,
                    'title' => $schedule->title,
                    'description' => $schedule->description,
                    'keynote' => $schedule->keynote,
                ];
            });
        });

        $result = [
            'id' => $event->id,
            'title' => $event->title,
            'slug' => $event->slug,
            'short_description' => $event->short_description,
            'body' => $event->body,
            'feature_image' => $event->feature_image,
            'cover_image' => $event->cover_image,
            'published_at' => $event->published_at,
            'venue' => $event->venue,
            'date_start' => $event->date_start,
            'date_end' => $event->date_end,
            'category' => $event->category,
            'author' => $event->author,
            'speakers' => $event->eventSpeaker,
            'schedules' => $scheduledDates,
        ];

        return response()->json([
            'status' => 'success',
            'message' => 'Detail event berhasil diambil',
            'data' => $result
        ]);
    }
}
