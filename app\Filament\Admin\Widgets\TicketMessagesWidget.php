<?php

namespace App\Filament\Admin\Widgets;

use App\Models\SupportTicketMessage;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class TicketMessagesWidget extends Component
{
    public function render()
    {
        return view('filament.admin.widgets.ticket-messages-widget');
    }
    
    public $ticketId;
    public $ticket;
    public $messages;

    protected $listeners = [
        'messageAdded' => 'refreshMessages',
        'refresh' => 'refreshMessages'
    ];

    public function mount($ticketId = null): void
    {
        if ($ticketId) {
            $this->ticketId = $ticketId;
            $this->ticket = \App\Models\SupportTicket::find($ticketId);
            $this->loadMessages();
        }
    }

    public function loadMessages(): void
    {
        if ($this->ticket) {
            $this->messages = SupportTicketMessage::where('ticket_id', $this->ticket->id)
                ->rootMessages()
                ->with([
                    'user',
                    'allReplies.user',
                    'allReplies.allReplies.user'
                ])
                ->orderBy('created_at', 'asc')
                ->get();
        } else {
            $this->messages = collect();
        }
    }

    public function refreshMessages(): void
    {
        $this->loadMessages();
    }

    public function canReply(): bool
    {
        if (!$this->ticket) {
            return false;
        }

        $user = Auth::user();

        // User yang membuat tiket bisa reply
        if ($this->ticket->user_id === $user->id) {
            return true;
        }

        // Staff yang di-assign bisa reply
        if ($this->ticket->staff_id === $user->id) {
            return true;
        }

        // Admin dan Super Admin bisa reply
        if ($user->hasAnyRole(['admin', 'Super Admin'])) {
            return true;
        }

        return false;
    }

    public function replyToMessage($messageId): void
    {
        $this->dispatch('reply-to-message', messageId: $messageId);
    }
}
