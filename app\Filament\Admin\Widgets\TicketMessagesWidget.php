<?php

namespace App\Filament\Admin\Widgets;

use App\Models\SupportTicket;
use App\Models\SupportTicketMessage;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class TicketMessagesWidget extends Widget
{
    protected static string $view = 'filament.admin.widgets.ticket-messages-widget';
    
    public SupportTicket $ticket;
    public $messages;

    protected $listeners = [
        'messageAdded' => 'refreshMessages',
        'refresh' => 'refreshMessages'
    ];

    public function mount(SupportTicket $ticket): void
    {
        $this->ticket = $ticket;
        $this->loadMessages();
    }

    public function loadMessages(): void
    {
        $this->messages = SupportTicketMessage::where('ticket_id', $this->ticket->id)
            ->rootMessages()
            ->with([
                'user',
                'allReplies.user',
                'allReplies.allReplies.user'
            ])
            ->orderBy('created_at', 'asc')
            ->get();
    }

    public function refreshMessages(): void
    {
        $this->loadMessages();
    }

    public function canReply(): bool
    {
        $user = Auth::user();
        
        // User yang membuat tiket bisa reply
        if ($this->ticket->user_id === $user->id) {
            return true;
        }
        
        // Staff yang di-assign bisa reply
        if ($this->ticket->staff_id === $user->id) {
            return true;
        }
        
        // Admin dan Super Admin bisa reply
        if ($user->hasAnyRole(['admin', 'Super Admin'])) {
            return true;
        }
        
        return false;
    }

    public function replyToMessage($messageId): void
    {
        $this->dispatch('reply-to-message', messageId: $messageId);
    }
}
