<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\FotoProduksi2024;
use App\Models\User;

class FotoProduksi2024Policy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any FotoProduksi2024');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, FotoProduksi2024 $fotoproduksi2024): bool
    {
        return $user->checkPermissionTo('view FotoProduksi2024');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create FotoProduksi2024');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, FotoProduksi2024 $fotoproduksi2024): bool
    {
        return $user->checkPermissionTo('update FotoProduksi2024');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, FotoProduksi2024 $fotoproduksi2024): bool
    {
        return $user->checkPermissionTo('delete FotoProduksi2024');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any FotoProduksi2024');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, FotoProduksi2024 $fotoproduksi2024): bool
    {
        return $user->checkPermissionTo('restore FotoProduksi2024');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any FotoProduksi2024');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, FotoProduksi2024 $fotoproduksi2024): bool
    {
        return $user->checkPermissionTo('replicate FotoProduksi2024');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder FotoProduksi2024');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, FotoProduksi2024 $fotoproduksi2024): bool
    {
        return $user->checkPermissionTo('force-delete FotoProduksi2024');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any FotoProduksi2024');
    }
}
