<?php

namespace App\Filament\Panel2025\Resources\Commitment2025Resource\Pages;

use App\Filament\Panel2025\Resources\Commitment2025Resource;
use Awcodes\TableRepeater\Components\{TableRepeater};
use Awcodes\TableRepeater\Header;
// use Filament\Actions;
use Filament\Actions\Action;
use Filament\Forms\Components\Actions\Action as ActionsAction;
use Filament\Forms\Components\{Actions, Group, Hidden, Placeholder, Section, TextInput};
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;

use function PHPUnit\Framework\isEmpty;

class DaftarPks extends ViewRecord
{
    protected static string $resource = Commitment2025Resource::class;
    protected static ?string $title = 'Daftar PKS';

    protected function getHeaderActions(): array
    {
        return [];
    }

    public function getHeading(): string
	{
        return 'Daftar PKS';
	}

    public function getSubheading(): ?string
    {
        $noIjin = $this->record ? $this->record->no_ijin : '##';
        return 'untuk PPRK No: ' . $noIjin;
    }

    public function form(Form $form): Form
	{
		return $form
		->schema([
            Section::make('Kelompok Tani dan PKS')
                ->aside()
                ->description('Lengkapi data dan unggah berkas perjanjian kerjasama dengan kelompoktani.')
                ->schema([
                    TableRepeater::make('pks')
                        ->hiddenLabel()
                        ->addable(false)
                        ->deletable(false)
                        ->relationship('pks')
                        ->headers([
                            Header::make('Poktan'),
                            Header::make('No. PKS'),
                            Header::make('Status Dinas'),
                            Header::make('Status Verifikasi'),
                            Header::make(''),
                        ])
                        ->schema([
                            Hidden::make('id'),
                            TextInput::make('kode_poktan')
                                ->formatStateUsing(fn ($record) => $record->poktan->nama_kelompok),
                            TextInput::make('no_perjanjian'),
                            Placeholder::make('status_dinas')
								->hiddenLabel()
								->extraAttributes(['class'=>'text-center'])
								->content(fn ($get) => view('components.status-icon', ['status' => $get('status_dinas')])),
                            Placeholder::make('status')
								->hiddenLabel()
								->extraAttributes(['class'=>'text-center'])
								->content(fn ($get) => view('components.status-badge-verifikasi', ['status' => $get('status')])),
                            Actions::make([
                                ActionsAction::make('Realisasi')
                                    ->hiddenLabel()
                                    ->icon('icon-journal-bookmark-fill')
                                    ->iconButton()
                                    ->color('info')
                                    ->url(function ($record) {
										$statusDinas = $record->status_dinas;
										$statusVerifikasi = $record->status;
										if ($statusDinas === '1' || is_null($statusDinas) || $statusVerifikasi == 'Tidak Sesuai') { 
											return route('filament.panel2025.resources.pks2025s.edit', $record->id);
										}
										
										return route('filament.panel2025.resources.pks2025s.view', $record->id);
									})
									
									
                            ])
                        ])
                ]),
        ]);
    }
}

//status lainnya
