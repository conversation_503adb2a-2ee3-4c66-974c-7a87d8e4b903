<?php

namespace App\Filament\Admin\Resources\AjuVerifSkl2024Resource\Pages;

use App\Filament\Admin\Resources\AjuVerifSkl2024Resource;
use App\Models\SklRekomendasi2024;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\{Group, Placeholder, Section, Select, Tabs, Textarea, TextInput};
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\HtmlString;
use Endroid\QrCode\Color\Color;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use TCPDF;

class ApprovalRekomendasi2024 extends EditRecord
{
    protected static string $resource = AjuVerifSkl2024Resource::class;

	public static string | Alignment $formActionsAlignment = Alignment::Right;

    // public static function canAccess(array $arguments = []): bool
    // {
    //    return Auth::user()?->hasAnyRole(['admin', 'Super Admin','direktur']);
    // }

	public function getTitle(): string | Htmlable
    {
        return 'Rekomendasi Penerbitan SKL';
    }
	// public function getHeading(): string
	// {
	// 	return 'Rekomendasi Penerbitan SKL';
	// }
	public function getSubheading(): ?string
    {
        return 'untuk RIPH Periode sebelum Tahun 2025';
    }

	protected function getHeaderActions(): array
	{
		return [
			// Actions\CreateAction::make(),
		];
	}

	public function form(Form $form): Form
	{
		return $form
		->schema([
			Tabs::make('Tabs')
				->tabs([
					Tab::make('Pemohon')
						->schema([
							Section::make('Pelaku Usaha')
								->aside()
								->relationship('datauser')
								->description('Data Pemohon Surat Keterangan Lunas')
								->schema([
									Placeholder::make('Company')
										->label('Perusahaan')
										->inlineLabel()
										->content(fn ($record) => new HtmlString('<span class="font-bold">'.$record->company_name.'</span>')),
									Placeholder::make('penanggungjawab')
										->label('Penanggung Jawab')
										->inlineLabel()
										->content(fn ($record) => new HtmlString('<span class="font-bold">'.$record->penanggungjawab.'</span>')),
									Placeholder::make('jabatan')
										->label('Jabatan')
										->inlineLabel()
										->content(fn ($record) => new HtmlString('<span class="font-bold">'.$record->jabatan.'</span>')),
									Placeholder::make('nib')
										->label('NIB')
										->inlineLabel()
										->content(fn ($record) => new HtmlString('<span>'.$record->nib_company.'</span>')),
									Placeholder::make('npwp')
										->label('NPWP')
										->inlineLabel()
										->content(fn ($record) => new HtmlString('<span>'.$record->npwp_company.'</span>')),
									Placeholder::make('address_company')
										->label('Alamat Perusahaan')
										->inlineLabel()
										->content(fn ($record) => new HtmlString(
											'<p>'.$record->address_company.'</p>
											<p>'.$record->mykabupaten->nama_kab.' - '.$record->myprovinsi->nama.', '.$record->kodepos.'</p>'
										)),
								]),
						]),

					Tab::make('Data RIPH')
						->schema([
							Section::make()
								->aside()
								->relationship('commitment')
								->description('Ringkasan Data RIPH yang dimohonkan')
								->schema([
									Placeholder::make('no_ijin')
										->label('No. RIPH')
										->inlineLabel()
										->content(fn ($record) => new HtmlString('<span class="font-bold">'.$record->no_ijin.'</span>')),
									Placeholder::make('periodetahun')
										->label('Periode Penerbitan')
										->inlineLabel()
										->content(fn ($record) => new HtmlString('<span class="font-bold">'.$record->periodetahun.'</span>')),
									Placeholder::make('tgl_ijin')
										->label('Mulai Berlaku')
										->inlineLabel()
										->content(fn ($record) => new HtmlString('<span class="font-bold">' . Carbon::parse($record->tgl_ijin)->translatedFormat('d F Y') . '</span>')),
									Placeholder::make('tgl_akhir')
										->label('Berlaku Hingga')
										->inlineLabel()
										->content(fn ($record) => new HtmlString('<span class="font-bold">' . Carbon::parse($record->tgl_akhir)->translatedFormat('d F Y') . '</span>')),
									Placeholder::make('volume_riph')
										->label('Volume RIPH')
										->inlineLabel()
										->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->volume_riph,2,',','.').' ton </span>')),
								]),
						]),

					Tab::make('Komitmen dan Realisasi')
						->schema([
							Group::make()
								->relationship('commitment')
								->schema([
									Section::make('Ringkasan')
										->aside()
										->description('Ringkasan Data Komitmen dan Realisasi')
										->schema([
											Placeholder::make('luas_wajib_tanam')
												->label('Komitmen Tanam')
												->inlineLabel()
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->luas_wajib_tanam,2,',','.').' ha</span>')),
												//perlu diperbaiki sumber data nya
											Placeholder::make('volume_produksi')
												->label('Komitmen Produksi')
												->inlineLabel()
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->volume_produksi,2,',','.').' ton</span>')),

											Placeholder::make('realisasiTanam')
												->label('Realisasi Tanam')
												->inlineLabel()
												->hintIcon(fn ($record) =>
													$record->datarealisasi->sum('luas_lahan') < 0.9 * $record->luas_wajib_tanam
														? 'heroicon-s-exclamation-circle'
														: 'heroicon-s-check-circle'
												)
												->hintColor(fn ($record) =>
													$record->datarealisasi->sum('luas_lahan') < 0.6 * $record->luas_wajib_tanam
														? 'danger'
														: ($record->datarealisasi->sum('luas_lahan') < 0.9 * $record->luas_wajib_tanam
															? 'warning'
															: 'success')
												)
												->hintIconTooltip(fn ($record) =>
													$record->datarealisasi->sum('luas_lahan') < 0.6 * $record->luas_wajib_tanam
														? 'Total realisasi tanam tidak realistis'
														: ($record->datarealisasi->sum('luas_lahan') < 0.9 * $record->luas_wajib_tanam
															? 'Total realisasi tanam kurang realistis'
															: null)
												)
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->datarealisasi->sum('luas_lahan'),2,',','.').' ha</span>')),

											Placeholder::make('realisasiProduksi')
												->label('Realisasi Produksi')
												->inlineLabel()
												->hintIcon(fn ($record) =>
													$record->datarealisasi->sum('volume') < $record->volume_produksi
														? 'heroicon-s-x-circle'
														: 'heroicon-s-check-circle'
												)
												->hintColor(fn ($record) =>
													$record->datarealisasi->sum('volume') < $record->volume_produksi
														? 'danger' : 'success'
												)
												->hintIconTooltip(fn ($record) =>
													$record->datarealisasi->sum('luas_lahan') < 0.6 * $record->luas_wajib_tanam
														? 'Total realisasi tanam tidak realistis'
														: ($record->datarealisasi->sum('luas_lahan') < 0.9 * $record->luas_wajib_tanam
															? 'Total realisasi produksi kurang realistis'
															: null)
												)
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->datarealisasi->sum('volume'),2,',','.').' ton</span>')),
										]),

									Section::make('Kemitraan dan Lokasi')
										->aside()
										->description('Ringkasan Data Kemitraan dan Lokasi Tanam')
										->schema([
											Placeholder::make('Poktan')
												->label('Komitmen Kerjasama')
												->inlineLabel()
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->pks->count(),0,',','.').'  kelompok</span>')),

											Placeholder::make('pks')
												->label('Realisasi Kerjasama')
												->inlineLabel()
												->hintIcon(fn ($record) =>
													$record->pks->count() < $record->pks->count('berkas_pks')
														? 'heroicon-s-x-circle'
														: 'heroicon-s-check-circle'
												)
												->hintColor(fn ($record) =>
													$record->pks->count() < $record->pks->count('berkas_pks')
														? 'warning'
														: 'success'
												)
												->hintIconTooltip(fn ($record) =>
													$record->pks->count() < $record->pks->count('berkas_pks')
														? 'Hanya menjalin kemitraan dengan sejumlah kelompok'
														: null
												)
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->pks->count('berkas_pks'),0,',','.').' kelompok</span>')),

											Placeholder::make('lokasi')
												->label('Anggota/Petani')
												->inlineLabel()
												->hidden()
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->lokasi->unique('anggota_id')->count(),0,',','.').'  anggota</span>')),

											Placeholder::make('anggotaId')
												->label('Realisasi Petani')
												->hidden()
												->inlineLabel()
												->hintIcon(fn ($record) =>
													$record->lokasi->unique('anggota_id')->count() > $record->datarealisasi->unique('anggota_id')->count()
														? 'heroicon-s-x-circle'
														: 'heroicon-s-check-circle'
												)
												->hintColor(fn ($record) =>
													$record->lokasi->unique('anggota_id')->count() > $record->datarealisasi->unique('anggota_id')->count()
														? 'warning'
														: 'success'
												)
												->hintIconTooltip(fn ($record) =>
													$record->pks->count() < $record->pks->count('berkas_pks')
														? 'Jumlah petani yang sama'
														: null
												)
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->datarealisasi->unique('anggota_id')->count(),0,',','.').'  anggota</span>')),

											Placeholder::make('Jumlah Lahan')
												->label('Jumlah Lahan')
												->inlineLabel()
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->datarealisasi->count(),0,',','.').'  titik</span>')),
										])
								])
						]),

					Tab::make('Rekomendasi')
						->schema([
							Group::make()
								->relationship('commitment')
								->schema([
									Section::make('Verifikasi Tanam')
										->aside()
										->description('Catatan hasil verifikasi realisasi tanam')
										->schema([
											Placeholder::make('verifikatorTanam')
												->label('Petugas Verifikasi')
												->inlineLabel()
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.$record->ajutanam->dataadmin->nama.'</span>')),
											Textarea::make('catatanVerifTanam')
												->label('Catatan Verifikasi')
												->inlineLabel()
												->autosize()
												->disabled()
												->formatStateUsing(fn ($record) => $record->ajutanam->note),
											]),
									Section::make('Verifikasi Produksi')
										->aside()
										->description('Catatan hasil verifikasi realisasi produksi')
										->schema([
											Placeholder::make('verifikatorProduksi')
												->label('Petugas Verifikasi')
												->inlineLabel()
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.$record->ajuproduksi->dataadmin->nama.'</span>')),
											Textarea::make('catatanVerifProduksi')
												->label('Catatan Verifikasi')
												->inlineLabel()
												->autosize()
												->disabled()
												->formatStateUsing(fn ($record) => $record->ajuproduksi->note),
										]),

									]),
									Section::make('Rekomendasi Penerbitan')
										->aside()
										->description('Catatan rekomendasi penerbitan Surat Keterangan Lunas')
										->schema([
											Placeholder::make('rekomendasiSkl')
												->label('Direkomendasikan Oleh')
												->inlineLabel()
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.($record->dataadmin->nama ?? 'Administrator').'</span>')),
											Textarea::make('catatanVerifProduksi')
												->label('Catatan Verifikasi')
												->inlineLabel()
												->autosize()
												->disabled()
												->formatStateUsing(fn ($record) => $record->note),]),
						]),

					Tab::make('Persetujuan')
						->schema([
							Section::make()
								->aside()
								->description(function ($get, $record) {
									// Persiapkan data untuk QR Code
									$noIjin = $record->no_ijin ?? '';
									$npwp = $record->npwp ?? '';
									$secretKey = config('app.qr_secret');
									$sanitizedNpwp = preg_replace('/[^A-Za-z0-9]/', '', $npwp);
									$sanitizedNoIjin = preg_replace('/[^A-Za-z0-9]/', '', $noIjin);
									$reversedNoIjin = strrev($sanitizedNoIjin);
									$data = "{$reversedNoIjin}|{$sanitizedNpwp}";
									$hash = hash_hmac('sha256', $data, $secretKey);
									$shortHash = substr($hash, 0, 8);
									$maskedNos = "{$reversedNoIjin}{$shortHash}";
									$verifyUrl = url("/verify-skl?mask={$maskedNos}&mock={$hash}");

									return 'Form Persetujuan Penerbitan Surat Keterangan Lunas';
									// . QR Secret: ' . env('QR_SECRET', 'tidak tersedia') .
									// 	' | Config QR Secret: ' . config('app.qr_secret', 'tidak tersedia') .
									// 	' | QR URL: ' . $verifyUrl;
								})
								->schema([
									Placeholder::make('disclaimer')
										->hiddenLabel()
										->label('')
										->content(fn ($record) => new HtmlString('<p>Menimbang dan mengingat hasil evaluasi laporan realisasi tanam dan produksi, dengan ini Surat Keterangan Lunas No.: <span class="font-bold">' .$record->sklrekomendasi->no_skl. '</span> :</p>')),

									Select::make('statusTerbit')
										->hiddenLabel()
										->reactive()
										->options([
											'tunda' => 'Tunda Penerbitan',
											'terbit' => 'Dapat diterbitkan'
										])

								]),
						]),

				])->contained(false)->activeTab(1),


			// Section::make()
		])->columns(1);
	}

	protected function mutateFormDataBeforeSave(array $data): array
	{
		$approvedBy = Auth::user()->id;
		$approvedAt = today();
		$record = $this->record;
		$sklRekomendasi = $record->sklrekomendasi;

		if ($data['statusTerbit'] === 'tunda') {
			// Log::info('Status: Tunda', [
			// 	'approvedBy' => $approvedBy ?? 'N/A',
			// 	'approvedAt' => $approvedAt ?? 'N/A',
			// 	'record' => $record ?? null,
			// 	'sklRekomendasi' => $sklRekomendasi ?? null,
			// ]);
		}

		if ($data['statusTerbit'] === 'terbit') {
			$data = [
				'status' => '4'
			];

			self::generateDraftSkl($record);
		}

		return $data;
	}

	/**
     * Metode lama untuk kompatibilitas
     */
    protected static function generateQrCode($noIjin, $npwp)
    {
        try {
            $secretKey = config('app.qr_secret');
            $sanitizedNpwp = preg_replace('/[^A-Za-z0-9]/', '', $npwp);
            $sanitizedNoIjin = preg_replace('/[^A-Za-z0-9]/', '', $noIjin);
            $reversedNoIjin = strrev($sanitizedNoIjin);
            $data = "{$reversedNoIjin}|{$sanitizedNpwp}";
            $hash = hash_hmac('sha256', $data, $secretKey);
            $shortHash = substr($hash, 0, 8);

            $maskedNos = "{$reversedNoIjin}{$shortHash}";
            $url = url("/verify-skl?mask={$maskedNos}&mock={$hash}");

            return self::generateQrCodeWithUrl($url);
        } catch (\Exception $e) {
            Log::error('Error generating QR code: ' . $e->getMessage(), [
                'exception' => $e,
                'no_ijin' => $noIjin
            ]);

            // Return placeholder jika gagal
            return '<div style="width:100px;height:100px;border:1px solid #ccc;text-align:center;line-height:100px;">QR Code</div>';
        }
    }

    /**
     * Metode baru yang lebih fleksibel dengan URL yang sudah disiapkan
     */
    protected static function generateQrCodeWithUrl($url)
    {
        try {
            $qrCode = new QrCode(
                data: $url,
                size: 200,
                margin: 0,
                errorCorrectionLevel: ErrorCorrectionLevel::High,
                foregroundColor: new Color(0, 0, 0),
                backgroundColor: new Color(255, 255, 255),
                encoding: new Encoding('UTF-8')
            );

            // Tulis ke PNG
            $writer = new PngWriter();
            $result = $writer->write($qrCode);

            // Konversi ke data URI
            $dataUri = $result->getDataUri();

            // Log sukses
            Log::info('QR Code berhasil dibuat dengan Endroid', [
                'url' => $url
            ]);

            return $dataUri;
        } catch (\Exception $endroidException) {
            // Log error Endroid
            Log::warning('Failed to generate QR code with Endroid: ' . $endroidException->getMessage(), [
                'exception' => $endroidException,
                'url' => $url
            ]);

            // Return placeholder jika gagal
            return '<div style="width:100px;height:100px;border:1px solid #ccc;text-align:center;line-height:100px;">QR Code</div>';
        }
    }

	public static function sklPayload($ajuskl)
	{
		$approvedBy = Auth::user()->id;
		$approvedAt = today();

		$ajuskl->sklrekomendasi->update([
			'approved_by' => $approvedBy,
			'approved_at' => $approvedAt,
		]);

		try {
			$company = $ajuskl->datauser->company_name;
			$npwp = $ajuskl->npwp;
			$noSkl = $ajuskl->sklrekomendasi->no_skl;
			$tglTerbit = $ajuskl->sklrekomendasi->published_date;
			$noIjin = $ajuskl->no_ijin;
			$approvedBy = $ajuskl->sklrekomendasi->approvedBy->dataadmin->nama;
			$approvedAt = $approvedAt;
			$nip = $ajuskl->sklrekomendasi->approvedBy->dataadmin->nip;
			$wajibTanam = $ajuskl->commitment->luas_wajib_tanam;
			$wajibProduksi = $ajuskl->commitment->volume_produksi;
			$realisasiTanam = $ajuskl->commitment->datarealisasi->sum('luas_lahan');
			$realisasiProduksi = $ajuskl->commitment->datarealisasi->sum('volume');

			// Persiapkan data untuk QR Code
			$secretKey = config('app.qr_secret');
			$sanitizedNpwp = preg_replace('/[^A-Za-z0-9]/', '', $npwp);
			$sanitizedNoIjin = preg_replace('/[^A-Za-z0-9]/', '', $noIjin);
			$reversedNoIjin = strrev($sanitizedNoIjin);
			$data = "{$reversedNoIjin}|{$sanitizedNpwp}";
			$hash = hash_hmac('sha256', $data, $secretKey);
			$shortHash = substr($hash, 0, 8);
			$maskedNos = "{$reversedNoIjin}{$shortHash}";
			$verifyUrl = url("/verify-skl?mask={$maskedNos}&mock={$hash}");

			// Generate QR Code dengan URL yang benar
			$qrCode = self::generateQrCodeWithUrl($verifyUrl);

			// Jika QR code gagal dibuat, gunakan placeholder
			if (empty($qrCode)) {
				Log::warning('QR Code gagal dibuat, menggunakan placeholder', [
					'no_ijin' => $noIjin,
					'npwp' => $npwp
				]);
				$qrCode = '<div style="width:100px;height:100px;border:1px solid #ccc;text-align:center;line-height:100px;">QR Code</div>';
			}

			return [
				'company' => $company,
				'noSkl' => $noSkl,
				'npwp' => $npwp,
				'noIjin' => $noIjin,
				'approvedBy' => $approvedBy,
				'nip' => $nip,
				'tglTerbit' => $tglTerbit,
				'wajibTanam' => $wajibTanam,
				'wajibProduksi' => $wajibProduksi,
				'realisasiTanam' => $realisasiTanam,
				'realisasiProduksi' => $realisasiProduksi,
				'QrCode' => $qrCode,
				'qrCodeUrl' => $verifyUrl // URL konsisten dengan QR Code
			];
		} catch (\Exception $e) {
			Log::error('Error generating SKL payload: ' . $e->getMessage(), [
				'exception' => $e,
				'no_ijin' => $noIjin ?? 'unknown'
			]);

			// Buat URL verifikasi minimal
			$noIjin = $ajuskl->no_ijin ?? 'unknown';
			$npwp = $ajuskl->npwp ?? 'unknown';
			$secretKey = config('app.qr_secret');
			$sanitizedNpwp = preg_replace('/[^A-Za-z0-9]/', '', $npwp);
			$sanitizedNoIjin = preg_replace('/[^A-Za-z0-9]/', '', $noIjin);
			$reversedNoIjin = strrev($sanitizedNoIjin);
			$data = "{$reversedNoIjin}|{$sanitizedNpwp}";
			$hash = hash_hmac('sha256', $data, $secretKey);
			$shortHash = substr($hash, 0, 8);
			$maskedNos = "{$reversedNoIjin}{$shortHash}";
			$verifyUrl = url("/verify-skl?mask={$maskedNos}&mock={$hash}");

			// Return minimal payload untuk mencegah error
			return [
				'company' => $ajuskl->datauser->company_name ?? 'Tidak tersedia',
				'noSkl' => $ajuskl->sklrekomendasi->no_skl ?? 'Tidak tersedia',
				'npwp' => $npwp,
				'noIjin' => $noIjin,
				'approvedBy' => 'Tidak tersedia',
				'nip' => 'Tidak tersedia',
				'tglTerbit' => $ajuskl->sklrekomendasi->published_date ?? now(),
				'wajibTanam' => 0,
				'wajibProduksi' => 0,
				'realisasiTanam' => 0,
				'realisasiProduksi' => 0,
				'QrCode' => '<div style="width:100px;height:100px;border:1px solid #ccc;text-align:center;line-height:100px;">QR Code</div>',
				'qrCodeUrl' => $verifyUrl,
			];
		}
	}

	/**
     * Membuat PDF dengan metode paling sederhana (tanpa template HTML)
     */
    protected static function createMinimalPdf($outputPath, $payload, $path)
    {
        try {
            // Tingkatkan batas memori dan waktu eksekusi
            ini_set('memory_limit', '512M');
            ini_set('max_execution_time', 300);

            Log::info('Mencoba membuat PDF minimal tanpa HTML', [
                'no_ijin' => $payload['noIjin'],
                'file_path' => $path
            ]);


            // Buat instance TCPDF
            $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, 'A4', true, 'UTF-8', false);

            // Set informasi dokumen
            $pdf->SetCreator('SIMETHRIS');
            $pdf->SetAuthor('Kementerian Pertanian');
            $pdf->SetTitle('SKL ' . $payload['noIjin']);
            $pdf->SetSubject('Surat Keterangan Lunas');

            // Hapus header dan footer default
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(false);

            // Set margin
            $pdf->SetMargins(15, 10, 15);
            $pdf->SetAutoPageBreak(TRUE, 15);

            // Set font
            $pdf->SetFont('helvetica', 'B', 16);

            // Tambahkan halaman
            $pdf->AddPage();

            // Tambahkan konten langsung tanpa HTML
            $pdf->Cell(0, 10, 'SURAT KETERANGAN LUNAS', 0, 1, 'C');
            $pdf->Cell(0, 10, 'Nomor: ' . $payload['noSkl'], 0, 1, 'C');

            $pdf->Ln(10);

            $pdf->SetFont('helvetica', '', 12);
            $pdf->Cell(0, 10, 'Dengan ini menyatakan bahwa:', 0, 1, 'L');

            $pdf->Ln(5);

            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(50, 10, 'Nama Perusahaan:', 0, 0, 'L');
            $pdf->SetFont('helvetica', '', 12);
            $pdf->Cell(0, 10, $payload['company'], 0, 1, 'L');

            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(50, 10, 'NPWP:', 0, 0, 'L');
            $pdf->SetFont('helvetica', '', 12);
            $pdf->Cell(0, 10, $payload['npwp'], 0, 1, 'L');

            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(50, 10, 'Nomor RIPH:', 0, 0, 'L');
            $pdf->SetFont('helvetica', '', 12);
            $pdf->Cell(0, 10, $payload['noIjin'], 0, 1, 'L');

            $pdf->Ln(10);

            $pdf->MultiCell(0, 10, 'Telah melaksanakan kewajiban tanam dan produksi bawang putih sesuai ketentuan.', 0, 'L');

            $pdf->Ln(10);

            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(50, 10, 'Tanggal:', 0, 0, 'L');
            $pdf->SetFont('helvetica', '', 12);
            $pdf->Cell(0, 10, \Carbon\Carbon::parse($payload['tglTerbit'])->format('d-m-Y'), 0, 1, 'L');

            $pdf->Ln(20);

            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(0, 10, 'Direktur,', 0, 1, 'R');
            $pdf->Ln(15);
            $pdf->Cell(0, 10, 'ttd', 0, 1, 'R');
            $pdf->Cell(0, 10, $payload['approvedBy'], 0, 1, 'R');
            $pdf->SetFont('helvetica', '', 12);
            $pdf->Cell(0, 10, 'NIP. ' . $payload['nip'], 0, 1, 'R');

            $pdf->Ln(20);

            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(50, 10, 'Verifikasi:', 0, 0, 'L');
            $pdf->SetFont('helvetica', '', 12);
            $pdf->Cell(0, 10, $payload['qrCodeUrl'], 0, 1, 'L');

            // Simpan PDF ke file
            $pdf->Output($outputPath, 'F');

            // Periksa apakah file PDF berhasil dibuat
            if (!file_exists($outputPath)) {
                Log::warning('TCPDF gagal membuat file PDF SKL minimal', [
                    'no_ijin' => $payload['noIjin'],
                    'file_path' => $path
                ]);
                return false;
            }

            Log::info('PDF SKL minimal berhasil dibuat', [
                'no_ijin' => $payload['noIjin'],
                'file_path' => $path,
                'file_size' => file_exists($outputPath) ? filesize($outputPath) . ' bytes' : 'file not found'
            ]);

            return $path;
        } catch (\Exception $e) {
            Log::error('Error pada pembuatan PDF minimal: ' . $e->getMessage(), [
                'no_ijin' => $payload['noIjin'],
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Mencoba membuat PDF dengan TCPDF
     */
    protected static function tryTCPDF($template, $outputPath, $payload, $path)
    {
        try {
            // Tingkatkan batas memori dan waktu eksekusi
            // ini_set('memory_limit', '512M');
            // ini_set('max_execution_time', 300);

            Log::info('Mencoba membuat PDF SKL dengan TCPDF', [
                'no_ijin' => $payload['noIjin'],
                'file_path' => $path,
                'template_size' => strlen($template) . ' bytes'
            ]);

            // Buat instance TCPDF
            $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, 'A4', true, 'UTF-8', false);
            Log::info('instance created');

            // Set informasi dokumen
            $pdf->SetCreator('SIMETHRIS');
            $pdf->SetAuthor('Kementerian Pertanian');
            $pdf->SetTitle('SKL ' . $payload['noIjin']);
            $pdf->SetSubject('Surat Keterangan Lunas');
            Log::info('Information set');

            // Hapus header dan footer default
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(false);
            Log::info('header and footer deleted');

            // Set margin
            $pdf->SetMargins(15, 10, 15);
            $pdf->SetAutoPageBreak(TRUE, 10);
            Log::info('margin has been set');

            // Set font
            $pdf->SetFont('helvetica', '', 10);
            Log::info('font has been set');

            // Tambahkan halaman
            $pdf->AddPage();
            Log::info('page added');

            try {
                // Tambahkan konten HTML
                Log::info('Mencoba menambahkan konten HTML...');
                $pdf->writeHTML($template, true, false, true, false, '');
                Log::info('content added successfully');
            } catch (\Exception $htmlException) {
                Log::error('Error saat menambahkan konten HTML: ' . $htmlException->getMessage(), [
                    'exception' => $htmlException->getMessage(),
                    'trace' => $htmlException->getTraceAsString()
                ]);

                // Tambahkan teks sederhana sebagai fallback
                $pdf->AddPage();
                $pdf->Cell(0, 10, 'Error saat membuat SKL dengan format HTML', 0, 1, 'C');
                $pdf->Cell(0, 10, 'Silakan hubungi administrator', 0, 1, 'C');
            }

            // Simpan PDF ke file
            Log::info('Mencoba menyimpan file PDF...');
            $pdf->Output($outputPath, 'F');
            Log::info('file saved');

            // Periksa apakah file PDF berhasil dibuat
            if (!file_exists($outputPath)) {
                Log::warning('TCPDF gagal membuat file PDF SKL', [
                    'no_ijin' => $payload['noIjin'],
                    'file_path' => $path
                ]);
                throw new \Exception('Gagal membuat file PDF SKL dengan TCPDF');
            }

            Log::info('PDF SKL berhasil dibuat dengan TCPDF', [
                'no_ijin' => $payload['noIjin'],
                'file_path' => $path,
                'file_size' => file_exists($outputPath) ? filesize($outputPath) . ' bytes' : 'file not found'
            ]);

            return $path;
        } catch (\Exception $e) {
            Log::error('Error pada TCPDF: ' . $e->getMessage(), [
                'no_ijin' => $payload['noIjin'],
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

	public static function generateDraftSkl($record)
	{
		try {
			// Persiapkan data untuk template
			$payload = self::sklPayload($record);

			// Cek jika payload kosong atau gagal dibuat
			if (empty($payload)) {
				Log::error('Validasi payload SKL gagal', [
					'payload' => $payload,
					'record_id' => $record->id ?? null,
					'no_ijin' => $record->no_ijin ?? null
				]);
				return back()->withErrors(['error' => 'Gagal menghasilkan SKL. Data tidak valid.']);
			}

			// Persiapkan path file
			$npwp = str_replace(['.', '-'], '', $payload['npwp']);
			$noIjin = str_replace(['.', '-', '/'], '', $payload['noIjin']);

			// Tambahkan uniqueId dan timestamp untuk menghindari caching browser
			$uniqueId = uniqid();
			$timestamp = date('YmdHis');
			$fileName = 'draftskl_' . $noIjin . '_' . $uniqueId . '_' . $timestamp . '.pdf';

			$directory = 'uploads/' . $npwp . '/' . $noIjin . '/dokumen';
			$path = $directory . '/' . $fileName;

			// Buat direktori jika belum ada
			if (!Storage::disk('public')->exists($directory)) {
				Storage::disk('public')->makeDirectory($directory);
			}

			// Path lengkap untuk file output
			$outputPath = Storage::disk('public')->path($path);

			// Langsung gunakan template TCPDF yang kompatibel
			try {
				Log::info('Mencoba membuat PDF dengan template skl-tcpdf');

				// Render template TCPDF ke HTML
				$template = view('velzon.realisasi.skl-tcpdf', [
					'payload' => $payload,
				])->render();

				// Buat PDF dengan TCPDF
				$result = self::tryTCPDF($template, $outputPath, $payload, $path);

				// Jika gagal, coba dengan metode paling sederhana
				if ($result === false || !file_exists($outputPath)) {
					throw new \Exception('Template TCPDF gagal, mencoba metode paling sederhana');
				}
			} catch (\Exception $templateException) {
				Log::warning('Template TCPDF gagal, mencoba metode paling sederhana: ' . $templateException->getMessage());

				// Buat PDF dengan metode paling sederhana (tanpa template)
				$result = self::createMinimalPdf($outputPath, $payload, $path);

				if ($result === false || !file_exists($outputPath)) {
					throw new \Exception('Gagal membuat PDF dengan metode paling sederhana');
				}
			}

			// Jika berhasil dibuat
			if (file_exists($outputPath)) {
				// Log informasi file yang dibuat
				Log::info('File PDF SKL berhasil dibuat dan disimpan.', [
					'no_ijin' => $payload['noIjin'],
					'file_path' => $path,
					'file_size' => filesize($outputPath) . ' bytes'
				]);

				// Update database
				$update = SklRekomendasi2024::where('no_ijin', $payload['noIjin'])
					->update(['skl_auto' => $path]);

				if (!$update) {
					Log::error('Gagal menyimpan path SKL ke database', [
						'no_ijin' => $payload['noIjin'],
						'path' => $path
					]);

					return back()->withErrors(['error' => 'Gagal menyimpan path SKL.']);
				}

				return $path;
			} else {
				throw new \Exception('Gagal membuat file PDF SKL');
			}
		} catch (\Exception $e) {
			Log::error('Error generating SKL with TCPDF: ' . $e->getMessage(), [
				'exception' => $e,
				'trace' => $e->getTraceAsString()
			]);

			return back()->withErrors(['error' => 'Gagal membuat SKL: ' . $e->getMessage()]);
		}
	}
}
