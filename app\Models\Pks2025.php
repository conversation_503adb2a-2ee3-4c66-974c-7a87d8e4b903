<?php

namespace App\Models;

use App\Observers\Pks2025Observer;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

#[ObservedBy([Pks2025Observer::class])]
class Pks2025 extends Model
{
	use Notifiable, LogsActivity;
	public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
			->logFillable('*')
			->logOnlyDirty();
    }
    public $table = 't2025_pks';

    protected $fillable = [
        'tcode',
        'npwp',
        'no_ijin',
        'kode_poktan',
        'nama_poktan',
        'no_perjanjian',
        'tgl_perjanjian_start',
        'tgl_perjanjian_end',
        'jumlah_anggota',
        'luas_rencana',
        'varietas_tanam',
        'periode_tanam',
        'provinsi_id',
        'kabupaten_id',
        'kecamatan_id',
        'kelurahan_id',
        'berkas_pks',
        'status',
        'status_dinas',
        'note',
        'verif_by',
        'verif_at',
		'deadline_at'
    ];

	protected static function booted()
	{
		static::addGlobalScope('npwp', function (Builder $builder) {
			if (Auth::check()) {
				$user = Auth::user();

				if ($user->hasAnyRole(['importir'])) {
					$builder->where('npwp', $user->npwp);
				}
			}
		});
	}

    public function commitment(): BelongsTo
    {
        return $this->belongsTo(Commitment2025::class, 'no_ijin', 'no_ijin');
    }

    public function poktan(): BelongsTo
    {
        return $this->belongsTo(MasterPoktan::class, 'kode_poktan', 'kode_poktan');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'npwp', 'npwp');
    }

    public function datauser(): BelongsTo
    {
        return $this->belongsTo(DataUser::class, 'npwp', 'npwp_company');
    }

    public function verifikator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verif_by', 'id');
    }

    public function varietas(): BelongsTo
    {
        return $this->belongsTo(Varietas::class, 'varietas_tanam', 'id');
    }

    public function provinsi(): BelongsTo
    {
        return $this->belongsTo(MasterProvinsi::class, 'provinsi_id', 'provinsi_id');
    }

    public function kabupaten(): BelongsTo
    {
        return $this->belongsTo(MasterKabupaten::class, 'kabupaten_id', 'kabupaten_id');
    }

    public function kecamatan(): BelongsTo
    {
        return $this->belongsTo(MasterKecamatan::class, 'kecamatan_id', 'kecamatan_id');
    }

    public function desa(): BelongsTo
    {
        return $this->belongsTo(MasterDesa::class, 'kelurahan_id', 'kelurahan_id');
    }
}
