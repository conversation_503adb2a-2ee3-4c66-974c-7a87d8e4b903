@php
    $user = \Illuminate\Support\Facades\Auth::user();
    $canReply = false;
    
    if ($ticket) {
        // Cek apakah tiket masih bisa di-reply
        if (!in_array($ticket->status, ['closed'])) {
            // User yang membuat tiket bisa reply
            if ($ticket->user_id === $user->id) {
                $canReply = true;
            }
            // Staff yang di-assign bisa reply
            elseif ($ticket->staff_id === $user->id) {
                $canReply = true;
            }
            // Admin dan Super Admin bisa reply
            elseif ($user->hasAnyRole(['admin', 'Super Admin'])) {
                $canReply = true;
            }
        }
    }
@endphp

<div class="space-y-4">
    @if(!$canReply)
        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                        Tidak dapat membalas
                    </h3>
                    <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                        <p>
                            @if($ticket->status === 'closed')
                                Tiket ini sudah ditutup. Tidak dapat menambahkan balasan baru.
                            @else
                                Anda tidak memiliki izin untuk membalas tiket ini.
                            @endif
                        </p>
                    </div>
                </div>
            </div>
        </div>
    @else
        <!-- Simple Reply Form -->
        <form action="{{ route('support-ticket.reply', $ticket->id) }}" method="POST" enctype="multipart/form-data" class="space-y-4">
            @csrf
            
            <div>
                <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Pesan Balasan
                </label>
                <textarea 
                    name="message" 
                    id="message" 
                    rows="4" 
                    required
                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                    placeholder="Tulis balasan Anda..."
                ></textarea>
            </div>
            
            <div>
                <label for="attachment" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Lampiran (opsional)
                </label>
                <input 
                    type="file" 
                    name="attachment" 
                    id="attachment"
                    accept="image/*,.pdf,.doc,.docx,.txt"
                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Maksimal 5MB. Format: gambar, PDF, DOC, DOCX, TXT
                </p>
            </div>
            
            <div class="flex items-center justify-end pt-4">
                <button 
                    type="submit"
                    class="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                    Kirim Balasan
                </button>
            </div>
        </form>
    @endif
</div>
