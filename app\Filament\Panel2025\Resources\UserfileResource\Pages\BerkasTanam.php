<?php

namespace App\Filament\Panel2025\Resources\UserfileResource\Pages;

use App\Filament\Panel2025\Resources\UserfileResource;
use App\Filament\Panel2025\Resources\UserfileResource\RelationManagers\UserfilesRelationManager;
// use Filament\Actions;
use Filament\Forms\Components\{Actions, FileUpload, Grid, Group, Hidden, Placeholder, Repeater, Section, Select, TextInput};
use Filament\Forms\Components\Actions\Action as ActionsAction;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;

class BerkasTanam extends EditRecord
{
    protected static string $resource = UserfileResource::class;
    protected static ?string $title = 'Laporan Kegiatan';

    protected function getHeaderActions(): array
    {
        return [];
    }

    protected function getFormActions(): array
	{
        return [];
	}

    public function getHeading(): string
	{
        return 'Kelengkapan Dokumen Realisasi';
	}

    public function getSubheading(): ?string
    {
        $noIjin = $this->record ? $this->record->no_ijin : '##';
        return 'untuk PPRK No: ' . $noIjin;
    }

    public function form(Form $form): Form
	{
		return $form
		->schema([]);
    }
}
