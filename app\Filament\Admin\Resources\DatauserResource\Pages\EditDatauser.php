<?php

namespace App\Filament\Admin\Resources\DatauserResource\Pages;

use App\Filament\Admin\Resources\DatauserResource;
use Filament\Actions;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;

class EditDatauser extends EditRecord
{
    protected static string $resource = DatauserResource::class;
    public static string | Alignment $formActionsAlignment = Alignment::Right;
    protected static ?string $title = 'Perbarui Data Pengguna';
    public function getHeading(): string
	{
		// $sales_no = $this->record ? $this->record->sales_no : 'N/A';
        return 'Perbarui Profile ';
	}
    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    // public function form(Form $form): Form
	// {
	// 	return $form
	// 	->schema([

    //         Group::make()
    //             ->relationship(
    //                 'user',
    //                 condition: fn (?array $state): bool => filled($state['name']),
    //             )
    //             ->schema([
    //                 TextInput::make('name')
    //                     ->label('Customer'),
    //                 TextInput::make('email')
    //                     ->label('Email address')
    //                     ->email()
    //                     ->requiredWith('name'),
    //             ])
    //     ]);
    // }
}
