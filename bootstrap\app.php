<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
        $middleware->redirectGuestsTo('/login');
		$middleware->trustProxies(at: '*');

        // Register custom middleware
        $middleware->alias([
            'log-monitoring' => \App\Http\Middleware\LogMonitoringAccess::class,
        ]);

        // Apply middleware to web group
        $middleware->web(append: [
            \App\Http\Middleware\LogMonitoringAccess::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
