<?php

namespace App\Filament\Admin\Resources\SupportTicketResource\Pages;

use App\Filament\Admin\Resources\SupportTicketResource;
use App\Models\SupportTicket;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Auth;

class ViewSupportTicket extends ViewRecord
{
    protected static string $resource = SupportTicketResource::class;
    
    protected static string $view = 'filament.admin.resources.support-ticket-resource.pages.view-support-ticket';

    public function getTitle(): string
    {
        return "Tiket #{$this->record->id}";
    }

    public function getSubheading(): ?string
    {
        return $this->record->subject;
    }

    protected function getHeaderActions(): array
    {
        $user = Auth::user();
        $actions = [];

        // Tombol Edit hanya untuk admin/super admin atau staff yang assigned
        if ($user->hasAnyRole(['admin', 'Super Admin']) || 
            ($user->hasRole('support') && $this->record->staff_id === $user->id)) {
            $actions[] = Actions\EditAction::make();
        }

        // Tombol untuk mengubah status
        if ($user->hasAnyRole(['admin', 'Super Admin', 'support'])) {
            $actions[] = Actions\Action::make('updateStatus')
                ->label('Update Status')
                ->icon('heroicon-o-arrow-path')
                ->form([
                    \Filament\Forms\Components\Select::make('status')
                        ->label('Status')
                        ->options([
                            'open' => 'Open',
                            'in_progress' => 'In Progress',
                            'resolved' => 'Resolved',
                            'closed' => 'Closed',
                        ])
                        ->default($this->record->status)
                        ->required(),
                ])
                ->action(function (array $data): void {
                    $this->record->update([
                        'status' => $data['status'],
                        'last_activity_at' => now(),
                        'closed_at' => $data['status'] === 'closed' ? now() : null,
                    ]);
                    
                    $this->refreshFormData(['status']);
                });
        }

        return $actions;
    }

    public function mount(int | string $record): void
    {
        parent::mount($record);
        
        // Update last activity saat ticket dibuka
        $this->record->update(['last_activity_at' => now()]);
    }
}
