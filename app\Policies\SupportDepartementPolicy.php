<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\SupportDepartement;
use App\Models\User;

class SupportDepartementPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any SupportDepartement');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, SupportDepartement $supportdepartement): bool
    {
        return $user->checkPermissionTo('view SupportDepartement');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create SupportDepartement');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, SupportDepartement $supportdepartement): bool
    {
        return $user->checkPermissionTo('update SupportDepartement');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, SupportDepartement $supportdepartement): bool
    {
        return $user->checkPermissionTo('delete SupportDepartement');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any SupportDepartement');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, SupportDepartement $supportdepartement): bool
    {
        return $user->checkPermissionTo('restore SupportDepartement');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any SupportDepartement');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, SupportDepartement $supportdepartement): bool
    {
        return $user->checkPermissionTo('replicate SupportDepartement');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder SupportDepartement');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, SupportDepartement $supportdepartement): bool
    {
        return $user->checkPermissionTo('force-delete SupportDepartement');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any SupportDepartement');
    }
}
