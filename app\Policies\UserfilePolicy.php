<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\Userfile;
use App\Models\User;

class UserfilePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any Userfile');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Userfile $userfile): bool
    {
        return $user->checkPermissionTo('view Userfile');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create Userfile');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Userfile $userfile): bool
    {
        return $user->checkPermissionTo('update Userfile');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Userfile $userfile): bool
    {
        return $user->checkPermissionTo('delete Userfile');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any Userfile');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Userfile $userfile): bool
    {
        return $user->checkPermissionTo('restore Userfile');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any Userfile');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, Userfile $userfile): bool
    {
        return $user->checkPermissionTo('replicate Userfile');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder Userfile');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Userfile $userfile): bool
    {
        return $user->checkPermissionTo('force-delete Userfile');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any Userfile');
    }
}
