<?php

namespace App\Filament\Admin\Resources\AnnouncementResource\Pages;

use App\Filament\Admin\Resources\AnnouncementResource;
use Filament\Actions;
use Filament\Infolists\Components\{Section, TextEntry};
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;

class ViewAnnouncement extends ViewRecord
{
	public function mount(int|string $record): void
    {
        parent::mount($record);
        if (!$this->record->isReadBy()) {
            $this->record->markAsRead();
        }
    }
    protected static string $resource = AnnouncementResource::class;

	public function getHeading(): string
	{
		return $this->record->title;
	}

	public function getSubheading(): string|Htmlable|null
	{
		return 'Pengumuman';
	}

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

	public function infolist(InfoList $infolist): Infolist
	{
		return $infolist
			->schema([
				Section::make()
					->hiddenLabel()
					->columns(12)
					->schema([
						TextEntry::make('title')
							->hiddenLabel()
							->columnSpanFull()
							->weight('bold'),

						// Entry yang sudah diubah ke view blade dengan is_active
						TextEntry::make('announcement_info')
							->hiddenLabel()
							->columnSpanFull()
							->state('announcement_info')
							->view('filament.admin.resources.announcement.announcement-info'),

						TextEntry::make('content')
							->hiddenLabel()
							->columnSpanFull()
							->markdown(),

						TextEntry::make('publisher')
							->hiddenLabel()
							->columnSpanFull()
							->state(fn () => '-- Administrator')
							->markdown(),
					]),
			]);
	}
	protected function beforeFill(): void
    {
        $this->record->markAsRead();
    }
}
