<?php

namespace App\Filament\Admin\Pages;

use Filament\Pages\Page;
use League\CommonMark\Environment\Environment;
use League\CommonMark\Extension\GithubFlavoredMarkdownExtension;
use League\CommonMark\Extension\CommonMark\CommonMarkCoreExtension;
use League\CommonMark\MarkdownConverter;

class WhatsNew extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-sparkles';
    protected static ?string $navigationLabel = "Fitur Terbaru";
    protected static ?string $title = "Simethris v4.0 build 2025: Transformasi Menyeluruh dari Simethris 3.x";
    protected static ?string $navigationGroup = 'Documentations';
    protected static ?int $navigationSort = 2;

    public static function shouldRegisterNavigation(): bool
    {
        return true; // Visible to all users
    }

    protected static string $view = 'filament.admin.pages.whats-new';

    public function getViewData(): array
    {
        $markdownPath = base_path('documentations/whats-new.md');

        if (!file_exists($markdownPath)) {
            return [
                'whatsNewHtml' => '<div class="text-red-500">What\'s New file not found. Please make sure the file exists at: ' . $markdownPath . '</div>',
            ];
        }

        $markdownContent = file_get_contents($markdownPath);

        if (empty($markdownContent)) {
            return [
                'whatsNewHtml' => '<div class="text-red-500">What\'s New file is empty.</div>',
            ];
        }

        // Create a new environment with GFM extension
        $environment = new Environment([
            'html_input' => 'strip',
            'allow_unsafe_links' => false,
        ]);

        // Add the CommonMark core extension and GFM extension
        $environment->addExtension(new CommonMarkCoreExtension());
        $environment->addExtension(new GithubFlavoredMarkdownExtension());

        // Create a new converter using the configured environment
        $converter = new MarkdownConverter($environment);

        // Convert the markdown to HTML
        $result = $converter->convert($markdownContent);
        $html = $result->getContent();

        return [
            'whatsNewHtml' => $html,
        ];
    }
}
