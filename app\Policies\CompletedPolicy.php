<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\Completed;
use App\Models\User;

class CompletedPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any Completed');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Completed $completed): bool
    {
        return $user->checkPermissionTo('view Completed');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create Completed');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Completed $completed): bool
    {
        return $user->checkPermissionTo('update Completed');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Completed $completed): bool
    {
        return $user->checkPermissionTo('delete Completed');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any Completed');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Completed $completed): bool
    {
        return $user->checkPermissionTo('restore Completed');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any Completed');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, Completed $completed): bool
    {
        return $user->checkPermissionTo('replicate Completed');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder Completed');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Completed $completed): bool
    {
        return $user->checkPermissionTo('force-delete Completed');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any Completed');
    }
}
