<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\SupportTicket;
use App\Models\User;

class SupportTicketPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any SupportTicket');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, SupportTicket $supportticket): bool
    {
        return $user->checkPermissionTo('view SupportTicket');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create SupportTicket');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, SupportTicket $supportticket): bool
    {
        return $user->checkPermissionTo('update SupportTicket');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, SupportTicket $supportticket): bool
    {
        return $user->checkPermissionTo('delete SupportTicket');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any SupportTicket');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, SupportTicket $supportticket): bool
    {
        return $user->checkPermissionTo('restore SupportTicket');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any SupportTicket');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, SupportTicket $supportticket): bool
    {
        return $user->checkPermissionTo('replicate SupportTicket');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder SupportTicket');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, SupportTicket $supportticket): bool
    {
        return $user->checkPermissionTo('force-delete SupportTicket');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any SupportTicket');
    }
}
