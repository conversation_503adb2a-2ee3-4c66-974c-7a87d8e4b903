<?php

namespace App\Providers\Filament;

use Filament\Http\Middleware\Authenticate;
use Laravel\Sanctum\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use App\Filament\Admin\Widgets\{AccountWidget,FilamentInfoWidget};
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
// use Althinect\FilamentSpatieRolesPermissions\FilamentSpatieRolesPermissionsPlugin;
use App\Filament\Admin\Pages\Dashboard;
use App\Filament\Pages\Auth\Login;
use App\Filament\Pages\Auth\Register;
use DiogoGPinto\AuthUIEnhancer\AuthUIEnhancerPlugin;
use Filament\Facades\Filament;
use Filament\Navigation\NavigationItem;
use Filament\Navigation\MenuItem;
use Filament\Navigation\NavigationGroup;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Joaopaulolndev\FilamentEditProfile\FilamentEditProfilePlugin;
// use App\Filament\Pages\EditProfilePage;
use Joaopaulolndev\FilamentEditProfile\Pages\EditProfilePage;
use Rmsramos\Activitylog\ActivitylogPlugin;
use TomatoPHP\FilamentBrowser\FilamentBrowserPlugin;

class AdminPanelProvider extends PanelProvider
{

	public function panel(Panel $panel): Panel
	{
		return $panel
			->default()
			->id('admin')
			->path('admin')
			->breadcrumbs(false)
			->brandName('Simethris v4.0 @2025')
			->brandLogo(asset('assets/img/logo-simet.png'))
			->databaseNotifications()
			->favicon(asset('assets/img/favicon.png'))
			->sidebarFullyCollapsibleOnDesktop()
			->colors([
				'gray' => Color::Gray,
				'primary' => Color::Indigo,
				'info' => Color::Blue,
				'success' => Color::Emerald,
				'warning' => Color::Orange,
				'danger' => Color::Rose,
				'critical' => 'rgb(185, 28, 28)',
			])
			->discoverResources(in: app_path('Filament/Admin/Resources'), for: 'App\\Filament\\Admin\\Resources')
			->discoverPages(in: app_path('Filament/Admin/Pages'), for: 'App\\Filament\\Admin\\Pages')
			->pages([
				Dashboard::class,
				// \App\Filament\Admin\Pages\KmlFailureReportsPage::class,
			])
			->discoverWidgets(in: app_path('Filament/Admin/Widgets'), for: 'App\\Filament\\Admin\\Widgets')
			->widgets([
				// AccountWidget::class,
				// FilamentInfoWidget::class,
			])
			->userMenuItems([
				MenuItem::make()
					->label('Beranda')
					->url('/')
					->icon('heroicon-o-home'),
				MenuItem::make()
					->label('Profil Saya')
					->url(function () {
						if(Auth::user()->hasRole('importir')){
							if(is_null(Auth::user()->datauser))
							{
								return '/admin/datausers/create';
							}
							return '/admin/datausers/' . Auth::user()->datauser->id . '/view';
						}

						return '/admin/users/'. Auth::user()->id .'/myprofile';
					})
					->icon('heroicon-o-user-circle'),
				'profile' => MenuItem::make()
					// ->label(fn() => Auth::user()->name)
					->label('My Sessions')
					->url(fn (): string => EditProfilePage::getUrl())
					->icon('heroicon-o-shield-check'),
			])
			->middleware([
				EncryptCookies::class,
				AddQueuedCookiesToResponse::class,
				StartSession::class,
				AuthenticateSession::class, // Using Laravel Sanctum's AuthenticateSession
				ShareErrorsFromSession::class,
				VerifyCsrfToken::class,
				SubstituteBindings::class,
				DisableBladeIconComponents::class,
				DispatchServingFilamentEvent::class,
			])
			->authMiddleware([
				Authenticate::class,
			])
			->navigationGroups([
				NavigationGroup::make()
					 ->label(function () {
						$user = Auth::user();
						if ($user->hasRole('importir')) {
							return 'Panel Realisasi';
						}
						if ($user->hasRole('verifikator')) {
							return 'Panel Verifikasi';
						}
						return 'Panel Kerja';
					})
					 ->icon('icon-grid-1x2'),
				NavigationGroup::make()
					 ->label('CMS')
					 ->icon('icon-rss'),
			])
			->navigationItems([
				NavigationItem::make('Panel Komitmen 2024')
					->url('/panel/2024', shouldOpenInNewTab: false)
					// ->icon('icon-calendar2-x')
					->label(function () {
						$user = Auth::user();
						if ($user->hasRole('importir')) {
							return 'Realisasi 2024';
						}
						if ($user->hasRole('verifikator')) {
							return 'Verifikasi 2024';
						}
						return 'Panel 2024';
					})
					->group(function () {
						$user = Auth::user();
						if ($user->hasRole('importir')) {
							return 'Panel Realisasi';
						}
						if ($user->hasRole('verifikator')) {
							return 'Panel Verifikasi';
						}
						return 'Panel Kerja';
					})
					->visible(function () {
						$user = Auth::user();

						// Jika user adalah importir, cek apakah datauser ada
						if ($user->hasRole('importir')) {
							return $user->relationLoaded('datauser')
								? !is_null($user->datauser)
								: $user->datauser()->exists(); // Cek di database jika belum ter-load
						}

						if ($user->hasRole('spatial')) {
							return false;
						}
						return true;
					})
					->sort(-1),
				NavigationItem::make('Panel Komitmen 2025')
					->url('/panel/2025', shouldOpenInNewTab: false)
					// ->icon('icon-calendar-check-fill')
					->label(function () {
						$user = Auth::user();
						if ($user->hasRole('importir')) {
							return 'Realisasi 2025';
						}
						if ($user->hasRole('verifikator')) {
							return 'Verifikasi 2025';
						}
						return 'Panel 2025';
					})
					->group(function () {
						$user = Auth::user();
						if ($user->hasRole('importir')) {
							return 'Panel Realisasi';
						}
						if ($user->hasRole('verifikator')) {
							return 'Panel Verifikasi';
						}
						return 'Panel Kerja';
					})
					->visible(function () {
						$user = Auth::user();

						// Jika user adalah importir, cek apakah datauser ada
						if ($user->hasRole('importir')) {
							return $user->relationLoaded('datauser')
								? !is_null($user->datauser)
								: $user->datauser()->exists(); // Cek di database jika belum ter-load
						}
						if ($user->hasRole('spatial')) {
							return false;
						}
						return true;
					})
					->sort(0),
				NavigationItem::make('Profile Saya')
					->hidden()
					->url(function () {
						if(Auth::user()->hasRole('importir')){
							if(is_null(Auth::user()->datauser))
							{
								return '/admin/datausers/create';
							}
							return '/admin/datausers/' . Auth::user()->datauser->id . '/view';
						}

						return '/admin/users/'. Auth::user()->id .'/myprofile';
					})
					->icon('heroicon-o-user-circle')
					->badge(function () {
						$user = Auth::user();

						if ($user->hasRole('importir')) {
							return is_null($user->datauser) ? '!' : null;
						}

						return is_null($user->dataadmin) ? '!' : null;
					}, 'danger')
					->sort(3),
			])
			->login(Login::class)
			// ->registration(Register::class)
			->emailVerification()
			->viteTheme('resources/css/filament/admin/theme.css')
			->plugins([
				ActivitylogPlugin::make()
					->label('Activity Log')
					->pluralLabel('Activity Logs')
					->navigationItem(true)
					->navigationGroup('System')
					->navigationIcon('heroicon-o-shield-check')
					->navigationCountBadge(false)
					->navigationSort(2)
					->authorize(
						fn () => Auth::user()->hasAnyRole(['admin', 'Super Admin'])
					),
				// FilamentSpatieRolesPermissionsPlugin::make(), // Dinonaktifkan karena menggunakan implementasi sendiri
				// FilamentBrowserPlugin::make()
				// 	->hiddenFolders([
				// 		base_path('app')
				// 	])
				// 	->hiddenFiles([
				// 		base_path('.env')
				// 	])
				// 	->hiddenExtantions([
				// 		"php"
				// 	])
				// 	->allowCreateFolder()
				// 	->allowEditFile()
				// 	->allowCreateNewFile()
				// 	->allowCreateFolder()
				// 	->allowRenameFile()
				// 	->allowDeleteFile()
				// 	->allowMarkdown()
				// 	->allowCode()
				// 	->allowPreview()
				// 	->basePath(base_path()),
				AuthUIEnhancerPlugin::make()
					->showEmptyPanelOnMobile(false)
					->formPanelPosition('right')
					->formPanelWidth('40%')
					->emptyPanelBackgroundImageOpacity('90%')
					->emptyPanelBackgroundImageUrl(asset('assets/img/simet-bawang-pagi.webp')),

				FilamentEditProfilePlugin::make()
					->slug('my-session')
					->setTitle('My Sessions')
					->setNavigationLabel('My Sessions')
					->setNavigationGroup('Group Profile')
					->setIcon('heroicon-o-user')
					->setSort(10)
					//    ->canAccess(fn () => Auth::user()->id === 1)
					->shouldRegisterNavigation(false)
					->shouldShowDeleteAccountForm(false)
					->shouldShowSanctumTokens(false)
					->shouldShowBrowserSessionsForm()
					->shouldShowEditPasswordForm(false)
					->shouldShowEditProfileForm(false)
					->shouldShowAvatarForm(
							value: true,
							directory: 'uploads/avatars',
							rules: 'mimes:jpeg,png|max:1024'
						)
					->shouldShowAvatarForm(false)
			]);
	}
}
