<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\DataRealisasi2024;
use App\Models\User;

class DataRealisasi2024Policy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any DataRealisasi2024');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, DataRealisasi2024 $datarealisasi2024): bool
    {
        return $user->checkPermissionTo('view DataRealisasi2024');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create DataRealisasi2024');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, DataRealisasi2024 $datarealisasi2024): bool
    {
        return $user->checkPermissionTo('update DataRealisasi2024');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, DataRealisasi2024 $datarealisasi2024): bool
    {
        return $user->checkPermissionTo('delete DataRealisasi2024');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any DataRealisasi2024');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, DataRealisasi2024 $datarealisasi2024): bool
    {
        return $user->checkPermissionTo('restore DataRealisasi2024');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any DataRealisasi2024');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, DataRealisasi2024 $datarealisasi2024): bool
    {
        return $user->checkPermissionTo('replicate DataRealisasi2024');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder DataRealisasi2024');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, DataRealisasi2024 $datarealisasi2024): bool
    {
        return $user->checkPermissionTo('force-delete DataRealisasi2024');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any DataRealisasi2024');
    }
}
