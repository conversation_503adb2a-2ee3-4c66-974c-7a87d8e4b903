<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\Lokasi2024;
use App\Models\User;

class Lokasi2024Policy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any Lokasi2024');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Lokasi2024 $lokasi2024): bool
    {
        return $user->checkPermissionTo('view Lokasi2024');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create Lokasi2024');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Lokasi2024 $lokasi2024): bool
    {
        return $user->checkPermissionTo('update Lokasi2024');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Lokasi2024 $lokasi2024): bool
    {
        return $user->checkPermissionTo('delete Lokasi2024');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any Lokasi2024');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Lokasi2024 $lokasi2024): bool
    {
        return $user->checkPermissionTo('restore Lokasi2024');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any Lokasi2024');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, Lokasi2024 $lokasi2024): bool
    {
        return $user->checkPermissionTo('replicate Lokasi2024');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder Lokasi2024');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Lokasi2024 $lokasi2024): bool
    {
        return $user->checkPermissionTo('force-delete Lokasi2024');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any Lokasi2024');
    }
}
