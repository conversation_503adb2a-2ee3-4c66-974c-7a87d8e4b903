<?php

namespace App\Filament\Panel2024\Resources\Pks2024Resource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class LokasiRelationManager extends RelationManager
{
    protected static string $relationship = 'lokasi';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('anggota_id')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('anggota_id')
            ->columns([
                TextColumn::make('masteranggota.nama_petani')
					->label('Nama Anggot<PERSON>'),
				TextColumn::make('luas_lahan')
					->numeric()
					->suffix(' ha'),
				TextColumn::make('lokasi.datarealisasi.luas_lahan')
					->numeric()
					->suffix(' ha'),
				TextColumn::make('volume')
					->numeric()
					->suffix(' ton'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                // Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                // Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ]);
    }
}
