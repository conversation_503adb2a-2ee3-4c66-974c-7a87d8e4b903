<?php

namespace App\Filament\Admin\Resources\MasterSpatialResource\Widgets;

use App\Models\MasterSpatial;
use Filament\Tables;
use Filament\Tables\Columns\ColumnGroup;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class SpatialTableWidgets extends BaseWidget
{
	protected static ?string $heading = 'Tabel Sebaran';
	// protected int | string | array $columnSpan = 'full';
	public function table(Table $table): Table
	{
		return $table
		->paginated(false)
			->query(
				MasterSpatial::query()
                ->where('is_active', 1)
                ->selectRaw('
                    kabupaten_id AS id, 
                    kabupaten_id, 
                    COUNT(*) as jumlah_lahan, 
                    SUM(luas_lahan) as total_luas,
                    COUNT(CASE WHEN status = 2 THEN 1 END) as countMitra,
                    SUM(CASE WHEN status = 2 THEN luas_lahan END) as sumMitra
                ')
                ->groupBy('kabupaten_id')
                ->orderBy('kabupaten_id')
			)
			->columns([
				TextColumn::make('index')
					->label('No.')
					->rowIndex(),
				TextColumn::make('kabupaten.nama_kab')
					->label('Kabupaten'),

				TextColumn::make('jumlah_lahan')
					->label('Jumlah Lahan')
					->alignEnd()
					->suffix(' titik')
					->formatStateUsing(fn($state) => number_format($state, 0, ',', '.'))
					->summarize([
						Sum::make()->label('')->suffix(' titik'),
					]),

				TextColumn::make('total_luas')
					->label('Total Luas')
					->alignEnd()
					->suffix(' m²')
					->formatStateUsing(fn($state) => number_format($state, 0, ',', '.'))
					->summarize([
						Sum::make()->label('')->suffix(' m²'),
					]),
				// ColumnGroup::make('Jumlah', [
				// 	TextColumn::make('countMitra')
				// 		->label('Bermitra')
				// 		->alignEnd()
				// 		->suffix(' titik')
				// 		->formatStateUsing(fn($state) => number_format($state, 0, ',', '.'))
				// 		->summarize([
				// 			Sum::make()->label('')->suffix(' titik'),
				// 		]),

				// 	]),
				// ColumnGroup::make('Total',[
		
				// 	TextColumn::make('sumMitra')
				// 		->label('Bermitra')
				// 		->alignEnd()
				// 		->suffix(' m²')
				// 		->formatStateUsing(fn($state) => number_format($state, 0, ',', '.'))
				// 		->summarize([
				// 			Sum::make()->label('')->suffix(' m²'),
				// 		]),
				// ])
			]);
	}
	public function getDescription(): ?string
	{
		return 'Sebaran lahan per kabupaten dalam satuan luas m².';
	}
}
