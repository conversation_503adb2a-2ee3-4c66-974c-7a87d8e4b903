<?php

namespace App\Filament\Admin\Resources\MobileAppResource\Pages;

use App\Filament\Admin\Resources\MobileAppResource;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Storage;

class CreateMobileApp extends CreateRecord
{
    protected static string $resource = MobileAppResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Validasi bahwa file ada di direktori
        $filePath = 'mobile_apps/' . $data['file_name'];
        if (!Storage::disk('public')->exists($filePath)) {
            $this->halt();
            Notification::make()
                ->title('File tidak ditemukan')
                ->body('File APK tidak ditemukan di direktori mobile_apps.')
                ->danger()
                ->send();
            return $data;
        }

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
