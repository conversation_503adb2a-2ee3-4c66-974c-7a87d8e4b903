<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\EventSchedule;
use App\Models\User;

class EventSchedulePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any EventSchedule');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, EventSchedule $eventschedule): bool
    {
        return $user->checkPermissionTo('view EventSchedule');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create EventSchedule');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, EventSchedule $eventschedule): bool
    {
        return $user->checkPermissionTo('update EventSchedule');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, EventSchedule $eventschedule): bool
    {
        return $user->checkPermissionTo('delete EventSchedule');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any EventSchedule');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, EventSchedule $eventschedule): bool
    {
        return $user->checkPermissionTo('restore EventSchedule');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any EventSchedule');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, EventSchedule $eventschedule): bool
    {
        return $user->checkPermissionTo('replicate EventSchedule');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder EventSchedule');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, EventSchedule $eventschedule): bool
    {
        return $user->checkPermissionTo('force-delete EventSchedule');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any EventSchedule');
    }
}
