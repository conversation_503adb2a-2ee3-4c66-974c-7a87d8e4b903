<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Faker\Factory as Faker;
use Illuminate\Support\Facades\Artisan;

class UsersTableSeeder extends Seeder
{
    public function run()
    {
        // Membuat pengguna superadmin
        // DB::table('users')->insert([
        //     'id' => 1,
        //     'username' => 'superadmin',
        //     'name' => 'Superadmin',
        //     'email' => '<EMAIL>',
        //     'password' => Hash::make('!pkEbXmp3rvq-Am'),
        //     'email_verified_at' => now(),
        //     'created_at' => now(),
        //     'updated_at' => now(),
        // ]);

        // // Menyematkan peran super-admin setelah superadmin dibuat
        // Artisan::call('shield:super-admin', ['--user' => $superAdminId]);

        // Data pengguna lainnya
        $users = [
            [
                'username' => 'defaultadmin',
                'name' => 'DefaultAdmin',
                'email' => '<EMAIL>',
                'password' => Hash::make('6FA7_wcPqU_tNex'),
            ],
            [
                'username' => 'defaultverificator',
                'name' => 'DefaultVerificator',
                'email' => '<EMAIL>',
                'password' => Hash::make('6FA7_wcPqU_tNex'),
            ],
            [
                'username' => 'defaultspatial',
                'name' => 'DefaultSpatial',
                'email' => '<EMAIL>',
                'password' => Hash::make('6FA7_wcPqU_tNex'),
            ],
            [
                'username' => 'defaultdinas',
                'name' => 'DefaultDinas',
                'email' => '<EMAIL>',
                'password' => Hash::make('6FA7_wcPqU_tNex'),
            ],
        ];

        // Menambahkan pengguna lain ke dalam database
        foreach ($users as $user) {
            DB::table('users')->insert([
                // 'id' => Str::uuid(),
                'username' => $user['username'],
                'name' => $user['name'],
                'email' => $user['email'],
                'password' => $user['password'],
                'email_verified_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}

