<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\PengajuanVerifikasi;
use App\Models\VerificatorAssignment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class MobileVerifikatorController extends Controller
{
    /**
     * Get assigned verifications for the logged-in verifikator
     */
    public function getAssignedVerifications()
    {
        $user = Auth::user();

        // Check if user is verifikator
        if (!$user->hasRole('verifikator')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access. This endpoint is only for verifikator role.'
            ], 403);
        }

        // Get verifications assigned to the verifikator
        $assignments = VerificatorAssignment::where('user_id', $user->id)
            ->with('pengajuan')
            ->get();

        $verifications = [];
        foreach ($assignments as $assignment) {
            if ($assignment->pengajuan) {
                $verification = $assignment->pengajuan;

                // Map kind to readable text
                $kindMap = [
                    'PVT' => 'Verifikasi Tanam',
                    'PVP' => 'Verifikasi Produksi',
                    'PVS' => 'Penerbitan SKL',
                ];

                // Map status to readable text based on kind
                $statusText = $this->getStatusText($verification->kind, $verification->status);

                // Calculate progress based on status
                $progress = $this->calculateProgress($verification->kind, $verification->status);

                // Get company name if available
                $company = null;
                if ($verification->user && $verification->user->datauser) {
                    $company = $verification->user->datauser->company_name;
                }

                $verifications[] = [
                    'id' => $verification->id,
                    'no_pengajuan' => $verification->no_pengajuan,
                    'no_ijin' => $verification->no_ijin,
                    'kind' => $verification->kind,
                    'kind_text' => $kindMap[$verification->kind] ?? $verification->kind,
                    'status' => $verification->status,
                    'status_text' => $statusText,
                    'progress' => $progress,
                    'created_at' => $verification->created_at,
                    'created_at_formatted' => $verification->created_at->format('d M Y'),
                    'verifikator' => $user->name,
                    'note' => $verification->note,
                    'company' => $company,
                    'pprk' => $verification->no_ijin,
                ];
            }
        }

        return response()->json([
            'success' => true,
            'data' => $verifications
        ]);
    }

    /**
     * Get verification detail by ID
     */
    public function getVerificationDetail($id)
    {
        $user = Auth::user();

        // Check if user is verifikator
        if (!$user->hasRole('verifikator')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access. This endpoint is only for verifikator role.'
            ], 403);
        }

        // Check if the verification is assigned to this verifikator
        $assignment = VerificatorAssignment::where('user_id', $user->id)
            ->where('pengajuan_id', $id)
            ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found or not assigned to you'
            ], 404);
        }

        // Get verification detail
        $verification = PengajuanVerifikasi::find($id);

        if (!$verification) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found'
            ], 404);
        }

        // Map kind to readable text
        $kindMap = [
            'PVT' => 'Verifikasi Tanam',
            'PVP' => 'Verifikasi Produksi',
            'PVS' => 'Penerbitan SKL',
        ];

        // Map status to readable text based on kind
        $statusText = $this->getStatusText($verification->kind, $verification->status);

        // Calculate progress based on status
        $progress = $this->calculateProgress($verification->kind, $verification->status);

        // Get company name if available
        $company = null;
        if ($verification->user && $verification->user->datauser) {
            $company = $verification->user->datauser->company_name;
        }

        // Format assignment data
        $assignmentData = [
            'id' => $assignment->id,
            'verifikator_id' => $user->id,
            'verifikator_name' => $user->name,
            'no_sk' => $assignment->no_sk,
            'tgl_sk' => $assignment->tgl_sk,
            'tgl_sk_formatted' => $assignment->tgl_sk ? date('d M Y', strtotime($assignment->tgl_sk)) : null,
            'note' => $assignment->note,
        ];

        // Format files data if available
        $filesData = null;
        if ($verification->files) {
            $filesData = [
                'id' => $verification->files->id,
                'spvt' => $verification->files->spvt,
                'sptjm' => $verification->files->sptjm,
                'rta' => $verification->files->rta,
                'sph' => $verification->files->sph,
                'logbook' => $verification->files->logbook,
                // Add more files as needed
            ];
        }

        $detailData = [
            'id' => $verification->id,
            'no_pengajuan' => $verification->no_pengajuan,
            'no_ijin' => $verification->no_ijin,
            'kind' => $verification->kind,
            'kind_text' => $kindMap[$verification->kind] ?? $verification->kind,
            'status' => $verification->status,
            'status_text' => $statusText,
            'progress' => $progress,
            'created_at' => $verification->created_at,
            'created_at_formatted' => $verification->created_at->format('d M Y'),
            'note' => $verification->note,
            'verif_at' => $verification->verif_at,
            'verif_at_formatted' => $verification->verif_at ? date('d M Y', strtotime($verification->verif_at)) : null,
            'metode' => $verification->metode,
            'assignment' => $assignmentData,
            'files' => $filesData,
        ];

        return response()->json([
            'success' => true,
            'data' => $detailData
        ]);
    }

    /**
     * Helper function to get status text based on kind and status
     */
    private function getStatusText($kind, $status)
    {
        if ($kind == 'PVS') {
            switch ($status) {
                case '0': return 'Baru';
                case '1': return 'Penugasan';
                case '2': return 'Penetapan';
                case '3': return 'Dimulai';
                case '4': return 'Direkomendasikan';
                case '5': return 'Perbaikan';
                case '6': return 'Ditolak';
                case '7': return 'Disetujui';
                case '8': return 'Diterbitkan/Lunas';
                default: return 'Tidak Diketahui';
            }
        } else {
            switch ($status) {
                case '0': return 'Baru';
                case '1': return 'Penugasan';
                case '2': return 'Penetapan';
                case '3': return 'Dimulai';
                case '4': return 'Selesai';
                case '5': return 'Perbaikan';
                default: return 'Tidak Diketahui';
            }
        }
    }

    /**
     * Helper function to calculate progress based on kind and status
     */
    private function calculateProgress($kind, $status)
    {
        if ($kind == 'PVS') {
            switch ($status) {
                case '0': return 10;
                case '1': return 20;
                case '2': return 30;
                case '3': return 40;
                case '4': return 60;
                case '5': return 50;
                case '6': return 70;
                case '7': return 80;
                case '8': return 100;
                default: return 0;
            }
        } else {
            switch ($status) {
                case '0': return 10;
                case '1': return 30;
                case '2': return 50;
                case '3': return 70;
                case '4': return 100;
                case '5': return 60;
                default: return 0;
            }
        }
    }

    /**
     * Get verification documents to be verified
     */
    public function getVerificationDocuments($id)
    {
        $user = Auth::user();

        // Check if user is verifikator
        if (!$user->hasRole('verifikator')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access. This endpoint is only for verifikator role.'
            ], 403);
        }

        // Check if the verification is assigned to this verifikator
        $assignment = VerificatorAssignment::where('user_id', $user->id)
            ->where('pengajuan_id', $id)
            ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found or not assigned to you'
            ], 404);
        }

        // Get verification detail
        $verification = PengajuanVerifikasi::find($id);

        if (!$verification) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found'
            ], 404);
        }

        // Get documents based on verification kind
        $documents = [];

        // Get relevant files using the berkaspengajuan relation
        $berkasPengajuan = $verification->berkaspengajuan()->get();

        // Get userfiles for additional documents
        $userFiles = $verification->userfiles()->get();

        if ($verification->kind == 'PVT') {
            // Documents for Verifikasi Tanam

            // Find SPVT document
            $spvtFile = $berkasPengajuan->where('kind', 'spvt')->first();
            $documents[] = [
                'id' => 'spvt',
                'name' => 'Surat Pengajuan Verifikasi Tanam',
                'file_url' => $spvtFile ? Storage::url($spvtFile->file_url) : null,
                'status' => null,
                'required' => true,
            ];

            // Find SPTJM document
            $sptjmFile = $userFiles->where('kind', 'sptjm')->first();
            $documents[] = [
                'id' => 'sptjm',
                'name' => 'Surat Pertanggungjawaban Mutlak',
                'file_url' => $sptjmFile ? Storage::url($sptjmFile->file_url) : null,
                'status' => null,
                'required' => true,
            ];

            // Find RTA document
            $rtaFile = $userFiles->where('kind', 'rta')->first();
            $documents[] = [
                'id' => 'rta',
                'name' => 'Form Realisasi Tanam',
                'file_url' => $rtaFile ? Storage::url($rtaFile->file_url) : null,
                'status' => null,
                'required' => true,
            ];

            // Find SPH document
            $sphFile = $userFiles->where('kind', 'sph')->first();
            $documents[] = [
                'id' => 'sph',
                'name' => 'Surat Pernyataan Hasil',
                'file_url' => $sphFile ? Storage::url($sphFile->file_url) : null,
                'status' => null,
                'required' => true,
            ];

            // Find Logbook document
            $logbookFile = $userFiles->where('kind', 'logbook')->first();
            $documents[] = [
                'id' => 'logbook',
                'name' => 'Logbook Tanam',
                'file_url' => $logbookFile ? Storage::url($logbookFile->file_url) : null,
                'status' => null,
                'required' => true,
            ];

        } else if ($verification->kind == 'PVP') {
            // Documents for Verifikasi Produksi

            // Find SPVP document
            $spvpFile = $berkasPengajuan->where('kind', 'spvp')->first();
            $documents[] = [
                'id' => 'spvp',
                'name' => 'Surat Pengajuan Verifikasi Produksi',
                'file_url' => $spvpFile ? Storage::url($spvpFile->file_url) : null,
                'status' => null,
                'required' => true,
            ];

            // Find SPTJM Produksi document
            $sptjmProduksiFile = $userFiles->where('kind', 'sptjm_produksi')->first();
            $documents[] = [
                'id' => 'sptjm_produksi',
                'name' => 'Surat Pertanggungjawaban Mutlak Produksi',
                'file_url' => $sptjmProduksiFile ? Storage::url($sptjmProduksiFile->file_url) : null,
                'status' => null,
                'required' => true,
            ];

            // Find RPO document
            $rpoFile = $userFiles->where('kind', 'rpo')->first();
            $documents[] = [
                'id' => 'rpo',
                'name' => 'Form Realisasi Produksi',
                'file_url' => $rpoFile ? Storage::url($rpoFile->file_url) : null,
                'status' => null,
                'required' => true,
            ];

            // Find SPH Produksi document
            $sphProduksiFile = $userFiles->where('kind', 'sph_produksi')->first();
            $documents[] = [
                'id' => 'sph_produksi',
                'name' => 'Surat Pernyataan Hasil Produksi',
                'file_url' => $sphProduksiFile ? Storage::url($sphProduksiFile->file_url) : null,
                'status' => null,
                'required' => true,
            ];

            // Find Logbook Produksi document
            $logbookProduksiFile = $userFiles->where('kind', 'logbook_produksi')->first();
            $documents[] = [
                'id' => 'logbook_produksi',
                'name' => 'Logbook Produksi',
                'file_url' => $logbookProduksiFile ? Storage::url($logbookProduksiFile->file_url) : null,
                'status' => null,
                'required' => true,
            ];

            // Find Form LA document
            $formLaFile = $userFiles->where('kind', 'form_la')->first();
            $documents[] = [
                'id' => 'form_la',
                'name' => 'Form LA',
                'file_url' => $formLaFile ? Storage::url($formLaFile->file_url) : null,
                'status' => null,
                'required' => true,
            ];
        }

        return response()->json([
            'success' => true,
            'data' => [
                'verification_id' => $verification->id,
                'no_pengajuan' => $verification->no_pengajuan,
                'kind' => $verification->kind,
                'documents' => $documents
            ]
        ]);
    }

    /**
     * Get locations to be verified for a verification
     */
    public function getVerificationLocations($id)
    {
        $user = Auth::user();

        // Check if user is verifikator
        if (!$user->hasRole('verifikator')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access. This endpoint is only for verifikator role.'
            ], 403);
        }

        // Check if the verification is assigned to this verifikator
        $assignment = VerificatorAssignment::where('user_id', $user->id)
            ->where('pengajuan_id', $id)
            ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found or not assigned to you'
            ], 404);
        }

        // Get verification detail
        $verification = PengajuanVerifikasi::find($id);

        if (!$verification) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found'
            ], 404);
        }

        // Get locations based on verification no_ijin
        // This is a simplified implementation - you'll need to adjust based on your actual data model
        $locations = \App\Models\Realisasi2025::where('no_ijin', $verification->no_ijin)
            ->with(['masterSpatial', 'pks'])
            ->get()
            ->map(function ($realisasi) {
                $spatial = $realisasi->masterSpatial;
                $pks = $realisasi->pks;

                return [
                    'id' => $realisasi->id,
                    'kode_spatial' => $realisasi->kode_spatial,
                    'luas_lahan' => $realisasi->luas_lahan,
                    'periode_tanam' => $realisasi->periode_tanam,
                    'status' => $realisasi->status,
                    'latitude' => $spatial ? $spatial->latitude : null,
                    'longitude' => $spatial ? $spatial->longitude : null,
                    'polygon' => $spatial ? $spatial->polygon : null,
                    'altitude' => $spatial ? $spatial->altitude : null,
                    'provinsi' => $spatial ? $spatial->provinsi : null,
                    'kabupaten' => $spatial ? $spatial->kabupaten : null,
                    'kecamatan' => $spatial ? $spatial->kecamatan : null,
                    'desa' => $spatial ? $spatial->desa : null,
                    'poktan' => $pks ? $pks->poktan_name : null,
                    'verification_status' => null, // This would be populated from your verification data
                ];
            });

        return response()->json([
            'success' => true,
            'data' => [
                'verification_id' => $verification->id,
                'no_pengajuan' => $verification->no_pengajuan,
                'no_ijin' => $verification->no_ijin,
                'kind' => $verification->kind,
                'locations' => $locations
            ]
        ]);
    }

    /**
     * Get detail of a specific location to be verified
     */
    public function getLocationDetail($verificationId, $locationId)
    {
        $user = Auth::user();

        // Check if user is verifikator
        if (!$user->hasRole('verifikator')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access. This endpoint is only for verifikator role.'
            ], 403);
        }

        // Check if the verification is assigned to this verifikator
        $assignment = VerificatorAssignment::where('user_id', $user->id)
            ->where('pengajuan_id', $verificationId)
            ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found or not assigned to you'
            ], 404);
        }

        // Get verification detail
        $verification = PengajuanVerifikasi::find($verificationId);

        if (!$verification) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found'
            ], 404);
        }

        // Get location detail
        $realisasi = \App\Models\Realisasi2025::find($locationId);

        if (!$realisasi || $realisasi->no_ijin !== $verification->no_ijin) {
            return response()->json([
                'success' => false,
                'message' => 'Location not found or not associated with this verification'
            ], 404);
        }

        // Load related data
        $realisasi->load(['masterSpatial', 'pks', 'pks.poktan', 'detailRealisasi']);

        // Get spatial data
        $spatial = $realisasi->masterSpatial;

        // Get PKS data
        $pks = $realisasi->pks;

        // Get poktan data
        $poktan = $pks ? $pks->poktan : null;

        // Get detail realisasi (activities)
        $activities = $realisasi->detailRealisasi->map(function ($detail) {
            return [
                'id' => $detail->id,
                'tahap' => $detail->tahap,
                'tanggal' => $detail->tanggal,
                'tanggal_formatted' => $detail->tanggal ? date('d M Y', strtotime($detail->tanggal)) : null,
                'description' => $detail->description,
                'status' => $detail->status,
                'photos' => $detail->photos ? json_decode($detail->photos) : [],
            ];
        });

        // Format polygon data if available
        $polygonData = null;
        if ($spatial && $spatial->polygon) {
            try {
                $polygonData = json_decode($spatial->polygon);
            } catch (\Exception $e) {
                $polygonData = null;
            }
        }

        // Prepare location detail data
        $locationDetail = [
            'id' => $realisasi->id,
            'kode_spatial' => $realisasi->kode_spatial,
            'no_ijin' => $realisasi->no_ijin,
            'luas_lahan' => $realisasi->luas_lahan,
            'periode_tanam' => $realisasi->periode_tanam,
            'status' => $realisasi->status,
            'created_at' => $realisasi->created_at,
            'created_at_formatted' => $realisasi->created_at ? date('d M Y', strtotime($realisasi->created_at)) : null,
            'spatial' => $spatial ? [
                'id' => $spatial->id,
                'latitude' => $spatial->latitude,
                'longitude' => $spatial->longitude,
                'polygon' => $polygonData,
                'altitude' => $spatial->altitude,
                'provinsi' => $spatial->provinsi,
                'kabupaten' => $spatial->kabupaten,
                'kecamatan' => $spatial->kecamatan,
                'desa' => $spatial->desa,
                'address' => $spatial->address,
            ] : null,
            'pks' => $pks ? [
                'id' => $pks->id,
                'no_perjanjian' => $pks->no_perjanjian,
                'tanggal_perjanjian' => $pks->tanggal_perjanjian,
                'tanggal_perjanjian_formatted' => $pks->tanggal_perjanjian ? date('d M Y', strtotime($pks->tanggal_perjanjian)) : null,
                'luas_rencana' => $pks->luas_rencana,
                'varietas' => $pks->varietas,
                'status' => $pks->status,
            ] : null,
            'poktan' => $poktan ? [
                'id' => $poktan->id,
                'poktan_name' => $poktan->poktan_name,
                'ketua_poktan' => $poktan->ketua_poktan,
                'hp_poktan' => $poktan->hp_poktan,
                'anggota_count' => $poktan->anggota_count,
            ] : null,
            'activities' => $activities,
            'verification_status' => null, // This would be populated from your verification data
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'verification_id' => $verification->id,
                'no_pengajuan' => $verification->no_pengajuan,
                'no_ijin' => $verification->no_ijin,
                'kind' => $verification->kind,
                'location' => $locationDetail
            ]
        ]);
    }

    /**
     * Submit document verification results
     */
    public function submitDocumentVerification(Request $request, $verificationId)
    {
        $user = Auth::user();

        // Check if user is verifikator
        if (!$user->hasRole('verifikator')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access. This endpoint is only for verifikator role.'
            ], 403);
        }

        // Validate request
        $validator = Validator::make($request->all(), [
            'documents' => 'required|array',
            'documents.*.id' => 'required|string',
            'documents.*.status' => 'required|in:sesuai,perbaiki',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if the verification is assigned to this verifikator
        $assignment = VerificatorAssignment::where('user_id', $user->id)
            ->where('pengajuan_id', $verificationId)
            ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found or not assigned to you'
            ], 404);
        }

        // Get verification detail
        $verification = PengajuanVerifikasi::find($verificationId);

        if (!$verification) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found'
            ], 404);
        }

        // SIMULASI SAJA - TIDAK MENYIMPAN KE DATABASE
        // Komentar: Kode di bawah ini hanya simulasi dan tidak benar-benar menyimpan data

        try {
            // Process document verification results (simulasi)
            $documents = $request->input('documents');

            // Log data yang diterima untuk debugging
            Log::info('Document verification request received', [
                'verification_id' => $verificationId,
                'user_id' => $user->id,
                'documents' => $documents
            ]);

            // Simulasi proses verifikasi dokumen
            foreach ($documents as $document) {
                $docId = $document['id'];
                $status = $document['status'];

                // Log setiap dokumen yang diverifikasi
                Log::info("Simulating verification for document: {$docId}", [
                    'status' => $status,
                    'verification_id' => $verificationId
                ]);

                // Kode asli (dikomentari):
                /*
                // Find the corresponding file record
                $userfile = Userfile::where('no_ijin', $verification->no_ijin)
                    ->where('kind', $docId)
                    ->first();

                if ($userfile) {
                    // Update the file status
                    $userfile->status = $status === 'sesuai' ? 'approved' : 'rejected';
                    $userfile->verif_by = $user->id;
                    $userfile->verif_at = Carbon::now();
                    $userfile->save();
                }
                */
            }

            // Simulasi update status verifikasi
            Log::info("Simulating verification status update", [
                'verification_id' => $verificationId,
                'current_status' => $verification->status,
                'would_update_to' => ($verification->status == '1') ? '2' : $verification->status
            ]);

            // Kode asli (dikomentari):
            /*
            // Update verification status if needed
            if ($verification->status == '1') {
                $verification->status = '2'; // Update to "Penetapan" status
                $verification->save();
            }
            */

            // Tidak perlu commit karena tidak ada transaksi yang dimulai
            // DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Document verification results submitted successfully (SIMULATION MODE)',
                'data' => [
                    'verification_id' => $verification->id,
                    'no_pengajuan' => $verification->no_pengajuan,
                    'status' => $verification->status,
                    'status_text' => $this->getStatusText($verification->kind, $verification->status),
                ]
            ]);
        } catch (\Exception $e) {
            // Log error
            Log::error('Error in document verification simulation', [
                'verification_id' => $verificationId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to simulate document verification results',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Submit location verification results
     */
    public function submitLocationVerification(Request $request, $verificationId, $locationId)
    {
        $user = Auth::user();

        // Check if user is verifikator
        if (!$user->hasRole('verifikator')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access. This endpoint is only for verifikator role.'
            ], 403);
        }

        // Validate request
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:sesuai,perbaiki',
            'notes' => 'nullable|string|max:500',
            'photos' => 'nullable|array',
            'photos.*' => 'nullable|string', // Base64 encoded images
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if the verification is assigned to this verifikator
        $assignment = VerificatorAssignment::where('user_id', $user->id)
            ->where('pengajuan_id', $verificationId)
            ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found or not assigned to you'
            ], 404);
        }

        // Get verification detail
        $verification = PengajuanVerifikasi::find($verificationId);

        if (!$verification) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found'
            ], 404);
        }

        // Get location detail
        $realisasi = \App\Models\Realisasi2025::find($locationId);

        if (!$realisasi || $realisasi->no_ijin !== $verification->no_ijin) {
            return response()->json([
                'success' => false,
                'message' => 'Location not found or not associated with this verification'
            ], 404);
        }

        // SIMULASI SAJA - TIDAK MENYIMPAN KE DATABASE
        // Komentar: Kode di bawah ini hanya simulasi dan tidak benar-benar menyimpan data

        try {
            // Log data yang diterima untuk debugging
            Log::info('Location verification request received', [
                'verification_id' => $verificationId,
                'location_id' => $locationId,
                'user_id' => $user->id,
                'status' => $request->input('status'),
                'notes' => $request->input('notes'),
                'has_photos' => $request->has('photos'),
                'photo_count' => $request->has('photos') ? count($request->input('photos')) : 0,
                'latitude' => $request->input('latitude'),
                'longitude' => $request->input('longitude'),
            ]);

            // Simulasi penyimpanan foto
            if ($request->has('photos') && is_array($request->input('photos'))) {
                foreach ($request->input('photos') as $index => $photoBase64) {
                    // Log info tentang foto (tanpa menyimpan konten base64 yang besar)
                    Log::info("Simulating saving photo {$index} for location {$locationId}");

                    // Kode asli (dikomentari):
                    /*
                    // Decode base64 image
                    $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $photoBase64));

                    // Generate filename
                    $filename = 'verifikasi_' . $verification->kind . '_' . $locationId . '_' . time() . '_' . ($index + 1) . '.jpg';

                    // Save to storage
                    Storage::disk('public')->put('verification_photos/' . $filename, $imageData);

                    // Save photo reference to database
                    // ...
                    */
                }
            }

            // Simulasi update status verifikasi lokasi
            Log::info("Simulating location verification status update", [
                'verification_id' => $verificationId,
                'location_id' => $locationId,
                'status' => $request->input('status'),
                'notes' => $request->input('notes'),
            ]);

            // Kode asli (dikomentari):
            /*
            // Update location verification status
            $realisasi->verification_status = $request->input('status') === 'sesuai' ? 'approved' : 'rejected';
            $realisasi->verification_notes = $request->input('notes');
            $realisasi->verified_by = $user->id;
            $realisasi->verified_at = Carbon::now();
            $realisasi->save();

            // Update verification status if needed
            if ($verification->status == '2') {
                $verification->status = '3'; // Update to "Dimulai" status
                $verification->save();
            }
            */

            return response()->json([
                'success' => true,
                'message' => 'Location verification results submitted successfully (SIMULATION MODE)',
                'data' => [
                    'verification_id' => $verification->id,
                    'location_id' => $locationId,
                    'no_pengajuan' => $verification->no_pengajuan,
                    'status' => $verification->status,
                    'status_text' => $this->getStatusText($verification->kind, $verification->status),
                ]
            ]);
        } catch (\Exception $e) {
            // Log error
            Log::error('Error in location verification simulation', [
                'verification_id' => $verificationId,
                'location_id' => $locationId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to simulate location verification results',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Submit final verification results with notes
     */
    public function submitVerificationResult(Request $request, $verificationId)
    {
        $user = Auth::user();

        // Check if user is verifikator
        if (!$user->hasRole('verifikator')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access. This endpoint is only for verifikator role.'
            ], 403);
        }

        // Validate request
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:sesuai,perbaiki',
            'notes' => 'nullable|string|max:1000',
            'recommendation' => 'nullable|string|max:1000',
            'files' => 'nullable|array',
            'files.*' => 'nullable|string', // Base64 encoded files
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if the verification is assigned to this verifikator
        $assignment = VerificatorAssignment::where('user_id', $user->id)
            ->where('pengajuan_id', $verificationId)
            ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found or not assigned to you'
            ], 404);
        }

        // Get verification detail
        $verification = PengajuanVerifikasi::find($verificationId);

        if (!$verification) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found'
            ], 404);
        }

        // SIMULASI SAJA - TIDAK MENYIMPAN KE DATABASE
        // Komentar: Kode di bawah ini hanya simulasi dan tidak benar-benar menyimpan data

        try {
            // Log data yang diterima untuk debugging
            Log::info('Final verification result request received', [
                'verification_id' => $verificationId,
                'user_id' => $user->id,
                'status' => $request->input('status'),
                'notes' => $request->input('notes'),
                'recommendation' => $request->input('recommendation'),
                'has_files' => $request->has('files'),
                'file_count' => $request->has('files') ? count($request->input('files')) : 0,
            ]);

            // Simulasi penyimpanan file hasil verifikasi
            if ($request->has('files') && is_array($request->input('files'))) {
                foreach ($request->input('files') as $index => $fileBase64) {
                    // Log info tentang file (tanpa menyimpan konten base64 yang besar)
                    Log::info("Simulating saving verification result file {$index}");

                    // Kode asli (dikomentari):
                    /*
                    // Decode base64 file
                    $fileData = base64_decode(preg_replace('#^data:application/\w+;base64,#i', '', $fileBase64));

                    // Generate filename
                    $filename = 'hasil_verifikasi_' . $verification->kind . '_' . $verificationId . '_' . time() . '_' . ($index + 1) . '.pdf';

                    // Save to storage
                    Storage::disk('public')->put('verification_results/' . $filename, $fileData);

                    // Save file reference to database
                    // ...
                    */
                }
            }

            // Simulasi update status verifikasi
            Log::info("Simulating final verification status update", [
                'verification_id' => $verificationId,
                'status' => $request->input('status'),
                'notes' => $request->input('notes'),
                'recommendation' => $request->input('recommendation'),
            ]);

            // Kode asli (dikomentari):
            /*
            // Update verification status
            $verification->status = $request->input('status') === 'sesuai' ? '4' : '5'; // 4 = Selesai, 5 = Perbaikan
            $verification->note = $request->input('notes');
            $verification->recommendation = $request->input('recommendation');
            $verification->verif_by = $user->id;
            $verification->verif_at = Carbon::now();
            $verification->save();
            */

            // Tentukan status baru berdasarkan input
            $newStatus = $request->input('status') === 'sesuai' ? '4' : '5'; // 4 = Selesai, 5 = Perbaikan
            $newStatusText = $this->getStatusText($verification->kind, $newStatus);

            return response()->json([
                'success' => true,
                'message' => 'Verification results submitted successfully (SIMULATION MODE)',
                'data' => [
                    'verification_id' => $verification->id,
                    'no_pengajuan' => $verification->no_pengajuan,
                    'current_status' => $verification->status,
                    'current_status_text' => $this->getStatusText($verification->kind, $verification->status),
                    'would_update_to' => $newStatus,
                    'would_update_to_text' => $newStatusText,
                ]
            ]);
        } catch (\Exception $e) {
            // Log error
            Log::error('Error in final verification result simulation', [
                'verification_id' => $verificationId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to simulate final verification results',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
