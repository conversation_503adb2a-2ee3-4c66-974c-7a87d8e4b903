<?php

namespace App\Jobs;

use App\Models\MasterSpatial;
use App\Models\Pks2025;
use App\Models\Realisasi2025;
use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ExpireOverdueRecords implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle()
    {
        $today = Carbon::now('Asia/Jakarta')->toDateString();

        // Ambil semua PKS yang sudah expired
        $expiredRecords = Pks2025::whereNull('berkas_pks')
            ->whereRaw("DATE(deadline_at) <= ?", [$today])
            ->get();

        if ($expiredRecords->isEmpty()) {
            Log::info('Tidak ada PKS yang kedaluwarsa hari ini.');
            return;
        }

        // Ambil semua data yang diperlukan dari koleksi hasil query
        $npwpList = $expiredRecords->pluck('npwp')->unique()->toArray();
        $noIjinList = $expiredRecords->pluck('no_ijin')->unique()->toArray();
        $kodePoktanList = $expiredRecords->pluck('kode_poktan')->unique()->toArray();

        // Logging untuk debugging
        Log::info('PKS Kedaluwarsa:', [
            'total' => $expiredRecords->count(),
            'npwpList' => $npwpList,
            'noIjinList' => $noIjinList,
            'kodePoktanList' => $kodePoktanList
        ]);

        // Kirim notifikasi ke pengguna yang bersangkutan di luar transaksi DB
        $owners = User::whereIn('npwp', $npwpList)->get();
        foreach ($owners as $owner) {
            Notification::make()
                ->title('⏳ PKS Kedaluwarsa')
                ->body("<p>Beberapa PKS Anda telah melewati batas waktu. Anda harus mengulangi prosedur dari awal.</p>")
                ->sendToDatabase($owner);
        }

        // Jalankan perubahan database dalam transaksi
        DB::transaction(function () use ($noIjinList, $kodePoktanList) {
            // Update status MasterSpatial
            MasterSpatial::whereIn('reserved_by', $noIjinList)->update([
                'reserved_by' => null,
                'reserved_at' => null,
                'status'      => 0,
            ]);

            // Logging sebelum penghapusan
            Log::info('Menghapus Realisasi2025:', ['no_ijin' => $noIjinList, 'kode_poktan' => $kodePoktanList]);
            Log::info('Menghapus Pks2025:', ['no_ijin' => $noIjinList, 'kode_poktan' => $kodePoktanList]);

            Realisasi2025::whereIn('no_ijin', $noIjinList)
                ->whereIn('kode_poktan', $kodePoktanList)
                ->delete();

            Pks2025::whereIn('no_ijin', $noIjinList)
                ->whereIn('kode_poktan', $kodePoktanList)
                ->delete();
        });

        Log::info('Proses penghapusan PKS yang kedaluwarsa selesai.');

        // **Pastikan tabel menggunakan SoftDeletes sebelum menjalankan forceDelete()**
        if (in_array('Illuminate\Database\Eloquent\SoftDeletes', class_uses(Realisasi2025::class))) {
            Realisasi2025::onlyTrashed()->forceDelete();
        }

        if (in_array('Illuminate\Database\Eloquent\SoftDeletes', class_uses(Pks2025::class))) {
            Pks2025::onlyTrashed()->forceDelete();
        }
    }
}
