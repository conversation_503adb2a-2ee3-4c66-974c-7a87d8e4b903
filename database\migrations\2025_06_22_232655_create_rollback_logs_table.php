<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rollback_logs', function (Blueprint $table) {
			$table->id();
			$table->string('log_type'); // Contoh: 'Cancel SKL Submission'
			$table->string('model_type'); // Class nama model terkait
			$table->unsignedBigInteger('model_id'); // ID dari model
			$table->unsignedBigInteger('request_by'); // user ID
			$table->text('data'); // Alasan pembatalan
			$table->json('metadata')->nullable(); // data form lengkap
			$table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rollback_logs');
    }
};
