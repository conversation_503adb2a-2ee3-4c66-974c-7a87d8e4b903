<?php
namespace App\Http\Responses;

use Filament\Pages\Dashboard;
use Illuminate\Http\RedirectResponse;
use Livewire\Features\SupportRedirects\Redirector;
use Filament\Http\Responses\Auth\LoginResponse as BaseLoginResponse;

class LoginResponse extends BaseLoginResponse
{
    public function toResponse($request): RedirectResponse|Redirector
    {
        //ini untuk yang belum login return redirect()->to(Filament::getLoginUrl());
        return redirect()->to(Dashboard::getUrl(panel: 'admin'));
    }
}
