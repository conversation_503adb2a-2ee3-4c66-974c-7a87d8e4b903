<?php

namespace App\Filament\Panel2024\Resources\Pks2024Resource\Pages;

use App\Filament\Panel2024\Resources\Pks2024Resource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\ContentTabPosition;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class ViewPks2024 extends ViewRecord
{
    protected static string $resource = Pks2024Resource::class;
    protected static ?string $title = 'PKS';
    public function getHeading(): string
	{
        $noPks = $this->record ? $this->record->masterpoktan->nama_kelompok : '#';
        return 'Poktan '.$noPks;
	}
    public function getSubheading(): ?string
    {
        $noIjin = $this->record ? $this->record->no_ijin : '##';
        return 'untuk RIPH No: ' . $noIjin;
    }
    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
			Action::make('back')
            ->label('Kembali')
            ->color('success')
            ->url(fn () => route('filament.panel2024.resources.commitment2024s.edit', $this->record->commitment->id)),
        ];
    }

	public function hasCombinedRelationManagerTabsWithContent(): bool
	{
		return true;
	}

	public function getContentTabPosition(): ?ContentTabPosition
	{
		return ContentTabPosition::Before;
	}
}
