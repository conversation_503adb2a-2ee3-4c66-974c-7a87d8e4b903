<?php

namespace App\Filament\Panel2025\Resources\PengajuanVerifikasiResource\Pages;

use App\Filament\Panel2025\Resources\PengajuanVerifikasiResource;
use App\Models\PengajuanVerifikasi;
use Filament\Actions;
use Filament\Actions\StaticAction;
use Filament\Forms\Components\DatePicker;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Columns\{TextColumn};
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Table;
use Filament\Tables\Actions\{Action, BulkActionGroup};
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class ListPengajuanVerifikasis extends ListRecords
{
    protected static string $resource = PengajuanVerifikasiResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
	
	protected function getTableQuery(): Builder
	{
		$user = Auth::user();
		$isImportir = $user->hasRole('importir');
		$isVerifikator = $user->hasAnyRole(['verifikator']);
		$isSU = $user->hasAnyRole(['Super Admin', 'admin']);
	
		return PengajuanVerifikasi::query()
			->when($isImportir, fn ($query) => 
				$query->where('npwp', $user->npwp)
			)
			->when($isVerifikator, fn ($query) => $query
				->whereNotIn('status', ['0', '5'])
				->whereIn('kind', ['PVT', 'PVP'])
				->whereHas('assignments', fn ($q) => 
					$q->where('user_id', $user->id)
				)
			)
			->when($isSU, fn ($query) => $query
				->where(function ($q) {
					$q->where(function ($sub) {
						$sub->whereIn('kind', ['PVT', 'PVP']);
							// ->where('status', '0');
					})
					->orWhere('kind', 'PVS');
				})
			);
	}
	

	public function table(Table $table): Table
    {
        return $table
            ->defaultGroup('kind')
            ->groupingSettingsHidden()
            ->groups([
                Group::make('kind')
                    ->collapsible()
                    ->titlePrefixedWithLabel(false)
                    ->getTitleFromRecordUsing(fn ($record) => match ($record->kind) {
                        'PVT' => 'Tanam',
                        'PVP' => 'Produksi',
                        'PVS' => 'SKL',
                        default => $record->kind, // Jika tidak cocok, gunakan nilai aslinya
                    }),
            ])
            ->columns([
                TextColumn::make('kind')
                    ->label('Verifikasi')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'PVT' => 'Tanam',
                        'PVP' => 'Produksi',
                        'PVS' => 'SKL',
                        default => $state, // Agar tidak error jika nilai tidak ada dalam daftar
                    })
                    ->color(fn (string $state): string => match ($state){
                        'PVT' => 'success',
                        'PVP' => 'warning',
                        'PVS' => 'danger',
                    })
                    ->searchable(),
                TextColumn::make('no_pengajuan')
                    ->searchable(),
                TextColumn::make('no_ijin')
                    ->searchable(),
                TextColumn::make('status')
                    ->badge()
                    ->color(function ($record, string $state): string {
						$colorMap = [
							'0' => 'warning',  // Baru
							'1' => 'info',     // Penugasan
							'2' => 'primary',  // Penetapan
							'3' => 'primary',  // Dimulai
							'4' => 'success',  // Selesai
							'5' => 'danger',   // Perbaikan
						];
					
						// Jika kind = PVS, gunakan warna khusus
						if ($record->kind === 'PVS') {
							$colorMap['4'] = 'warning';
							$colorMap += [
								'6' => 'danger',   // Ditolak
								'7' => 'primary',  // Disetujui
								'8' => 'success',  // Diterbitkan/Lunas
							];
						}
						return $colorMap[$state] ?? 'secondary';
					})					
					->formatStateUsing(function ($record, $state) {
						$statusMap = [
							'0' => 'Baru',
							'1' => 'Penugasan',
							'2' => 'Penetapan',
							'3' => 'Dimulai',
							'4' => 'Selesai',
							'5' => 'Perbaikan',
						];
						if ($record->kind === 'PVS') {
							$statusMap['4'] = 'Direkomendasikan';
							$statusMap += [
								'6' => 'Ditolak',
								'7' => 'Disetujui',
								'8' => 'Diterbitkan/Lunas'
							];
						}
						return $statusMap[$state] ?? 'Tidak Diketahui';
					}),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->label('Tanggal Pengajuan')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('kind')
                    ->label('Pengajuan Verifikasi')
                    ->options([
                        'PVT' => 'Tanam',
                        'PVP' => 'Produksi',
                        'PVS' => 'SKL',
                    ]),

                Filter::make('created_at')
                    ->label('Tanggal Pengajuan')
                    ->form([
                        DatePicker::make('start_date')
                            ->label('Dari Tanggal')
                            ->native(false),
                        DatePicker::make('end_date')
                            ->label('Sampai Tanggal')
                            ->native(false),
                    ])
                    ->query(function ($query, $data) {
                        $start = $data['start_date'] ?? null;
                        $end = $data['end_date'] ?? null;
            
                        if ($start && $end) {
                            $query->whereBetween('created_at', [$start, $end]);
                        } elseif ($start) {
                            $query->whereDate('created_at', '>=', $start);
                        } elseif ($end) {
                            $query->whereDate('created_at', '<=', $end);
                        }
                    })
            ])
            ->actions([
                // ViewAction::make()->hiddenLabel(),
				Action::make('verifikasiBerkas')
					->hiddenLabel()
					->visible(function ($record) {
						return Auth::user()->hasAnyRole(['verifikator']) 
							&& in_array($record->kind, ['PVT', 'PVP']) 
							&& in_array($record->status, ['2', '3']);
					})					
                    ->tooltip('Verifikasi Berkas Kelengkapan')
                    ->color(function ($record){
						if ($record->status === '4') {
							return 'success';
						}
						return 'danger';
					})
                    ->icon(function ($record){
						if ($record->status === '4') {
							return 'icon-file-check-fill';
						}
						return 'icon-file-check';
					})
                    ->action(function ($record) {
						if ($record->status === '4') {
							return;
						}
					
						if (in_array($record->kind, ['PVT', 'PVP']) && $record->status === '2') {
							$record->update(['status' => '3']);
						} elseif ($record->kind === 'PVS' && $record->status === '0') {
							$record->update(['status' => '3']);
						}
					
						return redirect()->route('filament.panel2025.resources.verifberkas.verifikasi', [
							'pengajuan' => $record->no_pengajuan,
							'kind'      => $record->kind,
						]);
					}),
				Action::make('verifikasiPKS')
					->hiddenLabel()
					->visible(function ($record) {
						return Auth::user()->hasAnyRole(['verifikator']) 
							&& in_array($record->kind, ['PVT', 'PVP']) 
							&& in_array($record->status, ['2', '3']);
					})
                    ->tooltip('Verifikasi Berkas PKS')
                    ->color(function ($record){
						if ($record->status === '4') {
							return 'success';
						}
						return 'danger';
					})
                    ->icon(function ($record){
						if ($record->status === '4') {
							return 'icon-people-fill';
						}
						return 'icon-people';
					})
                    ->action(function ($record) {
						if ($record->status === '4') {
							return;
						}
					
						if (in_array($record->kind, ['PVT', 'PVP']) && $record->status === '2') {
							$record->update(['status' => '3']);
						} elseif ($record->kind === 'PVS' && $record->status === '0') {
							$record->update(['status' => '3']);
						}
						$noijin = str_replace(['.', '-','/'], '', $record->no_ijin);				
						return redirect()->route('filament.panel2025.resources.pks2025s.verifpusat', [
							'noijin' => $noijin,
						]);
					}),
				Action::make('verifikasiLokasi')
					->hiddenLabel()
					->visible(function ($record) {
						return Auth::user()->hasAnyRole(['verifikator']) 
							&& in_array($record->kind, ['PVT', 'PVP']) 
							&& in_array($record->status, ['2', '3']);
					})
                    ->tooltip('Verifikasi Realisasi')
					->color(function ($record){
						if ($record->status === '4') {
							return 'success';
						}
						return 'danger';
					})
                    ->icon(function ($record){
						if ($record->status === '4') {
							return 'icon-geo-alt-fill';
						}
						return 'icon-geo-alt';
					})
                    ->action(function ($record) {
						if ($record->status === '4') {
							return;
						}
					
						if (in_array($record->kind, ['PVT', 'PVP']) && $record->status === '2') {
							$record->update(['status' => '3']);
						} elseif ($record->kind === 'PVS' && $record->status === '0') {
							$record->update(['status' => '3']);
						}
						return redirect()->route('filament.panel2025.resources.realisasi2025s.daftarlokasi', [
							'record' => $record->id,
							'pengajuan' => $record->no_pengajuan,
							'noijin' => preg_replace('/[\/.\-]/', '', $record->no_ijin)
						]);
					}),

                Action::make('finalProccess')
                    ->hiddenLabel()
					->visible(function ($record) {
						if (Auth::user()->hasAnyRole(['verifikator']) && in_array($record->kind, ['PVT', 'PVP'])) {
							return in_array($record->status, ['2', '3']);
						}
					
						if (Auth::user()->hasAnyRole(['admin', 'Super Admin']) && $record->kind === 'PVS') {
							return !in_array($record->status, ['4', '7', '8']);
						}
					
						return false;
					})					
                    ->tooltip(function ($record){
						if($record->kind === 'PVS')
						{
							return 'Proses Verifikasi';
						}
						return 'Akhir Proses Verifikasi';
					})
                    ->color('warning')
                    ->icon('icon-ui-checks')
                    ->requiresConfirmation()
					->modalHeading(function ($record){
						if($record->kind === 'PVS')
						{
							return 'Proses Verifikasi';
						}
						return 'Akhir Proses Verifikasi';
					})
					->modalDescription(function ($record){
						if($record->kind === 'PVS')
						{
							return 'Anda akan memproses pengajuan ini, lanjutkan?';
						}
						return 'Anda yakin keseluruhan proses verifikasi telah berakhir?';
					})
					->modalSubmitAction(fn (StaticAction $action) => $action->label('Ya'))
                    ->action(function ($record) {
						if ($record->status === '4') {
							return Notification::make()
									->title('Status Pengajuan Selesai')
									->body('Pengajuan ini telah berstatus SELESAI. Aksi tidak dapat dilanjutkan')
									->danger()
									->send();
						}
						if($record->kind === 'PVS' && $record->status === '0') {
							$record->update(['status' => '3']);
						}
						return redirect()->route('filament.panel2025.resources.pengajuan-verifikasis.verifikasi', ['record' => $record->id]);
					}),

                Action::make('verifikator_assignment')
                    ->label('Penunjukkan Verifikator')
                    ->hiddenLabel()
					->visible(fn($record) => Auth::user()->hasAnyRole(['admin', 'Super Admin']) && $record->kind !== 'PVS')
                    ->tooltip('Pilih/Tunjuk Verifikator')
                    ->color('warning')
                    ->icon('icon-person-fill-check')
                    ->requiresConfirmation()
					->modalHeading('Penunjukan Verifikator')
					->modalDescription('Anda akan memilih petugas verifikasi, lanjutkan?')
					->action(function ($record) {
						$statusUpdate = match ($record->kind) {
							'PVS' => $record->status === '0' ? '2' : $record->status,
							'PVT', 'PVP' => $record->status === '0' ? '1' : $record->status,
							default => $record->status,
						};
					
						if ($statusUpdate !== $record->status) {
							$record->update(['status' => $statusUpdate]);
						}
					
						return redirect()->route('filament.panel2025.resources.pengajuan-verifikasis.assignment', ['record' => $record->id]);
					}),					
				Action::make('viewReport')
					->hiddenLabel()
					->tooltip('Lihat Hasil Verifikasi')
					->icon('icon-binoculars-fill')
					->url(fn ($record) => route('filament.panel2025.resources.pengajuan-verifikasis.verifReport', ['record' => $record->id])),
                // EditAction::make()->visible($isSU)->icon('icon-person-fill-check')->hiddenLabel(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    // DeleteBulkAction::make(),
                ]),
            ]);
    }
}
