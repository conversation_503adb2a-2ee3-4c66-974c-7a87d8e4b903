<?php

namespace App\Filament\Admin\Resources\UserResource\Pages;

use App\Filament\Admin\Resources\UserResource;
use App\Models\DataAdministrator;
use App\Models\MasterKabupaten;
use App\Models\MasterProvinsi;
use App\Models\SupportDepartement;
use App\Models\User;
use Filament\Actions;
use Filament\Forms\Components\{Group, Hidden, Radio, Section, Select, TextInput, Toggle};
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;

class EditUser extends EditRecord
{
	protected static string $resource = UserResource::class;

	protected function getHeaderActions(): array
	{
		return [
			// Actions\ViewAction::make(),
			// Actions\DeleteAction::make(),
		];
	}

	public function getHeading(): string
	{
		$status = $this->record->status;
		$thisTitle = 'Data Pengguna';
		if($status === 'Baru') {
			$thisTitle = 'Pengguna Baru';
		}
        return $thisTitle;
	}
	
    protected static ?string $title = 'Data Pengguna';

    public function getSubheading(): ?string
    {
		$subTitle = '';
		$status = $this->record->status;
		if($status === 'Baru') {
			$subTitle = 'Persetujuan akses bagi pengguna baru';
		}
        return $subTitle;
    }

	protected function getRedirectUrl(): string
	{
		return $this->getResource()::getUrl('index');
	}

	public function form(Form $form): Form
	{
		return $form
			->schema([
				Section::make('Approval')
					->aside()
					->visible(fn ($record) => $record->status === 'Baru')
					->description('Pengguna Baru. Lakukan verifikasi dan berikan status pengguna ini')
					->schema([
						Radio::make('status')
							->hiddenLabel()
							->inline()
							->required()
							->inlineLabel(false)
							->options([
								'Baru' => 'Tunda',
								'Aktif' => 'Diterima',
								'Ditolak' => 'Ditolak',
							])
							->descriptions([
								'Baru' => 'Jika Anda tidak akan melakukan pemeriksaan dan persetujuan saat ini.',
								'Aktif' => 'Pengguna ini diijinkan mengakses aplikasi sesuai peran yang diberikan.',
								'Ditolak' => (new HtmlString('Pengguna ini <span class="font-bold">TIDAK DIIJINKAN</span> mengakses aplikasi serta <span class="text-danger-500 font-bold">data terkait akan DIHAPUS!</span>.')),
							])
							->reactive()
							->live()
							->afterStateUpdated(function ($state, callable $set, $record) {
								if ($state === 'Baru' || $state === 'Ditolak') {
									$set('email_verified_at', null);
									return;
								}
								$set('email_verified_at', now());
							}),
						Hidden::make('email_verified_at')->reactive(),


					]),

				Section::make('Peran Pengguna')
					->aside()
					->description('Pengguna ini diijinkan mengakses aplikasi sesuai peran yang diberikan.')
					->schema([
						Select::make('roles')
							->hiddenLabel()
							->required()
							->reactive()
							->relationship('roles', 'name')
							->preload(),
						
						Select::make('departement_id')
							->label('Support Departement')
							->inlineLabel()
							->multiple()
							->reactive()
							->options(fn () => SupportDepartement::pluck('name', 'id'))
							->visible(fn ($get) => $get('roles') === '9')
					]),

				Section::make('Akun Pengguna')
					->aside()
					->description('Data Akun Pengguna. username dan email tidak dapat diubah.')
					->schema([
						TextInput::make('name')
							->required()
							->inlineLabel()
							->maxLength(255),
						TextInput::make('username')
							->required()
							->readOnly()
							->inlineLabel()
							->maxLength(255),
						TextInput::make('email')
							->email()
							->inlineLabel()
							->readOnly()
							->required()
							->maxLength(255),
					]),

				Section::make('Katakunci')
					->aside()
					->description('Ubah/perbarui katakunci pengguna ini.')
					->schema([
						Toggle::make('visiblePass')
							->label('Tampilkan Kolom Password?')
							->onIcon('heroicon-m-bolt')
							->offIcon('heroicon-m-user')
							->columnSpanFull()
							->reactive(),

						TextInput::make('password')
							->password()
							->required()
							->reactive()
							->revealable()
							->visible(fn ($get)=>$get('visiblePass') === true)
							->maxLength(255),
					]),

				Section::make('Profil')
					->aside()
					->description('Data Profil')
					->relationship('dataadmin')
					->schema([
						TextInput::make('nama_dinas')
							->readOnly(fn () => Auth::id() !== $this->record->id),
						TextInput::make('jabatan')
							->readOnly(fn () => Auth::id() !== $this->record->id),
						TextInput::make('nip')
							->readOnly(fn () => Auth::id() !== $this->record->id),
						TextInput::make('jabatan')
							->readOnly(fn () => Auth::id() !== $this->record->id),
						TextInput::make('nip')
							->readOnly(fn () => Auth::id() !== $this->record->id),
						TextInput::make('mobile_number')
							->label('Nomor HP')
							->readOnly(fn () => Auth::id() !== $this->record->id),
						Select::make('provinsi_id')
							->searchable()
							->options(function () {
								return MasterProvinsi::pluck('nama', 'provinsi_id')->toArray();
							})
							->reactive()
							->afterStateUpdated(fn ($set) => $set('kabupaten_id', null)),
						Select::make('kabupaten_id')
							->hidden(fn ($get)=>$get('role_id') == 4)
							->searchable()
							->options(function ($get) {
								$provinsiId = $get('provinsi_id');
								if (!$provinsiId) {
									return [];
								}
								return MasterKabupaten::where('provinsi_id', $provinsiId)
									->pluck('nama_kab', 'kabupaten_id')
									->toArray();
							})
							->reactive(),
					])
			]);
	}

	public function mutateFormDataBeforeSave(array $data): array
	{
		if (!array_key_exists('status', $data)) {
			return $data;
		}

		$user = User::where('email', $data['email'])->first();


		if ($data['status'] === 'Ditolak' && $user) {
			$user->status = $data['status'];
			$user->delete();
		}

		return $data;
	}

}
