<?php

namespace App\Filament\Admin\Resources\MasterAnggotaResource\Pages;

use App\Filament\Admin\Resources\MasterAnggotaResource;
use App\Models\MasterAnggota;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class ListMasterAnggotas extends ListRecords
{
    protected static string $resource = MasterAnggotaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
	
	protected function getTableQuery(): Builder
	{
		$user = Auth::user();
		return $user->hasRole('dinas') 
			? MasterAnggota::query()->where('kabupaten_id', Auth::user()->dataadmin->kabupaten_id)
			: MasterAnggota::query();
	}
}
