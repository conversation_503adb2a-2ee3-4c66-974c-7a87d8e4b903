<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use App\Models\MasterProvinsi;
use App\Models\MasterKabupaten;
use App\Models\MasterKecamatan;
use App\Models\MasterDesa;
use Filament\Notifications\Notification;
use Illuminate\Bus\Batchable;
use Illuminate\Support\Facades\Log;

class ProcessWilayahJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    public function __construct() {}

	public function handle()
	{
		Log::info('Start Process Data');
	
		// ================= 1. Ambil Semua Data Sebelum Update =================
		$existingProvinsiIds = MasterProvinsi::pluck('provinsi_id')->toArray();
		$existingKabupatenIds = MasterKabupaten::pluck('kabupaten_id')->toArray();
		$existingKecamatanIds = MasterKecamatan::pluck('kecamatan_id')->toArray();
		$existingDesaIds = MasterDesa::pluck('kelurahan_id')->toArray();
	
		$newProvinsiIds = [];
		$newKabupatenIds = [];
		$newKecamatanIds = [];
		$newDesaIds = [];
	
		// ================= 2. Proses Update Provinsi =================
		$provinsi = json_decode(Storage::get('sig-wilayah/provinsi.json'), true);
		foreach ($provinsi as $p) {
			MasterProvinsi::updateOrCreate(
				['provinsi_id' => $p['kode']],
				['nama' => $p['nama'], 'status' => 'aktif']
			);
			$newProvinsiIds[] = $p['kode'];
			Log::info("Process Data Provinsi {$p['kode']} berhasil");
	
			// ================= 3. Proses Update Kabupaten =================
			$kabupatenFile = "sig-wilayah/kabupaten_{$p['kode']}.json";
			if (Storage::exists($kabupatenFile)) {
				$kabupaten = json_decode(Storage::get($kabupatenFile), true);
				foreach ($kabupaten as $k) {
					MasterKabupaten::updateOrCreate(
						['kabupaten_id' => $k['kode_bps']],
						['provinsi_id' => $p['kode'], 'kode_dagri' => $k['kode_dagri'], 'nama_kab' => $k['nama_bps'], 'status' => 'aktif']
					);
					$newKabupatenIds[] = $k['kode_bps'];
					Log::info("Process Data Kabupaten {$k['kode_bps']} berhasil");
	
					// ================= 4. Proses Update Kecamatan =================
					$kecamatanFile = "sig-wilayah/kecamatan_{$k['kode_bps']}.json";
					if (Storage::exists($kecamatanFile)) {
						$kecamatan = json_decode(Storage::get($kecamatanFile), true);
						foreach ($kecamatan as $c) {
							MasterKecamatan::updateOrCreate(
								['kecamatan_id' => $c['kode_bps']],
								['kabupaten_id' => $k['kode_bps'], 'kode_dagri' => $c['kode_dagri'], 'nama_kecamatan' => $c['nama_bps'], 'status' => 'aktif']
							);
							$newKecamatanIds[] = $c['kode_bps'];
							Log::info("Process Data Kecamatan {$c['kode_bps']} berhasil");
	
							// ================= 5. Proses Update Desa =================
							$desaFile = "sig-wilayah/desa_{$c['kode_bps']}.json";
							if (Storage::exists($desaFile)) {
								$desa = json_decode(Storage::get($desaFile), true);
								foreach ($desa as $d) {
									MasterDesa::updateOrCreate(
										['kelurahan_id' => $d['kode_bps']],
										['kecamatan_id' => $c['kode_bps'], 'kode_dagri' => $d['kode_dagri'], 'nama_desa' => $d['nama_bps'], 'status' => 'aktif']
									);
									$newDesaIds[] = $d['kode_bps'];
								}
								Log::info("Process Data Desa {$c['kode_bps']} berhasil");
							}
						}
					}
				}
			}
		}
	
		// ================= 6. Nonaktifkan Data yang Sudah Tidak Ada =================
		$this->nonaktifkanWilayah($existingProvinsiIds, $newProvinsiIds, MasterProvinsi::class, 'provinsi_id');
		$this->nonaktifkanWilayah($existingKabupatenIds, $newKabupatenIds, MasterKabupaten::class, 'kabupaten_id');
		$this->nonaktifkanWilayah($existingKecamatanIds, $newKecamatanIds, MasterKecamatan::class, 'kecamatan_id');
		$this->nonaktifkanWilayah($existingDesaIds, $newDesaIds, MasterDesa::class, 'kelurahan_id');
	}
	
	/**
	 * Fungsi untuk menonaktifkan data wilayah yang sudah tidak ada
	 */
	private function nonaktifkanWilayah($existingIds, $newIds, $model, $column)
	{
		$deletedIds = array_diff($existingIds, $newIds);
		if (!empty($deletedIds)) {
			$model::whereIn($column, $deletedIds)->update(['status' => 'nonaktif']);
			Log::info(count($deletedIds) . " wilayah di {$model} dinonaktifkan.");
		}
	}
	
}
