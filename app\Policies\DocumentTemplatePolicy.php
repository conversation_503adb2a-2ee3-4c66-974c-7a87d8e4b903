<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\DocumentTemplate;
use App\Models\User;

class DocumentTemplatePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any DocumentTemplate');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, DocumentTemplate $documenttemplate): bool
    {
        return $user->checkPermissionTo('view DocumentTemplate');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create DocumentTemplate');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, DocumentTemplate $documenttemplate): bool
    {
        return $user->checkPermissionTo('update DocumentTemplate');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, DocumentTemplate $documenttemplate): bool
    {
        return $user->checkPermissionTo('delete DocumentTemplate');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any DocumentTemplate');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, DocumentTemplate $documenttemplate): bool
    {
        return $user->checkPermissionTo('restore DocumentTemplate');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any DocumentTemplate');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, DocumentTemplate $documenttemplate): bool
    {
        return $user->checkPermissionTo('replicate DocumentTemplate');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder DocumentTemplate');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, DocumentTemplate $documenttemplate): bool
    {
        return $user->checkPermissionTo('force-delete DocumentTemplate');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any DocumentTemplate');
    }
}
