<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\PengajuansklResource\Pages;
use App\Filament\Admin\Resources\PengajuansklResource\RelationManagers;
use App\Models\Completed;
use App\Models\Pengajuanskl2025;
use App\Models\PengajuanVerifikasi;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

class PengajuansklResource extends Resource
{
    protected static ?string $model = Pengajuanskl2025::class;
	public static function getNavigationGroup(): ?string
	{
		return 'SKL';
	}
	protected static ?string $title = 'Pengajuan Penerbitan SKL';

    protected static ?string $navigationIcon = 'icon-award-v4';
    protected static ?string $navigationLabel = 'Pengajuan SKL V4';
	protected static ?int $navigationSort = 1;
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPengajuanskl::route('/'),
            'create' => Pages\CreatePengajuanskl::route('/create'),
            'sklapproval' => Pages\SklApproval2025::route('/{record}/approval'),
            'sklapprovalview' => Pages\SklApproval2025View::route('/{record}/approval-view'),
            'skl2025upload' => Pages\Skl2025Upload::route('/{record}/Skl2025Upload'),
        ];
    }

	public static function getNavigationBadge(): ?string
	{
		return static::getModel()::where('status', '0')->count() > 0 ? (string) static::getModel()::where('status', '0')->count() : null;
	}

	public static function getNavigationBadgeColor(): ?string
	{
		return static::getModel()::count() == 0 ? 'warning' : null;
	}

	public static function shouldRegisterNavigation(): bool
    {
        $user = Auth::user();
        if ($user->hasAnyRole(['admin','Super Admin', 'direktur'])) {
			return true;
        }else{
        	return false;
        }
    }
}
