<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\DataAdministrator;
use App\Models\User;

class DataAdministratorPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any DataAdministrator');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, DataAdministrator $dataadministrator): bool
    {
        return $user->checkPermissionTo('view DataAdministrator');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create DataAdministrator');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, DataAdministrator $dataadministrator): bool
    {
        return $user->checkPermissionTo('update DataAdministrator');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, DataAdministrator $dataadministrator): bool
    {
        return $user->checkPermissionTo('delete DataAdministrator');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any DataAdministrator');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, DataAdministrator $dataadministrator): bool
    {
        return $user->checkPermissionTo('restore DataAdministrator');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any DataAdministrator');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, DataAdministrator $dataadministrator): bool
    {
        return $user->checkPermissionTo('replicate DataAdministrator');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder DataAdministrator');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, DataAdministrator $dataadministrator): bool
    {
        return $user->checkPermissionTo('force-delete DataAdministrator');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any DataAdministrator');
    }
}
