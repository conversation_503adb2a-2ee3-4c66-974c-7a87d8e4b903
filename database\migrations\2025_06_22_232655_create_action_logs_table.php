<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('action_logs', function (Blueprint $table) {
			$table->id();
			$table->string('log_type');
			$table->string('model_type');
			$table->unsignedBigInteger('model_id');
			$table->string('npwp');
			$table->string('no_ijin');
			$table->unsignedBigInteger('request_by');
			$table->json('data');
			$table->json('metadata')->nullable();
			$table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rollback_logs');
    }
};
