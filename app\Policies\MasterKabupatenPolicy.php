<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\MasterKabupaten;
use App\Models\User;

class MasterKabupatenPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any MasterKabupaten');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, MasterKabupaten $masterkabupaten): bool
    {
        return $user->checkPermissionTo('view MasterKabupaten');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create MasterKabupaten');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, MasterKabupaten $masterkabupaten): bool
    {
        return $user->checkPermissionTo('update MasterKabupaten');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, MasterKabupaten $masterkabupaten): bool
    {
        return $user->checkPermissionTo('delete MasterKabupaten');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any MasterKabupaten');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, MasterKabupaten $masterkabupaten): bool
    {
        return $user->checkPermissionTo('restore MasterKabupaten');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any MasterKabupaten');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, MasterKabupaten $masterkabupaten): bool
    {
        return $user->checkPermissionTo('replicate MasterKabupaten');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder MasterKabupaten');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, MasterKabupaten $masterkabupaten): bool
    {
        return $user->checkPermissionTo('force-delete MasterKabupaten');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any MasterKabupaten');
    }
}
