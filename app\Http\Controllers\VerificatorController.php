<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\PengajuanVerifikasi;
use App\Models\VerificatorAssignment;
use App\Models\User;
use App\Models\Userfile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class VerificatorController extends Controller
{
    /**
     * Get assigned verifications for the logged-in verifikator
     */
    public function getAssignedVerifications()
    {
        $user = Auth::user();

        // Check if user is verifikator
        if (!$user->hasRole('verifikator')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access. This endpoint is only for verifikator role.'
            ], 403);
        }

        // Get verifications assigned to the verifikator
        $assignments = VerificatorAssignment::where('user_id', $user->id)
            ->with('pengajuan')
            ->get();

        $verifications = [];
        foreach ($assignments as $assignment) {
            if ($assignment->pengajuan) {
                $verification = $assignment->pengajuan;

                // Map kind to readable text
                $kindMap = [
                    'PVT' => 'Verifikasi Tanam',
                    'PVP' => 'Verifikasi Produksi',
                    'PVS' => 'Penerbitan SKL',
                ];

                // Map status to readable text based on kind
                $statusText = $this->getStatusText($verification->kind, $verification->status);

                // Calculate progress based on status
                $progress = $this->calculateProgress($verification->kind, $verification->status);

                // Get company name if available
                $company = null;
                if ($verification->user && $verification->user->datauser) {
                    $company = $verification->user->datauser->company_name;
                }

                $verifications[] = [
                    'id' => $verification->id,
                    'no_pengajuan' => $verification->no_pengajuan,
                    'no_ijin' => $verification->no_ijin,
                    'kind' => $verification->kind,
                    'kind_text' => $kindMap[$verification->kind] ?? $verification->kind,
                    'status' => $verification->status,
                    'status_text' => $statusText,
                    'progress' => $progress,
                    'created_at' => $verification->created_at,
                    'created_at_formatted' => $verification->created_at->format('d M Y'),
                    'verifikator' => $user->name,
                    'note' => $verification->note,
                    'company' => $company,
                    'pprk' => $verification->no_ijin,
                ];
            }
        }

        return response()->json([
            'success' => true,
            'data' => $verifications
        ]);
    }

    /**
     * Get verification detail by ID
     */
    public function getVerificationDetail($id)
    {
        $user = Auth::user();

        // Check if user is verifikator
        if (!$user->hasRole('verifikator')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access. This endpoint is only for verifikator role.'
            ], 403);
        }

        // Check if the verification is assigned to this verifikator
        $assignment = VerificatorAssignment::where('user_id', $user->id)
            ->where('pengajuan_id', $id)
            ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found or not assigned to you'
            ], 404);
        }

        // Get verification detail
        $verification = PengajuanVerifikasi::find($id);

        if (!$verification) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found'
            ], 404);
        }

        // Map kind to readable text
        $kindMap = [
            'PVT' => 'Verifikasi Tanam',
            'PVP' => 'Verifikasi Produksi',
            'PVS' => 'Penerbitan SKL',
        ];

        // Map status to readable text based on kind
        $statusText = $this->getStatusText($verification->kind, $verification->status);

        // Calculate progress based on status
        $progress = $this->calculateProgress($verification->kind, $verification->status);

        // Get company name if available
        $company = null;
        if ($verification->user && $verification->user->datauser) {
            $company = $verification->user->datauser->company_name;
        }

        // Format assignment data
        $assignmentData = [
            'id' => $assignment->id,
            'verifikator_id' => $user->id,
            'verifikator_name' => $user->name,
            'no_sk' => $assignment->no_sk,
            'tgl_sk' => $assignment->tgl_sk,
            'tgl_sk_formatted' => $assignment->tgl_sk ? date('d M Y', strtotime($assignment->tgl_sk)) : null,
            'note' => $assignment->note,
        ];

        // Format files data if available
        $filesData = null;
        if ($verification->files) {
            $filesData = [
                'id' => $verification->files->id,
                'spvt' => $verification->files->spvt,
                'sptjm' => $verification->files->sptjm,
                'rta' => $verification->files->rta,
                'sph' => $verification->files->sph,
                'logbook' => $verification->files->logbook,
                // Add more files as needed
            ];
        }

        $detailData = [
            'id' => $verification->id,
            'no_pengajuan' => $verification->no_pengajuan,
            'no_ijin' => $verification->no_ijin,
            'kind' => $verification->kind,
            'kind_text' => $kindMap[$verification->kind] ?? $verification->kind,
            'status' => $verification->status,
            'status_text' => $statusText,
            'progress' => $progress,
            'created_at' => $verification->created_at,
            'created_at_formatted' => $verification->created_at->format('d M Y'),
            'note' => $verification->note,
            'verif_at' => $verification->verif_at,
            'verif_at_formatted' => $verification->verif_at ? date('d M Y', strtotime($verification->verif_at)) : null,
            'metode' => $verification->metode,
            'assignment' => $assignmentData,
            'files' => $filesData,
        ];

        return response()->json([
            'success' => true,
            'data' => $detailData
        ]);
    }

    /**
     * Helper function to get status text based on kind and status
     */
    private function getStatusText($kind, $status)
    {
        if ($kind == 'PVS') {
            switch ($status) {
                case '0': return 'Baru';
                case '1': return 'Penugasan';
                case '2': return 'Penetapan';
                case '3': return 'Dimulai';
                case '4': return 'Direkomendasikan';
                case '5': return 'Perbaikan';
                case '6': return 'Ditolak';
                case '7': return 'Disetujui';
                case '8': return 'Diterbitkan/Lunas';
                default: return 'Tidak Diketahui';
            }
        } else {
            switch ($status) {
                case '0': return 'Baru';
                case '1': return 'Penugasan';
                case '2': return 'Penetapan';
                case '3': return 'Dimulai';
                case '4': return 'Selesai';
                case '5': return 'Perbaikan';
                default: return 'Tidak Diketahui';
            }
        }
    }

    /**
     * Helper function to calculate progress based on kind and status
     */
    private function calculateProgress($kind, $status)
    {
        if ($kind == 'PVS') {
            switch ($status) {
                case '0': return 10;
                case '1': return 20;
                case '2': return 30;
                case '3': return 40;
                case '4': return 60;
                case '5': return 50;
                case '6': return 70;
                case '7': return 80;
                case '8': return 100;
                default: return 0;
            }
        } else {
            switch ($status) {
                case '0': return 10;
                case '1': return 30;
                case '2': return 50;
                case '3': return 70;
                case '4': return 100;
                case '5': return 60;
                default: return 0;
            }
        }
    }

    /**
     * Get verification documents to be verified
     */
    public function getVerificationDocuments($id)
    {
        $user = Auth::user();

        // Check if user is verifikator
        if (!$user->hasRole('verifikator')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access. This endpoint is only for verifikator role.'
            ], 403);
        }

        // Check if the verification is assigned to this verifikator
        $assignment = VerificatorAssignment::where('user_id', $user->id)
            ->where('pengajuan_id', $id)
            ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found or not assigned to you'
            ], 404);
        }

        // Get verification detail
        $verification = PengajuanVerifikasi::find($id);

        if (!$verification) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found'
            ], 404);
        }

        // Get documents based on verification kind
        $documents = [];

        if ($verification->kind == 'PVT') {
            // Documents for Verifikasi Tanam
            $documents = [
                [
                    'id' => 'spvt',
                    'name' => 'Surat Pengajuan Verifikasi Tanam',
                    'file_url' => $verification->files && $verification->files->spvt ?
                        url('/uploads/' . str_replace(['.', '-'], '', $verification->npwp) . '/' . $verification->periodetahun . '/' . $verification->files->spvt) : null,
                    'status' => null,
                    'required' => true,
                ],
                [
                    'id' => 'sptjm',
                    'name' => 'Surat Pertanggungjawaban Mutlak',
                    'file_url' => $verification->files && $verification->files->sptjm ?
                        url('/uploads/' . str_replace(['.', '-'], '', $verification->npwp) . '/' . $verification->periodetahun . '/' . $verification->files->sptjm) : null,
                    'status' => null,
                    'required' => true,
                ],
                [
                    'id' => 'rta',
                    'name' => 'Form Realisasi Tanam',
                    'file_url' => $verification->files && $verification->files->rta ?
                        url('/uploads/' . str_replace(['.', '-'], '', $verification->npwp) . '/' . $verification->periodetahun . '/' . $verification->files->rta) : null,
                    'status' => null,
                    'required' => true,
                ],
                [
                    'id' => 'sph',
                    'name' => 'Surat Pernyataan Hasil',
                    'file_url' => $verification->files && $verification->files->sph ?
                        url('/uploads/' . str_replace(['.', '-'], '', $verification->npwp) . '/' . $verification->periodetahun . '/' . $verification->files->sph) : null,
                    'status' => null,
                    'required' => true,
                ],
                [
                    'id' => 'logbook',
                    'name' => 'Logbook Tanam',
                    'file_url' => $verification->files && $verification->files->logbook ?
                        url('/uploads/' . str_replace(['.', '-'], '', $verification->npwp) . '/' . $verification->periodetahun . '/' . $verification->files->logbook) : null,
                    'status' => null,
                    'required' => true,
                ],
            ];
        } else if ($verification->kind == 'PVP') {
            // Documents for Verifikasi Produksi
            $documents = [
                [
                    'id' => 'spvp',
                    'name' => 'Surat Pengajuan Verifikasi Produksi',
                    'file_url' => $verification->files && $verification->files->spvp ?
                        url('/uploads/' . str_replace(['.', '-'], '', $verification->npwp) . '/' . $verification->periodetahun . '/' . $verification->files->spvp) : null,
                    'status' => null,
                    'required' => true,
                ],
                [
                    'id' => 'sptjm_produksi',
                    'name' => 'Surat Pertanggungjawaban Mutlak Produksi',
                    'file_url' => $verification->files && $verification->files->sptjm_produksi ?
                        url('/uploads/' . str_replace(['.', '-'], '', $verification->npwp) . '/' . $verification->periodetahun . '/' . $verification->files->sptjm_produksi) : null,
                    'status' => null,
                    'required' => true,
                ],
                [
                    'id' => 'rpo',
                    'name' => 'Form Realisasi Produksi',
                    'file_url' => $verification->files && $verification->files->rpo ?
                        url('/uploads/' . str_replace(['.', '-'], '', $verification->npwp) . '/' . $verification->periodetahun . '/' . $verification->files->rpo) : null,
                    'status' => null,
                    'required' => true,
                ],
                [
                    'id' => 'sph_produksi',
                    'name' => 'Surat Pernyataan Hasil Produksi',
                    'file_url' => $verification->files && $verification->files->sph_produksi ?
                        url('/uploads/' . str_replace(['.', '-'], '', $verification->npwp) . '/' . $verification->periodetahun . '/' . $verification->files->sph_produksi) : null,
                    'status' => null,
                    'required' => true,
                ],
                [
                    'id' => 'logbook_produksi',
                    'name' => 'Logbook Produksi',
                    'file_url' => $verification->files && $verification->files->logbook_produksi ?
                        url('/uploads/' . str_replace(['.', '-'], '', $verification->npwp) . '/' . $verification->periodetahun . '/' . $verification->files->logbook_produksi) : null,
                    'status' => null,
                    'required' => true,
                ],
                [
                    'id' => 'form_la',
                    'name' => 'Form LA',
                    'file_url' => $verification->files && $verification->files->form_la ?
                        url('/uploads/' . str_replace(['.', '-'], '', $verification->npwp) . '/' . $verification->periodetahun . '/' . $verification->files->form_la) : null,
                    'status' => null,
                    'required' => true,
                ],
            ];
        }

        return response()->json([
            'success' => true,
            'data' => [
                'verification_id' => $verification->id,
                'no_pengajuan' => $verification->no_pengajuan,
                'kind' => $verification->kind,
                'documents' => $documents
            ]
        ]);
    }

    /**
     * Get locations to be verified for a verification
     */
    public function getVerificationLocations($id)
    {
        $user = Auth::user();

        // Check if user is verifikator
        if (!$user->hasRole('verifikator')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access. This endpoint is only for verifikator role.'
            ], 403);
        }

        // Check if the verification is assigned to this verifikator
        $assignment = VerificatorAssignment::where('user_id', $user->id)
            ->where('pengajuan_id', $id)
            ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found or not assigned to you'
            ], 404);
        }

        // Get verification detail
        $verification = PengajuanVerifikasi::find($id);

        if (!$verification) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found'
            ], 404);
        }

        // Get locations based on verification no_ijin
        // This is a simplified implementation - you'll need to adjust based on your actual data model
        $locations = \App\Models\Realisasi2025::where('no_ijin', $verification->no_ijin)
            ->with(['masterSpatial', 'pks'])
            ->get()
            ->map(function ($realisasi) {
                $spatial = $realisasi->masterSpatial;
                $pks = $realisasi->pks;

                return [
                    'id' => $realisasi->id,
                    'kode_spatial' => $realisasi->kode_spatial,
                    'luas_lahan' => $realisasi->luas_lahan,
                    'periode_tanam' => $realisasi->periode_tanam,
                    'status' => $realisasi->status,
                    'latitude' => $spatial ? $spatial->latitude : null,
                    'longitude' => $spatial ? $spatial->longitude : null,
                    'polygon' => $spatial ? $spatial->polygon : null,
                    'altitude' => $spatial ? $spatial->altitude : null,
                    'provinsi' => $spatial ? $spatial->provinsi : null,
                    'kabupaten' => $spatial ? $spatial->kabupaten : null,
                    'kecamatan' => $spatial ? $spatial->kecamatan : null,
                    'desa' => $spatial ? $spatial->desa : null,
                    'poktan' => $pks ? $pks->poktan_name : null,
                    'verification_status' => null, // This would be populated from your verification data
                ];
            });

        return response()->json([
            'success' => true,
            'data' => [
                'verification_id' => $verification->id,
                'no_pengajuan' => $verification->no_pengajuan,
                'no_ijin' => $verification->no_ijin,
                'kind' => $verification->kind,
                'locations' => $locations
            ]
        ]);
    }

    /**
     * Get detail of a specific location to be verified
     */
    public function getLocationDetail($verificationId, $locationId)
    {
        $user = Auth::user();

        // Check if user is verifikator
        if (!$user->hasRole('verifikator')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access. This endpoint is only for verifikator role.'
            ], 403);
        }

        // Check if the verification is assigned to this verifikator
        $assignment = VerificatorAssignment::where('user_id', $user->id)
            ->where('pengajuan_id', $verificationId)
            ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found or not assigned to you'
            ], 404);
        }

        // Get verification detail
        $verification = PengajuanVerifikasi::find($verificationId);

        if (!$verification) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found'
            ], 404);
        }

        // Get location detail
        $realisasi = \App\Models\Realisasi2025::find($locationId);

        if (!$realisasi || $realisasi->no_ijin !== $verification->no_ijin) {
            return response()->json([
                'success' => false,
                'message' => 'Location not found or not associated with this verification'
            ], 404);
        }

        // Load related data
        $realisasi->load(['masterSpatial', 'pks', 'pks.poktan', 'detailRealisasi']);

        // Get spatial data
        $spatial = $realisasi->masterSpatial;

        // Get PKS data
        $pks = $realisasi->pks;

        // Get poktan data
        $poktan = $pks ? $pks->poktan : null;

        // Get detail realisasi (activities)
        $activities = $realisasi->detailRealisasi->map(function ($detail) {
            return [
                'id' => $detail->id,
                'tahap' => $detail->tahap,
                'tanggal' => $detail->tanggal,
                'tanggal_formatted' => $detail->tanggal ? date('d M Y', strtotime($detail->tanggal)) : null,
                'description' => $detail->description,
                'status' => $detail->status,
                'photos' => $detail->photos ? json_decode($detail->photos) : [],
            ];
        });

        // Format polygon data if available
        $polygonData = null;
        if ($spatial && $spatial->polygon) {
            try {
                $polygonData = json_decode($spatial->polygon);
            } catch (\Exception $e) {
                $polygonData = null;
            }
        }

        // Prepare location detail data
        $locationDetail = [
            'id' => $realisasi->id,
            'kode_spatial' => $realisasi->kode_spatial,
            'no_ijin' => $realisasi->no_ijin,
            'luas_lahan' => $realisasi->luas_lahan,
            'periode_tanam' => $realisasi->periode_tanam,
            'status' => $realisasi->status,
            'created_at' => $realisasi->created_at,
            'created_at_formatted' => $realisasi->created_at ? date('d M Y', strtotime($realisasi->created_at)) : null,
            'spatial' => $spatial ? [
                'id' => $spatial->id,
                'latitude' => $spatial->latitude,
                'longitude' => $spatial->longitude,
                'polygon' => $polygonData,
                'altitude' => $spatial->altitude,
                'provinsi' => $spatial->provinsi,
                'kabupaten' => $spatial->kabupaten,
                'kecamatan' => $spatial->kecamatan,
                'desa' => $spatial->desa,
                'address' => $spatial->address,
            ] : null,
            'pks' => $pks ? [
                'id' => $pks->id,
                'no_perjanjian' => $pks->no_perjanjian,
                'tanggal_perjanjian' => $pks->tanggal_perjanjian,
                'tanggal_perjanjian_formatted' => $pks->tanggal_perjanjian ? date('d M Y', strtotime($pks->tanggal_perjanjian)) : null,
                'luas_rencana' => $pks->luas_rencana,
                'varietas' => $pks->varietas,
                'status' => $pks->status,
            ] : null,
            'poktan' => $poktan ? [
                'id' => $poktan->id,
                'poktan_name' => $poktan->poktan_name,
                'ketua_poktan' => $poktan->ketua_poktan,
                'hp_poktan' => $poktan->hp_poktan,
                'anggota_count' => $poktan->anggota_count,
            ] : null,
            'activities' => $activities,
            'verification_status' => null, // This would be populated from your verification data
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'verification_id' => $verification->id,
                'no_pengajuan' => $verification->no_pengajuan,
                'no_ijin' => $verification->no_ijin,
                'kind' => $verification->kind,
                'location' => $locationDetail
            ]
        ]);
    }

    /**
     * Submit document verification results
     */
    public function submitDocumentVerification(Request $request, $verificationId)
    {
        $user = Auth::user();

        // Check if user is verifikator
        if (!$user->hasRole('verifikator')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access. This endpoint is only for verifikator role.'
            ], 403);
        }

        // Validate request
        $validator = Validator::make($request->all(), [
            'documents' => 'required|array',
            'documents.*.id' => 'required|string',
            'documents.*.status' => 'required|in:sesuai,perbaiki',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if the verification is assigned to this verifikator
        $assignment = VerificatorAssignment::where('user_id', $user->id)
            ->where('pengajuan_id', $verificationId)
            ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found or not assigned to you'
            ], 404);
        }

        // Get verification detail
        $verification = PengajuanVerifikasi::find($verificationId);

        if (!$verification) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found'
            ], 404);
        }

        // Start transaction
        DB::beginTransaction();

        try {
            // Process document verification results
            $documents = $request->input('documents');

            foreach ($documents as $document) {
                $docId = $document['id'];
                $status = $document['status'];

                // Find the corresponding file record
                $userfile = Userfile::where('no_ijin', $verification->no_ijin)
                    ->where('kind', $docId)
                    ->first();

                if ($userfile) {
                    // Update the file status
                    $userfile->status = $status === 'sesuai' ? 'approved' : 'rejected';
                    $userfile->verif_by = $user->id;
                    $userfile->verif_at = Carbon::now();
                    $userfile->save();
                }
            }

            // Update verification status if needed
            if ($verification->status == '1') {
                $verification->status = '2'; // Update to "Penetapan" status
                $verification->save();
            }

            // Commit transaction
            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Document verification results submitted successfully',
                'data' => [
                    'verification_id' => $verification->id,
                    'no_pengajuan' => $verification->no_pengajuan,
                    'status' => $verification->status,
                    'status_text' => $this->getStatusText($verification->kind, $verification->status),
                ]
            ]);
        } catch (\Exception $e) {
            // Rollback transaction on error
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to submit document verification results',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
