<?php

namespace App\Filament\Admin\Resources\MasterProvinsiResource\Pages;

use App\Filament\Admin\Resources\MasterProvinsiResource;
use App\Jobs\FetchWilayahJob;
use App\Jobs\ProcessWilayahJob;
use App\Models\MasterDesa;
use App\Models\MasterKabupaten;
use App\Models\MasterKecamatan;
use App\Models\MasterProvinsi;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ListMasterProvinsis extends ListRecords
{
    protected static string $resource = MasterProvinsiResource::class;

	public function getSubheading(): string|Htmlable|null
	{
		return 'Data diperoleh dari BPS-RI (sig.bps.go.id:2024)';
	}

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make()->label('Provinsi Baru'),
			Action::make('syncWilayah')
                ->label('Sync Wilayah')
                ->color('danger')
                ->requiresConfirmation()
				->modalHeading('Penyelarasan Data Provinsi dan kabupaten')
				->modalDescription('Aksi ini akan memakan waktu yang cukup lama. Setelah konfirmasi, Anda tidak dapat menghentikan proses, menutup, meninggalkan maupun melakukan tugas-tugas lain di aplikasi ini. Anda yakin akan menjalankan aksi ini?')
				->modalSubmitActionLabel('Konfirmasi')
				// ->closeModalByClickingAway(false)
				// ->closeModalByEscaping(false)
				// ->modalCloseButton(false)
                ->action(function () {
					ini_set('max_execution_time', 0);
					// self::FetchWilayahJob();
					// self::ProcessWilayahJob();
					Bus::batch([
                        // new FetchWilayahJob(),
                        new ProcessWilayahJob()
                    ])->dispatch();
					Notification::make()
						->title('Penyelarasan dimulai.')
						->success()
						->send();
                }),
        ];
    }

	public function FetchWilayahJob()
    {
		Log::info('start Fetch Data');
        $provinsiUrl = "https://sig.bps.go.id/rest-drop-down/getwilayah?level=provinsi&parent=2024_1.2022&periode_merge=2024_1.2022";
        $provinsi = Http::get($provinsiUrl)->json();

        Storage::disk('local')->put('sig-wilayah/provinsi.json', json_encode($provinsi));

        foreach ($provinsi as $p) {
            sleep(5);
            $kabupatenUrl = "https://sig.bps.go.id/rest-bridging/getwilayah?level=kabupaten&parent={$p['kode']}&periode_merge=2024_1.2022";
            $kabupaten = Http::get($kabupatenUrl)->json();
            Storage::disk('local')->put("sig-wilayah/kabupaten_{$p['kode']}.json", json_encode($kabupaten));
            // foreach ($kabupaten as $k) {
            //     sleep(5);
            //     $kecamatanUrl = "https://sig.bps.go.id/rest-bridging/getwilayah?level=kecamatan&parent={$k['kode_bps']}&periode_merge=2024_1.2022";
            //     $kecamatan = Http::get($kecamatanUrl)->json();
            //     Storage::disk('local')->put("sig-wilayah/kecamatan_{$k['kode_bps']}.json", json_encode($kecamatan));

            //     foreach ($kecamatan as $c) {
            //         sleep(5);
            //         $desaUrl = "https://sig.bps.go.id/rest-bridging/getwilayah?level=desa&parent={$c['kode_bps']}&periode_merge=2024_1.2022";
            //         $desa = Http::get($desaUrl)->json();
            //         Storage::disk('local')->put("sig-wilayah/desa_{$c['kode_bps']}.json", json_encode($desa));
            //     }
            // }
        }
    }

	public function ProcessWilayahJob()
    {
        $provinsi = json_decode(Storage::get('sig-wilayah/provinsi.json'), true);
		Log::info('start proccess data');
        foreach ($provinsi as $p) {
            MasterProvinsi::updateOrCreate(
                ['provinsi_id' => $p['kode']],
                ['nama' => $p['nama']]
            );
            
            sleep(2);
            $kabupatenFile = "sig-wilayah/kabupaten_{$p['kode']}.json";
			if (Storage::exists($kabupatenFile)) {
				$kabupaten = json_decode(Storage::get($kabupatenFile), true);
				$kabupatenLama = MasterKabupaten::where('provinsi_id', $p['kode'])->pluck('kabupaten_id')->toArray();
				$kabupatenBaru = [];
			
				MasterKabupaten::where('provinsi_id', $p['kode'])
					->whereNotIn('kabupaten_id', $kabupatenBaru)
					->update(['status' => 'nonaktif']);
			}
        }
		return Notification::make()
			->title('Selesai')
			->success();
    }
}
