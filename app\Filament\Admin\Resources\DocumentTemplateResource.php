<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\DocumentTemplateResource\Pages;
use App\Filament\Admin\Resources\DocumentTemplateResource\RelationManagers;
use App\Models\DocumentTemplate;
use Filament\Forms;
use Filament\Forms\Components\{FileUpload, Textarea, TextInput};
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\{TextColumn};
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class DocumentTemplateResource extends Resource
{
    protected static ?string $model = DocumentTemplate::class;

	protected static ?string $navigationGroup = 'Data Induk';
	protected static ?string $navigationLabel = 'Contoh Dokumen';
    protected static ?string $navigationIcon = 'heroicon-o-inbox-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('form')
					->label('Nama Form')
					->required()
                    ->maxLength(20),
                TextInput::make('judul')
					->label('Judul Dokumen')
					->required()
                    ->maxLength(50),
                Textarea::make('deskripsi')
                    ->maxLength(255)->columnSpanFull()
					->required(),
				FileUpload::make('file_url')
					->openable()
					->hiddenLabel()
					->deletable()
					->required()
					->maxSize(2048)
					->columnSpan(1)
					->downloadable()
					->disk('public')
					->visibility('public')
					->panelAspectRatio('5:1')
					->imagePreviewHeight('50')
					->fetchFileInformation(true)
					->helperText('Maksimal 2MB, format PDF')
					->directory(function () {
						return "uploads/templates/";
					})
					->rules([
						'file',
						'mimetypes:application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document',
						'mimes:pdf,docx',
						'max:2048',
					])
					->validationMessages([
						'mimetypes' => 'Hanya file PDF atau MS Word  yang diperbolehkan',
						'mimes' => 'Ekstensi file harus .pdf atau .docx',
					])
					->getUploadedFileNameForStorageUsing(
						function (TemporaryUploadedFile $file, $get): string {
							$cleanForm = str_replace(['.', ',', '-', '/', ' '], '', $get('form'));
							
							// Format nama file: [ID]_[NPWP]_[NOIJIN].[ext]
							return 'form_'.$cleanForm . '.' . $file->getClientOriginalExtension();
						}
					),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('form')
                    ->searchable(),
                TextColumn::make('judul')
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                EditAction::make()->hiddenLabel(),
                ViewAction::make()->hiddenLabel()->icon('icon-download'),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDocumentTemplates::route('/'),
            // 'create' => Pages\CreateDocumentTemplate::route('/create'),
            'edit' => Pages\EditDocumentTemplate::route('/{record}/edit'),
        ];
    }
}
