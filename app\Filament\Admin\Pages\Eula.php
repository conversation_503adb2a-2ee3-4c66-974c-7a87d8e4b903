<?php

namespace App\Filament\Admin\Pages;

use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;
use League\CommonMark\Environment\Environment;
use League\CommonMark\Extension\GithubFlavoredMarkdownExtension;
use League\CommonMark\Extension\CommonMark\CommonMarkCoreExtension;
use League\CommonMark\MarkdownConverter;

class Eula extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationLabel = 'EULA';
    protected static ?string $title = '<PERSON><PERSON><PERSON><PERSON> (EULA)';
    protected static ?string $navigationGroup = 'Documentations';
    protected static ?int $navigationSort = 5;

    public static function shouldRegisterNavigation(): bool
    {
        return true; // Visible to all users
    }

    protected static string $view = 'filament.admin.pages.eula';

    public function getViewData(): array
    {
        $markdownPath = base_path('documentations/eula.md');

        if (!file_exists($markdownPath)) {
            return [
                'eulaHtml' => '<div class="text-red-500">EULA file not found. Please make sure the file exists at: ' . $markdownPath . '</div>',
            ];
        }

        $markdownContent = file_get_contents($markdownPath);

        if (empty($markdownContent)) {
            return [
                'eulaHtml' => '<div class="text-red-500">EULA file is empty.</div>',
            ];
        }

        // Create a new environment with GFM extension
        $environment = new Environment([
            'html_input' => 'strip',
            'allow_unsafe_links' => false,
        ]);

        // Add the CommonMark core extension and GFM extension
        $environment->addExtension(new CommonMarkCoreExtension());
        $environment->addExtension(new GithubFlavoredMarkdownExtension());

        // Create a new converter using the configured environment
        $converter = new MarkdownConverter($environment);

        // Convert the markdown to HTML
        $result = $converter->convert($markdownContent);
        $html = $result->getContent();

        return [
            'eulaHtml' => $html,
        ];
    }
}
