## Pendahuluan

Simethris (Sistem Informasi Monitoring dan <PERSON><PERSON><PERSON> Tanam Hortikultura dan Realisasi Impor Semusim) adalah aplikasi berbasis web yang dikembangkan untuk Kementerian Pertanian Republik Indonesia. Aplikasi ini bertujuan untuk memantau dan mengevaluasi kegiatan tanam hortikultura dan realisasi impor semusim, khususnya untuk komoditas bawang putih.

Dokumentasi ini memberikan gambaran teknis tentang arsitektur, komponen, dan fitur utama dari Simethris v4.0.

## Arsitektur Sistem

### Teknologi Utama

Simethris v4.0 dibangun menggunakan teknologi-teknologi berikut:

- **Framework**: Laravel 11
- **Admin Panel**: Filament 3
- **Database**: MySQL/MariaDB
- **Frontend**: Tailwind CSS, Alpine.js
- **Maps API**: Google Maps API
- **PDF Generation**: TCPDF, DomPDF
- **QR Code Generation**: Endroid QR Code

### Struktur Aplikasi

Aplikasi Simethris v4.0 menggunakan arsitektur MVC (Model-View-Controller) yang disediakan oleh Laravel, dengan tambahan komponen Filament untuk admin panel.

#### Panel Utama

Aplikasi memiliki tiga panel utama:

1. **Admin Panel** (`/admin`) - Panel utama untuk administrasi sistem
2. **Panel 2024** (`/panel/2024`) - Panel untuk data tahun 2024
3. **Panel 2025** (`/panel/2025`) - Panel untuk data tahun 2025 dan seterusnya

Setiap panel memiliki konfigurasi, tema, dan fitur yang terpisah, namun berbagi model data yang sama.

## Model Data

### Model Utama

Berikut adalah model-model utama dalam aplikasi:

#### 1. Commitment Models

- **Commitment2025**: Menyimpan data komitmen tanam dan produksi untuk tahun 2025
- **Commitment2024**: Menyimpan data komitmen tanam dan produksi untuk tahun 2024
- **CommitmentRegion**: Menyimpan data wilayah komitmen

#### 2. Realisasi Models

- **Realisasi2025**: Menyimpan data realisasi tanam dan produksi untuk tahun 2025
- **DataRealisasi2024**: Menyimpan data realisasi tanam dan produksi untuk tahun 2024
- **DetailRealisasi2025**: Menyimpan detail kegiatan realisasi

#### 3. Master Data Models

- **MasterPoktan**: Data kelompok tani
- **MasterAnggota**: Data anggota kelompok tani
- **MasterSpatial**: Data spasial lokasi tanam
- **MasterProvinsi**: Data provinsi
- **MasterKabupaten**: Data kabupaten
- **MasterKecamatan**: Data kecamatan
- **MasterDesa**: Data desa/kelurahan

#### 4. User Models

- **User**: Model pengguna sistem
- **DataUser**: Data tambahan untuk pengguna

#### 5. Document Models

- **Userfile**: Menyimpan data file yang diunggah pengguna
- **SklRekomendasi2024**: Menyimpan data SKL (Surat Keterangan Lunas) untuk tahun 2024
- **Completed**: Menyimpan data SKL yang telah selesai

### Relasi Antar Model

Model-model dalam Simethris memiliki relasi yang kompleks. Beberapa relasi utama:

- **Commitment** memiliki banyak **Realisasi**
- **MasterPoktan** memiliki banyak **MasterAnggota**
- **MasterSpatial** terkait dengan **MasterPoktan** dan **MasterAnggota**
- **User** memiliki satu **DataUser**
- **Commitment** memiliki banyak **Userfile**

## Fitur Utama

### 1. Manajemen Komitmen

Fitur ini memungkinkan pengguna untuk:
- Mendaftarkan komitmen tanam dan produksi
- Mengunggah dokumen pendukung (RIPH, SPTJM, dll)
- Memantau status komitmen

Implementasi utama:
- `Commitment2025Resource` dan `Commitment2024Resource`
- Controller: `CommitmentController`

### 2. Pemetaan Spasial

Fitur ini memungkinkan pengguna untuk:
- Menandai lokasi tanam pada peta
- Menggambar polygon area tanam
- Menyimpan data koordinat dan luas lahan

Implementasi utama:
- `MasterSpatialResource`
- `SpatialMapPage`
- Controller: `SpatialController` dan `DrawSpatialsController`
- Menggunakan Google Maps API untuk visualisasi peta

Contoh kode untuk menampilkan peta:

```php
Placeholder::make('map')
    ->hiddenLabel()
    ->content(function ($param) {
        //array
		//return array
    })
```

### 3. Manajemen Realisasi

Fitur ini memungkinkan pengguna untuk:
- Mencatat kegiatan tanam dan produksi
- Mengunggah foto bukti kegiatan
- Melaporkan hasil panen

Implementasi utama:
- `Realisasi2025Resource` dan `DataRealisasi2024Resource`
- `LaporanKegiatan` page

### 4. Verifikasi dan SKL

Fitur ini memungkinkan admin untuk:
- Memverifikasi data realisasi
- Menerbitkan SKL (Surat Keterangan Lunas)
- Membuat QR Code untuk verifikasi SKL

Implementasi utama:
- `PengajuansklResource`
- Menggunakan Endroid QR Code untuk generate QR Code

Contoh kode untuk generate QR Code:

```php
protected static function generateQrCode($param$)
{
    //setup qr code
	//return qr code
}
```

### 5. Pembuatan Dokumen PDF

Fitur ini memungkinkan sistem untuk:
- Membuat dokumen PDF untuk SKL
- Membuat laporan realisasi
- Membuat logbook kegiatan

Implementasi utama:
- Menggunakan TCPDF dan DomPDF
- Template blade untuk konten PDF

## Struktur Database

### Tabel Utama

1. **t2025_commitments**: Menyimpan data komitmen tanam dan produksi
2. **t2025_master_poktans**: Menyimpan data kelompok tani
3. **t2025_master_anggotas**: Menyimpan data anggota kelompok tani
4. **t2025_master_spatials**: Menyimpan data spasial lokasi tanam
5. **t2025_realisasis**: Menyimpan data realisasi tanam dan produksi
6. **t2025_detail_realisasis**: Menyimpan detail kegiatan realisasi
7. **users**: Menyimpan data pengguna sistem
8. **data_users**: Menyimpan data tambahan untuk pengguna
9. **userfiles**: Menyimpan data file yang diunggah pengguna
10. **skls**: Menyimpan data SKL

### Tabel Referensi

1. **data_provinsis**: Data provinsi
2. **data_kabupatens**: Data kabupaten
3. **data_kecamatans**: Data kecamatan
4. **data_desas**: Data desa/kelurahan

## Keamanan

### Autentikasi dan Otorisasi

Simethris menggunakan sistem autentikasi Laravel dengan tambahan fitur dari Filament. Otorisasi menggunakan Spatie Laravel Permission.

Peran pengguna:
- **Super Admin**: Akses penuh ke seluruh sistem
- **Admin**: Akses ke fitur administrasi
- **Verifikator**: Akses untuk verifikasi data
- **Importir**: Akses terbatas untuk importir
- **Dinas**: Akses untuk dinas pertanian

### Keamanan Data

- Enkripsi data sensitif menggunakan Laravel's encryption
- HTTPS untuk semua komunikasi
- Validasi input untuk mencegah injeksi
- Rate limiting untuk mencegah brute force

## Integrasi

### Google Maps API

Digunakan untuk fitur pemetaan spasial, dengan kemampuan:
- Menampilkan peta
- Menandai lokasi
- Menggambar dan menyimpan polygon
- Menghitung luas area

### QR Code

Menggunakan Endroid QR Code untuk membuat QR Code yang digunakan untuk verifikasi SKL.

## Optimasi

### Optimasi Database

- Penggunaan indeks untuk mempercepat query
- Relasi yang dioptimalkan untuk mengurangi jumlah query
- Lazy loading untuk data yang besar

### Optimasi Frontend

- Lazy loading untuk komponen UI
- Optimasi gambar
- Minifikasi CSS dan JavaScript

## Deployment

### Persyaratan Server

- PHP 8.2 atau lebih tinggi
- MySQL 8.0 atau MariaDB 10.5 atau lebih tinggi
- Composer
- Node.js dan NPM
- Web server (Apache/Nginx)
- SSL Certificate

### Konfigurasi

Konfigurasi utama aplikasi menggunakan enviorment variable serta configuration file dengan parameter penting

## Kesimpulan

Simethris v4.0 adalah aplikasi yang komprehensif untuk monitoring dan evaluasi tanam hortikultura dan realisasi impor semusim. Dengan arsitektur yang modular dan fitur yang lengkap, aplikasi ini dapat membantu Kementerian Pertanian dalam memantau dan mengevaluasi kegiatan tanam hortikultura dan realisasi impor semusim.

Dokumentasi ini memberikan gambaran teknis tentang arsitektur, komponen, dan fitur utama dari Simethris v4.0. Untuk informasi lebih detail, silakan merujuk ke kode sumber atau dokumentasi API.
