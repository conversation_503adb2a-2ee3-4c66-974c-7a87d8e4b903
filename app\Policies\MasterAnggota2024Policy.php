<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\MasterAnggota2024;
use App\Models\User;

class MasterAnggota2024Policy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any MasterAnggota2024');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, MasterAnggota2024 $masteranggota2024): bool
    {
        return $user->checkPermissionTo('view MasterAnggota2024');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create MasterAnggota2024');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, MasterAnggota2024 $masteranggota2024): bool
    {
        return $user->checkPermissionTo('update MasterAnggota2024');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, MasterAnggota2024 $masteranggota2024): bool
    {
        return $user->checkPermissionTo('delete MasterAnggota2024');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any MasterAnggota2024');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, MasterAnggota2024 $masteranggota2024): bool
    {
        return $user->checkPermissionTo('restore MasterAnggota2024');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any MasterAnggota2024');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, MasterAnggota2024 $masteranggota2024): bool
    {
        return $user->checkPermissionTo('replicate MasterAnggota2024');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder MasterAnggota2024');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, MasterAnggota2024 $masteranggota2024): bool
    {
        return $user->checkPermissionTo('force-delete MasterAnggota2024');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any MasterAnggota2024');
    }
}
