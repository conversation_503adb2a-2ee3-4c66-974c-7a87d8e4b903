<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mobile_apps', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('version');
            $table->integer('version_code');
            $table->text('description')->nullable();
            $table->string('file_name');
            $table->string('min_android_version')->nullable();
            $table->integer('min_ram')->nullable()->comment('Minimum RAM in MB');
            $table->integer('min_storage')->nullable()->comment('Minimum storage in MB');
            $table->json('required_features')->nullable();
            $table->text('recommended_specs')->nullable();
            $table->text('release_notes')->nullable();
            $table->boolean('is_active')->default(true);
            $table->json('allowed_roles');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mobile_apps');
    }
};
