<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use \DateTimeInterface;

class Penangkar2025 extends Model
{
    use HasFactory, SoftDeletes;

    public $table = 't2025_penangkar_riph';

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public $fillable = [
        'npwp',
        'no_ijin',
        'nama_penangkar',
        'nama_pimpinan',
        'hp_pimpinan',
        'alamat',
        'varietas',
        'ketersediaan',
    ];

    public function commitment()
    {
        return $this->belongsTo(Commitment2025::class, 'no_ijin', 'no_ijin');
    }

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }
}
