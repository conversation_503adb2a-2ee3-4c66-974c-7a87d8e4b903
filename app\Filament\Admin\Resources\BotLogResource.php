<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\BotLogResource\Pages;
use App\Filament\Admin\Resources\BotLogResource\RelationManagers;
use App\Models\BotLog;
use Filament\Forms;
use Filament\Forms\Components\{Textarea, TextInput};
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class BotLogResource extends Resource
{
	protected static ?string $model = BotLog::class;

	protected static ?string $navigationIcon = 'heroicon-o-shield-exclamation';
	protected static ?int $navigationSort = 3;
	public static function getNavigationGroup(): ?string
	{
		return 'System';
	}
	public static function form(Form $form): Form
	{
		return $form
			->schema([
				TextInput::make('ip')
					->maxLength(255),
				Textarea::make('user_agent')
					->columnSpanFull(),
			]);
	}

	public static function table(Table $table): Table
	{
		return $table
			->columns([
				TextColumn::make('ip'),
				TextColumn::make('user_agent')
					->searchable()
					->wrap(),
				TextColumn::make('created_at')
					->dateTime()
					->sortable(),
			])
			->filters([
				//
			])
			->actions([
				Tables\Actions\ViewAction::make()->iconButton(),
				// Tables\Actions\EditAction::make(),
			])
			->bulkActions([
				Tables\Actions\BulkActionGroup::make([
					Tables\Actions\DeleteBulkAction::make(),
				]),
			]);
	}

	public static function getRelations(): array
	{
		return [
			//
		];
	}

	public static function getPages(): array
	{
		return [
			'index' => Pages\ListBotLogs::route('/'),
			// 'create' => Pages\CreateBotLog::route('/create'),
			'view' => Pages\ViewBotLog::route('/{record}'),
			// 'edit' => Pages\EditBotLog::route('/{record}/edit'),
		];
	}
}
