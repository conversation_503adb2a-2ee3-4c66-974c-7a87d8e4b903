<x-filament-panels::page>
    <!-- Flash Messages -->
    @if(session('success'))
        <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800 dark:text-green-200">
                        {{ session('success') }}
                    </p>
                </div>
            </div>
        </div>
    @endif

    @if(session('error'))
        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-red-800 dark:text-red-200">
                        {{ session('error') }}
                    </p>
                </div>
            </div>
        </div>
    @endif

    <div class="space-y-6">
        <!-- Ticket Information -->
        <x-filament::section>
            <x-slot name="heading">
                Informasi Tiket
            </x-slot>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                    <dd class="mt-1">
                        <x-filament::badge 
                            :color="match($record->status) {
                                'open' => 'danger',
                                'in_progress' => 'warning', 
                                'resolved' => 'success',
                                'closed' => 'gray',
                                default => 'gray'
                            }"
                        >
                            {{ ucfirst(str_replace('_', ' ', $record->status)) }}
                        </x-filament::badge>
                    </dd>
                </div>
                
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Prioritas</dt>
                    <dd class="mt-1">
                        <x-filament::badge 
                            :color="match($record->priority) {
                                'low' => 'success',
                                'medium' => 'warning',
                                'high' => 'danger', 
                                'urgent' => 'danger',
                                default => 'gray'
                            }"
                        >
                            {{ ucfirst($record->priority) }}
                        </x-filament::badge>
                    </dd>
                </div>
                
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Departemen</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                        {{ $record->department->name }}
                    </dd>
                </div>
                
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Layanan Terkait</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                        {{ $record->related_service ?? '-' }}
                    </dd>
                </div>
                
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Dibuat oleh</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                        {{ $record->user->name }}
                    </dd>
                </div>
                
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Staff Assigned</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                        {{ $record->staff->name ?? 'Belum di-assign' }}
                    </dd>
                </div>
                
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Dibuat</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                        {{ $record->created_at->format('d M Y H:i') }}
                    </dd>
                </div>
                
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Aktivitas Terakhir</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                        {{ $record->last_activity_at?->format('d M Y H:i') ?? '-' }}
                    </dd>
                </div>
            </div>
        </x-filament::section>

        <!-- Original Message -->
        <x-filament::section>
            <x-slot name="heading">
                Pesan Awal
            </x-slot>
            
            <div class="prose dark:prose-invert max-w-none">
                {!! $record->message !!}
            </div>
            
            @if($record->attachment)
                <div class="mt-4">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Lampiran</dt>
                    <dd class="mt-1">
                        <a href="{{ \Illuminate\Support\Facades\Storage::url($record->attachment) }}"
                           target="_blank"
                           class="text-primary-600 hover:text-primary-500 dark:text-primary-400">
                            {{ basename($record->attachment) }}
                        </a>
                    </dd>
                </div>
            @endif
        </x-filament::section>

        <!-- Thread Messages -->
        <x-filament::section>
            <x-slot name="heading">
                Diskusi
            </x-slot>
            
            @include('filament.admin.widgets.ticket-messages-display', ['ticket' => $record])
        </x-filament::section>

        <!-- Reply Form -->
        @if(in_array($record->status, ['open', 'in_progress']))
            <x-filament::section>
                <x-slot name="heading">
                    Balas Pesan
                </x-slot>
                
                @include('filament.admin.widgets.simple-reply-form', ['ticket' => $record])
            </x-filament::section>
        @endif
    </div>
</x-filament-panels::page>
