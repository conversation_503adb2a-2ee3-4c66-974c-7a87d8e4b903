<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Ticket Information -->
        <x-filament::section>
            <x-slot name="heading">
                Informasi Tiket
            </x-slot>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                    <dd class="mt-1">
                        <x-filament::badge 
                            :color="match($record->status) {
                                'open' => 'danger',
                                'in_progress' => 'warning', 
                                'resolved' => 'success',
                                'closed' => 'gray',
                                default => 'gray'
                            }"
                        >
                            {{ ucfirst(str_replace('_', ' ', $record->status)) }}
                        </x-filament::badge>
                    </dd>
                </div>
                
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Prioritas</dt>
                    <dd class="mt-1">
                        <x-filament::badge 
                            :color="match($record->priority) {
                                'low' => 'success',
                                'medium' => 'warning',
                                'high' => 'danger', 
                                'urgent' => 'danger',
                                default => 'gray'
                            }"
                        >
                            {{ ucfirst($record->priority) }}
                        </x-filament::badge>
                    </dd>
                </div>
                
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Departemen</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                        {{ $record->department->name }}
                    </dd>
                </div>
                
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Layanan Terkait</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                        {{ $record->related_service ?? '-' }}
                    </dd>
                </div>
                
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Dibuat oleh</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                        {{ $record->user->name }}
                    </dd>
                </div>
                
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Staff Assigned</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                        {{ $record->staff->name ?? 'Belum di-assign' }}
                    </dd>
                </div>
                
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Dibuat</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                        {{ $record->created_at->format('d M Y H:i') }}
                    </dd>
                </div>
                
                <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Aktivitas Terakhir</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                        {{ $record->last_activity_at?->format('d M Y H:i') ?? '-' }}
                    </dd>
                </div>
            </div>
        </x-filament::section>

        <!-- Original Message -->
        <x-filament::section>
            <x-slot name="heading">
                Pesan Awal
            </x-slot>
            
            <div class="prose dark:prose-invert max-w-none">
                {!! $record->message !!}
            </div>
            
            @if($record->attachment)
                <div class="mt-4">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Lampiran</dt>
                    <dd class="mt-1">
                        <a href="{{ \Illuminate\Support\Facades\Storage::url($record->attachment) }}"
                           target="_blank"
                           class="text-primary-600 hover:text-primary-500 dark:text-primary-400">
                            {{ basename($record->attachment) }}
                        </a>
                    </dd>
                </div>
            @endif
        </x-filament::section>

        <!-- Thread Messages -->
        <x-filament::section>
            <x-slot name="heading">
                Diskusi
            </x-slot>
            
            @livewire('ticket-messages-widget', ['ticketId' => $record->id])
        </x-filament::section>

        <!-- Reply Form -->
        @if(in_array($record->status, ['open', 'in_progress']))
            <x-filament::section>
                <x-slot name="heading">
                    Balas Pesan
                </x-slot>
                
                @livewire('message-reply-widget', ['ticketId' => $record->id])
            </x-filament::section>
        @endif
    </div>
</x-filament-panels::page>
