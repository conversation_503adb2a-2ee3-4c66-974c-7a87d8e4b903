<?php

namespace App\Filament\Admin\Resources\ForeignApiResource\Pages;

use App\Filament\Admin\Resources\ForeignApiResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewForeignApi extends ViewRecord
{
    protected static string $resource = ForeignApiResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
