<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\DataUser;
use App\Models\User;

class DataUserPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any DataUser');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, DataUser $datauser): bool
    {
        return $user->checkPermissionTo('view DataUser');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create DataUser');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, DataUser $datauser): bool
    {
        return $user->checkPermissionTo('update DataUser');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, DataUser $datauser): bool
    {
        return $user->checkPermissionTo('delete DataUser');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any DataUser');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, DataUser $datauser): bool
    {
        return $user->checkPermissionTo('restore DataUser');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any DataUser');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, DataUser $datauser): bool
    {
        return $user->checkPermissionTo('replicate DataUser');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder DataUser');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, DataUser $datauser): bool
    {
        return $user->checkPermissionTo('force-delete DataUser');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any DataUser');
    }
}
