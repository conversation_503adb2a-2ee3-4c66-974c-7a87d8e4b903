<?php

namespace App\Filament\Panel2025\Resources;

use App\Filament\Panel2025\Resources\CommitmentRegionResource\Pages;
use App\Filament\Admin\Resources\CommitmentRegionResource\RelationManagers;
use App\Models\CommitmentRegion;
use Filament\Forms;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CommitmentRegionResource extends Resource
{
    protected static ?string $model = CommitmentRegion::class;
    protected static bool $shouldRegisterNavigation = false;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Placeholder::make('quota')
                    ->label('Luas Rencana')
                    ->content(fn ($record)=>number_format($record->quota,2,',','.') . ' m2'),
                Placeholder::make('realisasi')
                    ->label('Realisasi')
                    ->content(fn ($record)=>number_format($record->fullfiled,2,',','.') . ' m2'),

                Placeholder::make('status')
                    ->content(fn ($record) => match ($record->status) {
                        '0' => 'Baru',
                        '1' => 'Selesai',
                        '2' => 'Verifikasi',
                        default => 'Tidak Diketahui',
                    }),

            ])->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('npwp')
                    ->searchable(),
                Tables\Columns\TextColumn::make('no_ijin')
                    ->searchable(),
                Tables\Columns\TextColumn::make('kabupaten_id')
                    ->searchable(),
                Tables\Columns\TextColumn::make('quota')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fullfilled')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCommitmentRegions::route('/'),
            'create' => Pages\CreateCommitmentRegion::route('/create'),
            'view' => Pages\ViewCommitmentRegion::route('/{record}'),
            'edit' => Pages\EditCommitmentRegion::route('/{record}/edit'),
        ];
    }
}
