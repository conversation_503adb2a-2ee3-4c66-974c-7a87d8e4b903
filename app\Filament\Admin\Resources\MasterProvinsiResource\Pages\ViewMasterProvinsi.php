<?php

namespace App\Filament\Admin\Resources\MasterProvinsiResource\Pages;

use App\Filament\Admin\Resources\MasterProvinsiResource;
use App\Models\MasterDesa;
use App\Models\MasterKabupaten;
use App\Models\MasterKecamatan;
use App\Models\MasterProvinsi;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ViewMasterProvinsi extends ViewRecord
{
	protected static string $resource = MasterProvinsiResource::class;

	public function getTitle(): string|Htmlable
	{
		$nama = $this->record ? $this->record->nama : '##';
		return 'Data Provinsi ' . $nama;
	}

	public function getHeading(): string
	{
		$nama = $this->record ? $this->record->nama : '##';
		return 'Data Provinsi ' . $nama;
	}

	protected function getHeaderActions(): array
	{
		return [
			Actions\EditAction::make(),
			Action::make('syncWilayah')
				->label('Sync Wilayah')
				->hidden()
				->color('success')
				->requiresConfirmation()
				->modalHeading('Konfirmasi Sinkronisasi Data Wilayah')
				->modalDescription('Tindakan ini akan menjalankan proses sinkronisasi data wilayah di latar belakang. Anda dapat meninggalkan halaman ini setelah menekan tombol Lanjutkan, dan proses akan terus berjalan hingga selesai.')
				->modalSubmitActionLabel('Lanjutkan')
				->action(function ($record) {
					ini_set('max_execution_time', 0); // 5 menit
					self::FetchWilayahJob($record);
					self::ProcessWilayahJob($record);
					Notification::make()
						->title('Proses Sinkronisasi Kabupaten')
						->success()
						->send();
				}),
		];
	}

	public function FetchWilayahJob($record)
	{
			$user = Auth::user()->id;
			$provinsiId = $record->provinsi_id;
			$kabupatenUrl = "https://sig.bps.go.id/rest-bridging/getwilayah?level=kabupaten&parent={$provinsiId}&periode_merge=2024_1.2022";
			$kabupaten = Http::get($kabupatenUrl)->json();
			Storage::disk('local')->put("sig-wilayah/kabupaten_{$provinsiId}.json", json_encode($kabupaten));

			foreach ($kabupaten as $k) {
				sleep(5);
				$kecamatanUrl = "https://sig.bps.go.id/rest-bridging/getwilayah?level=kecamatan&parent={$k['kode_bps']}&periode_merge=2024_1.2022";
				$kecamatan = Http::get($kecamatanUrl)->json();
				Storage::disk('local')->put("sig-wilayah/kecamatan_{$k['kode_bps']}.json", json_encode($kecamatan));
				// foreach ($kecamatan as $c) {
				//     sleep(5);
				//     $desaUrl = "https://sig.bps.go.id/rest-bridging/getwilayah?level=desa&parent={$c['kode_bps']}&periode_merge=2024_1.2022";
				//     $desa = Http::get($desaUrl)->json();
				//     Storage::disk('local')->put("sig-wilayah/desa_{$c['kode_bps']}.json", json_encode($desa));
				// 	Notification::make()
				// 		->title('Satu DESA berhasil disimpan')
				// 		->success()
				// 		->sendToDatabase($user);
				// }
			}
	}

	public function ProcessWilayahJob($record)
	{
		$user = Auth::user()->id;
		$provinsiId = $record->provinsi_id;
		$provinsi = json_decode(Storage::get('sig-wilayah/provinsi.json'), true);
		// Log::info($provinsi);
		$kabupatenFile = "sig-wilayah/kabupaten_{$provinsiId}.json";
		if (Storage::exists($kabupatenFile)) {
			$kabupaten = json_decode(Storage::get($kabupatenFile), true);
			$kabupatenLama = MasterKabupaten::where('provinsi_id', $provinsiId)->pluck('kabupaten_id')->toArray();
			$kabupatenBaru = [];
		
			foreach ($kabupaten as $k) {
				$kabupatenBaru[] = $k['kode_bps'];
				MasterKabupaten::updateOrCreate(
					['kabupaten_id' => $k['kode_bps']],
					['provinsi_id' => $provinsiId, 'nama_kab' => $k['nama_bps']]
				);
				$kecamatanFile = "sig-wilayah/kecamatan_{$k['kode_bps']}.json";
				if (Storage::exists($kecamatanFile)) {
					$kecamatan = json_decode(Storage::get($kecamatanFile), true);
		
					$kecamatanLama = MasterKecamatan::where('kabupaten_id', $k['kode_bps'])->pluck('kecamatan_id')->toArray();
					$kecamatanBaru = [];
		
					foreach ($kecamatan as $c) {
						$kecamatanBaru[] = $c['kode_bps'];
						MasterKecamatan::updateOrCreate(
							['kecamatan_id' => $c['kode_bps']],
							['kabupaten_id' => $k['kode_bps'], 'nama_kecamatan' => $c['nama_bps']]
						);

						// $desaFile = "sig-wilayah/desa_{$c['kode_bps']}.json";
						// if (Storage::exists($desaFile)) {
						// 	$desa = json_decode(Storage::get($desaFile), true);
		
						// 	$desaLama = MasterDesa::where('kecamatan_id', $c['kode_bps'])->pluck('kelurahan_id')->toArray();
						// 	$desaBaru = [];
		
						// 	foreach ($desa as $d) {
						// 		$desaBaru[] = $d['kode_bps'];
		
						// 		MasterDesa::updateOrCreate(
						// 			['kelurahan_id' => $d['kode_bps']],
						// 			['kecamatan_id' => $c['kode_bps'], 'nama_desa' => $d['nama_bps']]
						// 		);
						// 	}
						// 	MasterDesa::where('kecamatan_id', $c['kode_bps'])
						// 		->whereNotIn('kelurahan_id', $desaBaru)
						// 		->update(['status' => 'nonaktif']);
						// }
					}
					MasterKecamatan::where('kabupaten_id', $k['kode_bps'])
						->whereNotIn('kecamatan_id', $kecamatanBaru)
						->update(['status' => 'nonaktif']);

				}
			}
			MasterKabupaten::where('provinsi_id', $provinsiId)
				->whereNotIn('kabupaten_id', $kabupatenBaru)
				->update(['status' => 'nonaktif']);
		}

		return Notification::make()
			->title('Selesai')
			->success();
	}
}
