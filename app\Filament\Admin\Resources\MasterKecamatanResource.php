<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\MasterKecamatanResource\Pages;
use App\Filament\Admin\Resources\MasterKecamatanResource\RelationManagers;
use App\Filament\Admin\Resources\MasterKecamatanResource\RelationManagers\DesaRelationManager;
use App\Models\MasterKecamatan;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MasterKecamatanResource extends Resource
{
    protected static ?string $model = MasterKecamatan::class;

    protected static ?string $modelLabel = 'Master Kecamatan';
    protected static ?string $pluralModelLabel = 'Daftar Kecamatan';

	protected static ?string $navigationGroup = 'Data Induk';
    protected static ?string $navigationLabel = 'Master Kecamatan';
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationIcon = 'heroicon-o-globe-asia-australia';
	public static function shouldRegisterNavigation(): bool
    {
		return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('kabupaten_id')
                    ->required()
                    ->maxLength(4),
                Forms\Components\TextInput::make('kecamatan_id')
                    ->required()
                    ->maxLength(7),
                Forms\Components\Textarea::make('kode_dagri')
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('nama_kecamatan')
                    ->required()
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('lat')
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('lng')
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('polygon')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('kabupaten_id')
                    ->searchable(),
                Tables\Columns\TextColumn::make('kecamatan_id')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            DesaRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMasterKecamatans::route('/'),
            'create' => Pages\CreateMasterKecamatan::route('/create'),
            'view' => Pages\ViewMasterKecamatan::route('/{record}'),
            'edit' => Pages\EditMasterKecamatan::route('/{record}/edit'),
        ];
    }
}
