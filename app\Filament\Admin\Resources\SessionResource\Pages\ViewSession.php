<?php

namespace App\Filament\Admin\Resources\SessionResource\Pages;

use App\Filament\Admin\Resources\SessionResource;
use App\Models\User;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\DB;

class ViewSession extends ViewRecord
{
    protected static string $resource = SessionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('delete')
                ->label('Hapus Session')
                ->color('danger')
                ->icon('heroicon-o-trash')
                ->requiresConfirmation()
                ->modalHeading('Hapus Session')
                ->modalDescription('Apakah Anda yakin ingin menghapus session ini? Pengguna akan terlogout dari perangkat tersebut.')
                ->modalSubmitActionLabel('Ya, Hapus')
                ->action(function () {
                    $record = $this->getRecord();
                    $record->delete();
                    
                    $this->redirect(SessionResource::getUrl('index'));
                    Notification::make()
                        ->success()
                        ->title('Session berhasil dihapus.')
                        ->send();
                }),
        ];
    }
    
    protected function mutateFormData(array $data): array
    {
        $session = $this->getRecord();
        
        // Tambahkan informasi pengguna jika ada
        if ($session->user_id) {
            $user = User::find($session->user_id);
            if ($user) {
                $data['user_name'] = $user->name;
                $data['user_email'] = $user->email;
                
                // Tambahkan informasi peran
                $role = DB::table('model_has_roles')
                    ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
                    ->where('model_has_roles.model_id', $user->id)
                    ->where('model_has_roles.model_type', 'App\\Models\\User')
                    ->value('roles.name');
                
                $data['role'] = $role ?? 'Tidak Ada Peran';
            }
        } else {
            $data['user_name'] = 'Guest';
            $data['user_email'] = '-';
            $data['role'] = '-';
        }
        
        // Tambahkan informasi perangkat
        $deviceInfo = $session->getDeviceInfo();
        $data['device'] = $deviceInfo['device'];
        $data['browser'] = $deviceInfo['browser'];
        $data['platform'] = $deviceInfo['platform'];
        
        // Tambahkan informasi payload
        $data['payload'] = $session->payload;
        $data['payload_formatted'] = $session->payload_formatted;
        
        // Tambahkan informasi tambahan
        $this->getHeaderActions();
        $this->heading = 'Detail Session: ' . substr($session->id, 0, 8) . '...';
        
        return $data;
    }
    
    protected function getFooterWidgets(): array
    {
        return [];
    }
    
    protected function getHeaderDescription(): ?string
    {
        $session = $this->getRecord();
        $description = '';
        
        if ($session->user_id) {
            $description .= 'Session pengguna ' . $session->user_name;
        } else {
            $description .= 'Session tamu (tidak login)';
        }
        
        $description .= ' | IP: ' . $session->ip_address;
        $description .= ' | ' . $session->status;
        
        return $description;
    }
}
