<?php

namespace App\Filament\Panel2025\Resources\Commitment2025Resource\Pages;

use App\Filament\Panel2025\Resources\Commitment2025Resource;
use App\Filament\Panel2025\Resources\Commitment2025Resource\RelationManagers\MySpatialsRelationManager;
use App\Models\CommitmentRegion;
use App\Models\MasterSpatial;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Actions;
use Filament\Forms\Components\{Placeholder, Section, Textarea, TextInput};
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Support\Facades\Log;

class PickLocations extends EditRecord
{
	protected static string $resource = Commitment2025Resource::class;
	public static string | Alignment $formActionsAlignment = Alignment::Right;

	public function getTitle(): string
	{
		return 'Pemilihan Lok<PERSON> Tanam';
	}

	public function getHeading(): string
	{
		return 'Pemilihan Lokasi Tanam';
	}

	public function getSubheading(): ?string
	{
		$noIjin = $this->record ? $this->record->no_ijin : '##';
		return 'untuk PPRK No: ' . $noIjin;
	}

	protected function getHeaderActions(): array
	{
		return [
			// Action::make('gotomap')
			// 	->hidden()
			// 	->icon('icon-geo-alt-fill')
			// 	->color('info')
			// 	->label('Lihat Peta Lokasi')
			// 	->url(fn ($record) => route('panel.2025.report.singleMap', $record->id) ),
		];
	}
	protected function getRedirectUrl(): string
	{
		return $this->getResource()::getUrl('locationConfirm', ['record' => $this->getRecord()]);
	}

	public function getLocations(): array
	{
		$noIjin = $this->record->no_ijin;
		$commitmentRegion = CommitmentRegion::where('no_ijin', $noIjin)
			->with(['kabupaten:kabupaten_id,nama_kab'])
			->select('kabupaten_id', 'quota')
			->get();
		$kabupatenIds = $commitmentRegion->pluck('kabupaten_id');

		$query = MasterSpatial::where('is_active', 1)
			->whereIn('kabupaten_id', $kabupatenIds)
			->limit(10);

		$spatials = $query->get()->map(function ($item) {
			return [
				'id' => $item->id,
				'kode_spatial' => e($item->kode_spatial),
				'latitude' => $item->latitude,
				'longitude' => $item->longitude,
				'luas_lahan' => $item->luas_lahan,
				'kabupaten_id' => $item->kabupaten_id,
				'kecamatan_id' => $item->kecamatan_id,
				'status' => $item->status,
			];
		});

		return ['data' => $spatials->toArray()];
	}

	public function searchLocation(string $search): array
	{
		// Melakukan pencarian kode_spatial berdasarkan input search
		$result = MasterSpatial::where('kode_spatial', 'like', '%' . $search . '%')
			->limit(10) // Membatasi jumlah hasil pencarian
			->get()
			->map(function ($item) {
				return [
					'id' => $item->id,
					'kode_spatial' => e($item->kode_spatial),
					'latitude' => $item->latitude,
					'longitude' => $item->longitude,
					'luas_lahan' => $item->luas_lahan,
					'kabupaten_id' => $item->kabupaten_id,
					'kecamatan_id' => $item->kecamatan_id,
					'status' => $item->status,
				];
			});

		return ['data' => $result->toArray()];
	}


	public function form(Form $form): Form
	{
		return $form
			->schema([
				Section::make('Peta Lokasi')
					->columns(4)
					->schema([
						Placeholder::make('map')
							->label('Peta')
							->hiddenLabel()
							// ->reactive()
							->columnSpan([
								'sm' => '4',
								'md' => '3',
							])
							->content(fn() => view('components.maps', ['data' => $this->getLocations()])),

						// 	->hiddenLabel()
						// 	->columnSpanFull()
						// 	->placeholder('cari lokasi')
						// 	->suffixIcon('icon-search')
						// 	->extraAttributes([
						// 		'onkeydown' => 'if(event.key === "Enter"){event.preventDefault();}'
						// 	]),

						// Textarea::make('array_lokasi')
						// 	->columnSpanFull()
						// 	->reactive()
						// 	->autosize(),
					]),

				Section::make('Lokasi Terpilih')
					->schema([
						TableRepeater::make('selected_location')
							->hiddenLabel()
							->addable(false)
							->relationship('realisasi')
							->headers([
								Header::make('Lokasi'),
								Header::make('Kelompok Tani'),
								Header::make('Petani'),
								Header::make('NIK'),
								Header::make('Kabupaten'),
								Header::make('Kecamatan'),
								Header::make('Luas Lahan'),
							])
							->schema([])
					])
			]);
	}
}
