<?php

namespace App\Jobs;

use Filament\Notifications\Notification;
use GuzzleHttp\Client;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class FetchWilayahJob implements ShouldQueue
{
	use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

	public function __construct() {}

	public function handle()
    {
        Log::info('Start fetch data');
        Notification::make()
            ->title('Proses Sinkronisasi Dimulai')
            ->success()
            ->send();
        $client = new Client(['verify' => false]); // Nonaktifkan SSL Verification

        try {
            // 1. Fetch Provinsi
            $response = $client->get("https://sig.bps.go.id/rest-drop-down/getwilayah?level=provinsi&parent=2024_1.2022&periode_merge=2024_1.2022");
            $provinsi = json_decode($response->getBody(), true);

            Storage::disk('local')->put('sig-wilayah/provinsi.json', json_encode($provinsi));
			Log::info('Provinsi Sukses');
			Notification::make()
				->title('Pembentukan file provinsi berhasil.')
				->success()
				->send();
        } catch (\Exception $e) {
            Log::error("Gagal mengambil data provinsi: " . $e->getMessage());
            return;
        }

        foreach ($provinsi as $p) {

            // 2. Fetch Kabupaten
            $kabupatenUrl = "https://sig.bps.go.id/rest-bridging/getwilayah?level=kabupaten&parent={$p['kode']}&periode_merge=2024_1.2022";
            
            try {
                $response = $client->get($kabupatenUrl);
                $kabupaten = json_decode($response->getBody(), true);
                Storage::disk('local')->put("sig-wilayah/kabupaten_{$p['kode']}.json", json_encode($kabupaten));
				Log::info("Kabupaten {$p['kode']} Sukses");
            } catch (\Exception $e) {
                Log::error("Gagal mengambil data kabupaten untuk provinsi {$p['kode']}: " . $e->getMessage());
                continue;
            }

            foreach ($kabupaten as $k) {

                // 3. Fetch Kecamatan
                $kecamatanUrl = "https://sig.bps.go.id/rest-bridging/getwilayah?level=kecamatan&parent={$k['kode_bps']}&periode_merge=2024_1.2022";

                try {
                    $response = $client->get($kecamatanUrl);
                    $kecamatan = json_decode($response->getBody(), true);
                    Storage::disk('local')->put("sig-wilayah/kecamatan_{$k['kode_bps']}.json", json_encode($kecamatan));
					Log::info("Kecamatan {$k['kode_bps']} Sukses");
                } catch (\Exception $e) {
                    Log::error("Gagal mengambil data kecamatan untuk kabupaten {$k['kode_bps']}: " . $e->getMessage());
                    continue;
                }

                foreach ($kecamatan as $c) {

                    // 4. Fetch Desa
                    $desaUrl = "https://sig.bps.go.id/rest-bridging/getwilayah?level=desa&parent={$c['kode_bps']}&periode_merge=2024_1.2022";

                    try {
                        $response = $client->get($desaUrl);
                        $desa = json_decode($response->getBody(), true);
                        Storage::disk('local')->put("sig-wilayah/desa_{$c['kode_bps']}.json", json_encode($desa));
						Log::info("Desa {$c['kode_bps']} Sukses");
                    } catch (\Exception $e) {
                        Log::error("Gagal mengambil data desa untuk kecamatan {$c['kode_bps']}: " . $e->getMessage());
                        continue;
                    }
                }
            }
        }
    }
}
