<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\PengajuanVerifikasi;
use App\Models\User;

class PengajuanVerifikasiPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any PengajuanVerifikasi');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, PengajuanVerifikasi $pengajuanverifikasi): bool
    {
        return $user->checkPermissionTo('view PengajuanVerifikasi');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create PengajuanVerifikasi');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, PengajuanVerifikasi $pengajuanverifikasi): bool
    {
        return $user->checkPermissionTo('update PengajuanVerifikasi');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, PengajuanVerifikasi $pengajuanverifikasi): bool
    {
        return $user->checkPermissionTo('delete PengajuanVerifikasi');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any PengajuanVerifikasi');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, PengajuanVerifikasi $pengajuanverifikasi): bool
    {
        return $user->checkPermissionTo('restore PengajuanVerifikasi');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any PengajuanVerifikasi');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, PengajuanVerifikasi $pengajuanverifikasi): bool
    {
        return $user->checkPermissionTo('replicate PengajuanVerifikasi');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder PengajuanVerifikasi');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, PengajuanVerifikasi $pengajuanverifikasi): bool
    {
        return $user->checkPermissionTo('force-delete PengajuanVerifikasi');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any PengajuanVerifikasi');
    }
}
