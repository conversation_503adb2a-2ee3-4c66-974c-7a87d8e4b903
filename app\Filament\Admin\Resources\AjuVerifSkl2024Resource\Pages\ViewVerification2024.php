<?php

namespace App\Filament\Admin\Resources\AjuVerifSkl2024Resource\Pages;

use App\Filament\Admin\Resources\AjuVerifSkl2024Resource;
use App\Models\AjuVerifSkl2024;
use App\Models\Pks2024;
use App\Models\User;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Actions\Action as ActionsAction;
use Filament\Forms\Components\{Actions as ComponentsActions, DatePicker, Fieldset, Grid, Group, Placeholder, Section, Select, Tabs, Textarea, TextInput};
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;

class ViewVerification2024 extends ViewRecord
{
	protected static string $resource = AjuVerifSkl2024Resource::class;
	
	public static string | Alignment $formActionsAlignment = Alignment::Right;
	
	public function getTitle(): string | Htmlable
	{
		return 'Verifikasi Akhir Pengajuan SKL';
	}
	
	public function getSubheading(): ?string
	{
		return 'untuk RIPH Periode sebelum Tahun 2025';
	}

	protected function getHeaderActions(): array
	{
		return [
			// Actions\CreateAction::make(),
			ActionsAction::make('back')
				->label('Kembali')
				->color('success')
				->url(fn () => url()->previous()),
		];
	}

	public function form(Form $form): Form
	{
		return $form
		->schema([
			Tabs::make('Tabs')
				->tabs([
					Tab::make('Pemohon')
						->schema([
							Section::make('Pelaku Usaha')
								->aside()
								->relationship('datauser')
								->description('Data Pemohon Surat Keterangan Lunas')
								->schema([
									Placeholder::make('Company')
										->label('Perusahaan')
										->inlineLabel()
										->content(fn ($record) => new HtmlString('<span class="font-bold">'.$record->company_name.'</span>')),
									Placeholder::make('penanggungjawab')
										->label('Penanggung Jawab')
										->inlineLabel()
										->content(fn ($record) => new HtmlString('<span class="font-bold">'.$record->penanggungjawab.'</span>')),
									Placeholder::make('jabatan')
										->label('Jabatan')
										->inlineLabel()
										->content(fn ($record) => new HtmlString('<span class="font-bold">'.$record->jabatan.'</span>')),
									Placeholder::make('nib')
										->label('NIB')
										->inlineLabel()
										->content(fn ($record) => new HtmlString('<span>'.$record->nib_company.'</span>')),
									Placeholder::make('npwp')
										->label('NPWP')
										->inlineLabel()
										->content(fn ($record) => new HtmlString('<span>'.$record->npwp_company.'</span>')),
									Placeholder::make('address_company')
										->label('Alamat Perusahaan')
										->inlineLabel()
										->content(fn ($record) => new HtmlString(
											'<p>'.$record->address_company.'</p>
											<p>'.$record->mykabupaten->nama_kab.' - '.$record->myprovinsi->nama.', '.$record->kodepos.'</p>'
										)),
								]),
						]),
					
					Tab::make('Data RIPH')
						->schema([
							Group::make()
								->relationship('commitment')
								->schema([
									Section::make()
										->aside()
										->description('Ringkasan Data RIPH yang dimohonkan')
										->schema([
											Placeholder::make('no_ijin')
												->label('No. RIPH')
												->inlineLabel()
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.$record->no_ijin.'</span>')),
											Placeholder::make('periodetahun')
												->label('Periode Penerbitan')
												->inlineLabel()
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.$record->periodetahun.'</span>')),
											Placeholder::make('tgl_ijin')
												->label('Mulai Berlaku')
												->inlineLabel()
												->content(fn ($record) => new HtmlString('<span class="font-bold">' . Carbon::parse($record->tgl_ijin)->translatedFormat('d F Y') . '</span>')),
											Placeholder::make('tgl_akhir')
												->label('Berlaku Hingga')
												->inlineLabel()
												->content(fn ($record) => new HtmlString('<span class="font-bold">' . Carbon::parse($record->tgl_akhir)->translatedFormat('d F Y') . '</span>')),
											Placeholder::make('volume_riph')
												->label('Volume RIPH')
												->inlineLabel()
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->volume_riph,2,',','.').' ton </span>')),
										]),
								])
						]),
					
					Tab::make('Komitmen dan Realisasi')
						->schema([
							Group::make()
								->relationship('commitment')
								->schema([
									Section::make('Ringkasan')
										->aside()
										->description('Ringkasan Data Komitmen dan Realisasi')
										->afterStateHydrated(function ($set, $record) {
											// Log::info('Detailed data:', [
											// 	'total_luas' => $record->datarealisasi?->sum('luas_lahan') ?? 'data tidak terlacak',
											// ]);
											$set('datarealisasi', $record?->datarealisasi?->sum('luas_lahan') ?? 'data tidak terlacak');

										})
										->schema([
											Placeholder::make('luas_wajib_tanam')
												->label('Komitmen Tanam')
												->inlineLabel()
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->luas_wajib_tanam,2,',','.').' ha</span>')),
											Placeholder::make('volume_produksi')
												->label('Komitmen Produksi')
												->inlineLabel()
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->volume_produksi,2,',','.').' ton</span>')),
		
											Placeholder::make('realisasiTanam')
												->label('Realisasi Tanam')
												->inlineLabel()
												->hintIcon(fn ($record) => 
													$record->datarealisasi->sum('luas_lahan') < 0.9 * $record->luas_wajib_tanam
														? 'heroicon-s-exclamation-circle'
														: 'heroicon-s-check-circle'
												)
												->hintColor(fn ($record) => 
													$record->datarealisasi->sum('luas_lahan') < 0.6 * $record->luas_wajib_tanam
														? 'danger'
														: ($record->datarealisasi->sum('luas_lahan') < 0.9 * $record->luas_wajib_tanam
															? 'warning'
															: 'success')
												)
												->hintIconTooltip(fn ($record) => 
													$record->datarealisasi->sum('luas_lahan') < 0.6 * $record->luas_wajib_tanam
														? 'Total realisasi tanam tidak realistis'
														: ($record->datarealisasi->sum('luas_lahan') < 0.9 * $record->luas_wajib_tanam
															? 'Total realisasi tanam kurang realistis'
															: null)
												)
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->datarealisasi->sum('luas_lahan'),2,',','.').' ha</span>')),
		
											Placeholder::make('realisasiProduksi')
												->label('Realisasi Produksi')
												->inlineLabel()
												->hintIcon(fn ($record) => 
													$record->datarealisasi->sum('volume') < $record->volume_produksi
														? 'heroicon-s-x-circle'
														: 'heroicon-s-check-circle'
												)
												->hintColor(fn ($record) => 
													$record->datarealisasi->sum('volume') < $record->volume_produksi
														? 'danger' : 'success'
												)
												->hintIconTooltip(fn ($record) => 
													$record->datarealisasi->sum('luas_lahan') < 0.6 * $record->luas_wajib_tanam
														? 'Total realisasi tanam tidak realistis'
														: ($record->datarealisasi->sum('luas_lahan') < 0.9 * $record->luas_wajib_tanam
															? 'Total realisasi produksi kurang realistis'
															: null)
												)
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->datarealisasi->sum('volume'),2,',','.').' ton</span>')),
										]),
		
									Section::make('Kemitraan dan Lokasi')
										->aside()
										->description('Ringkasan Data Kemitraan dan Lokasi Tanam')
										->schema([
											Placeholder::make('Poktan')
												->label('Komitmen Kerjasama')
												->inlineLabel()
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->pks->count(),0,',','.').'  kelompok</span>')),

											Placeholder::make('pks')
												->label('Realisasi Kerjasama')
												->inlineLabel()
												->hintIcon(fn ($record) => 
													$record->pks->count() < $record->pks->count('berkas_pks')
														? 'heroicon-s-x-circle'
														: 'heroicon-s-check-circle'
												)
												->hintColor(fn ($record) => 
													$record->pks->count() < $record->pks->count('berkas_pks')
														? 'warning'
														: 'success'
												)
												->hintIconTooltip(fn ($record) => 
													$record->pks->count() < $record->pks->count('berkas_pks')
														? 'Hanya menjalin kemitraan dengan sejumlah kelompok'
														: null
												)
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->pks->count('berkas_pks'),0,',','.').' kelompok</span>')),

											Placeholder::make('lokasi')
												->label('Anggota/Petani')
												->inlineLabel()
												->hidden()
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->lokasi->unique('anggota_id')->count(),0,',','.').'  anggota</span>'))
												->helperText('Jumlah anggota unik berdasarkan id anggota (anggota yang sama tidak dihitung berulang).'),

											Placeholder::make('anggotaId')
												->label('Realisasi Petani')
												->hidden()
												->inlineLabel()
												->hintIcon(fn ($record) =>
													$record->lokasi->unique('anggota_id')->count() > $record->datarealisasi->unique('anggota_id')->count()
														? 'heroicon-s-x-circle'
														: 'heroicon-s-check-circle'
												)
												->hintColor(fn ($record) => 
													$record->lokasi->unique('anggota_id')->count() > $record->datarealisasi->unique('anggota_id')->count()
														? 'warning'
														: 'success'
												)
												->hintIconTooltip(fn ($record) => 
													$record->pks->count() < $record->pks->count('berkas_pks')
														? 'Jumlah petani yang sama'
														: null
												)
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->datarealisasi->unique('anggota_id')->count(),0,',','.').'  anggota</span>')),

											Placeholder::make('Jumlah Lahan')
												->label('Jumlah Lahan')
												->inlineLabel()
												->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->datarealisasi->count(),0,',','.').'  titik</span>')),
										])
								]),

							Group::make()
								->schema([
									Section::make('Perjanjian Kemitraan')
										->aside()
										->description('Pemeriksaan Berkas dan Data Perjanjian antara Pelaku Usaha dengan Kelompok Tani Mitra.')
										->schema([
											TableRepeater::make('berkaspks')
											->addable(false)
											->deletable(false)
											->hiddenLabel()
											->relationship('pks')
											->streamlined()
											->headers([
												Header::make('Kelompok Tani'),
												Header::make('No. Perjanjian'),
												Header::make('Tautan'),
												Header::make('Status'),
												Header::make('Rinci'),
											])
											->schema([
												Placeholder::make('poktan')
													->hiddenLabel()
													->content(fn($record) => $record->masterpoktan->nama_kelompok),
												Placeholder::make('no_Perjanjian')
													->hiddenLabel()
													->content(fn($record) => $record->no_perjanjian),
												Placeholder::make('Tautan')
													->hiddenLabel()
													->extraAttributes(['class' => 'text-info-500'])
													->content(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

														$tahun = $record->commitment->periodetahun;

														$fileName = $record->berkas_pks;

														if ($fileName) {
															if (str_starts_with($fileName, 'uploads/')) {
																$fileUrl = Storage::url("{$fileName}");
															} else {
																$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
															}

															return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
														}

														return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
													}),
												Placeholder::make('statuspks')
													->hiddenLabel()
													->content(fn($get) => view('components.status-badge-verifikasi', ['status' => $get('status')])),

												ComponentsActions::make([
													Action::make('detail')
														->hiddenLabel()
														->iconButton()
														->color('warning')
														->fillForm(fn (Pks2024 $record): array => [
															'status' => $record->status,
															'note' => $record->note,
														])
														->form([
															Grid::make([
																'default' => 1,
																'sm' => 1,
																'lg' => 2,
															])->schema([
																Group::make([
																	Placeholder::make('poktan')
																		->inlineLabel()
																		->content(fn($record) => $record->masterpoktan->nama_kelompok),
																	Placeholder::make('no_Perjanjian')
																		->inlineLabel()
																		->content(fn($record) => $record->no_perjanjian),
																	Placeholder::make('masa_berlaku')
																		->inlineLabel()
																		->content(
																			fn($record) => ($record->tgl_perjanjian_start ?
																				Carbon::parse($record->tgl_perjanjian_start)->format('d M Y') : '-') .
																				' s.d ' .
																				($record->tgl_perjanjian_end ?
																					Carbon::parse($record->tgl_perjanjian_end)->format('d M Y') : '-')
																		),
																	Placeholder::make('memberCount')
																		->inlineLabel()
																		->label('Anggota')
																		->content(fn($record) => $record->lokasi->count() .' orang'),
																	Placeholder::make('luas_rencana')
																		->inlineLabel()
																		->label('Luas Rencana')
																		->content(fn($record) => number_format($record->lokasi->sum('luas_lahan'), 2, ',', '.') . ' ha'),
																	Placeholder::make('varietas')
																		->inlineLabel()
																		->label('Rencana Varietas')
																		->content(fn($record) => $record->varietas?->nama_varietas ?? '-')

																]),
																Fieldset::make('Kesimpulan Pemeriksaan')
																	->columnSpan(1)
																	->columns(1)
																	->schema([
																		Textarea::make('note')
																			->label('Catatan Pemeriksaan')
																			->required(),
																		Select::make('status')
																			->inlineLabel()
																			->label('Kesimpulan')
																			->required()
																			->options([
																				'sesuai' => 'Sesuai',
																				'perbaikan' => 'Tidak Sesuai/Perbaikan',
																			]),
																	])
															]),
														])
														->icon('icon-layout-text-window-reverse')
														->modal()
														->modalCancelAction(fn($action) => $action->label('Tutup'))
														->modalHeading(fn($record) => 'Detail Perjanjian ' . ($record->no_perjanjian ?? $record->nama_poktan))
														->action(function (array $data, Pks2024 $record): void {
															$record->update([
																'status' => $data['status'] ?? null,
																'note' => $data['note'] ?? null,
															]);
														}),

													Action::make('viewReport')
														->hiddenLabel()
														->tooltip('Lihat Daftar Realisasi')
														->iconButton()
														->icon('heroicon-o-map')
														->url(fn ($record) => route(
															'panel.admin.realisasiReportView',
															['pksId' => Crypt::encryptString($record->id, config('app.qr_secret'))] // Enkripsi dengan key dari config
														)), //encrypt 256 menggunakan key dari config/app 'qr_secret' => env('QR_SECRET', 'B1804bio')
												])->alignCenter(),
												// Placeholder::make('detail')
												//     ->hiddenLabel()
												//     ->extraAttributes(['class'=>'text-center'])
												//     ->content(fn ($record) => $record->no_perjanjian),
											])
										])
									])
						]),
					
					Tab::make('Berkas')
						->schema([
							Group::make()
								->relationship('commitment')
								->schema([
									Section::make('Tahap Tanam')
										->aside()
										->description('Berkas-berkas yang diunggah oleh importir untuk tahap tanam')
										->schema([
											Placeholder::make('formSptjmTanam')
												->label('Form SPTJM Tanam')
												->inlineLabel()
												->content(function ($record) {
													$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

													$tahun = $record->periodetahun;

													$fileName = $record->userDocs->sptjmtanam;

													if ($fileName) {
														if (str_starts_with($fileName, 'uploads/')) {
															$fileUrl =Storage::url("{$fileName}");
														} else {
															$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
														}

														return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
													}

													return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
												}),

											Placeholder::make('logBookTanam')
												->label('Logbook Tanam')
												->inlineLabel()
												->content(function ($record) {
													$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

													$tahun = $record->periodetahun;

													$fileName = $record->userDocs->logbooktanam;

													if ($fileName) {
														if (str_starts_with($fileName, 'uploads/')) {
															$fileUrl =Storage::url("{$fileName}");
														} else {
															$fileUrl = Storage::url("storage/uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
														}

														return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
													}

													return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
												}),

											Placeholder::make('formRta')
												->label('Form RTA')
												->inlineLabel()
												->content(function ($record) {
													$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

													$tahun = $record->periodetahun;

													$fileName = $record->userDocs->rta;

													if ($fileName) {
														if (str_starts_with($fileName, 'uploads/')) {
															$fileUrl =Storage::url("{$fileName}");
														} else {
															$fileUrl = Storage::url("storage/uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
														}

														return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
													}

													return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
												}),

										]),
									Section::make('Tahap Produksi')
										->aside()
										->description('Berkas-berkas yang diunggah oleh importir untuk tahap produksi')
										->schema([
											Placeholder::make('formSptjmProduksi')
												->label('Form SPTJM Produksi')
												->inlineLabel()
												->content(function ($record) {
													$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

													$tahun = $record->periodetahun;

													$fileName = $record->userDocs->sptjmproduksi;

													if ($fileName) {
														if (str_starts_with($fileName, 'uploads/')) {
															$fileUrl =Storage::url("{$fileName}");
														} else {
															$fileUrl = Storage::url("storage/uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
														}

														return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
													}

													return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
												}),

											Placeholder::make('logBookProduksi')
												->label('Logbook Produksi')
												->inlineLabel()
												->content(function ($record) {
													$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

													$tahun = $record->periodetahun;

													$fileName = $record->userDocs->logbookproduksi;

													if ($fileName) {
														if (str_starts_with($fileName, 'uploads/')) {
															$fileUrl =Storage::url("{$fileName}");
														} else {
															$fileUrl = Storage::url("storage/uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
														}

														return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
													}

													return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
												}),

											Placeholder::make('formRpo')
												->label('Form RPO')
												->inlineLabel()
												->content(function ($record) {
													$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

													$tahun = $record->periodetahun;

													$fileName = $record->userDocs->rpo;

													if ($fileName) {
														if (str_starts_with($fileName, 'uploads/')) {
															$fileUrl =Storage::url("{$fileName}");
														} else {
															$fileUrl = Storage::url("storage/uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
														}

														return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
													}

													return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
												}),

											Placeholder::make('formLa')
												->label('Laporan Akhir')
												->inlineLabel()
												->content(function ($record) {
													$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

													$tahun = $record->periodetahun;

													$fileName = $record->userDocs->formLa;

													if ($fileName) {
														if (str_starts_with($fileName, 'uploads/')) {
															$fileUrl =Storage::url("{$fileName}");
														} else {
															$fileUrl = Storage::url("storage/uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
														}

														return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
													}

													return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
												}),
										]),
								])
						]),
					
					Tab::make('Verifikasi')
						->schema([
							Group::make()
								->relationship('commitment')
								->schema([
									Section::make('Verifikasi Tanam')
										->aside()
										->description('Catatan hasil verifikasi realisasi tanam')
										->schema([
											Placeholder::make('verifikatorTanam')
												->label('Petugas Verifikasi')
												->inlineLabel()
												->content(function ($record) {
                                                    $ajutanam = $record->ajutanam()
                                                        ->withoutGlobalScope('npwp')
                                                        ->with('dataadmin')
                                                        ->first();
                                                        
                                                    return new HtmlString(
                                                        '<span class="font-bold">' . 
                                                        ($ajutanam?->dataadmin?->nama ?? '-') . 
                                                        '</span>'
                                                    );
                                                }),

											Placeholder::make('statusVerifTanam')
												->label('Status Verifikasi')
												->inlineLabel()
												->content(function ($record) {
                                                    $ajutanam = $record->ajutanam()
                                                        ->withoutGlobalScope('npwp')
                                                        ->with('dataadmin')
                                                        ->first();

													//status 3 = 'Perbaikan'
													//status 4 = 'Selesai'
													if($ajutanam?->status == '3'){
                                                        $status = 'Perbaikan';
                                                    } else if($ajutanam?->status == '4'){
                                                        $status = 'Selesai';
                                                    } else {
                                                        $status = '-';
                                                    }
                                                        
                                                    return new HtmlString(
                                                        '<span class="font-bold">' . 
                                                        ($status ?? '-') . 
                                                        '</span>'
                                                    );
                                                }),

											Placeholder::make('catatanVerifTanam')
												->label('Catatan Verifikasi')
												->inlineLabel()
												->content(function ($record) {
                                                    $ajutanam = $record->ajutanam()
                                                        ->withoutGlobalScope('npwp')
                                                        ->with('dataadmin')
                                                        ->first();
                                                        
                                                    return new HtmlString(
                                                        '<p class="font-bold">' . 
                                                        ($ajutanam?->note ?? '-') . 
                                                        '</p>'
                                                    );
                                                }),
												Placeholder::make('baVerTan')
													->label('Berita Acara Verifikasi')
													->inlineLabel()
													->extraAttributes(['class' => 'text-info-500'])
													->content(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

														$tahun = $record->periodetahun;

														$fileName = $record->ajutanam()->withoutGlobalScope('npwp')->first()->batanam;

														if ($fileName) {
															if (str_starts_with($fileName, 'uploads/')) {
																$fileUrl = Storage::url("{$fileName}");
															} else {
																$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
															}

															return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
														}

														return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
													}),
												Placeholder::make('notaDinasTanam')
													->label('Nota Dinas Verifikasi')
													->inlineLabel()
													->extraAttributes(['class' => 'text-info-500'])
													->content(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

														$tahun = $record->periodetahun;

														$fileName = $record->ajutanam()->withoutGlobalScope('npwp')->first()->ndhprt;

														if ($fileName) {
															if (str_starts_with($fileName, 'uploads/')) {
																$fileUrl = Storage::url("{$fileName}");
															} else {
																$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
															}

															return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
														}

														return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
													}),
											]),
										Section::make('Verifikasi Produksi')
											->aside()
											->description('Catatan hasil verifikasi realisasi produksi')
											->schema([
												Placeholder::make('verifikatorProduksi')
													->label('Petugas Verifikasi')
													->inlineLabel()
													->content(function ($record) {
														$ajuproduksi = $record->ajuproduksi()
															->withoutGlobalScope('npwp')
															->with('dataadmin')
															->first();
															
														return new HtmlString(
															'<span class="font-bold">' . 
															($ajuproduksi?->dataadmin?->nama ?? '-') . 
															'</span>'
														);
													}),

												//status 3 = 'Perbaikan'
												//Status 4 = 'Sesuai'
												Placeholder::make('statusVerifTanam')
													->label('Status Verifikasi')
													->inlineLabel()
													->content(function ($record) {
														$ajuproduksi = $record->ajuproduksi()
															->withoutGlobalScope('npwp')
															->with('dataadmin')
															->first();

														//status 3 = 'Perbaikan'
														//status 4 = 'Selesai'
														if($ajuproduksi?->status == '3'){
															$status = 'Perbaikan';
														} else if($ajuproduksi?->status == '4'){
															$status = 'Selesai';
														} else {
															$status = '-';
														}
															
														return new HtmlString(
															'<span class="font-bold">' . 
															($status ?? '-') . 
															'</span>'
														);
													}),



												Placeholder::make('catatanVerifProduksi')
													->label('Catatan Verifikasi')
													->inlineLabel()
													->content(function ($record) {
														$ajuproduksi = $record->ajuproduksi()
															->withoutGlobalScope('npwp')
															->with('dataadmin')
															->first();
															
														return new HtmlString(
															'<p class="font-bold">' . 
															($ajuproduksi?->note ?? '-') . 
															'</p>'
														);
													}),
												Placeholder::make('baVerProd')
													->label('Berita Acara Verifikasi')
													->inlineLabel()
													->extraAttributes(['class' => 'text-info-500'])
													->content(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

														$tahun = $record->periodetahun;

														$fileName = $record->ajuproduksi()->withoutGlobalScope('npwp')->first()->baproduksi;

														if ($fileName) {
															if (str_starts_with($fileName, 'uploads/')) {
																$fileUrl = Storage::url("{$fileName}");
															} else {
																$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
															}

															return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
														}

														return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
													}),
												Placeholder::make('notaDinasProduksi')
													->label('Nota Dinas Verifikasi')
													->inlineLabel()
													->extraAttributes(['class' => 'text-info-500'])
													->content(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

														$tahun = $record->periodetahun;

														$fileName = $record->ajuproduksi()->withoutGlobalScope('npwp')->first()->ndhprp;

														if ($fileName) {
															if (str_starts_with($fileName, 'uploads/')) {
																$fileUrl = Storage::url("{$fileName}");
															} else {
																$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
															}

															return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
														}

														return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
													}),
											]),
								])
						]),
				])->contained(false)->activeTab(1),
		])->columns(1);
	}


}
