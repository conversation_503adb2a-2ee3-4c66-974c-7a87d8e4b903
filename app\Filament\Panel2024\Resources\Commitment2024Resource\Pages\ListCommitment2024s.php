<?php

namespace App\Filament\Panel2024\Resources\Commitment2024Resource\Pages;

use App\Filament\Panel2024\Resources\Commitment2024Resource;
use App\Models\ActionLog;
use App\Models\AjuVerifProduksi2024;
use App\Models\AjuVerifSkl2024;
use App\Models\AjuVerifTanam2024;
use App\Models\Commitment2024;
use App\Models\DataRealisasi2024;
use App\Models\Lokasi2024;
use App\Models\MasterAnggota2024;
use App\Models\MasterPoktan2024;
use App\Models\PengajuanVerifikasi;
use App\Models\Pks2024;
use App\Models\UserDocs2024;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\{Action, DeleteAction, EditAction,ViewAction};
use Filament\Tables\Columns\{ColumnGroup, TextColumn, TextInputColumn};
use Filament\Tables\Filters\{SelectFilter,TernaryFilter};
use Filament\Tables\Table;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ListCommitment2024s extends ListRecords
{
    protected static string $resource = Commitment2024Resource::class;
    public function getHeading(): string
	{
        return 'Daftar Komitmen';
	}
    protected function getHeaderActions(): array
    {
        if(Auth::user()->hasRole('importir')){
            return [
                Actions\CreateAction::make()
					->label('Unduh Data')
					->icon('icon-download')
					->url(route('filament.panel2024.resources.commitment2024s.sync')),
            ];
        }
        return [];
    }

	protected function getTableQuery(): Builder
	{
		$user = Auth::user();
		return $user->hasRole('importir')
			? Commitment2024::query()->with([
				'ajutanam' => $user ? fn($query) => $query->withoutGlobalScopes() : fn($query) => $query,
				'ajuproduksi' => $user ? fn($query) => $query->withoutGlobalScopes() : fn($query) => $query,
			])
			: Commitment2024::query();
	}

	public function table(Table $table): Table
	{
		$isImportir = Auth::user()->hasRole('importir');
		return $table
			->columns([
				TextColumn::make('periodetahun')->label('Periode')->searchable()->sortable(),
				TextColumn::make('no_ijin')->label('No. RIPH')->searchable()->sortable(),
				TextColumn::make('nama')
					->label('Perusahaan')
					->hidden(fn () => Auth::user()->hasAnyRole(['importir']))
					->searchable()
					->sortable(),
				TextColumn::make('volume_riph')->label('Volume')
					->suffix(' ton')
					->formatStateUsing(fn($state) => number_format($state, 2, ',', '.')),
				ColumnGroup::make('Komitmen', [
					TextColumn::make('luas_wajib_tanam')
						->suffix(' ha')
						->label('Tanam')
						->formatStateUsing(fn($state) => number_format($state, 2, ',', '.')),
					TextColumn::make('volume_produksi')
						->suffix(' ton')
						->label('Produksi')
						->formatStateUsing(fn($state) => number_format($state, 2, ',', '.')),
				]),

				TextColumn::make('tgl_akhir')
					->label('Akhir Berlaku')
					->visible(fn () => Auth::user()->hasAnyRole(['admin','Super Admin']))
					->badge()
					->color(fn ($record) =>
						!$record->skl && $record->tgl_akhir < now() ? 'danger' :
						(!$record->skl && $record->tgl_akhir >= now() ? 'warning' :
						'success')
					)
					->since(),
				ColumnGroup::make('Status Verifikasi', [
					TextColumn::make('ajutanam.status')
						->label('Tanam')
						->badge()
						->color(fn ($state) => match ($state) {
							'1' => 'primary',   // Diajukan
							'2' => 'warning',   // Diperiksa
							'3' => 'danger',    // Perbaikan
							'4' => 'success',   // Selesai
							default => 'secondary', // Warna default jika status tidak sesuai
						})
						->formatStateUsing(fn ($state) => match ($state) {
							'1' => 'Diajukan',
							'2' => 'Diperiksa',
							'3' => 'Perbaikan',
							'4' => 'Selesai',
							default => 'Tidak Diketahui', // Format default jika status tidak sesuai
						}),

					TextColumn::make('ajuproduksi.status')
						->label('Produksi')
						->badge()
						->color(fn ($state) => match ($state) {
							'1' => 'primary',   // Diajukan
							'2' => 'warning',   // Diperiksa
							'3' => 'danger',    // Perbaikan
							'4' => 'success',   // Selesai
							default => 'secondary', // Warna default jika status tidak sesuai
						})
						->formatStateUsing(fn ($state) => match ($state) {
							'1' => 'Diajukan',
							'2' => 'Diperiksa',
							'3' => 'Perbaikan',
							'4' => 'Selesai',
							default => 'Tidak Diketahui', // Format default jika status tidak sesuai
						}),
					TextColumn::make('ajuskl.status')
						->label('SKL')
						->badge()
						->color(fn ($record) => match ($record->ajuskl?->status) {
							'1' => 'warning',   // Diajukan
							'2' => 'info',      // Direkomendasikan
							'3' => 'primary',   // Antrian TTD
							'4' => $record->completed && $record->completed->url ? 'success' : 'gray', // Proses Penerbitan vs. Diterbitkan
							'5' => 'danger',   // Perbaikan
							default => 'danger' // Status tidak valid atau belum diajukan
						})
						->formatStateUsing(function ($record) {
							if (!$record->ajuskl) {
								return 'Belum Diajukan'; // Jika belum ada pengajuan SKL
							}

							return match ($record->ajuskl->status) {
								'1' => 'Diajukan',
								'2' => 'Direkomendasikan',
								'3' => 'Antrian TTD',
								'4' => $record->completed && $record->completed->url ? 'Terbit' : 'Proses Penerbitan',
								'5' => 'Perbaikan',
								default => 'Status Tidak Dikenal'
							};
						}),
				]),

			])
			->filters([
				SelectFilter::make('periodetahun')
					->label('Periode')
					->options(
						Commitment2024::query()
							->select('periodetahun')
							->whereNotNull('periodetahun') // Filter out null values
							->where('periodetahun', '!=', '') // Filter out empty strings
							->distinct()
							->orderBy('periodetahun', 'desc')
							->pluck('periodetahun', 'periodetahun')
							->filter() // Remove any null/empty values from the collection
							->toArray()
					),
					// ->default($maxYear),
				TernaryFilter::make('skl')
					->label('Status')
					->placeholder('Semua')
					->trueLabel('Lunas')
					->falseLabel('Belum Lunas')
					->queries(
						true: fn (Builder $query) => $query->whereNotNull('skl'),
						false: fn (Builder $query) => $query->whereNull('skl'),
						blank: fn (Builder $query) => $query,
					)
            ])
			->actions([
				ViewAction::make()->iconButton(),
                EditAction::make()->iconButton()->visible(function ($record) {
					return $record->skl != null && self::isOwner($record);
				}),
				DeleteAction::make()
					->iconButton()
					// ->hidden()
					->visible(function ($record) {
						return $record->completed == null && self::isOwner($record);
					})
					// ->visible(fn () => Auth::user()->hasRole('Super Admin'))
					->requiresConfirmation()
					->modalHeading('Hapus Data RIPH')
					->form([
						Textarea::make('reason')
							->label('Alasan Penghapusan')
							->required()
							->autosize()
							->placeholder('Berikan alasan penghapusan data')
							->columnSpanFull()
					])
					->modalDescription(fn ($record) => "Anda akan menghapus data RIPH {$record->no_ijin}. Seluruh data terkait beserta turunannya akan terhapus. Tindakan ini tidak dapat dibatalkan. Anda yakin akan melakukan ini?")
					->modalSubmitActionLabel('Ya, Hapus Semua Data')
					->action(function ($data, Commitment2024 $record):void {
						// Ambil nomor RIPH dan NPWP untuk referensi
						$noIjin = $record->no_ijin;
						$npwp = $record->npwp;

						// Cek apakah nomor RIPH sudah memiliki SKL
						if (AjuVerifSkl2024::where('no_ijin', $noIjin)->exists()) {
							Notification::make()
								->title('Tidak Dapat Menghapus')
								->body("RIPH {$noIjin} sudah memiliki SKL dan tidak dapat dihapus.")
								->danger()
								->send();
							return;
						}

						// Ambil ID Poktan untuk referensi
						$poktanIds = Pks2024::where('no_ijin', $noIjin)
							->where('no_ijin', $noIjin)
							->pluck('poktan_id')
							->toArray();

						// Lakukan force delete langsung
						try {
							DB::beginTransaction();

							// Hapus data verifikasi tanam dan produksi jika ada
							AjuVerifTanam2024::where('no_ijin', $noIjin)->forceDelete();
							AjuVerifProduksi2024::where('no_ijin', $noIjin)->forceDelete();

							// Hapus data realisasi dan data terkait lainnya
							DataRealisasi2024::where([
								'npwp_company' => $npwp,
								'no_ijin' => $noIjin,
							])->forceDelete();

							// Hapus data lokasi
							Lokasi2024::where('npwp', $npwp)
								->where('no_ijin', $noIjin)
								->whereIn('poktan_id', $poktanIds)
								->forceDelete();

							// Hapus data PKS
							Pks2024::where('npwp', $npwp)
								->where('no_ijin', $noIjin)
								->forceDelete();

							// Hapus data master anggota
							MasterAnggota2024::where('npwp', $npwp)
								->whereIn('poktan_id', $poktanIds)
								->forceDelete();

							// Hapus data master poktan
							MasterPoktan2024::where('npwp', $npwp)
								->whereIn('poktan_id', $poktanIds)
								->forceDelete();

							// Hapus data user docs
							UserDocs2024::where('no_ijin', $noIjin)->forceDelete();

							// Create Deletion Log
							ActionLog::create([
								'log_type' => 'Delete',
								'model_type' => get_class($record),
								'model_id' => $record->id,
								'npwp' => $record->npwp,
								'no_ijin' => $record->no_ijin,
								'request_by' => Auth::id(),
								'data' => $data,
								'metadata' => $record->toArray(),
							]);
							// Hapus data commitment
							$record->forceDelete();

							DB::commit();

							Notification::make()
								->title('Data Berhasil Dihapus')
								->body("Data RIPH {$noIjin} beserta semua data terkait telah berhasil dihapus secara permanen.")
								->success()
								->send();
						} catch (\Exception $e) {
							DB::rollBack();
							Notification::make()
								->title('Error')
								->body("Terjadi kesalahan saat menghapus data: {$e->getMessage()}")
								->danger()
								->send();
						}
					}),
				Action::make('verifikasiTanam')
					->iconButton()
					->icon('icon-growing-plant')
					->color('danger')
					->requiresConfirmation()
					->visible(function ($record) {
						if ($record->npwp == Auth::user()->npwp) {

							$dokumenLengkap = $record->userDocs?->spvt &&
											  $record->userDocs?->sptjmtanam &&
											  $record->userDocs?->rta &&
											  $record->userDocs?->sphtanam &&
											  $record->userDocs?->logbooktanam;
							$pksLengkap = $record->pks?->count() > 0;

							$luasLahanTerisi = $record->datarealisasi?->sum('luas_lahan');
							$luasLahanMinimal = $record->volume_produksi / 8;
							$luasLahanWajar = $luasLahanTerisi >= $luasLahanMinimal;

							$statusAjutanam = is_null($record->ajutanam?->status) || $record->ajutanam?->status === '3';
							$statusAjuproduksi = is_null($record->ajuproduksi?->status) || $record->ajuproduksi?->status === '3';

							if ($dokumenLengkap && $pksLengkap && $luasLahanWajar && $statusAjutanam && $statusAjuproduksi) {
								return true;
							}
						}

						return false;
					})
					->tooltip('Ajukan Verifikasi Tanam')
					->modal()
					->modalWidth(MaxWidth::Medium)
					->modalHeading('Verifikasi Tanam')
					->modalDescription('Ajukan permohonan verifikasi tanam. Pastikan seluruh syarat telah dipenuhi. Lanjutkan?')
					->modalSubmitActionLabel('Ajukan')
					->action(function (Model $record): void {
						$record->ajutanam()->updateOrCreate(
							['no_ijin' => $record->no_ijin],
							[
								'commitment_id' => $record->id,
								'no_ijin' => $record->no_ijin,
								'status' => '1',
								'npwp' => $record->npwp,
								'created_at' => today()
							]
						);

						ActionLog::create([
							'log_type' => 'ajuTanam',
							'model_type' => get_class($record),
							'model_id' => $record->id,
							'npwp' => $record->npwp,
							'no_ijin' => $record->no_ijin,
							'request_by' => Auth::id(),
							'data' => [
								'reason' => 'Pengajuan Verifikasi Tanam',
								'status' => '1',
							],
							'metadata' => $record->toArray(),
						]);

						Notification::make()
							->title('Verifikasi Tanam Diajukan')
							->success()
							->send();
					}),
				Action::make('verifikasiPanen')
					->iconButton()
					->icon('icon-garlic-fill')
					->color('danger')
					->requiresConfirmation()
					->visible(function ($record) {
						if ($record->npwp == Auth::user()->npwp) {
							//syarat kelengkapan dokumen pendukung
							$dokumenLengkap = $record->userDocs?->spvt &&
											  $record->userDocs?->sptjmtanam &&
											  $record->userDocs?->rta &&
											  $record->userDocs?->sphtanam &&
											  $record->userDocs?->logbooktanam &&
											  $record->userDocs?->spvp &&
											  $record->userDocs?->sptjmproduksi &&
											  $record->userDocs?->rpo &&
											  $record->userDocs?->sphproduksi &&
											  $record->userDocs?->logbookproduksi &&
											  $record->userDocs?->formLa;
							//syarat kelengkapan pks
							$pksLengkap = $record->pks?->count() > 0;
							//syarat menanam dihitung dengan rata-rata 8 ton per hektar sehingga minimum luas wajib tanam dapat dikurangi
							$luasLahanTerisi = $record->datarealisasi?->sum('luas_lahan');
							$luasLahanMinimal = $record->volume_produksi / 8;
							$luasLahanWajar = $luasLahanTerisi >= $luasLahanMinimal;

							//syarat minimum produksi di set 95%
							$volumePanen = $record->datarealisasi?->sum('volume') >= 0.95 * $record->volume_produksi;
							// $statusAjutanam = is_null($record->ajutanam?->status) || $record->ajutanam->status === '3';

							//hanya jika belum memiliki status atau jika status verifikasi dikembalikan
							$statusAjuproduksi = is_null($record->ajuproduksi?->status) || $record->ajuproduksi?->status === '3';

							if ($dokumenLengkap && $pksLengkap && $luasLahanWajar && $volumePanen && $statusAjuproduksi) {
								return true;
							}
						}

						return false;
					})
					->tooltip('Ajukan Verifikasi Produksi')
					->modal()
					->modalWidth(MaxWidth::Medium)
					->modalHeading('Verifikasi Produksi')
					->modalDescription('Ajukan permohonan verifikasi produksi. Pastikan seluruh syarat telah dipenuhi. Lanjutkan?')
					->modalSubmitActionLabel('Ajukan')
					->action(function (Model $record): void {
						$record->ajuproduksi()->updateOrCreate(
							['no_ijin' => $record->no_ijin],
							[
								'commitment_id' => $record->id,
								'no_ijin' => $record->no_ijin,
								'status' => '1',
								'npwp' => $record->npwp,
								'created_at' => today()
							]
						);
						

						ActionLog::create([
							'log_type' => 'ajuProduksi',
							'model_type' => get_class($record),
							'model_id' => $record->id,
							'npwp' => $record->npwp,
							'no_ijin' => $record->no_ijin,
							'request_by' => Auth::id(),
							'data' => [
								'reason' => 'Pengajuan Verifikasi Produksi',
								'status' => '1',
							],
							'metadata' => $record->toArray(),
						]);
						Notification::make()
							->title('Verifikasi Produksi Diajukan')
							->success()
							->send();
					}),

				Action::make('perbaikanSkl')
					->iconButton()
					->icon('heroicon-o-wrench-screwdriver')
					->color('danger')
					->modal()
					->modalWidth(MaxWidth::Medium)
					->modalHeading('Catatan Perbaikan')
					->modalDescription(fn ($record) => $record->ajuskl?->note)
					->visible(function ($record) {
						if ($record->npwp == Auth::user()->npwp) {
							$ajuskl = $record->ajuskl()->withoutGlobalScopes()->first();
							if ($ajuskl && $ajuskl->status === '5') {
								return true;
							}
						}
						return false;
					})
					->tooltip('Lihat Catatan Perbaikan')
					->action(function ($record) {
                        return redirect(route('filament.panel2024.resources.commitment2024s.edit', $record->id));
                    }),


				Action::make('ajuSkl')
					->label('Aju SKL')
					->hiddenLabel()
					->visible(function ($record) {
						return self::allowToApplySKL($record);
					})
					->tooltip('Ajukan SKL')
					->color('danger')
					->icon('icon-award-fill')
					->requiresConfirmation()
					->modalHeading('Ajukan Penerbitan SKL')
					->modalDescription('Anda akan mengajukan penerbitan Surat Keterangan Lunas, lanjutkan?')
					->action(function ($record) {
						DB::beginTransaction();
						try {
							$pengajuan = AjuVerifSkl2024::updateOrCreate(
                                [
                                    'no_ijin' => $record->no_ijin,
                                ],
                                [
                                    'npwp' => $record->npwp,
                                    'commitment_id' => $record->id,
                                    'status' => 1,
                                ]
                            );

							$newId = $pengajuan->id;
							$noAju = $pengajuan->no_pengajuan;
							
							ActionLog::create([
								'log_type' => 'ajuSkl',
								'model_type' => get_class($record),
								'model_id' => $record->id,
								'npwp' => $record->npwp,
								'no_ijin' => $record->no_ijin,
								'request_by' => Auth::id(),
								'data' => [
									'reason' => 'Pengajuan SKL',
									'status' => '1',
								],
								'metadata' => $record->toArray(),
							]);
							DB::commit();
							Notification::make()
								->title('Pengajuan Keterangan Lunas')
								->body('Permohonan Penerbitan SKL berhasil diajukan, dengan Nomor Pengajuan: ')
								->success()
								->send();

							// return redirect(route('filament.panel2025.resources.pengajuan-verifikasis.verifReport', ['record' => $newId]));
						} catch (\Exception $e) {
							DB::rollBack();
							Notification::make()
								->title('Pengajuan SKL')
								->body('Terjadi kesalahan: ' . $e->getMessage())
								->danger()
								->send();
						}
					}),
			]);
	}

	protected function paginateTableQuery(Builder $query): Paginator
	{
		return $query->simplePaginate(($this->getTableRecordsPerPage() === 'all') ? $query->count() : $this->getTableRecordsPerPage());
	}

	public static function isOwner($record)
	{
		return Auth::user()->hasRole('importir') && $record->user_id === Auth::id();
	}

	public static function allowToApplySKL($record)
	{
		if (!self::isOwner($record)) {
			return false; // Hanya pemilik yang bisa mengajukan
		}

		$ajuproduksi = $record->ajuproduksi()->withoutGlobalScopes()->first();
		$ajuskl = $record->ajuskl()->withoutGlobalScopes()->first();
		$completed = $record->completed()->first();

		if ($completed) {
			return false; // Jika sudah memiliki SKL, tidak bisa mengajukan lagi
		}

		// Status produksi harus 4, tetapi pastikan ajuskl tidak berstatus 4
		if ($ajuproduksi && $ajuproduksi->status === '4' && (!$ajuskl || $ajuskl->status === '5')) {
			return true;
		}

		return false; // Jika semua kondisi tidak terpenuhi, tidak bisa mengajukan
	}

}
