<?php

namespace App\Filament\Pages\Auth;

use App\Http\Responses\LoginResponse;
use App\Models\DataUser;
use App\Models\User;
use DanH<PERSON>rin\LivewireRateLimiting\Exceptions\TooManyRequestsException;
use DiogoGPinto\AuthUIEnhancer\Pages\Auth\Concerns\HasCustomLayout;
use Filament\Facades\Filament;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
// use Filament\Http\Responses\Auth\Contracts\LoginResponse;
use Filament\Pages\Auth\Login as BasePage;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Illuminate\Validation\ValidationException;
use Spatie\Permission\Models\Role;

class Login extends BasePage
{
    use HasCustomLayout;

    public function mount(): void
    {
        parent::mount();

        $this->form->fill([
            // 'email' => '<EMAIL>',
            // 'password' => 'superadmin',
            'login' => '',
            'password' => '',
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                // $this->getEmailFormComponent()->label('Email'),
                $this->getLoginInformation(),
                $this->getLoginComponent(),
                $this->getPasswordFormComponent()->helperText(new HtmlString('<span class="text-xs">Password Anda.</span>')),
                $this->getRememberFormComponent(),
            ]);
    }

    protected function getLoginInformation(): Component
    {
        return Placeholder::make('Information')
            ->extraAttributes(['class' => 'text-xs text-center', 'style' => 'color: #ff5467;'])
            ->hiddenLabel()
            ->content(new HtmlString('<ul class="list-none" style="line-height: 0.9rem;">
                    <li><span class="font-bold">Untuk IMPORTIR</span><p>. Jika Anda baru menggunakan aplikasi ini, gunakan kredensial (username, kata sandi) yang sama dengan yang Anda gunakan di aplikasi (SIAP)RIPH.</p></li>
                    </ul>
            '));
    }

    protected function getLoginComponent(): Component
    {
        return TextInput::make('login')
            ->label('Masuk')
            ->helperText(new HtmlString('<span class="text-xs">email atau username.</span>'))
            ->required()
            ->autocomplete()
            ->autofocus()
            ->extraInputAttributes(['tabindex' => 1]);
    }

    protected function getCredentialsFromFormData(array $data): array
    {
        $login_type = filter_var($data['login'], FILTER_VALIDATE_EMAIL ) ? 'email' : 'username';
        return [
            $login_type => $data['login'],
            'password'  => $data['password'],
        ];
    }

    protected function throwFailureValidationException(): never
    {
        throw ValidationException::withMessages([
            'data.login' => 'Kredensial yang diberikan tidak dapat ditemukan',
        ]);
    }

    public function authenticate(): ?LoginResponse
    {
        try {
            $this->rateLimit(5);
        } catch (TooManyRequestsException $exception) {
            $this->getRateLimitedNotification($exception)?->send();
            return null;
        }
    
        try {
            $data = $this->form->getState();
            try {
                if (!Filament::auth()->attempt($this->getCredentialsFromFormData($data), $data['remember'] ?? false)) {
                    try {
                        if (!$this->attemptSoapAuthentication($data['login'], $data['password'])) {
                            Notification::make()
                                ->title('Login Gagal')
                                ->body('Autentikasi gagal. Periksa kembali kredensial Anda.')
                                ->danger()
                                ->send();
                            throw ValidationException::withMessages([
                                'data.login' => 'Data Tidak ditemukan',
                            ]);
                        }
                    } catch (\Exception $e) {
                        throw ValidationException::withMessages([
                            'data.login' => 'Kesalahan teknis saat autentikasi SOAP.',
                        ]);
                    }
    
                    try {
                        if (!Filament::auth()->attempt($this->getCredentialsFromFormData($data), $data['remember'] ?? false)) {
                            throw ValidationException::withMessages([
                                'data.login' => 'Check Point Error (2)',
                            ]);
                        }
                    } catch (\Exception $e) {
                        throw ValidationException::withMessages([
                            'data.login' => 'Kesalahan teknis saat login ulang.',
                        ]);
                    }
                }
            } catch (\Exception $e) {
                throw ValidationException::withMessages([
                    'data.login' => 'Kesalahan teknis saat autentikasi.',
                ]);
            }
            return app(LoginResponse::class);
    
        } catch (\Exception $e) {
            throw ValidationException::withMessages([
                'data.login' => 'Kesalahan teknis saat proses login. Silakan coba lagi.',
            ]);
        }
    }
    

    protected function attemptSoapAuthentication(string $email, string $password): bool
    {
        try {
            $options = [
                'soap_version' => SOAP_1_1,
                'exceptions' => true,
                'trace' => 1,
                'cache_wsdl' => WSDL_CACHE_MEMORY,
                'connection_timeout' => 25,
                'style' => SOAP_RPC,
                'use' => SOAP_ENCODED,
            ];
            $client = new \SoapClient('https://riph.pertanian.go.id/api.php/simethris?wsdl', $options);

            $parameter = [
                'user' => 'simethris',
                'pass' => 'wsriphsimethris',
                'user_riph' => $this->data['login'],
                'pass_riph' => $this->data['password'],
            ];

            $response = $client->__soapCall('get_akses', $parameter);

            if (!$response) {
				Log::warning('Empty SOAP response');
                return false;
            }

            $res = simplexml_load_string($response);
            if ((string)$res->return_cek === 'R99') {
				Log::warning('Authentication failed: R99 response');
                return false;
            }

            if ((string)$res->return_cek === 'R00') {
                $npwp = (string)$res->riph->company_profile->npwp;
                $mask = "%s%s.%s%s%s.%s%s%s.%s-%s%s%s.%s%s%s";
                $formattedNpwp = vsprintf($mask, str_split($npwp));

                $userData = [
                    'npwp' => $formattedNpwp,
                    'username' => $this->data['login'],
                    'password' => Hash::make($this->data['password']),
                    'name' => (string) $res->riph->user_profile->nama,
                    'email' => (string) $res->riph->user_profile->email,
                    'email_verified_at' => now(),
                    'status' => 'Aktif',
					'nib' => (string) $res->riph->company_profile->nib,
                ];

                $companyData = [
                    'name' => (string) $res->riph->user_profile->nama,
                    'mobile_phone' => (string) $res->riph->user_profile->telepon,
                    'ktp' => (string) $res->riph->user_profile->ktp,
                    'npwp_company' => $formattedNpwp,
                    'nib_company' => (string) $res->riph->company_profile->nib,
                    'company_name' => (string) $res->riph->company_profile->nama,
                    'address_company' => (string) $res->riph->company_profile->alamat,
                    'provinsi' => (string) $res->riph->company_profile->kdprop,
                    'kabupaten' => (string) $res->riph->company_profile->kdkab,
                    'kodepos' => (string) $res->riph->company_profile->kodepos,
                    'fix_phone' => (string) $res->riph->company_profile->telepon,
                    'fax' => (string) $res->riph->company_profile->fax,
                    'email_company' => (string) $res->riph->company_profile->email,
                    'penanggungjawab' => (string) $res->riph->company_profile->penanggung_jawab,
                    'jabatan' => (string) $res->riph->company_profile->jabatan,
                ];

				Log::info('Company data: ' . json_encode($companyData));

                DB::beginTransaction();

                try{
                    $user = User::firstOrCreate(
                        [
                            'email' => $userData['email'],
                            'npwp' => $userData['npwp']
                        ],
                        $userData,
                    );

                    if (!$user) {
                        Log::error('Failed to create/update user');
                        throw new \Exception('Gagal menyimpan user.');
                    }
                    $user->update([
                        'password' => Hash::make($this->data['password']),
                    ]);

					$role = Role::where('name', 'importir')
                       ->where('guard_name', 'web')
                       ->first();

					$user->assignRole($role);

                    $datauser = DataUser::updateOrCreate(
                        [
                            'user_id' => $user->id,
                            'npwp_company' => $userData['npwp']
                        ],
                        $companyData,
                    );

                    if (!$datauser) {
                        Log::error('Failed to create/update DataUser');
                        throw new \Exception('Gagal menyimpan DataUser.');
                    }
                    DB::commit();
                    return true;
                }catch (\Exception $e){
                    Log::error('Transaction failed: ' . $e->getMessage());
					Log::error('Stack trace: ' . $e->getTraceAsString());
                    DB::rollBack();
                    return false;
                }

            }
			Log::warning('Unexpected response code: ' . (string)$res->return_cek);
			return false;
        } catch (\Exception $e) {
            Log::error('SOAP Authentication error: ' . $e->getMessage());
			Log::error('Stack trace: ' . $e->getTraceAsString());
            return false;
        }
    }

    public function getHeading(): string | Htmlable
    {
        return '';
    }
}
