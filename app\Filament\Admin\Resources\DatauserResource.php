<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\DatauserResource\Pages;
use App\Filament\Admin\Resources\DatauserResource\RelationManagers;
use App\Models\DataUser;
use App\Models\MasterKabupaten;
use App\Models\MasterProvinsi;
use App\Models\User;
use Filament\Facades\Filament;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms;
use Filament\Forms\Components\{Actions, Fieldset, Group, Hidden, Section, TextInput, Select};
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class DatauserResource extends Resource
{
    protected static ?string $model = DataUser::class;
	protected static ?string $navigationGroup = 'Data Induk';
	protected static ?int $navigationSort = 0;
    protected static ?string $title = 'Data Pengguna';

    public static function getPluralModelLabel(): string
    {
        return Auth::user()->hasAnyRole(['Super Admin']) ? 'Pelaku Usaha' : static::$pluralModelLabel ?? 'Profile Anda';
    }

    protected static ?string $pluralModelLabel = 'Profile Anda';
    // protected static ?string $navigationLabel = 'Profile Pengguna';
	public static function getNavigationLabel(): string
	{
        return Auth::user()->hasRole('Super Admin') ? 'Perusahaan' : static::$pluralModelLabel ?? 'Profile Anda';
	}


    protected static ?string $navigationIcon = 'heroicon-o-building-office-2';

	public static function getGloballySearchableAttributes(): array
	{
		return ['user.name', 'company_name', 'penanggungjawab'];
	}

	public static function getGlobalSearchResultDetails(Model $record): array
	{
		return [
			'Pengguna' => $record->user->name,
			'Perusahaan' => $record->company_name,
			'Penanggungjawab' => $record->penanggungjawab,
		];
	}

    public static function form(Form $form): Form
    {
        return $form
        ->schema([
            Select::make('User')
                ->searchable()
                ->visible(fn(): bool => Auth::user()->hasRole('super_admin'))
                ->preload()
                ->reactive()
                ->options(
                    User::pluck('name', 'id')->toArray()
                )
                ->afterStateUpdated(function ($set, $state) {
                    $user = Auth::user()->roles;
                    $role = $user->id;
                    dd($role);
                    if ($state) {
                        $user = User::find($state);
                        if ($user) {
                            $set('test_username', $user->username);
                            $set('user_id', $user->id);
                            // $set('test_password', $user->password);
                        }
                    } else {
                        $set('test_username', null);
                        $set('user_id', null);
                        // $set('test_password', null);
                    }
                }),
            Hidden::make('visible'),
            Hidden::make('me')
                ->afterStateHydrated(function ($set, $state){
                    $user = Auth::user();
                    $userName = $user->username;
                    $set('me', $userName);
                }),
            Group::make()
                ->columnStart(2)
                ->schema([
                ]),
            
            Section::make('Katakunci')
                ->aside()
                ->description('Masukkan katakunci yang Anda gunakan untuk mengakses aplikasi/sistem SIAPRIPH.')
                ->schema([
                    TextInput::make('test_password')
                        ->password()
                        ->label('Katakunci')
                        ->inlineLabel()
                        ->dehydrateStateUsing(fn(string $state): string => Hash::make($state))
                        ->dehydrated(fn(?string $state): bool => filled($state))
                        ->revealable()
                        ->required(),
        
                    Actions::make([
                        Action::make('fetchProfile')
                            ->label('Ambil Data')
                            ->icon('heroicon-m-arrow-down-tray')
                            ->requiresConfirmation()
                            ->modalHeading('Penyelarasan Profil Pengguna')
                            ->modalDescription('Anda akan melakukan penyelarasan data profil pengguna Anda dengan aplikasi RIPH, lanjutkan?.')
                            ->modalSubmitActionLabel('Ya, Lanjutkan')
                            ->action(function ($get, $set) {
                                try {
                                    $options = array(
                                        'soap_version' => SOAP_1_1,
                                        'exceptions' => true,
                                        'trace' => 1,
                                        'cache_wsdl' => WSDL_CACHE_MEMORY,
                                        'connection_timeout' => 25,
                                        'style' => SOAP_RPC,
                                        'use' => SOAP_ENCODED,
                                    );
        
                                    $client = new \SoapClient('https://riph.pertanian.go.id/api.php/simethris?wsdl', $options);
                                    $parameter = array(
                                        'user' => 'simethris',
                                        'pass' => 'wsriphsimethris',
                                        'user_riph' => $get('me'),
                                        'pass_riph' => $get('test_password')
                                    );
                                    $response = $client->__soapCall('get_akses', $parameter);
                                } catch (\Exception $e) {
                                    return Notification::make()
                                        ->title('Authentication Error')
                                        ->body($e->getMessage())
                                        ->danger()
                                        ->persistent()
                                        ->send();
                                }
        
                                if ($response) {
                                    $res = simplexml_load_string($response);
        
                                    if ($res === false) {
                                        Notification::make()
                                            ->title('Response Error')
                                            ->body('Invalid XML Response for user: '. Auth::user()->id)
                                            ->danger()
                                            ->persistent()
                                            ->send();
                                        return response()->json([
                                            'success' => false,
                                            'message' => 'Invalid XML Response for user: '. Auth::user()->id,
                                        ], 400);
                                    }
        
                                    if ((string)$res->return_cek === 'R00') {
                                        Notification::make()
                                            ->title('Profil Pengguna')
                                            ->body('Data berhasil ditemukan dan diunduh')
                                            ->success()
                                            ->persistent()
                                            ->send();
                                        $xmlContent = $response;
                                        $fileName = 'riph_profile_' . $get('me') . '.xml';
                                        $directory = 'uploads/';
                                        if (!Storage::disk('public')->exists($directory)) {
                                            Storage::disk('public')->makeDirectory($directory);
                                        }
                                        Storage::disk('public')->put($directory . $fileName, $xmlContent);
                                        $fileUrl = asset('storage/' . $directory . $fileName);
                                        $userData = [
                                            'name' => (string) $res->riph->user_profile->nama,
                                            'mobile_phone'  => (string) $res->riph->user_profile->telepon,
                                            'email'  => (string) $res->riph->user_profile->email,
                                            'ktp'  => (string) $res->riph->user_profile->ktp,
                                        ];
        
                                        $npwp = (string)$res->riph->company_profile->npwp;
                                        $mask = "%s%s.%s%s%s.%s%s%s.%s-%s%s%s.%s%s%s";
                                        $formattedNpwp = vsprintf($mask, str_split($npwp));
        
                                        $companyData = [
                                            'npwp_company'  => (string) $formattedNpwp,
                                            'nib_company'   => (string) $res->riph->company_profile->nib,
                                            'company_name'  => (string) $res->riph->company_profile->nama,
                                            'address_company' => (string) $res->riph->company_profile->alamat,
                                            'provinsi'      => (string) $res->riph->company_profile->kdprop,
                                            'kabupaten'     => (string) $res->riph->company_profile->kdkab,
                                            'kodepos'     => (string) $res->riph->company_profile->kodepos,
                                            'fix_phone'     => (string) $res->riph->company_profile->telepon,
                                            'fax'     => (string) $res->riph->company_profile->fax,
                                            'email_company' => (string) $res->riph->company_profile->email,
                                            'penanggungjawab' => (string) $res->riph->company_profile->penanggung_jawab,
                                            'jabatan'       => (string) $res->riph->company_profile->jabatan,
                                        ];
        
                                        $set('name', $userData['name']);
                                        $set('mobile_phone', $userData['mobile_phone']);
                                        $set('email', $userData['email']);
                                        $set('ktp', $userData['ktp']);
        
                                        $set('npwp_company', $companyData['npwp_company']);
                                        $set('nib_company', $companyData['nib_company']);
                                        $set('company_name', $companyData['company_name']);
                                        $set('address_company', $companyData['address_company']);
                                        $set('provinsi', $companyData['provinsi']);
                                        $set('kabupaten', $companyData['kabupaten']);
                                        $set('kodepos', $companyData['kodepos']);
                                        $set('fix_phone', $companyData['fix_phone']);
                                        $set('fax', $companyData['fax']);
                                        $set('email_company', $companyData['email_company']);
                                        $set('penanggungjawab', $companyData['penanggungjawab']);
                                        $set('jabatan', $companyData['jabatan']);
                                        $set('visible', 'visible');
        
                                        return response()->json([
                                            'success'  => true,
                                            'data'     => $userData,
                                            'xml_file' => $fileUrl,
                                        ]);
                                    } else {
                                        Notification::make()
                                            ->title('Authentication Error')
                                            ->body('Katakunci yang diberikan tidak sesuai.')
                                            ->danger()
                                            ->persistent()
                                            ->send();
                                        return response()->json([
                                            'success' => false,
                                            'message' => 'Response contains errors',
                                        ], 400);
                                    }
                                }
                            }),
                    ])->columnStart(2),
                ]),

            Section::make('Data Pengguna')
                ->aside()
                ->description('Data Perusahaan ditemukan dan berhasil diunduh.')
                ->visible(fn ($get) => $get('visible') === 'visible' || (Auth::user() && Auth::user()->datauser))
                ->schema([
                    Hidden::make('user_id')
                        ->afterStateHydrated(function ($set, $state){
                            $user = Auth::user();
                            $userId = $user->id;
                            $set('user_id', $userId);
                        }),
                    TextInput::make('name')->label('Nama Lengkap')->inlineLabel()->columnSpanFull(),
                    TextInput::make('mobile_phone')->label('No. HP')->inlineLabel()->columnSpanFull(),
                    TextInput::make('email')->readOnly()->inlineLabel()->columnSpanFull(),
                    TextInput::make('ktp')->label('NIK')->inlineLabel()->columnSpanFull(),
                ]),

            Section::make('Data Perusahaan')
                ->aside()
                ->description('Data Perusahaan ditemukan dan berhasil diunduh.')
                ->visible(fn ($get) => $get('visible') === 'visible' || (Auth::user() && Auth::user()->datauser))
                ->schema([
                    TextInput::make('npwp_company')->label('NPWP')->readOnly()->inlineLabel()->columnSpanFull(),
                    TextInput::make('nib_company')->label('NIB')->readOnly()->inlineLabel()->columnSpanFull(),
                    TextInput::make('company_name')->label('Nama Perusahaan')->inlineLabel()->columnSpanFull(),
                    TextInput::make('address_company')->label('Alamat')->inlineLabel()->columnSpanFull(),
                    Select::make('provinsi')
                        ->searchable()
                        ->options([
                            MasterProvinsi::query()
                            ->pluck('nama', 'provinsi_id')
                            ->toArray()
                        ])
                        ->reactive()
                        ->inlineLabel()
                        ->columnSpanFull()
                        ->afterStateUpdated(fn (callable $set, $state) =>
                            $set('kabupaten', null)
                        ),
                    Select::make('kabupaten')
                        ->searchable()
                        ->preload()
                        ->options(fn ($get) =>
                            MasterKabupaten::where('provinsi_id', $get('provinsi'))
                                ->pluck('nama_kab', 'kabupaten_id')
                                ->toArray()
                        )
                        ->inlineLabel()
                        ->reactive()
                        ->columnSpanFull(),
                    TextInput::make('kodepos')->inlineLabel()->columnSpanFull(),
                    TextInput::make('fix_phone')->label('No. Telp')->inlineLabel()->columnSpanFull(),
                    TextInput::make('fax')->inlineLabel()->columnSpanFull(),
                    TextInput::make('email_company')->label('Email Perusahaan')->readOnly()->inlineLabel()->columnSpanFull(),
                    TextInput::make('penanggungjawab')->inlineLabel()->columnSpanFull(),
                    TextInput::make('jabatan')->inlineLabel()->columnSpanFull(),
                ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        $isImportir = Auth::user()->hasRole('importir');
        return $table
            ->paginated(!$isImportir)
            ->columns([
				TextColumn::make('company_name')->label('Pelaku Usaha')->searchable(),
                TextColumn::make('penanggungjawab')->label('Penanggungjawab')->searchable()
					->description(fn ($record) => $record->jabatan),
				TextColumn::make('myprovinsi.nama')->label('Provinsi'),
            ])
            ->filters([
                //
            ])
            ->actions([
                // Tables\Actions\EditAction::make()
				// 	->modalHeading('Profile')
				// 	->iconButton()->tooltip('Ubah Data'),
                Tables\Actions\ViewAction::make()->iconButton(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDatausers::route('/'),
            'create' => Pages\CreateDatauser::route('/create'),
            // 'edit' => Pages\EditDatauser::route('/{record}/edit'),
            'view' => Pages\ViewDatauser::route('/{record}/view'),
        ];
    }

    public static function shouldRegisterNavigation(): bool
    {
        $user = Auth::user();
        if ($user->hasAnyRole(['Super Admin', 'admin'])) {
			return true;
        }else{
        	return false;
        }
    }
	
}
