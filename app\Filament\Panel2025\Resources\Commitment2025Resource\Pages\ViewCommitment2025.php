<?php

namespace App\Filament\Panel2025\Resources\Commitment2025Resource\Pages;

use App\Filament\Panel2025\Resources\Commitment2025Resource;
use App\Models\Commitment2025;
use App\Models\CommitmentRegion;
use App\Models\MasterSpatial;
use App\Models\Pks2025;
use App\Models\Realisasi2025;
use App\Models\Varietas;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
// use Filament\Actions;
use Filament\Actions\Action;
use Filament\Forms\Components\Actions\Action as ActionsAction;
use Filament\Forms\Components\{Actions, DatePicker, Fieldset, FileUpload, Group, Hidden, Placeholder, Repeater, Section, Select, TextInput};
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ContentTabPosition;
use Filament\Resources\Pages\ViewRecord;
use Filament\Support\Enums\ActionSize;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

use function Pest\Laravel\options;

class ViewCommitment2025 extends ViewRecord
{
    protected static string $resource = Commitment2025Resource::class;

    public function getHeading(): string
	{
        return 'Realisasi Komitmen';
	}
    protected static ?string $title = 'Lihat Data PPRK';
    // protected static string $view = 'realisasi.view-map';

    public function getSubheading(): ?string
    {
        $noIjin = $this->record ? $this->record->no_ijin : '##';
        return 'PPRK No: ' . $noIjin;
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('efilling')
                ->visible(function ($record) {
					return self::isOwner($record) && !self::checkPksCompletion($record);
                })
                ->icon('heroicon-s-map')
                ->label('Pilih Lokasi')
				->url(fn($record) => '/panel/2025/location-picker-page?noRiph=' . preg_replace('/[\/.\-]/', '', $record->no_ijin))
        ];
    }

    public function form(Form $form): Form
	{
		return $form
		->schema([
            Section::make('Data Komitmen')
                ->aside()
                ->description('Data Komitmen sesuai PPRK')
                ->schema([
                    Placeholder::make('komoditas')
                        ->label('Komoditas')
                        ->inlineLabel()
                        ->content(fn ($record) => $record->no_hs),
                    Placeholder::make('kuota')
                        ->label('Volume Import')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->volume_riph,0,',','.') .' ton'),
                    Placeholder::make('wajibproduksi')
                        ->label('Komitmen Produksi')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->volume_produksi,0,',','.') .' ton'),
                    Placeholder::make('wajibtanam')
                        ->label('Komitmen Tanam')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->luas_wajib_tanam,0,',','.').' m2'),
                    Placeholder::make('mulai')
                            ->label('Tgl Ijin')
                            ->inlineLabel()
							->content(fn ($record) => Carbon::parse($record->tgl_ijin)->translatedFormat('d F Y')),
                    Placeholder::make('akhir')
                        ->label('Tgl Berakhir')
                        ->inlineLabel()
						->content(fn ($record) => Carbon::parse($record->tgl_akhir)->translatedFormat('d F Y')),
                ]),

            Section::make('Data Wilayah')
                ->aside()
                ->description('Wilayah Kabupaten rencana lokasi pelaksanaan komitmen tanam-produksi')
                ->schema([
                    TableRepeater::make('wilayah')
                        ->label('Kabupaten')
                        ->addable(false)
                        ->reorderable(false)
                        ->relationship('myRegions')
                        ->headers([
                            Header::make('Kabupaten')->width('25%'),
                            Header::make('Luas Tersedia')->width('25%'),
                            Header::make('Realisasi')->width('25%'),
                            Header::make('')->width('5%'),
                        ])
                        ->schema([
                            TextInput::make('kabupaten')
                                ->formatStateUsing(fn ($record) => $record->kabupaten->nama_kab)
                                ->hiddenLabel(),
                            TextInput::make('quota')
                                ->hiddenLabel()
                                ->extraInputAttributes(['class'=>'text-end'])
								->formatStateUsing(fn ($state) => number_format($state,0,',','.'))
                                ->suffix('m2'),
                            TextInput::make('fullfilled')
                                ->hiddenLabel()
                                ->extraInputAttributes(['class'=>'text-end'])
								->formatStateUsing(fn ($state) => number_format($state,0,',','.'))
                                ->suffix('m2'),
                            Actions::make([
                                ActionsAction::make('Recalculate')
                                    ->hiddenLabel()
                                    ->tooltip('Hitung rekapitulasi realisasi di kabupaten ini')
                                    ->icon('icon-arrow-clockwise')
                                    ->iconButton()
                                    ->visible(fn ()=> Auth::user()->hasRole('importir'))
                                    ->color('warning')
                                    ->action(function ($record) {
                                        try {
                                            $commitmentRegion = CommitmentRegion::findOrFail($record->id);
                                            $kabId = $commitmentRegion->kabupaten_id;
                                            $noIjin = $commitmentRegion->no_ijin;
                                            $spatialCodes = MasterSpatial::where('reserved_by', $noIjin)
                                                ->where('kabupaten_id', $kabId)
                                                ->pluck('kode_spatial');
                                            $realisasis = Realisasi2025::where('no_ijin', $noIjin)
                                                ->whereIn('kode_spatial', $spatialCodes)
                                                ->get();

											$fullfill = $realisasis->flatMap->detailrealisasi
												->where('jenis_keg', 'tanam')
												->sum('value');

                                            $commitmentRegion->fullfilled = $fullfill;
                                            $commitmentRegion->save();
                                            Notification::make()
                                                ->title('Data Berhasil Dihitung Ulang')
                                                ->success()
                                                ->send();
                                        } catch (\Exception $e) {
                                            Notification::make()
                                                ->title('Terjadi Kesalahan')
                                                ->body($e->getMessage())
                                                ->danger()
                                                ->send();
                                        }
                                    })
                                    ->requiresConfirmation()
                                    ->modalHeading('Recalculate Data')
                                    ->modalDescription('Hitung rekapitulasi realisasi di kabupaten ini?')
                                    ->modalSubmitActionLabel('Ya, Hitung rekapitulasi'),
                            ])->alignCenter()
                        ])
                ]),

            Section::make('Data Kelompok Tani dan PKS')
                ->aside()
                ->description('Mitra Kelompok Tani beserta data Perjanjian Kerjasama')
                ->schema([
					Repeater::make('pks')
						->addable(false)
						->hiddenLabel()
						->columns(12)
						->deletable(false)
						->relationship('pks')
						->collapsed()
						->itemLabel(fn (array $state): ?string => 'Poktan ' . $state['namaKelompok'] ?? null)
						->schema([
                            Hidden::make('id'),
							Hidden::make('namaKelompok')
								->formatStateUsing(fn ($record) => $record->poktan->nama_kelompok),
							Group::make()
								->schema([
									Placeholder::make('pks')
										->label('PKS')
										->columnSpan(3)
										->content(fn ($record) => view('components.status-badge-null', [
											'status' => $record->berkas_pks ? $record->no_perjanjian : 'Belum Ada'
										])),

									Placeholder::make('deadline')
										->label('Tenggat akhir')
										->columnSpan(3)
										->content(fn ($record) => Carbon::parse($record->deadline_at)->translatedFormat('d F Y')),

									Placeholder::make('status_dinas')
										->label('Status Berkas')
										->columnSpan(2)
										->extraAttributes(['class'=>'text-center'])
										->content(fn ($get) => view('components.status-icon', ['status' => $get('status_dinas')])),
									Placeholder::make('status')
										->label('Status Verifikasi')
										->columnSpan(2)
										->extraAttributes(['class'=>'text-center'])
										->content(fn ($get) => view('components.status-badge-verifikasi', ['status' => $get('status')])),
								])->columns(10)->columnSpanFull(),


							Actions::make([
								ActionsAction::make('Realisasi')
									// ->hiddenLabel()
									->label('Lengkapi Data')
									->icon('icon-journal-bookmark-fill')
									// ->iconButton()
									->color(fn ($record) => $record->berkas_pks ? 'success' : 'danger')
									->url(function ($state) {
										// Dalam repeater, $record adalah record induk (Commitment2025)
										// Kita perlu menggunakan $state untuk mendapatkan ID PKS

										// Pastikan kita memiliki ID PKS yang valid
										if (!isset($state['id'])) {
											return '#'; // Fallback jika ID tidak ada
										}

										// Ambil data PKS berdasarkan ID
										$pksId = $state['id'];

										// Tentukan route berdasarkan status
										$pks = Pks2025::find($pksId);
										if (!$pks) {
											return '#';
										}

										$statusDinas = $pks->status_dinas;
										$statusVerifikasi = $pks->status;

										if ($statusDinas === '1' || is_null($statusDinas) || $statusVerifikasi == 'Tidak Sesuai') {
											return route('filament.panel2025.resources.pks2025s.edit', $pksId);
										}

										return route('filament.panel2025.resources.pks2025s.view', $pksId);
									}),


								])->columnSpanFull()->alignEnd(),
						]),
                ]),

            Section::make('Kebutuhan Pupuk dan Mulsa')
                ->aside()
                ->description('Rencana kebutuhan pupuk dan mulsa untuk memenuhi pelaksanaan komitmen tanam-produksi')
                ->schema([
                    Placeholder::make('organik')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->pupuk_organik,0,',','.').' kg'),
                    Placeholder::make('dolomit')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->dolomit,0,',','.').' kg'),
                    Placeholder::make('npk')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->npk,0,',','.').' kg'),
                    Placeholder::make('za')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->za,0,',','.').' kg'),
                    Placeholder::make('mulsa')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->mulsa,0,',','.').' roll'),
                ]),

            Section::make('Kebutuhan Benih')
                ->aside()
                ->hidden()
                ->description('Rencana kebutuhan benih untuk memenuhi pelaksanaan komitmen tanam-produksi')
                ->schema([
                    Placeholder::make('kebutuhan')
                        ->inlineLabel()
                        ->content(fn ($record) => $record->kebutuhan_benih),
                    Placeholder::make('mandiri')
                        ->inlineLabel()
                        ->content(fn ($record) => $record->mandiri),
                    Placeholder::make('penangkar')
                        ->inlineLabel()
                        ->content(fn ($record) => $record->beli_penangkar),
                ]),

            Section::make('Penangkar')
                ->aside()
                ->hidden()
                ->description('Daftar penangkar benih')
                ->schema([
                    TableRepeater::make('penangkar')
                        ->hiddenLabel()
                        ->addable(false)
                        ->reorderable(false)
                        ->relationship('myPenangkars')
                        ->headers([
                            Header::make('Penangkar'),
                            Header::make('Pimpinan'),
                            Header::make('Varietas'),
                        ])
                        ->schema([
                            TextInput::make('nama_penangkar')
                                ->hiddenLabel(),
                                TextInput::make('nama_pimpinan')
                                ->hiddenLabel(),
                                TextInput::make('varietas')
                                ->hiddenLabel(),
                        ])
                ]),
        ]);
    }

    public function hasCombinedRelationManagerTabsWithContent(): bool
    {
        return true;
    }
    public function getContentTabPosition(): ?ContentTabPosition
    {
        return ContentTabPosition::Before;
    }

	public static function isOwner($record)
	{
		return Auth::user()->hasRole('importir') && $record->user_id === Auth::id();
	}

	public static function checkPksCompletion($record)
	{
		$pksList = Pks2025::where('no_ijin', $record->no_ijin)
			->select('berkas_pks')
			->get();
		if ($pksList->isEmpty()) {
			return false;
		}
		if ($pksList->contains('berkas_pks', null)) {
			return false;
		}
		return true;
	}
}
