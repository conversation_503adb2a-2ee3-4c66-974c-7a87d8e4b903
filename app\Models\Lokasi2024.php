<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Lokasi2024 extends Model
{
	use HasFactory, SoftDeletes, LogsActivity;
	public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
			->logFillable('*')
			->logOnlyDirty();
    }

	protected $table = 'lokasis';

	protected $dates = [
		'created_at',
		'updated_at',
		'deleted_at',
	];

	protected $fillable = [
		'npwp',
		'no_ijin',
		'poktan_id',
		'anggota_id',
		'nama_lokasi',
		'luas_lahan',
		'periode_tanam',
		'latitude',
		'longitude',
		'altitude',
		'polygon',
		'luas_kira',
		'tgl_tanam',
		'luas_tanam',
		'tanam_doc',
		'tanam_pict',
		'tgl_panen',
		'volume',
		'panen_doc',
		'panen_pict',
		'status',
		'varietas', //unused
	];

	protected static function booted()
	{
		static::addGlobalScope('npwp', function (Builder $builder) {
			if (Auth::check()) {
				$user = Auth::user();

				if ($user->hasAnyRole(['admin', 'direktur', 'Super Admin', 'verifikator'])) {
				}
				else {
					$builder->where('npwp', $user->npwp);
				}
			}
		});
	}

	public function masteranggota()
	{
		return $this->belongsTo(MasterAnggota2024::class, 'anggota_id', 'anggota_id');
	}

	public function masterkelompok()
	{
		return $this->belongsTo(MasterPoktan2024::class, 'poktan_id');
	}

	public function pullriph()
	{
		return $this->belongsTo(Commitment2024::class, 'no_ijin', 'no_ijin');
	}

	public function pks()
	{
		return $this->belongsTo(Pks2024::class, 'poktan_id', 'poktan_id');
	}

	public function datarealisasi()
	{
		return $this->hasMany(DataRealisasi2024::class, 'lokasi_id', 'id');
	}
}
