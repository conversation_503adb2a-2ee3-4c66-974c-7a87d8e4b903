<?php

namespace App\Filament\Panel2025\Resources\Pks2025Resource\Pages;

use App\Filament\Panel2025\Resources\Pks2025Resource;
use App\Models\Commitment2025;
use App\Models\Varietas;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\{DatePicker, Placeholder, Radio, Section, Select, Textarea, TextInput};
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Support\HtmlString;

class DinasEditPks2025 extends EditRecord
{
    protected static string $resource = Pks2025Resource::class;
    protected static ?string $title = 'PKS';
    public function getHeading(): string
	{
        $noPks = $this->record ? $this->record->no_perjanjian : '#';
        return 'PKS No: '.$noPks;
	}
    public function getSubheading(): ?string
    {
        $noIjin = $this->record ? $this->record->no_ijin : '##';
        return 'untuk PPRK No: ' . $noIjin;
    }

    protected function getRedirectUrl(): string
    {
        $noIjin = $this->record->no_ijin;
        $commitmentId = Commitment2025::where('no_ijin', $noIjin)->select('id')->first()->id;
        return url(route('filament.panel2025.resources.commitment2025s.view', $commitmentId));
    }

    public static string | Alignment $formActionsAlignment = Alignment::Right;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\ViewAction::make(),
            // Actions\DeleteAction::make(),
        ];
    }

	public function form(Form $form): Form
	{
		return $form
			->schema([
				Section::make('Data PKS')
					->aside()
					->description('Data Isian Detail PKS')
					->schema([
						Placeholder::make('Kelompoktani')
							->inlineLabel()
							->content(fn ($record)=>$record->nama_poktan),
						Placeholder::make('Jumlah Lokasi dipilih')
							->inlineLabel()
							->content(fn ($record)=>$record->jumlah_anggota .' titik'),
						Placeholder::make('Total Luas dipilih')
							->inlineLabel()
							->content(fn ($record)=> number_format($record->luas_rencana,0,',','.') . ' m2'),

						Placeholder::make('no_perjanjian')
							->inlineLabel()
							->content(fn ($record)=> $record->no_perjanjian),

						Placeholder::make('tgl_perjanjian_start')
							->inlineLabel()
							->label('Mulai Berlaku')
							->content(fn ($record) => 
								$record->tgl_perjanjian_start 
									? Carbon::parse($record->tgl_perjanjian_start)->translatedFormat('d F Y') 
									: 'Tidak ada data'
							),

						Placeholder::make('tgl_perjanjian_end')
							->inlineLabel()
							->label('Berakhir pada')
							->content(fn ($record) => 
								$record->tgl_perjanjian_end 
									? Carbon::parse($record->tgl_perjanjian_end)->translatedFormat('d F Y') 
									: 'Tidak ada data'
							),

						Placeholder::make('varietas_tanam')
							->inlineLabel()
							->content(fn ($record)=> $record->varietas->nama_varietas),

						Placeholder::make('periode_tanam')
							->inlineLabel()
							->content(fn ($record)=> $record->periode_tanam),

						Placeholder::make('berkas_pks')
							->inlineLabel()
							->content(fn ($record) => new HtmlString(
								'<a class="font-bold text-info-500" href="' . asset($record->berkas_pks) . '" target="_blank" rel="nofollow noreferrer">Lihat/Unduh Berkas</a>'
							)),
					]),

				Section::make('Hasil Pemeriksaan')
					->aside()
					->description('Keputusan atas hasil pemeriksaan')
					->schema([
						Radio::make('status_dinas')
							->hiddenLabel()
							->reactive()
							->required()
							// ->inlineLabel(false)
							->options([
								'2' => 'Lanjutkan',
								'1' => 'Perbaikan',
								'0' => 'Belum diperiksa/Tunda',
							])
							->descriptions([
								'2' => 'Jika berkas sesuai dan pelaku usaha diijinkan untuk melanjutkan',
								'1' => 'Jika berkas perlu diperbaiki oleh pelaku usaha',
								'0' => 'Anda belum atau menunda pemeriksaan',
							]),

						Textarea::make('catatan_dinas')
							->reactive()
							->placeholder('isi dengan catatan atau alasan yang perlu diketahui oleh pelaku usaha')
							->autosize()
							->required(fn ($get) =>$get('status_dinas') === '1')
							->helperText('Wajib diisi jika status pemeriksaan adalah PERBAIKAN'),
					])
			]);
	}
}
