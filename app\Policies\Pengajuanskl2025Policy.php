<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\Pengajuanskl2025;
use App\Models\User;

class Pengajuanskl2025Policy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any Pengajuanskl2025');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Pengajuanskl2025 $pengajuanskl2025): bool
    {
        return $user->checkPermissionTo('view Pengajuanskl2025');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create Pengajuanskl2025');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Pengajuanskl2025 $pengajuanskl2025): bool
    {
        return $user->checkPermissionTo('update Pengajuanskl2025');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Pengajuanskl2025 $pengajuanskl2025): bool
    {
        return $user->checkPermissionTo('delete Pengajuanskl2025');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any Pengajuanskl2025');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Pengajuanskl2025 $pengajuanskl2025): bool
    {
        return $user->checkPermissionTo('restore Pengajuanskl2025');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any Pengajuanskl2025');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, Pengajuanskl2025 $pengajuanskl2025): bool
    {
        return $user->checkPermissionTo('replicate Pengajuanskl2025');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder Pengajuanskl2025');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Pengajuanskl2025 $pengajuanskl2025): bool
    {
        return $user->checkPermissionTo('force-delete Pengajuanskl2025');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any Pengajuanskl2025');
    }
}
