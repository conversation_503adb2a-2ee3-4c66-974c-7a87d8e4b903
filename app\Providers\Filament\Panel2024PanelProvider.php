<?php

namespace App\Providers\Filament;

use Filament\Http\Middleware\Authenticate;
use Lara<PERSON>\Sanctum\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\NavigationItem;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use App\Filament\Pages\Auth\Login;
use App\Filament\Panel2024\Pages\Dashboard;
use DiogoGPinto\AuthUIEnhancer\AuthUIEnhancerPlugin;
use Filament\Navigation\MenuItem;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Joaopaulolndev\FilamentEditProfile\Pages\EditProfilePage;

class Panel2024PanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('panel2024')
            ->path('panel/2024')
            ->brandName('Simethris v3.0 @2023')
			->favicon(asset('assets/img/favicon.png'))
            ->brandLogo(asset('assets/img/logo-simet.png'))
			->topNavigation(true)
            ->breadcrumbs(false)
			->databaseNotifications()
            ->colors([
                'danger' => Color::Red,
                'gray' => Color::Gray,
                'info' => Color::Fuchsia,
                'primary' => Color::Violet,
                'success' => Color::Cyan,
                'warning' => Color::Amber,
            ])
            ->discoverResources(in: app_path('Filament/Panel2024/Resources'), for: 'App\\Filament\\Panel2024\\Resources')
            ->discoverPages(in: app_path('Filament/Panel2024/Pages'), for: 'App\\Filament\\Panel2024\\Pages')
            ->pages([
                Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Panel2024/Widgets'), for: 'App\\Filament\\Panel2024\\Widgets')
            ->widgets([
                // Widgets\AccountWidget::class,
                // Widgets\FilamentInfoWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class, // Using Laravel Sanctum's AuthenticateSession
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
			->userMenuItems([
				MenuItem::make()
					->label('Beranda')
					->url('/')
					->icon('heroicon-o-home'),
				MenuItem::make()
					->label('Profil Saya')
					->url(function () {
						if(Auth::user()->hasRole('importir')){
							if(is_null(Auth::user()->datauser))
							{
								return '/admin/datausers/create';
							}
							return '/admin/datausers/' . Auth::user()->datauser->id . '/view';
						}

						return '/admin/users/'. Auth::user()->id .'/myprofile';
					})
					->icon('heroicon-o-user-circle')
					->visible(function () {
						$user = Auth::user();

						// Jika user adalah importir, cek apakah datauser ada
						if ($user->hasRole('importir')) {
							return $user->relationLoaded('datauser')
								? !is_null($user->datauser)
								: $user->datauser()->exists(); // Cek di database jika belum ter-load
						}
						return true;
					}),
			])
            ->navigationItems([
                NavigationItem::make('Panel Utama')
                    ->url('/admin', shouldOpenInNewTab: false)
                    ->icon('heroicon-o-arrow-left-end-on-rectangle')
                    // ->group('Pilih Tahun')
                    ->sort(-99),
            ])
            // ->login(Login::class)
            ->viteTheme('resources/css/filament/panel2024/theme.css')
            ->plugins([
                AuthUIEnhancerPlugin::make()
                    ->showEmptyPanelOnMobile(false)
                    ->formPanelPosition('right')
                    ->formPanelWidth('40%')
                    ->emptyPanelBackgroundImageOpacity('90%')
                    ->emptyPanelBackgroundImageUrl(asset('assets/img/simet-bawang-pagi.webp')),
            ]);
    }
}
