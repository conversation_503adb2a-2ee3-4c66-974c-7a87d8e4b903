<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\CmsCategoryResource\Pages;
use App\Filament\Admin\Resources\CmsCategoryResource\RelationManagers;
use App\Models\CmsCategory;
use Illuminate\Support\Str;
use Filament\Forms;
use Filament\Forms\Components\{ColorPicker, FileUpload, Grid, Section, Select, Textarea, TextInput, Toggle};
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use TomatoPHP\FilamentIcons\Components\IconPicker;

class CmsCategoryResource extends Resource
{
    protected static ?string $model = CmsCategory::class;

    // protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    public static function getNavigationGroup(): ?string
    {
        return 'CMS';
    }

    public static function getPluralLabel(): ?string
    {
        return 'Daftar Kategori';
    }

    public static function getLabel(): ?string
    {
        return 'Kategori';
    }

    public static function getNavigationLabel(): string
    {
        return 'Kategori';
    }

    public Static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make([
                    'default' => 1,
                    'sm' => 1,
                    'md' => 6,
                    'lg' => 12,
                ])->schema([
                    Grid::make()
                        ->schema([
                            Section::make('Detail Kategori')
                                ->description('')
                                ->schema([
                                    TextInput::make('name')
                                        ->label('Nama')
                                        ->required()
                                        ->lazy()
                                        ->afterStateUpdated(fn($get, $set)=> $set('slug', Str::of($get('for'))->replace(' ', '-')->lower()->toString().'_'. Str::of($get('name'))->replace(' ', '-')->lower()->toString())),
                                    TextInput::make('slug')
                                        ->readOnly(),
                                    Textarea::make('description')
                                        ->label('Deskripsi')
                                        ->columnSpanFull(),
                                    // IconPicker::make('icon')
                                    //     ->label('Icon'),
                                    ColorPicker::make('color')
                                        ->label('Warna')
                                        ->label('Color')
                                ])->columns(2),
                            Section::make('Post Image')
                                ->description('image for the post')
                                ->columns(2)
                                ->schema([
                                    FileUpload::make('feature_image')
                                        ->openable()
										->deletable()
										->imageEditor()
                                        ->maxSize(2048)
                                        ->downloadable()
                                        ->disk('public')
                                        ->previewable(true)
                                        ->columnSpan(1)
                                        ->visibility('public')
                                        ->imagePreviewHeight('250')
                                        ->panelAspectRatio('1:1')
                                        ->fetchFileInformation(true)
                                        ->helperText('Maksimal 2MB, format gambar')
										->rules([
											'mimetypes:image/jpeg,image/png',
											'mimes:jpg,jpeg,png',
										])
										->validationMessages([
											'mimetypes' => 'Hanya file gambar (JPG, JPEG, PNG) yang diperbolehkan',
											'mimes' => 'Ekstensi file harus .jpg, .jpeg atau .png',
										])
                                        ->directory('uploads/cmsimage/category/features'),
                                        
                                    FileUpload::make('cover_image')
                                        ->openable()
										->deletable()
										->imageEditor()
                                        ->maxSize(2048)
                                        ->downloadable()
                                        ->disk('public')
                                        ->previewable(true)
                                        ->columnSpan(1)
                                        ->visibility('public')
                                        ->imagePreviewHeight('250')
                                        ->panelAspectRatio('1:1')
                                        ->fetchFileInformation(true)
                                        ->helperText('Maksimal 2MB, format gambar')
										->rules([
											'mimetypes:image/jpeg,image/png',
											'mimes:jpg,jpeg,png',
										])
										->validationMessages([
											'mimetypes' => 'Hanya file gambar (JPG, JPEG, PNG) yang diperbolehkan',
											'mimes' => 'Ekstensi file harus .jpg, .jpeg atau .png',
										])
                                        ->directory('uploads/cmsimage/category/cover'),
                                ]),
                        ])
                        ->columnSpan([
                            'sm' => 1,
                            'md' => 4,
                            'lg' => 8,
                        ]),
                    Section::make('Setting')
                        ->description('Post setting')
                        ->schema([
                            Select::make('for')
                                ->label('Kategori untuk')
                                ->searchable()
                                ->live()
								->lazy()
                                ->options([
                                    'Artikel' => 'Artikel',
                                    'Berita' => 'Berita',
                                    'Event' => 'Event',
                                ])

                                ->default('Artikel')
								->afterStateUpdated(fn($get, $set)=> $set('slug', Str::of($get('for'))->replace(' ', '-')->lower()->toString().'_'. Str::of($get('name'))->replace(' ', '-')->lower()->toString())),
                            // Select::make('type')
                            //     ->label('Type')
                            //     ->searchable()
                            //     ->options([
                            //         'Category' => 'Category',
                            //         'Tags' => 'Tags'
                            //     ])
                            //     ->default('category'),
                            Select::make('parent_id')
                                ->label('Induk Kategori')
                                ->searchable()
                                ->options(fn() => CmsCategory::query()->pluck('name', 'id')->toArray()),
                            Toggle::make('is_active')
                                ->label('Active'),
                            Toggle::make('show_in_menu')
                                ->label('Tampilkan di menu'),
                        ])
                        ->columnSpan([
                            'sm' => 1,
                            'md' => 2,
                            'lg' => 4,
                        ])
                ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                //
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCmsCategories::route('/'),
            'create' => Pages\CreateCmsCategory::route('/create'),
            'view' => Pages\ViewCmsCategory::route('/{record}'),
            'edit' => Pages\EditCmsCategory::route('/{record}/edit'),
        ];
    }
}
