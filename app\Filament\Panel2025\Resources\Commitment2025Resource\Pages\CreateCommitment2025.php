<?php

namespace App\Filament\Panel2025\Resources\Commitment2025Resource\Pages;

use App\Filament\Panel2025\Resources\Commitment2025Resource;
use App\Models\{MasterKabupaten, Commitment2025, Completed, DetailRealisasi2025, PengajuanVerifikasi, Pks2025, Realisasi2025, Userfile};
use Filament\Forms\Form;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\{Actions, DatePicker, FileUpload, Grid, Group, TextInput, Placeholder, Section, Hidden};
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Enums\Alignment;
use Awcodes\TableRepeater\Header;
use Awcodes\TableRepeater\Components\TableRepeater;
use Illuminate\Support\Facades\{Auth, DB, Log, Storage};
use Illuminate\Support\HtmlString;

class CreateCommitment2025 extends CreateRecord
{
	protected static string $resource = Commitment2025Resource::class;
	protected static ?string $title = 'Data PPRK';

	public function getHeading(): string
	{
		return 'Unduh Data PPRK';
	}
	public static string | Alignment $formActionsAlignment = Alignment::Right;

	protected function getFormActions(): array
	{
		$visible = $this->data['visible'];
		if ($visible === 'visible') {
			return [
				$this->getCreateFormAction()->label('Simpan untuk Pelaporan'),
				$this->getCancelFormAction()
			];
		} else {
			return [];
		}
	}

	public function form(Form $form): Form
	{
		return $form
			->schema([
				Hidden::make('user_id')
					->default(Auth::user()->id)
					->afterStateHydrated(fn($set) => $set('user_id', Auth::user()->id)),
				Hidden::make('npwp')
					->default(function () {
						if (Auth::user()->hasRole('importir')) {
							return optional(Auth::user()->datauser)->npwp_company;
						}
						return null;
					}),
				Hidden::make('visible')->default('hidden'),

				Section::make('Nomor PPRK')
					->aside()
					->description('Isi dengan nomor PPRK yang berlaku. Pastikan Data PPRK/RIPH dimaksud sudah final di aplikasi SIAPRIPH.')
					->schema([
						TextInput::make('no_ijin')->unique()
							->hiddenLabel()
							->live(onBlur: true)
							->placeholder('Nomor PPRK')
							->helperText('Isi dengan nomor yang berlaku pada periode ini. Pastikan Data PPRK/RIPH dimaksud sudah final di aplikasi SIAPRIPH.')
							->afterStateUpdated(function ($state, $set) {
								$tahun = substr($state, -4);
								if (is_numeric($tahun) && (int)$tahun < 2025) {
									$set('no_ijin', null);
									Notification::make()
										->title('Nomor Tidak Valid')
										->body('Gunakan Nomor PPRK yang berlaku.')
										->danger()
										->send();
								}
							}),

						Actions::make([
							Action::make('Unduh data')
								->icon('heroicon-m-arrow-down-tray')
								->requiresConfirmation()
								->modalHeading('Unduh Data')
								->modalDescription('Anda akan melakukan pengunduhan data. Lanjutkan?')
								->modalSubmitActionLabel('Ya, lanjutkan.')
								->action(function ($get, $set) {
									try {
										$npwp = self::sanitizeNpwp($get('npwp'));
										$noijin = $get('no_ijin');

										if (!self::validateInputs($noijin, $npwp, $set)) return;
										if (!self::validateNoIjin($noijin, $npwp, $set)) return;

										$response = self::callSoapService($npwp, $noijin);

										if ($response) {
											self::processSoapResponse($response, $npwp, $noijin, $set);
										}
									} catch (\Exception $e) {
										self::handleError($e, $npwp ?? '', $noijin ?? '');
									}
								}),
						])->alignment(Alignment::End)->columnSpanFull(),
					]),


				Placeholder::make('hr')
					->columnSpanFull()
					->hiddenLabel()
					->content(new HtmlString('<hr>')),
				Section::make('Informasi Umum')
					->aside()
					->description('Data PPRK')
					->visible(fn($get) => $get('visible') === 'visible')
					->schema([
						Placeholder::make('namaHolder')
							->inlineLabel()
							->label('Pemegang PPRK')
							->columnSpanFull()
							->content(fn($get) => $get('nama')),
						Placeholder::make('hsHolder')
							->inlineLabel()
							->label('Kode HS')
							->columnSpanFull()
							->content(fn($get) => $get('no_hs')),
						Placeholder::make('periodeHolder')
							->inlineLabel()
							->label('Periode PPRK')
							->columnSpanFull()
							->content(fn($get) => $get('periodetahun')),
						Placeholder::make('awalHolder')
							->inlineLabel()
							->label('Mulai Berlaku')
							->columnSpanFull()
							->content(fn($get) => $get('tgl_ijin')),
						Placeholder::make('akhirHolder')
							->inlineLabel()
							->label('Tanggal berakhir')
							->columnSpanFull()
							->content(fn($get) => $get('tgl_akhir')),
						Placeholder::make('volHolder')
							->inlineLabel()
							->label('Volume PPRK')
							->columnSpanFull()
							->content(fn($get) => number_format($get('volume_riph'), 0, ',', '.') . ' ton'),
						Placeholder::make('prdHolder')
							->inlineLabel()
							->label('Volume Komitmen Produksi')
							->columnSpanFull()
							->content(fn($get) => number_format($get('volume_produksi'), 0, ',', '.') . ' kg'),
						Placeholder::make('tanamHolder')
							->inlineLabel()
							->label('Luas Komitmen Tanam')
							->columnSpanFull()
							->content(fn($get) => number_format($get('luas_wajib_tanam'), 0, ',', '.') . ' m2'),
					]),

				Section::make('Wilayah dan Kuota')
					->aside()
					->description('Ini adalah daftar Wilayah yang telah Anda tentukan di aplikasi/sistem SIAPRIPH sebelumnya.')
					->visible(fn($get) => $get('visible') === 'visible')
					->schema([
						TableRepeater::make('kab_quotas')
							->hiddenLabel()
							->addable(false)
							->deletable(false)
							->reorderable(false)
							->relationship('myRegions')
							->headers([
								Header::make('Kabupaten'),
								Header::make('Rencana Luas'),
							])
							->schema([
								Placeholder::make('kabupatenHolder')
									->hiddenLabel()
									->content(
										fn($get) =>
										MasterKabupaten::where('kabupaten_id', $get('kabupaten_id'))->first()?->nama_kab ?? '-'
									),

								Placeholder::make('kuotaHolder')
									->hiddenLabel()
									->extraAttributes(['class' => 'text-end'])
									->content(fn($get) => number_format($get('quota'), 0, ',', '.') . ' m2'),
								Hidden::make('npwp'),
								Hidden::make('no_ijin'),
								Hidden::make('kabupaten_id'),
								Hidden::make('quota'),
							])
					]),

				Section::make('Pupuk & Mulsa')
					->aside()
					->description('Ini adalah total kebutuhan pupuk dan mulsa untuk pelaksanaan Komitmen wajib tanam-produksi Anda.')
					->visible(fn($get) => $get('visible') === 'visible')
					->schema([
						Placeholder::make('organikHolder')
							->inlineLabel()
							->label('Organik')

							->extraAttributes(['class' => 'text-end'])
							->content(fn($get) => number_format($get('pupuk_organik'), 0, ',', '.') . ' kg'),

						Placeholder::make('dolomitkHolder')
							->inlineLabel()
							->label('Dolomit')
							->columnSpanFull()
							->extraAttributes(['class' => 'text-end'])
							->content(fn($get) => number_format($get('dolomit'), 0, ',', '.') . ' kg'),

						Placeholder::make('npkHolder')
							->inlineLabel()
							->label('NPK')
							->extraAttributes(['class' => 'text-end'])
							->content(fn($get) => number_format($get('npk'), 0, ',', '.') . ' kg'),

						Placeholder::make('zakHolder')
							->inlineLabel()
							->label('ZA')
							->extraAttributes(['class' => 'text-end'])
							->content(fn($get) => number_format($get('za'), 0, ',', '.') . ' kg'),

						Placeholder::make('mulsakHolder')
							->inlineLabel()
							->label('mulsa')
							->extraAttributes(['class' => 'text-end'])
							->content(fn($get) => number_format($get('mulsa'), 0, ',', '.') . ' roll'),
					]),
				Group::make()
					->schema([
						Hidden::make('nama'),
						Hidden::make('no_hs'),
						Hidden::make('periodetahun'),
						Hidden::make('tgl_ijin'),
						Hidden::make('tgl_akhir'),
						Hidden::make('volume_riph'),
						Hidden::make('volume_produksi'),
						Hidden::make('luas_wajib_tanam'),
						Hidden::make('pupuk_organik'),
						Hidden::make('npk'),
						Hidden::make('dolomit'),
						Hidden::make('za'),
						Hidden::make('mulsa'),
					]),
			]);
	}


	// function
	private static function sanitizeNpwp(?string $npwp): string
	{
		return str_replace(['.', '-'], '', $npwp ?? '');
	}

	private static function validateInputs(?string $noijin, string $npwp, $set): bool
	{
		if (empty($noijin) || empty($npwp)) {
			self::sendNotification('Data Tidak Lengkap', 'NPWP dan No Ijin harus diisi.', 'danger');
			return false;
		}
		return true;
	}

	private static function validateNoIjin(string $noijin, string $npwp, $set): bool
	{
		if ($noijin) {
		}
		if (Commitment2025::where('no_ijin', $noijin)->exists()) {
			$set('no_ijin', null);
			self::sendNotification('Nomor Izin Sudah Digunakan!', 'Silakan masukkan nomor yang berbeda.', 'danger');
			return false;
		}
		return true;
	}

	private static function callSoapService(string $npwp, string $noijin): ?string
	{
		try {
			$client = new \SoapClient(
				'https://riph.pertanian.go.id/api.php/simethris?wsdl',
				[
					'soap_version' => SOAP_1_1,
					'exceptions' => true,
					'trace' => 1,
					'cache_wsdl' => WSDL_CACHE_MEMORY,
					'connection_timeout' => 25,
					'style' => SOAP_RPC,
					'use' => SOAP_ENCODED,
				]
			);

			$response = $client->__soapCall('get_riph_2025', [
				'user' => 'simethris',
				'pass' => 'wsriphsimethris',
				'npwp' => $npwp,
				'nomor' => $noijin,
			]);

			self::saveSoapResponse($npwp, $noijin, $response);
			return $response;
		} catch (\SoapFault $e) {
			self::sendNotification('Sinkronisasi Gagal!', 'SOAP Fault: ' . $e->getMessage(), 'danger');
			return null;
		}
	}

	private static function processSoapResponse(string $response, string $npwp, string $noijin, $set): void
	{
		$set('visible', 'visible');

		try {
			$xml = simplexml_load_string($response);
			$datariph = json_decode(json_encode($xml), true);

			if (!isset($datariph['return_cek'])) {
				self::handleInvalidResponse($noijin);
				return;
			}

			match ($datariph['return_cek']) {
				'R00' => self::handleValidResponse($datariph, $npwp, $noijin, $set),
				'R99' => self::handleNotFoundResponse($noijin),
				default => self::handleUnknownError()
			};
		} catch (\Exception $e) {
			self::handleError($e, $npwp, $noijin);
		}
	}

	private static function handleValidResponse(array $datariph, string $npwp, string $noijin, $set): void
	{
		$wajibTanam = $datariph['riph']['wajib_tanam'];

		self::setBasicInfo($datariph, $noijin, $set);
		self::setTanamInfo($wajibTanam, $set);
		self::setPupukInfo($wajibTanam, $set);
		// self::setBenihInfo($wajibTanam, $set);

		$kabupatenList = self::processLokasiTanam($wajibTanam['lokasi_tanam'], $npwp, $noijin);
		// $penangkarList = self::processPenangkar($wajibTanam['datapenangkar'], $npwp, $noijin);

		$set('kab_quotas', $kabupatenList);
		// $set('daftarPenangkar', $penangkarList);
		self::sendNotification('Pengunduhan', 'Data ditemukan dan berhasil diunduh.', 'success');
	}

	private static function saveSoapResponse(string $npwp, string $noijin, string $response): void
	{
		$fijin = str_replace(['/', '.'], '', $noijin);
		$filepath = 'uploads/' . $npwp . '/' . $fijin . '.json';
		Storage::disk('public')->put($filepath, json_encode((array)simplexml_load_string($response)));
	}

	private static function setBasicInfo(array $datariph, string $noijin, $set): void
	{
		$persetujuan = $datariph['riph']['persetujuan'];
		$komoditas = self::processKomoditas($datariph['riph']['komoditas']);

		$set('nama', (string) $persetujuan['nama']);
		$set('periodetahun', substr($noijin, -4));
		$set('tgl_ijin', (string) $persetujuan['tgl_ijin']);
		$set('tgl_akhir', (string) $persetujuan['tgl_akhir']);
		$set('no_hs', $komoditas['no_hs'] . ' ' . $komoditas['nama_produk']);
	}

	private static function processKomoditas(array $komoditas): array
	{
		return isset($komoditas['loop'][0]) ? $komoditas['loop'][0] : $komoditas['loop'];
	}

	private static function setTanamInfo(array $wajibTanam, $set): void
	{
		$wajibProduksi = $wajibTanam['volume_produksi'] * 1000;
		$luasWajibTanam = $wajibTanam['luas_wajib_tanam'] * 10000;
		$set('volume_riph', (string) $wajibTanam['volume_riph']);
		$set('volume_produksi', $wajibProduksi);
		$set('luas_wajib_tanam', $luasWajibTanam);
	}

	private static function setPupukInfo(array $wajibTanam, $set): void
	{
		$kebutuhanpupuk = $wajibTanam['kebutuhan_pupuk'] ?? [];

		$set('pupuk_organik', (string) ($kebutuhanpupuk['pupuk_organik'] ?? 0));
		$set('npk', (string) ($kebutuhanpupuk['npk'] ?? 0));
		$set('dolomit', (string) ($kebutuhanpupuk['dolomit'] ?? 0));
		$set('za', (string) ($kebutuhanpupuk['za'] ?? 0));
		$set('mulsa', (string) ($wajibTanam['mulsa'] ?? 0));
	}

	private static function setBenihInfo(array $wajibTanam, $set): void
	{
		$kebutuhanbenih = (float) ($wajibTanam['kebutuhan_benih'] ?? 0);
		$beli = (float) ($wajibTanam['beli_penangkar'] ?? 0);
		$stokmandiri = max(0, $kebutuhanbenih - $beli);

		$set('kebutuhan_benih', $kebutuhanbenih);
		$set('stok_mandiri', $stokmandiri);
		$set('beli_penangkar', $beli);
	}

	private static function processLokasiTanam(array $LokasiData, string $npwp, string $noijin): array
	{
		$kabupatenList = [];
		$formattedNpwp = preg_replace(
			"/^(\d{2})(\d{3})(\d{3})(\d)(\d{3})(\d{3})$/",
			"$1.$2.$3.$4-$5.$6",
			$npwp
		);

		if (!empty($LokasiData)) {
			$kabupatenArray = self::normalizeArray($LokasiData);

			foreach ($kabupatenArray as $kabupaten) {
				$idList = is_array($kabupaten['idkabupaten']) ? $kabupaten['idkabupaten'] : [$kabupaten['idkabupaten']];
				$luasList = is_array($kabupaten['luas_ha']) ? $kabupaten['luas_ha'] : [$kabupaten['luas_ha']];

				foreach ($idList as $index => $idkabupaten) {
					$luasHa = floatval($luasList[$index] ?? 0);

					$kabupatenList[] = [
						'npwp' => $formattedNpwp,
						'no_ijin' => $noijin,
						'kabupaten_id' => $idkabupaten,
						'quota' => $luasHa * 10000,
					];
				}
			}
		}

		return $kabupatenList;
	}

	private static function processPenangkar(array $penangkarData, string $npwp, string $noijin): array
	{
		$penangkarList = [];

		if (isset($penangkarData['loop'])) {
			$penangkarArray = self::normalizeArray($penangkarData['loop']);

			foreach ($penangkarArray as $penangkar) {
				$penangkarList[] = [
					'npwp' => $npwp,
					'no_ijin' => $noijin,
					'nama_penangkar' => self::getStringValue($penangkar['nama_penangkar'] ?? ''),
					'nama_pimpinan' => self::getStringValue($penangkar['nama_pimpinan'] ?? ''),
					'hp_pimpinan' => self::getStringValue($penangkar['hp_pimpinan'] ?? ''),
					'alamat' => self::getStringValue($penangkar['alamat'] ?? ''),
					'varietas' => self::getStringValue($penangkar['varietas'] ?? ''),
					'ketersediaan' => self::getStringValue($penangkar['waktu'] ?? ''),
				];
			}
		}

		return $penangkarList;
	}

	// Helper untuk menormalisasi data array
	private static function normalizeArray($data): array
	{
		if (is_array($data) && isset($data[0])) {
			return $data;
		}
		return [$data];
	}

	private static function getStringValue($data): string
	{
		if (is_array($data) || (is_object($data) && empty((array)$data))) {
			return ''; // Jika data adalah array kosong atau objek kosong, return string kosong
		}
		return (string) $data; // Mengkonversi data menjadi string
	}

	private static function handleError(\Exception $e, string $npwp, string $noijin): void
	{
		self::sendNotification('Error Fetching Data', 'Terjadi kesalahan saat mengunduh data: ' . $e->getMessage(), 'danger');
	}

	private static function sendNotification(string $title, string $body, string $type = 'success'): void
	{
		Notification::make()
			->title($title)
			->{$type}()
			->body($body)
			->send();
	}

	private static function handleInvalidResponse(string $noijin): void
	{
		self::sendNotification('Gagal', 'Format data tidak valid atau respons kosong. (Error 225)', 'danger');
	}

	private static function handleNotFoundResponse(string $noijin): void
	{
		self::sendNotification('GAGAL!', "Data dengan nomor {$noijin} tidak dapat ditemukan di aplikasi/sistem RIPH. Gunakan nomor lain yang berlaku.", 'danger');
	}

	private static function handleUnknownError(): void
	{
		self::sendNotification('Gagal', 'Terjadi kesalahan tidak dikenali. (Error 214)', 'danger');
	}
}
