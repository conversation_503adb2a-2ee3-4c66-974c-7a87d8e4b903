<?php

namespace App\Models;

use App\Models\MasterDesa;
use App\Models\MasterKabupaten;
use App\Models\MasterKecamatan;
use App\Models\MasterProvinsi;
use App\Observers\MasterSpatialObserver;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

#[ObservedBy([MasterSpatialObserver::class])]
class MasterSpatial extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;
	
	public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
			->logFillable('*')
			->logOnlyDirty();
    }

	public $table = 't2025_master_spatials';

	protected $dates = [
		'created_at',
		'updated_at',
		'deleted_at',
	];
	protected $guarded = [
		'id',
		'created_at',
		'updated_at',
		'deleted_at',
	];

	public $fillable = [
		'origin',
		'komoditas',
		'kode_spatial',
		'kode_poktan',
		'ktp_petani',
		'nama_petani',
		'latitude',
		'longitude',
		'polygon',
		'altitude',
		'imagery',
		'luas_lahan',
		'catatan',
		'provinsi_id',
		'kabupaten_id',
		'kecamatan_id',
		'kelurahan_id',
		'kml_url',
		'is_active',
		'status',
		'reserved_by',
		'reserved_at',
		'deadline_at',
	];

	// // Accessor untuk mendekripsi ktp_petani
    // public function getKtpPetaniAttribute($value)
    // {
    //     return Crypt::decryptString($value);
    // }

    // // Mutator untuk mengenkripsi ktp_petani
    // public function setKtpPetaniAttribute($value)
    // {
    //     $this->attributes['ktp_petani'] = Crypt::encryptString($value);
    // }

    public function commitment():BelongsTo
    {
        return $this->belongsTo(Commitment2025::class, 'reserved_by', 'no_ijin');
    }

	public function masterpoktan()
	{
		return $this->belongsTo(MasterPoktan::class, 'kode_poktan', 'kode_poktan');
	}

	public function anggota()
	{
		return $this->belongsTo(MasterAnggota::class, 'ktp_petani', 'ktp_petani');
	}

	public function provinsi()
	{
		return $this->belongsTo(MasterProvinsi::class, 'provinsi_id', 'provinsi_id');
	}
	public function kabupaten()
	{
		return $this->belongsTo(MasterKabupaten::class, 'kabupaten_id', 'kabupaten_id');
	}

	public function kecamatan()
	{
		return $this->belongsTo(MasterKecamatan::class, 'kecamatan_id', 'kecamatan_id');
	}

	public function desa()
	{
		return $this->belongsTo(MasterDesa::class, 'kelurahan_id', 'kelurahan_id');
	}
}
