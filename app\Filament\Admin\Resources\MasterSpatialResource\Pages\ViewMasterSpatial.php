<?php

namespace App\Filament\Admin\Resources\MasterSpatialResource\Pages;

use App\Filament\Admin\Resources\MasterSpatialResource;
use Filament\Actions;
use Filament\Forms\Components\{Fieldset, Group, Placeholder, Section, Toggle};
use Filament\Forms\Form;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class ViewMasterSpatial extends ViewRecord
{
    protected static string $resource = MasterSpatialResource::class;

    public function mount($record): void
    {
        parent::mount($record);

        // Periksa apakah pengguna memiliki izin untuk mengakses halaman ini
        $user = Auth::user();
        if (!$user || !$user->hasAnyRole(['Super Admin', 'admin', 'spatial'])) {
            // Redirect ke dashboard jika tidak memiliki izin
            redirect()->route('filament.admin.pages.dashboard');
            return;
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            // Actions\EditAction::make(),
        ];
    }

    public function getHeading(): string
    {
        return 'Data Lahan';
    }

    protected static ?string $title = 'Data Spatial Lahan';

    public function getSubheading(): ?string
    {
        $kodeSpatial = $this->record ? $this->record->kode_spatial : '##';
        return 'Lokasi: ' . $kodeSpatial;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Data Spatial')
                    ->description('Data Spatial Pemetaan Lahan')
					->aside()
                    ->schema([
						Placeholder::make('map')
							->label('')
							->content(function ($record) {
								// Ambil API key dari database atau env
								$mapKey = \App\Models\ForeignApi::where('status', 1)->select('key')->first()?->key ?? env('GOOGLE_MAPS_API_KEY');

								// Siapkan data untuk view
								$polygon = $record->polygon;
								if ($polygon) {
									// Coba parse polygon jika berbentuk JSON string
									$polygonData = json_decode($polygon, true);

									// Jika gagal parse (hasil null dan bukan karena string 'null'), gunakan string asli
									if ($polygonData === null && $polygon !== 'null') {
										$polygonData = $polygon;
									}
								} else {
									$polygonData = null;
								}

								$viewData = [
									'mapKey' => $mapKey,
									'latitude' => $record->latitude,
									'longitude' => $record->longitude,
									'kodeSpatial' => $record->kode_spatial,
									'polygon' => $polygonData,
								];

								// Render view
								return new HtmlString(
									view('filament.admin.resources.master-spatial-resource.pages.spatial-map', $viewData)->render()
								);
							}),
                        Group::make()
                            ->schema([
                                Placeholder::make('latitude')
                                    ->inlineLabel()
                                    ->content(fn ($record)=>$record->latitude),
                                Placeholder::make('longitude')
                                    ->inlineLabel()
                                    ->content(fn ($record)=>$record->longitude),
                                Placeholder::make('polygon')
                                    ->inlineLabel()
									->extraAttributes(['class' => 'text-xs truncate'])
                                    ->content(fn ($record) => new HtmlString(
                                        '<p>' . $record->polygon . '</p>'
                                    )),
                                Placeholder::make('altitude')
                                    ->inlineLabel()
                                    ->content(fn ($record)=>$record->altitude),
                                Placeholder::make('luas_lahan')
                                    ->inlineLabel()
                                    ->content(fn ($record)=>number_format($record->luas_lahan,0,',','.') . ' m2'),
								Placeholder::make('is_active')
									->inlineLabel()
									->label('Status Lahan:')
									->content(function ($record) {
										$stateColor = $record->is_active === 1 ? 'text-success-500"' : 'text-gray-500"';
										$stateText = $record->is_active === 1 ? 'Aktif' : 'Tidak Aktif';
										return new HtmlString(
											'<a class="font-bold '.$stateColor.'" >'.$stateText.'</a>' // Ganti warna sesuai kebutuhan Anda
										);
									}),
                                Placeholder::make('kml_url')
                                    ->label('Unduh Berkas KML')
                                    ->inlineLabel()
                                    ->content(fn ($record) => new HtmlString(
                                        '<a class="font-bold text-info-500" href="' . asset($record->kml_url) . '" target="_blank" rel="nofollow noreferrer" download>Klik untuk mengunduh</a>'
									)),
                            ])
                    ]),

                Section::make('Data Pengelola')
                    ->aside()
                    ->description('Petani pemilik/pengelola/penggarap lahan ini.')
                    ->schema([
                        Placeholder::make('kode_poktan')
                            ->label('Kelompok Tani')
                            ->inlineLabel()
                            ->content(fn ($record)=>$record->masterpoktan?->nama_kelompok),
                        Placeholder::make('nama_petani')
                            ->inlineLabel()
                            ->label('Nama Petani')
                            ->content(fn ($record)=>$record->nama_petani),
                        Placeholder::make('ktp_petani')
                            ->inlineLabel()
                            ->label('NIK')
                            ->content(fn ($record)=>$record->ktp_petani),
                        Placeholder::make('catatan')
                            ->inlineLabel()
                            ->content(fn ($record) => new HtmlString(
                                '<p>' . ($record->catatan ?? 'Tidak ada catatan'). '</p>'
                            ))
                    ]),

                Section::make('Wilayah')
                    ->aside()
                    ->description('Domisili Administratif')
                    ->schema([
                        Placeholder::make('provinsi')
                            ->inlineLabel()
                            ->content(fn ($record)=>$record->provinsi->nama),
                        Placeholder::make('kabupaten')
                            ->inlineLabel()
                            ->content(fn ($record)=>$record->kabupaten->nama_kab),
                        Placeholder::make('kecamatan')
                            ->inlineLabel()
                            ->content(fn ($record)=>$record->kecamatan->nama_kecamatan),
                        Placeholder::make('desa')
                            ->inlineLabel()
                            ->content(fn ($record)=>$record->desa->nama_desa)
                    ])
            ]);
    }
}
