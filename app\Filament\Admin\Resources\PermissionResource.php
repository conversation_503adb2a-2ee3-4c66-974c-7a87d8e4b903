<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\PermissionResource\Pages\CreatePermission;
use App\Filament\Admin\Resources\PermissionResource\Pages\EditPermission;
use App\Filament\Admin\Resources\PermissionResource\Pages\ListPermissions;
use App\Filament\Admin\Resources\PermissionResource\Pages\ViewPermission;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionResource extends Resource
{
    protected static ?string $model = Permission::class;

    protected static ?string $navigationIcon = 'heroicon-o-lock-closed';

    protected static ?string $navigationGroup = 'Manajemen Akses';

    protected static ?int $navigationSort = 1;

    public static function getLabel(): string
    {
        return 'Permission';
    }

    public static function getPluralLabel(): string
    {
        return 'Permissions';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()
                    ->schema([
                        Grid::make(2)->schema([
                            TextInput::make('name')
                                ->label('Nama Permission')
                                ->required(),
                            Select::make('guard_name')
                                ->label('Guard Name')
                                ->options([
                                    'web' => 'web',
                                    'api' => 'api',
                                ])
                                ->default('web')
                                ->live()
                                ->afterStateUpdated(fn(\Filament\Forms\Set $set) => $set('roles', []))
                                ->required(),
                            Select::make('roles')
                                ->multiple()
                                ->label('Roles')
                                ->relationship(
                                    name: 'roles',
                                    titleAttribute: 'name',
                                    modifyQueryUsing: function (Builder $query, $get) {
                                        if (!empty($get('guard_name'))) {
                                            $query->where('guard_name', $get('guard_name'));
                                        }
                                        return $query;
                                    }
                                )
                                ->preload(),
                        ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
			->deferLoading()
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->searchable(),
                TextColumn::make('name')
                    ->label('Nama Permission')
                    ->searchable(),
                TextColumn::make('guard_name')
                    ->label('Guard Name')
                    ->badge()
                    ->color(fn (string $state): string => $state === 'web' ? 'primary' : 'danger')
                    ->searchable(),
            ])
            ->filters([
                SelectFilter::make('models')
                    ->label('Models')
                    ->multiple()
                    ->options(function () {
                        $models = Permission::query()
                            ->get()
                            ->pluck('name')
                            ->map(function ($name) {
                                $parts = explode(' ', $name);
                                return $parts[1] ?? '';
                            })
                            ->unique()
                            ->filter()
                            ->sort()
                            ->toArray();

                        return array_combine($models, $models);
                    })
                    ->query(function (Builder $query, array $data) {
                        if (isset($data['values']) && count($data['values'])) {
                            $query->where(function (Builder $query) use ($data) {
                                foreach ($data['values'] as $model) {
                                    $query->orWhere('name', 'like', "% {$model} %");
                                }
                            });
                        }

                        return $query;
                    }),
                SelectFilter::make('guard_name')
                    ->label('Guard Name')
                    ->multiple()
                    ->options([
                        'web' => 'web',
                        'api' => 'api',
                    ]),
            ])->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
                BulkAction::make('Attach to roles')
                    ->label('Tambahkan ke Roles')
                    ->action(function (Collection $records, array $data): void {
                        Role::whereIn('id', $data['roles'])->each(function (Role $role) use ($records): void {
                            $records->each(fn(Permission $permission) => $role->givePermissionTo($permission));
                        });
                    })
                    ->form([
                        Select::make('roles')
                            ->multiple()
                            ->label('Roles')
                            ->options(Role::query()->pluck('name', 'id'))
                            ->required(),
                    ])->deselectRecordsAfterCompletion(),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListPermissions::route('/'),
            'create' => CreatePermission::route('/create'),
            'edit' => EditPermission::route('/{record}/edit'),
            'view' => ViewPermission::route('/{record}'),
        ];
    }
}
