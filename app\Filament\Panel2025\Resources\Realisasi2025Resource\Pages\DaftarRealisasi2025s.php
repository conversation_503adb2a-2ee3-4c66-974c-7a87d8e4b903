<?php

namespace App\Filament\Panel2025\Resources\Realisasi2025Resource\Pages;

use App\Filament\Panel2025\Resources\Realisasi2025Resource;
use App\Models\Commitment2025;
use App\Models\PengajuanVerifikasi;
use App\Models\Realisasi2025;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Support\Enums\Alignment;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\ColumnGroup;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class DaftarRealisasi2025s extends ListRecords
{
    protected static string $resource = Realisasi2025Resource::class;
	// protected static ?string $title = 'Data Realisasi';

    public ?string $noijin = null;
    public ?string $noPengajuan = null;
    public ?string $commitmentId = null;
    public function mount(): void
	{
		// Ambil nilai no_ijin dari request
		$this->noijin = request('noijin');
		$this->noPengajuan = request('pengajuan');
		// Simpan dalam variabel agar lebih mudah dibaca
		$noIjin = $this->noijin;

		// Format no_ijin agar sesuai dengan format yang diharapkan
		$noijinFormatted = substr($noIjin, 0, 4) . '/' . 
			substr($noIjin, 4, 2) . '.' . 
			substr($noIjin, 6, 3) . '/' . 
			substr($noIjin, 9, 1) . '/' . 
			substr($noIjin, 10, 2) . '/' . 
			substr($noIjin, 12, 4);

		// Ambil ID dari Commitment2025 berdasarkan no_ijin yang telah diformat
		$this->commitmentId = Commitment2025::where('no_ijin', $noijinFormatted)->value('id');
	}
	public function getTitle(): string
	{
		// $isImportir = Auth::user()->hasRole('importir');
		$isVerifikator = Auth::user()->hasAnyRole(['Super Admin', 'admin', 'verifikator']);
		if($isVerifikator){
			return 'Verifikasi Data';
		}
		return 'Laporan Realisasi Komitmen Tanam dan Produksi';
	}
	public function getHeading(): string
	{
		$isVerifikator = Auth::user()->hasAnyRole(['Super Admin', 'admin', 'verifikator']);
		if($isVerifikator){
			return 'Daftar Lokasi Komitmen';
		}
		return 'Laporan Realisasi Komitmen Tanam dan Produksi';
	}

	public function getSubheading(): ?string
	{
        $noIjin = $this->noijin;
		return 'untuk PPRK No.: '. $noIjin;
	}

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }

    public function table(Table $table): Table
    {
        return $table
			->deferLoading()
			->emptyStateHeading('Belum ada PKS yang disetujui')
			->emptyStateDescription('Isi data dan unggah berkas PKS lalu tetapkan sebagai FINAL agar PKS anda diperiksa oleh pihak Dinas terkait')
			->emptyStateActions([
				Action::make('createPks')
					->label('Lengkapi dan Ajukan PKS')
					->url(route('filament.panel2025.resources.commitment2025s.daftarPks', ['record' => $this->commitmentId])) //id dari Commitment2025 dengan no_ijin = $noIjin
					->icon('heroicon-m-plus')
					->button(),
			])
            ->modifyQueryUsing(function ($query) {
				$user = Auth::user();
				$isImportir = $user->hasRole('importir');
				$noijin = $this->noijin;
			
				if (empty($noijin)) {
					return $query;
				}
			
				$noijinFormatted = substr($noijin, 0, 4) . '/' . 
								   substr($noijin, 4, 2) . '.' . 
								   substr($noijin, 6, 3) . '/' . 
								   substr($noijin, 9, 1) . '/' . 
								   substr($noijin, 10, 2) . '/' . 
								   substr($noijin, 12, 4);
			
				$statusDinas = 2;
			
				// Ambil kode_poktan unik dari Pks2025 berdasarkan no_ijin dan status_dinas
				$kodePoktan = \App\Models\Pks2025::where('no_ijin', $noijinFormatted)
					->where('status_dinas', $statusDinas)
					->pluck('kode_poktan')
					->unique()
					->toArray();
			
				return $query
					->when($isImportir, fn ($query) => 
						$query->where('npwp', $user->npwp)
					)
					->where('no_ijin', $noijinFormatted)
					->whereIn('kode_poktan', $kodePoktan);
			})
            ->defaultGroup('poktan.nama_kelompok')->groupingSettingsHidden()
            ->groups([
                Group::make('poktan.nama_kelompok')->collapsible(),
            ])
            ->columns([
                TextColumn::make('kode_spatial')
                    ->label('No. Lokasi')
                    ->searchable(),
                TextColumn::make('anggota.nama_petani')
                    ->label('Nama Petani')
                    ->searchable(),
                ColumnGroup::make('Tanggal Verifikasi',[
                    TextColumn::make('vt_at')
                        ->date()
                        ->label('Tanam')
                        ->sortable(),
                    TextColumn::make('vp_at')
                        ->date()
                        ->label('Produksi')
                        ->sortable(),
                ]),
                ColumnGroup::make('Status Akhir Verifikasi', [
                    TextColumn::make('vt_status')
                        ->label('Tanam')
                        ->badge()
						->color(fn (string $state): string => match ($state) {
							'Sesuai' => 'success',
							'Tidak Sesuai' => 'danger',
						})
                        ->placeholder('-')
                        ->numeric()
                        ->sortable(),
                    TextColumn::make('vp_status')
                        ->label('Produksi')
                        ->placeholder('-')
                        ->badge()
						->color(fn (string $state): string => match ($state) {
							'Sesuai' => 'success',
							'Tidak Sesuai' => 'danger',
						})
                        ->numeric()
                        ->sortable(),
                ]),
            ])
            ->filters([
                //
            ])
            ->actions([
                Action::make('reporting')
                    ->label('Pelaporan Realisasi Komitmen')
                    ->hiddenLabel()
                    // ->visible(function ($record){
                    //     $isImportir = Auth::user()->hasRole('importir');
                    //     $hasPengajuan = PengajuanVerifikasi::where('no_ijin', $record->no_ijin)->where('status', '!=', 3)->select('no_ijin')->orderByDesc('created_at')->first();
                    //     if($isImportir && $hasPengajuan){
                    //         return false;
                    //     }
                    //     return true;
                    // })
                    ->tooltip(function (){
						$isImportir = Auth::user()->hasRole('importir');
						if($isImportir)
						{
							return 'Isi Laporan Realisasi';
						}
						return 'Verifikasi Laporan Realisasi';
					})
                    ->color('success')
                    ->url(function ($record) {
                        $isImportir = Auth::user()->hasRole('importir');
                        $isVerifikator = Auth::user()->hasRole('verifikator');
                        if($isImportir){
                            return route('filament.panel2025.resources.realisasi2025s.lapgiat', ['record' => $record->id]);
                        }
                        if($isVerifikator){
                            return route('filament.panel2025.resources.pengajuan-verifikasis.verifSpatial', [
								'record' =>$record->id,
                                'pengajuan' => $this->noPengajuan,
                                'spatial' => $record->kode_spatial,
                            ]);
                        }
                    })
                    ->icon('icon-ui-checks'),

                Action::make('viewlapgiat')
                    ->hiddenLabel()
                    // ->visible(function ($record){
                    //     $isImportir = Auth::user()->hasRole('importir');
                    //     $hasPengajuan = PengajuanVerifikasi::where('no_ijin', $record->no_ijin)->where('status', '!=', '6')->select('no_ijin')->orderByDesc('created_at')->first();

                    //     //jika importir dan memiliki pengajuan, return false. jika importir tapi tidak memiliki pengajuan return true;
                    //     if($isImportir && !$hasPengajuan){
                    //         return false;
                    //     }
                    //     return true;
                    // })
                    ->tooltip('Lihat data Realisasi Tanam-Produksi')
                    ->color('info')
                    ->url(function ($record){
                        return route('filament.panel2025.resources.realisasi2025s.viewlapgiat', ['record' => $record->id]);
                    })
                    ->icon('icon-binoculars-fill'),

                // Action::make('goToMap')
				// 	->hidden()
                //     ->label('Peta Lokasi')
                //     ->hiddenLabel()
                //     ->tooltip('Lihat Peta dan Data Realisasi Komitmen')
                //     ->color('info')
                //     ->url(fn ($record) => route('panel.2025.report.singleMap', ['id' => $record->id]))
                //     ->icon('icon-geo-alt-fill'),
            ]);
    }
}
