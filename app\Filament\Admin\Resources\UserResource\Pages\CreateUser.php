<?php

namespace App\Filament\Admin\Resources\UserResource\Pages;

use App\Filament\Admin\Resources\UserResource;
use App\Models\SupportDepartement;
use App\Models\User;
use Filament\Actions;
use Filament\Forms\Form;
use Filament\Forms\Components\{FileUpload, Group, Hidden, Radio, Section, Select, TextInput, Toggle};
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\HtmlString;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class CreateUser extends CreateRecord
{
    protected static string $resource = UserResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

	public function getHeading(): string
	{
		$thisTitle = 'Pengguna Baru';
        return $thisTitle;
	}
    protected static ?string $title = 'Pengguna Baru';

    public function getSubheading(): ?string
    {
		$subTitle = 'Menambahkan Pengguna baru. (Khusus: Akun <PERSON>t dan <PERSON>tial dan RIPH.)';
        return $subTitle;
    }

	public function form(Form $form): Form
	{
		return $form
			->schema([
				Section::make('Kredensial')
					->aside()
					->description('Data kredensial pengguna baru')
					->schema([
						Hidden::make('status')
							->default('Aktif'),
						Hidden::make('email_verified_at')
							->default(now()),
						TextInput::make('name')
							->required()
							->label('Nama Lengkap')
							->inlineLabel()
							->live(onBlur:true)
							->afterStateUpdated(fn ($set, $state)=>$set('dataadmin.nama', $state))
							->maxLength(255),
						TextInput::make('username')
							->required()
							->inlineLabel()
							->unique('users', 'username')
							->maxLength(255),
						TextInput::make('email')
							->email()
							->inlineLabel()
							->unique()
							->required()
							->maxLength(255),
						TextInput::make('password')
							->password()
							->inlineLabel()
							->label('Default Password')
							->required()->revealable()
							->maxLength(255)
							->minLength(8)
							->helperText(fn () => new HtmlString('<span class="text-danger-500">Informasikan agar Pengguna ini segera mengganti password Utama.</span>')),
					]),

				Section::make('Peran')
					->aside()
					->description('Ijin Akses/Peran yang diberikan kepada pengguna ini. Untuk Verifikator dan Dinas, Gunakan Menu Registrasi. Untuk Importir Tidak perlu mendaftar.')
					->schema([
						Select::make('roles')
							// ->multiple()
							->hiddenLabel()
							->required()
							->reactive()
							->placeholder('pilih satu peran')
							->options([
								'3' => 'Direktur',
								'4' => 'Verifikator',
								'5' => 'Tim Spatial',
								'6' => 'Dinas',
								'8' => 'Tim RIPH',
								'9' => 'Support'
							])
							->preload(),

						Select::make('departement_id')
							->label('Support Departement')
							->inlineLabel()
							->multiple()
							->reactive()
							->options(fn () => SupportDepartement::pluck('name', 'id'))
							->visible(fn ($get) => $get('roles') === '9')
					]),

				Section::make('Profil Pengguna')
					->aside()
					->description(fn () => new HtmlString('<span class="text-danger-500">Data profil pengguna baru. Informasikan kepada pengguna agar segera melengkapi/mengubah data.</span>'))
					->schema([
						Group::make()
							->relationship('dataadmin')
							->schema([
								TextInput::make('nama'),
								TextInput::make('jabatan')
									->required(),
								TextInput::make('nip')
									->label('NIP')
									->required(),

								FileUpload::make('sign_img')
									->openable()
									// ->required()
									->moveFiles()
									->deletable()
									->imageEditor()
									->maxSize(2048)
									->downloadable()
									->disk('public')
									->previewable(true)
									->visibility('public')
									->hiddenLabel()
									->reactive()
									->imagePreviewHeight('100')
									->panelAspectRatio('4:1')
									->fetchFileInformation(true)
									->visible(fn ($get) => $get('../roles') === '3')
									->helperText('Maksimal 2MB, format gambar: .jpg, .jpeg, .png')
									->directory(function ($record) {
										return "uploads/ttdpejabat/";
									})
									->rules([
										'mimetypes:image/jpeg,image/png',
										// 'mimes:jpg,jpeg,png,pdf',
									])
									->validationMessages([
										'mimetypes' => 'Hanya file gambar (JPG, JPEG, PNG) yang diperbolehkan',
										'mimes' => 'Ekstensi file harus .jpg, .jpeg, .png',
									])
									->getUploadedFileNameForStorageUsing(
										function (TemporaryUploadedFile $file, $get): string {
											return 'ttdpejabat'.$get('username') . '_'  . '.' . $file->getClientOriginalExtension();
										}
									),
							])
					]),
			]);
	}

	protected function handleRecordCreation(array $data): Model
	{
		// Gunakan proses pembuatan user dari Filament
		$user = parent::handleRecordCreation($data);
		if (!empty($data['roles'])) {
			DB::table('model_has_roles')->updateOrInsert(
				[
					'model_type' => User::class,
					'model_id' => $user->id,
				],
				[
					'role_id' => $data['roles'],
				]
			);
		}

		return $user;
	}
}
