<?php

namespace App\Filament\Panel2024\Pages;

use App\Models\Commitment2024;
use App\Models\Summary2024;
use Filament\Facades\Filament;
use Filament\Forms\Components\{DatePicker, Section, Select};
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Support\Facades\FilamentIcon;
use Filament\Widgets\Widget;
use Filament\Pages\Dashboard as BaseDashboard;
use Filament\Pages\Dashboard\Concerns\HasFiltersForm;
use Filament\Widgets\WidgetConfiguration;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class Dashboard extends BaseDashboard
{
	use HasFiltersForm;
    protected static string $routePath = '/';

    protected static ?int $navigationSort = -98;

	public function filtersForm(Form $form): Form
    {
		$user = Auth::user();

		if ($user->hasRole('importir')) {
			// Untuk importir, ambil periode dari komitmen mereka sendiri
			$periodetahunOptions = Commitment2024::where('npwp', $user->npwp)
				->select('periodetahun')
				->whereNotNull('periodetahun') // Filter out null values
				->where('periodetahun', '!=', '') // Filter out empty strings
				->distinct()
				->orderBy('periodetahun', 'asc')
				->pluck('periodetahun', 'periodetahun')
				->filter() // Remove any null/empty values from the collection
				->toArray();

			$lastPeriode = Commitment2024::where('npwp', $user->npwp)
				->whereNotNull('periodetahun')
				->where('periodetahun', '!=', '')
				->orderBy('periodetahun', 'desc')
				->value('periodetahun');
		} else {
			// Untuk admin, gunakan Summary2024 seperti sebelumnya
			$periodetahunOptions = Summary2024::select('periode')
				->whereNotNull('periode') // Filter out null values
				->where('periode', '!=', '') // Filter out empty strings
				->orderBy('periode', 'asc')
				->pluck('periode', 'periode')
				->filter() // Remove any null/empty values from the collection
				->toArray();

			$lastPeriode = Summary2024::whereNotNull('periode')
				->where('periode', '!=', '')
				->orderBy('periode', 'asc')
				->value('periode');
		}

		// Ensure we have valid options before adding 'all'
		$periodetahunOptions = array_filter($periodetahunOptions, function($value, $key) {
			return !is_null($value) && !is_null($key) && $value !== '' && $key !== '';
		}, ARRAY_FILTER_USE_BOTH);

		$periodetahunOptions = ['all' => 'Semua Periode'] + $periodetahunOptions;
		$defaultPeriode = $lastPeriode ?? 'all';

		return $form
			->schema([
				Select::make('periodeFilter')
					->label('Pilih Periode')
					->inlineLabel()
					->visible()
					->columnStart(3)
					->options($periodetahunOptions)
					->default($defaultPeriode)
					->placeholder('Pilih Periode Tahun')
					->live(),
			])->columns(3);
	}


    /**
     * @var view-string
     */
    protected static string $view = 'filament-panels::pages.dashboard';
	protected static ?string $title = 'Panel 2024';
    public static function getNavigationLabel(): string
    {
        return static::$navigationLabel ??
            static::$title ??
            __('filament-panels::pages/dashboard.title');
    }

    public static function getNavigationIcon(): string | Htmlable | null
    {
        return static::$navigationIcon
            ?? FilamentIcon::resolve('panels::pages.dashboard.navigation-item')
            ?? (Filament::hasTopNavigation() ? 'heroicon-m-home' : 'heroicon-o-home');
    }

    public static function getRoutePath(): string
    {
        return static::$routePath;
    }

    /**
     * @return array<class-string<Widget> | WidgetConfiguration>
     */
    public function getWidgets(): array
    {
        return Filament::getWidgets();
    }

    /**
     * @return array<class-string<Widget> | WidgetConfiguration>
     */
    public function getVisibleWidgets(): array
    {
        return $this->filterVisibleWidgets($this->getWidgets());
    }

    /**
     * @return int | string | array<string, int | string | null>
     */
    public function getColumns(): int | string | array
    {
        return 2;
    }

    public function getTitle(): string | Htmlable
    {
        return new HtmlString('Panel 2024');
    }

	public function getSubheading(): string|Htmlable
    {
        return new HtmlString('<span class="text-md">Panel ini digunakan untuk memantau data tahun 2024 dan sebelumnya.</span>');
    }
}
