<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\MasterKabupatenResource\Pages;
use App\Filament\Admin\Resources\MasterKabupatenResource\RelationManagers;
use App\Filament\Admin\Resources\MasterKabupatenResource\RelationManagers\KecamatanRelationManager;
use App\Models\MasterKabupaten;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MasterKabupatenResource extends Resource
{
    protected static ?string $model = MasterKabupaten::class;
    protected static ?string $modelLabel = 'Master Kabupaten';
    protected static ?string $pluralModelLabel = 'Daftar Kabupaten/Kota';

    protected static ?int $navigationSort = 1;
	protected static ?string $navigationGroup = 'Data Induk';
    protected static ?string $navigationLabel = 'Master Kabupaten';
    protected static ?string $navigationIcon = 'heroicon-o-globe-asia-australia';

	public static function shouldRegisterNavigation(): bool
    {
		return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('provinsi_id')
                    ->required()
                    ->maxLength(2),
                Forms\Components\TextInput::make('kabupaten_id')
                    ->required()
                    ->maxLength(4),
                Forms\Components\Textarea::make('kode_dagri')
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('nama_kab')
                    ->required()
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('lat')
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('lng')
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('polygon')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('provinsi_id')
                    ->searchable(),
                Tables\Columns\TextColumn::make('kabupaten_id')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            KecamatanRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMasterKabupatens::route('/'),
            'create' => Pages\CreateMasterKabupaten::route('/create'),
            'view' => Pages\ViewMasterKabupaten::route('/{record}'),
            'edit' => Pages\EditMasterKabupaten::route('/{record}/edit'),
        ];
    }
}
