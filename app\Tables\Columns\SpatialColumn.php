<?php

namespace App\Tables\Columns;

use Filament\Tables\Columns\Column;
use Illuminate\Database\Eloquent\Builder;

class SpatialColumn extends Column
{
    protected string $view = 'tables.columns.spatial-column';

	public function getStatus(): string
    {
        $record = $this->getRecord();

        $fields = ['latitude', 'longitude', 'polygon'];
        $missing = [];

        foreach ($fields as $field) {
            if (empty($record->{$field})) {
                $missing[] = ucfirst($field); // Ubah "latitude" jadi "Latitude"
            }
        }

        return empty($missing) ? 'Lengkap' : implode(', ', $missing);
    }
	public function applySort(Builder $query, string $direction = 'asc'): Builder
    {
        return $query->orderByRaw("
            CASE
                WHEN latitude IS NOT NULL AND longitude IS NOT NULL AND polygon IS NOT NULL THEN 1
                ELSE 2
            END $direction
        ");
    }
}
