<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\AjuVerifProduksi2024;
use App\Models\User;

class AjuVerifProduksi2024Policy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any AjuVerifProduksi2024');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, AjuVerifProduksi2024 $ajuverifproduksi2024): bool
    {
        return $user->checkPermissionTo('view AjuVerifProduksi2024');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create AjuVerifProduksi2024');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, AjuVerifProduksi2024 $ajuverifproduksi2024): bool
    {
        return $user->checkPermissionTo('update AjuVerifProduksi2024');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, AjuVerifProduksi2024 $ajuverifproduksi2024): bool
    {
        return $user->checkPermissionTo('delete AjuVerifProduksi2024');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any AjuVerifProduksi2024');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, AjuVerifProduksi2024 $ajuverifproduksi2024): bool
    {
        return $user->checkPermissionTo('restore AjuVerifProduksi2024');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any AjuVerifProduksi2024');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, AjuVerifProduksi2024 $ajuverifproduksi2024): bool
    {
        return $user->checkPermissionTo('replicate AjuVerifProduksi2024');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder AjuVerifProduksi2024');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, AjuVerifProduksi2024 $ajuverifproduksi2024): bool
    {
        return $user->checkPermissionTo('force-delete AjuVerifProduksi2024');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any AjuVerifProduksi2024');
    }
}
