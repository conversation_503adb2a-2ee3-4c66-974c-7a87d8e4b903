<?php

namespace App\Filament\Panel2025\Resources\Pks2025Resource\Pages;

use App\Filament\Panel2025\Resources\Pks2025Resource;
use App\Models\Varietas;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Support\Facades\Auth;

class PusatEditPks2025 extends EditRecord
{
    protected static string $resource = Pks2025Resource::class;
    public static string | Alignment $formActionsAlignment = Alignment::Right;

	protected static ?string $title = 'Verifikasi PKS';
    public function getHeading(): string
	{
        return 'Verifikasi PKS';
	}

    public function getSubheading(): ?string
    {
		$poktan = $this->record->poktan->nama_kelompok;
        return 'Poktan '.$poktan. ', No. Perjanjian: '. $this->record->no_perjanjian;
    }

	public function form(Form $form): Form
	{
		return $form
			->schema([
				Section::make('Berkas PKS')
					->aside()
					->description('Unggah berkas PKS  (Wajib)')
					->schema([
						FileUpload::make('berkas_pks')
							->openable()
							->hiddenLabel()
							->deletable(false)
							->columnSpan(1)
							->downloadable()
							->label('Berkas PKS')
							->panelAspectRatio('5:1')
							->imagePreviewHeight('50')
							->fetchFileInformation(true),
					]),

				Section::make('Data PKS')
					->aside()
					->description('Data Isian Detail PKS')
					->schema([
						Placeholder::make('Kelompoktani')
							->inlineLabel()
							->content(fn ($record)=>$record->nama_poktan),
						Placeholder::make('Jumlah Lokasi dipilih')
							->inlineLabel()
							->content(fn ($record)=>$record->jumlah_anggota .' titik'),
						Placeholder::make('Total Luas dipilih')
							->inlineLabel()
							->content(fn ($record)=> number_format($record->luas_rencana,0,',','.') . ' m2') ,
						Placeholder::make('Total Luas dipilih')
							->inlineLabel()
							->content(fn ($record)=> number_format($record->luas_rencana,0,',','.') . ' m2') ,
						Placeholder::make('tgl_perjanjian_start')
							->inlineLabel()
							->label('Tanggal Mulai')
							->content(fn ($record) => Carbon::parse($record->tgl_perjanjian_start)->translatedFormat('d F Y')),
						Placeholder::make('tgl_perjanjian_end')
							->inlineLabel()
							->label('Tanggal Berakhir')
							->content(fn ($record) => Carbon::parse($record->tgl_perjanjian_end)->translatedFormat('d F Y')),
						Placeholder::make('varietas')
							->inlineLabel()
							->label('Varietas')
							->content(fn ($record) => $record->varietas->nama_varietas),
					]),

				Section::make('Verifikasi')
					->aside()
					->description('Laporan Hasil Verifikasi Data PKS')
					->schema([
						Hidden::make('verif_by')
							->default(Auth::user()->id)
							->formatStateUsing(fn () => Auth::user()->id),
						Hidden::make('verif_at')
							->default(today()),
						Placeholder::make('status_dinas')
							->label('Status dari Dinas')
							->inlineLabel()
							->content(fn ($get) => view('components.status-icon', ['status' => $get('status_dinas')])),
						Select::make('status')
							->inlineLabel()
							->required()
							->reactive()
							->options([
								'Sesuai' => 'Sesuai',
								'Tidak Sesuai' => 'Tidak Sesuai'
							]),
						Textarea::make('note')
							->label('Catatan Verifikasi')
							->autosize()
							->required(fn ($get) => $get('status') === 'Tidak Sesuai'),
					])
			])->columns(3);
	}
}
