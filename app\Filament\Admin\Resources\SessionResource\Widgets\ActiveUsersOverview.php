<?php

namespace App\Filament\Admin\Resources\SessionResource\Widgets;

use App\Models\Session;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ActiveUsersOverview extends BaseWidget
{
    protected static ?string $pollingInterval = '15s';

    /**
     * Menentukan apakah widget ini dapat ditampilkan
     * Widget ini hanya ditampilkan untuk admin dan super admin
     */
    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && $user->hasAnyRole(['admin', 'Super Admin']);
    }

    protected function getStats(): array
    {
        // 1. Pengguna Online (auth/logged in)
        $onlineUsers = Session::query()
            ->whereNotNull('user_id')
            ->active()
            ->count();

        // 2. Importir online
        $onlineImportir = Session::query()
            ->whereNotNull('user_id')
            ->active()
            ->join('users', 'sessions.user_id', '=', 'users.id')
            ->join('model_has_roles', function($join) {
                $join->on('users.id', '=', 'model_has_roles.model_id')
                    ->where('model_has_roles.model_type', '=', 'App\\Models\\User');
            })
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->where('roles.name', '=', 'importir')
            ->count();

        // 3. Aktivitas pengguna hari ini (hanya terautentikasi)
        $todayUsers = Session::query()
            ->whereNotNull('user_id')
            ->where('last_activity', '>=', Carbon::today()->getTimestamp())
            ->count();

        // 4. Tamu hari ini (hanya guest)
        $todayGuests = Session::query()
            ->whereNull('user_id')
            ->where('last_activity', '>=', Carbon::today()->getTimestamp())
            ->count();

        return [
            Stat::make('Pengguna Online', $onlineUsers)
                ->description('Pengguna sedang online')
                ->descriptionIcon('heroicon-m-user')
                ->color('success'),

            Stat::make('Importir', $onlineImportir)
                ->description('Importir sedang online')
                ->descriptionIcon('heroicon-m-user-circle')
                ->color('warning'),

            Stat::make('Aktivitas', $todayUsers)
                ->description('Pengguna hari ini')
                ->descriptionIcon('heroicon-m-calendar')
                ->color('primary'),

            Stat::make('Tamu', $todayGuests)
                ->description('Tamu hari ini')
                ->descriptionIcon('heroicon-m-user-group')
                ->color('gray'),
        ];
    }
}
