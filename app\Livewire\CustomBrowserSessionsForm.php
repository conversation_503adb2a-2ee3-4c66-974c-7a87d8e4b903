<?php

namespace App\Livewire;

use App\Support\CustomAgent;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Joaopaulolndev\FilamentEditProfile\Livewire\BrowserSessionsForm;

class CustomBrowserSessionsForm extends BrowserSessionsForm
{
    /**
     * Override metode getSessions untuk menangani aplikasi mobile dengan lebih baik
     */
    public static function getSessions(): array
    {
        if (config(key: 'session.driver') !== 'database') {
            return [];
        }

        return collect(
            value: DB::connection(config(key: 'session.connection'))->table(table: config(key: 'session.table', default: 'sessions'))
                ->where(column: 'user_id', operator: Auth::user()->getAuthIdentifier())
                ->latest(column: 'last_activity')
                ->get()
        )->map(callback: function ($session): object {
            // Gunakan CustomAgent alih-alih Agent standar
            $agent = self::createCustomAgent($session);
            
            // Coba ekstrak informasi dari payload session jika tersedia
            $deviceInfo = self::extractDeviceInfoFromPayload($session);
            
            // Gabungkan informasi dari agent dan payload
            $browser = $deviceInfo['browser'] ?? $agent->browser();
            $platform = $deviceInfo['platform'] ?? $agent->platform();
            $isDesktop = $deviceInfo['is_desktop'] ?? $agent->isDesktop();
            $isMobile = $deviceInfo['is_mobile'] ?? $agent->isMobile();
            $isTablet = $deviceInfo['is_tablet'] ?? $agent->isTablet();
            
            // Deteksi aplikasi mobile kustom
            if ($agent->isCustomMobileApp()) {
                $isDesktop = false;
                $isMobile = true;
                $isTablet = false;
            }

            return (object) [
                'device' => [
                    'browser' => $browser,
                    'desktop' => $isDesktop,
                    'mobile' => $isMobile,
                    'tablet' => $isTablet,
                    'platform' => $platform,
                ],
                'ip_address' => $session->ip_address,
                'is_current_device' => $session->id === request()->session()->getId(),
                'last_active' => Carbon::createFromTimestamp($session->last_activity)->diffForHumans(),
            ];
        })->toArray();
    }

    /**
     * Buat instance CustomAgent alih-alih Agent standar
     */
    protected static function createCustomAgent(mixed $session)
    {
        return tap(
            value: new CustomAgent,
            callback: fn ($agent) => $agent->setUserAgent(userAgent: $session->user_agent)
        );
    }
    
    /**
     * Ekstrak informasi perangkat dari payload session jika tersedia
     */
    protected static function extractDeviceInfoFromPayload($session)
    {
        $result = [
            'browser' => null,
            'platform' => null,
            'is_desktop' => null,
            'is_mobile' => null,
            'is_tablet' => null,
        ];
        
        try {
            // Coba unserialize payload
            $payload = @unserialize(base64_decode($session->payload));
            
            if ($payload === false) {
                // Coba decode sebagai JSON jika unserialize gagal
                $payload = json_decode(base64_decode($session->payload), true);
            }
            
            // Jika payload berisi informasi perangkat
            if (is_array($payload) && isset($payload['device_name']) && $payload['device_name'] === 'mobile_app') {
                $result['browser'] = $payload['browser'] ?? 'Simethris Mobile App';
                $result['platform'] = $payload['platform'] ?? 'Mobile';
                $result['is_desktop'] = false;
                $result['is_mobile'] = true;
                $result['is_tablet'] = false;
            }
        } catch (\Exception $e) {
            // Jika terjadi error, gunakan nilai default
        }
        
        return $result;
    }
}
