<?php

namespace App\Filament\Admin\Resources\MobileAppResource\Pages;

use App\Filament\Admin\Resources\MobileAppResource;
use App\Models\MobileApp;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class ListMobileApps extends ListRecords
{
    protected static string $resource = MobileAppResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Actions\Action::make('scanDirectory')
				->visible(fn () => Auth::user()->hasAnyRole(['admin', 'Super Admin']))
                ->label('Scan Direktori')
                ->icon('heroicon-o-arrow-path')
                ->action(function () {
                    $this->scanDirectory();
                })
                ->requiresConfirmation()
                ->modalHeading('Scan Direktori Aplikasi')
                ->modalDescription('Sistem akan memindai direktori mobile_apps untuk file APK baru dan memperbarui database. Lanjutkan?')
                ->modalSubmitActionLabel('Scan Direktori'),
        ];
    }

	protected static ?string $title = 'Mobile Apps';


    public function getHeading(): string
	{
        return 'Daftar Versi Mobile Apps';
	}

    public function getSubheading(): ?string
    {
        return 'Aplikasi Simethris mobile resmi yang tersedia. Hanya unduh dari situs resmi ini';
    }

    protected function scanDirectory()
    {
        $directory = 'mobile_apps';

        // Pastikan direktori ada
        if (!Storage::disk('public')->exists($directory)) {
            Storage::disk('public')->makeDirectory($directory);
            Log::info('Direktori dibuat: ' . $directory);
        }

        // Log untuk debug
        Log::info('Scanning direktori: ' . $directory);

        $files = Storage::disk('public')->files($directory);
        Log::info('File yang ditemukan: ' . count($files), ['files' => $files]);

        $apkFiles = array_filter($files, function ($file) {
            return pathinfo($file, PATHINFO_EXTENSION) === 'apk';
        });

        Log::info('File APK yang ditemukan: ' . count($apkFiles), ['apk_files' => $apkFiles]);

        $newApps = 0;
        $updatedApps = 0;
        $errors = 0;

        foreach ($apkFiles as $file) {
            $fileName = basename($file);

            // Cek apakah aplikasi sudah ada di database
            $existingApp = MobileApp::where('file_name', $fileName)->first();

            if (!$existingApp) {
                // Ekstrak metadata dari nama file
                $metadata = MobileApp::getMetadataFromFileName($fileName);

                // Cek apakah ada file JSON dengan metadata tambahan
                $jsonFileName = pathinfo($fileName, PATHINFO_FILENAME) . '.json';
                $jsonFilePath = $directory . '/' . $jsonFileName;

                if (Storage::disk('public')->exists($jsonFilePath)) {
                    try {
                        $jsonContent = Storage::disk('public')->get($jsonFilePath);
                        $jsonMetadata = json_decode($jsonContent, true);

                        if (is_array($jsonMetadata)) {
                            $metadata = array_merge($metadata, $jsonMetadata);
                        }
                    } catch (\Exception $e) {
                        Log::error('Error reading JSON metadata file: ' . $e->getMessage(), [
                            'file' => $jsonFilePath,
                            'exception' => $e
                        ]);
                    }
                }

                try {
                    // Buat entri baru di database
                    MobileApp::create($metadata);
                    $newApps++;
                } catch (\Exception $e) {
                    Log::error('Error creating mobile app entry: ' . $e->getMessage(), [
                        'file' => $fileName,
                        'metadata' => $metadata,
                        'exception' => $e
                    ]);
                    $errors++;
                }
            } else {
                // Update file size jika perlu
                $updatedApps++;
            }
        }

        // Notifikasi hasil
        if ($newApps > 0) {
            Notification::make()
                ->title($newApps . ' aplikasi baru ditemukan dan ditambahkan.')
                ->success()
                ->send();
        }

        if ($updatedApps > 0) {
            Notification::make()
                ->title($updatedApps . ' aplikasi yang sudah ada diperbarui.')
                ->success()
                ->send();
        }

        if ($newApps === 0 && $updatedApps === 0) {
            Notification::make()
                ->title('Tidak ada aplikasi baru yang ditemukan.')
                ->info()
                ->send();
        }

        if ($errors > 0) {
            Notification::make()
                ->title($errors . ' error terjadi saat memproses file.')
                ->body('Periksa log untuk detail.')
                ->danger()
                ->send();
        }
    }
}
