<?php

namespace App\Observers;

use App\Models\PengajuanSkl2025;
use App\Models\User;
use Filament\Notifications\Notification;

class PengajuanSkl2025Observer
{
    /**
     * Handle the PengajuanSkl2025 "created" event.
     */
    public function created(PengajuanSkl2025 $pengajuanSkl2025): void
    {
		$direktur = User::whereHas('roles', function ($query) {
			$query->whereIn('name', ['Super Admin', 'direktur']);
		})->get();

		Notification::make()
			->title("Pengajuan SKL")
			->body("Administrator telah merekomen<PERSON>ikan Surat Keterangan Lunas untuk
				<p>
					<ul class='list-group'>
						<li class='list-group-items flex justify-between'>
							<span class='col-4'>PPRK No:</span>
							<span class='col-7 font-bold text-primary-500'>{$pengajuanSkl2025->no_ijin}</span>
						</li>
						<li class='list-group-items flex justify-between'>
							<span class='col-4'>Atas Nama:</span>
							<span class='col-7 font-bold text-success-500'>{$pengajuanSkl2025->datauser->company_name}</span>
						</li>
					</ul>
				</p>")
			->sendToDatabase($direktur);
    }

    /**
     * Handle the PengajuanSkl2025 "updated" event.
     */
    public function updated(PengajuanSkl2025 $pengajuanSkl2025): void
    {
        $pusat = User::whereHas('roles', function ($query) {
			$query->whereIn('name', ['Super Admin', 'admin']);
		})->get();
		if ($pengajuanSkl2025->wasChanged('status') && $pengajuanSkl2025->status === '1') {
			Notification::make()
				->title("Pengajuan SKL")
				->body("Pimpinan telah menetapkan penerbitan Surat Keterangan Lunas
					<p>
						<table class='table w-100'>
							<tbody>
								<tr>
									<td>PPRK No</td>
									<td class='font-bold text-primary-500'>{$pengajuanSkl2025->no_ijin}</td>
								</tr>
								<tr>
									<td>Atas Nama</td>
									<td class='font-bold text-primary-500'>{$pengajuanSkl2025->datauser->company_name}</td>
								</tr>
								<tr>
									<td>Status</td>
									<td class='font-bold text-success-500'>DISETUJUI</td>
								</tr>
							</tbody>
						</table>
					</p>")
				->sendToDatabase($pusat);
		}
		if ($pengajuanSkl2025->wasChanged('status') && $pengajuanSkl2025->status === '2') {
			Notification::make()
				->title("Pengajuan SKL")
				->body("Pimpinan telah menetapkan penerbitan Surat Keterangan Lunas
					<p>
						<table class='table w-100 '>
							<tbody>
								<tr>
									<td>PPRK No: </td>
									<td class='font-bold text-primary-500'>{$pengajuanSkl2025->no_ijin}</td>
								</tr>
								<tr>
									<td>Atas Nama</td>
									<td class='font-bold text-primary-500'>{$pengajuanSkl2025->datauser->company_name}</td>
								</tr>
								<tr>
									<td>Status</td>
									<td class='font-bold text-danger-500'>DITOLAK</td>
								</tr>
							</tbody>
						</table>
					</p>")
				->sendToDatabase($pusat);
		}
    }

    /**
     * Handle the PengajuanSkl2025 "deleted" event.
     */
    public function deleted(PengajuanSkl2025 $pengajuanSkl2025): void
    {
        //
    }

    /**
     * Handle the PengajuanSkl2025 "restored" event.
     */
    public function restored(PengajuanSkl2025 $pengajuanSkl2025): void
    {
        //
    }

    /**
     * Handle the PengajuanSkl2025 "force deleted" event.
     */
    public function forceDeleted(PengajuanSkl2025 $pengajuanSkl2025): void
    {
        //
    }
}
