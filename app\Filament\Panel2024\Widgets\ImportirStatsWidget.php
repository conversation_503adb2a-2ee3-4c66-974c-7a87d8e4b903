<?php

namespace App\Filament\Panel2024\Widgets;

use App\Models\Commitment2024;
use App\Models\Completed;
use App\Models\DataRealisasi2024;
use App\Models\Lokasi2024;
use Carbon\Carbon;
use Filament\Support\Enums\IconPosition;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Number;

class ImportirStatsWidget extends BaseWidget
{
    use InteractsWithPageFilters;

    protected static ?int $sort = 0;

    public static function canView(): bool
    {
        $user = Auth::user();
        
        return $user->hasRole('importir');
    }

    protected function getStats(): array
    {
        $periode = $this->filters['periodeFilter'] ?? null;
        $npwp = Auth::user()->npwp;

        // Query untuk data komitmen
        $commitments = Commitment2024::where('npwp', $npwp)
            ->when($periode && $periode !== 'all', function ($query) use ($periode) {
                return $query->where('periodetahun', $periode);
            })
            ->get();
        
        // Hitung jumlah RIPH
        $countCommitment = $commitments->count();
        
        // Hitung total komitmen tanam dan produksi
        $commitmentTanam = $commitments->sum('luas_wajib_tanam');
        $commitmentPanen = $commitments->sum('volume_produksi');
        
        // Ambil semua nomor RIPH untuk digunakan dalam query lain
        $noIjinList = $commitments->pluck('no_ijin')->toArray();
        
        // Query untuk data SKL
        $completedCount = Completed::where('npwp', $npwp)
            ->whereIn('no_ijin', $noIjinList)
            ->count();
        
        // Query untuk data petani (lokasi)
        $petaniCount = Lokasi2024::whereIn('no_ijin', $noIjinList)
            ->distinct('anggota_id')
            ->count('anggota_id');
        
        // Query untuk data realisasi tanam dan produksi
        $realisasiData = DataRealisasi2024::whereIn('no_ijin', $noIjinList)
            ->selectRaw('SUM(luas_lahan) as total_luas, SUM(volume) as total_volume')
            ->first();
        
        $totalLuasLahan = $realisasiData ? $realisasiData->total_luas : 0;
        $totalVolume = $realisasiData ? $realisasiData->total_volume : 0;
        
        // Format angka untuk tampilan
        $countCommitmentFormatted = number_format($countCommitment, 0, ',', '.');
        $completedCountFormatted = number_format($completedCount, 0, ',', '.');
        $petaniCountFormatted = number_format($petaniCount, 0, ',', '.');
        
        $commitmentTanamFormatted = number_format($commitmentTanam, 2, ',', '.');
        $commitmentPanenFormatted = number_format($commitmentPanen, 2, ',', '.');
        $totalLuasLahanFormatted = number_format($totalLuasLahan, 2, ',', '.');
        $totalVolumeFormatted = number_format($totalVolume, 2, ',', '.');
        
        // Hitung persentase realisasi
        $persenTanam = $commitmentTanam > 0 ? ($totalLuasLahan / $commitmentTanam) * 100 : 0;
        $persenPanen = $commitmentPanen > 0 ? ($totalVolume / $commitmentPanen) * 100 : 0;
        
        $persenTanamFormatted = number_format($persenTanam, 2, ',', '.');
        $persenPanenFormatted = number_format($persenPanen, 2, ',', '.');

        return [
            Stat::make('', $completedCountFormatted . ' SKL')
                ->label('SKL Terbit')
                ->description("dari " . $countCommitmentFormatted . ' komitmen')
                ->chart([7, 2, 10, 3, 15, 4, 17])
                ->descriptionIcon('icon-journal-bookmark-fill', IconPosition::Before)
                ->color('danger'),
            Stat::make('', $petaniCountFormatted)
                ->label('Partisipan')
                ->description("Petani")
                ->chart([7, 2, 10, 3, 15, 4, 17])
                ->descriptionIcon('icon-people-fill', IconPosition::Before)
                ->color('info'),
            Stat::make('', $totalLuasLahanFormatted . ' ha')
                ->label('Realisasi Tanam')
                ->description("dari " . $commitmentTanamFormatted . " ha komitmen (" . $persenTanamFormatted . "%)")
                ->chart([7, 2, 10, 3, 15, 4, 17])
                ->descriptionIcon('icon-growing-plant', IconPosition::Before)
                ->color('success'),
            Stat::make('', $totalVolumeFormatted . ' ton')
                ->label('Realisasi Produksi')
                ->description("dari " . $commitmentPanenFormatted . " ton komitmen (" . $persenPanenFormatted . "%)")
                ->chart([7, 2, 10, 3, 15, 4, 17])
                ->descriptionIcon('icon-garlic-line', IconPosition::Before)
                ->color('warning'),
        ];
    }
}
