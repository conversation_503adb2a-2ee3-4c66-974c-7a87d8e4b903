<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;

class InvestigateMonitoringAccess extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'monitoring:investigate {--days=30 : Number of days to look back}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Investigate monitoring tool access to the application';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        $cutoff = Carbon::now()->subDays($days)->timestamp;

        $this->info("Investigating monitoring access in the last {$days} days...");

        // Daftar user agent dari sistem monitoring
        $monitoringAgents = [
            'Uptime-Kuma',
            'UptimeRobot',
            'Pingdom',
            'StatusCake',
            'Site24x7',
            'Nagios',
            'Zabbix',
        ];

        $whereClause = [];
        foreach ($monitoringAgents as $agent) {
            $whereClause[] = "user_agent LIKE '%{$agent}%'";
        }

        $whereClauseStr = implode(' OR ', $whereClause);

        // Hitung jumlah session monitoring
        $monitoringCount = DB::table('sessions')
            ->whereRaw("({$whereClauseStr})")
            ->where('last_activity', '>=', $cutoff)
            ->count();

        $this->info("Found {$monitoringCount} monitoring sessions in the last {$days} days.");

        if ($monitoringCount === 0) {
            $this->info("No monitoring sessions found in the specified time period.");
            return Command::SUCCESS;
        }

        // Analisis berdasarkan user agent
        $this->info("\nAnalysis by User Agent:");
        $userAgentStats = DB::table('sessions')
            ->whereRaw("({$whereClauseStr})")
            ->where('last_activity', '>=', $cutoff)
            ->select('user_agent', DB::raw('COUNT(*) as count'))
            ->groupBy('user_agent')
            ->orderBy('count', 'desc')
            ->get();

        $headers = ['User Agent', 'Count'];
        $rows = [];

        foreach ($userAgentStats as $stat) {
            $rows[] = [
                $stat->user_agent,
                $stat->count,
            ];
        }

        $this->table($headers, $rows);

        // Analisis berdasarkan IP address
        $this->info("\nAnalysis by IP Address:");
        $ipStats = DB::table('sessions')
            ->whereRaw("({$whereClauseStr})")
            ->where('last_activity', '>=', $cutoff)
            ->select('ip_address', DB::raw('COUNT(*) as count'))
            ->groupBy('ip_address')
            ->orderBy('count', 'desc')
            ->get();

        $headers = ['IP Address', 'Count', 'IP Info'];
        $rows = [];

        foreach ($ipStats as $stat) {
            // Coba dapatkan informasi IP (dengan penanganan error yang lebih baik)
            try {
                $response = Http::timeout(5)->get("https://ipinfo.io/{$stat->ip_address}/json");
                if ($response->successful()) {
                    $ipInfo = $response->json();
                    $ipInfoStr = isset($ipInfo['org']) ? $ipInfo['org'] : '';
                    $ipInfoStr .= isset($ipInfo['country']) ? ' (' . $ipInfo['country'] . ')' : '';
                    $ipInfoStr .= isset($ipInfo['city']) ? ', ' . $ipInfo['city'] : '';
                    $ipInfoStr = empty($ipInfoStr) ? 'No info available' : $ipInfoStr;
                } else {
                    $ipInfoStr = 'IP info service unavailable';
                }
            } catch (\Exception $e) {
                $ipInfoStr = 'Error: ' . $e->getMessage();
            }

            $rows[] = [
                $stat->ip_address,
                $stat->count,
                $ipInfoStr,
            ];
        }

        $this->table($headers, $rows);

        // Analisis berdasarkan waktu
        $this->info("\nAnalysis by Time:");
        $timeStats = DB::table('sessions')
            ->whereRaw("({$whereClauseStr})")
            ->where('last_activity', '>=', $cutoff)
            ->select(DB::raw('FROM_UNIXTIME(last_activity, "%Y-%m-%d") as date'), DB::raw('COUNT(*) as count'))
            ->groupBy('date')
            ->orderBy('date', 'desc')
            ->get();

        $headers = ['Date', 'Count'];
        $rows = [];

        foreach ($timeStats as $stat) {
            $rows[] = [
                $stat->date,
                $stat->count,
            ];
        }

        $this->table($headers, $rows);

        // Contoh session terakhir
        $this->info("\nLast 5 Monitoring Sessions:");
        $lastSessions = DB::table('sessions')
            ->whereRaw("({$whereClauseStr})")
            ->select('id', 'user_agent', 'ip_address', 'last_activity')
            ->orderBy('last_activity', 'desc')
            ->limit(5)
            ->get();

        $headers = ['ID', 'User Agent', 'IP Address', 'Last Activity'];
        $rows = [];

        foreach ($lastSessions as $session) {
            $rows[] = [
                $session->id,
                $session->user_agent,
                $session->ip_address,
                date('Y-m-d H:i:s', $session->last_activity),
            ];
        }

        $this->table($headers, $rows);

        // Rekomendasi
        $this->info("\nRecommendations:");
        $this->line("1. If these monitoring accesses are unauthorized, they are now being blocked by the middleware.");
        $this->line("2. If they are authorized, contact the monitoring service owner to use the /health endpoint instead.");
        $this->line("3. Run 'php artisan sessions:clean-monitoring' to clean up old monitoring sessions.");

        // Informasi tambahan tentang status pemblokiran
        $this->info("\nCurrent Blocking Status:");
        $this->line("The following monitoring tools are currently being BLOCKED:");
        foreach ((new \App\Http\Middleware\LogMonitoringAccess())->blockedAgents as $agent) {
            $this->line("- $agent");
        }

        $this->line("\nThe following monitoring tools are currently being MONITORED (but not blocked):");
        foreach ((new \App\Http\Middleware\LogMonitoringAccess())->monitoredAgents as $agent) {
            $this->line("- $agent");
        }

        $this->info("\nTo modify this list, edit the \$blockedAgents and \$monitoredAgents arrays in:");
        $this->line("app/Http/Middleware/LogMonitoringAccess.php");

        return Command::SUCCESS;
    }
}
