<?php

namespace App\Support;

use <PERSON><PERSON><PERSON>\Agent\Agent;

class CustomAgent extends Agent
{
    /**
     * Deteksi apakah user agent berasal dari aplikasi mobile kustom
     *
     * @return bool
     */
    public function isCustomMobileApp()
    {
        $userAgent = $this->getUserAgent();
        
        // Deteksi aplikasi mobile berdasarkan user agent
        return (
            strpos($userAgent, 'Simethris Mobile App') !== false ||
            strpos($userAgent, 'Dart') !== false ||
            strpos($userAgent, 'Flutter') !== false ||
            strpos($userAgent, 'okhttp') !== false
        );
    }
    
    /**
     * Dapatkan nama aplikasi mobile kustom
     *
     * @return string|null
     */
    public function getCustomAppName()
    {
        $userAgent = $this->getUserAgent();
        
        if (strpos($userAgent, 'Simethris Mobile App') !== false) {
            return 'Simethris Mobile App';
        }
        
        if (strpos($userAgent, 'Dart') !== false) {
            return 'Simethris Mobile App';
        }
        
        if (strpos($userAgent, 'Flutter') !== false) {
            return 'Simethris Mobile App';
        }
        
        if (strpos($userAgent, 'okhttp') !== false) {
            return 'Simethris Mobile App';
        }
        
        return null;
    }
    
    /**
     * Override metode browser untuk menangani aplikasi mobile kustom
     *
     * @return string
     */
    public function browser()
    {
        if ($this->isCustomMobileApp()) {
            return $this->getCustomAppName() ?: 'Mobile App';
        }
        
        return parent::browser();
    }
    
    /**
     * Override metode platform untuk menangani aplikasi mobile kustom
     *
     * @return string
     */
    public function platform()
    {
        if ($this->isCustomMobileApp()) {
            // Coba deteksi platform dari user agent
            $userAgent = $this->getUserAgent();
            
            if (strpos($userAgent, 'Android') !== false) {
                return 'Android';
            }
            
            if (strpos($userAgent, 'iOS') !== false) {
                return 'iOS';
            }
            
            return 'Mobile';
        }
        
        return parent::platform();
    }
}
