<div class="space-y-4" wire:poll.30s="refreshMessages">
    @if($messages->isEmpty())
        <div class="text-center py-8 text-gray-500 dark:text-gray-400">
            <p>Belum ada diskusi untuk tiket ini.</p>
        </div>
    @else
        @foreach($messages as $message)
            @include('filament.admin.widgets.partials.message-thread', ['message' => $message, 'depth' => 0])
        @endforeach
    @endif
</div>

@push('scripts')
<script>
    document.addEventListener('livewire:init', () => {
        Livewire.on('reply-to-message', (event) => {
            // Scroll ke form reply dan focus
            const replyForm = document.querySelector('[wire\\:key="reply-form"]');
            if (replyForm) {
                replyForm.scrollIntoView({ behavior: 'smooth' });
                
                // Set parent_id di form reply
                Livewire.dispatch('set-reply-parent', { parentId: event.messageId });
            }
        });
    });
</script>
@endpush
