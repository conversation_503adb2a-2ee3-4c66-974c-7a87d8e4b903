<?php

namespace App\Models;

use App\Traits\Auditable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class DataRealisasi2024 extends Model
{
	use HasFactory, SoftDeletes, LogsActivity;
	public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
			->logFillable('*')
			->logOnlyDirty();
    }

	/**
     * Mutator untuk memastikan no_ijin selalu dalam format yang benar
     */
    public function setNoIjinAttribute($value)
    {
        // Hapus spasi di awal dan akhir
        $this->attributes['no_ijin'] = trim($value);
    }

	public $table = 'data_realisasi';

	protected $fillable = [
		'npwp_company',
		'no_ijin',
		'poktan_id', //relasi ke master kelompok
		'pks_id', //relasi ke table pks
		'anggota_id', //relasi ke table master anggota
		'lokasi_id', //relasi ke table lokasis

		//spasial
		'nama_lokasi',
		'latitude',
		'longitude',
		'polygon',
		'altitude',
		'luas_kira',

		//tanam
		'mulai_tanam',
		'akhir_tanam',
		'luas_lahan',


		//produksi
		'mulai_panen',
		'akhir_panen',
		'volume',
	];

	protected static function booted()
	{
		static::addGlobalScope('npwp', function (Builder $builder) {
			if (Auth::check()) {
				$user = Auth::user();

				if ($user->hasAnyRole(['admin', 'direktur', 'Super Admin', 'verifikator'])) {
				}
				else {
					$builder->where('npwp_company', $user->npwp);
				}
			}
		});
	}

	public function commitment()
	{
		return $this->belongsTo(Commitment2024::class, 'no_ijin', 'no_ijin');
	}

	public function pks()
	{
		return $this->belongsTo(Pks2024::class, 'pks_id');
	}

	public function masterkelompok()
	{
		return $this->belongsTo(MasterPoktan2024::class, 'poktan_id', 'poktan_id');
	}

	public function masteranggota()
	{
		return $this->belongsTo(MasterAnggota2024::class, 'anggota_id', 'anggota_id');
	}

	public function lokasi()
	{
		return $this->belongsTo(Lokasi2024::class, 'lokasi_id', 'id');
	}

	public function fototanam()
	{
		return $this->hasMany(FotoTanam2024::class, 'realisasi_id');
	}

	public function fotoproduksi()
	{
		return $this->hasMany(FotoProduksi2024::class, 'realisasi_id');
	}
}
