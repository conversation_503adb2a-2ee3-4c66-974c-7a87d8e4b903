<?php

namespace App\Models;

use App\Models\CmsCategory;
use App\Models\User;
use App\Observers\PostObserver;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;

#[ObservedBy([PostObserver::class])]
class Post extends Model
{
    use SoftDeletes;


    /**
     * @var array
     */
    protected $fillable = [
        'author_id',
        'type', //Jenis artikel: Artikel, Berita, Event, Halaman
        'category_id', //Kategori
        'title',
        'slug',
        'short_description',
        'keywords',
        'body',
        'is_published',
        'is_trend',
        'published_at',
        'feature_image',
        'cover_image',
        'created_at',
        'updated_at',
		'venue',
		'date_start',
		'date_end',
    ];

	protected static function booted()
    {
        static::addGlobalScope('latestFirst', function (Builder $builder) {
            $builder->orderByRaw('COALESCE(published_at, created_at) DESC');
        });
    }

    public function author()
    {
        return $this->belongsTo(User::class,'author_id', 'id');
    }
    public function category()
    {
        return $this->belongsTo(CmsCategory::class, 'category_id', 'id');
    }

	public function eventSpeaker()
	{
		return $this->hasMany(EventSpeaker::class);
	}
	public function eventSchedule()
	{
		return $this->hasMany(EventSchedule::class);
	}
	
}
