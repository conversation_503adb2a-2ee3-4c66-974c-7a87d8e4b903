<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\Summary2024;
use App\Models\User;

class Summary2024Policy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any Summary2024');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Summary2024 $summary2024): bool
    {
        return $user->checkPermissionTo('view Summary2024');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create Summary2024');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Summary2024 $summary2024): bool
    {
        return $user->checkPermissionTo('update Summary2024');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Summary2024 $summary2024): bool
    {
        return $user->checkPermissionTo('delete Summary2024');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any Summary2024');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Summary2024 $summary2024): bool
    {
        return $user->checkPermissionTo('restore Summary2024');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any Summary2024');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, Summary2024 $summary2024): bool
    {
        return $user->checkPermissionTo('replicate Summary2024');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder Summary2024');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Summary2024 $summary2024): bool
    {
        return $user->checkPermissionTo('force-delete Summary2024');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any Summary2024');
    }
}
