<?php

use App\Filament\Admin\Pages\LogViewerDetail;
use App\Http\Controllers\Thn2025\SpatialController;
use App\Http\Controllers\LandingController;
use App\Http\Controllers\PostPageController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\Thn2024\DrawSpatialsController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Thn2025\CommitmentController;
use App\Http\Controllers\Thn2025\CommitmentRegionController;
use App\Http\Controllers\Thn2025\DataFeederController;
use App\Http\Controllers\Thn2025\UpdateUserController;
use App\Http\Controllers\ValidateQRController;
use App\Filament\Pages\Auth\RegisterError;
use App\Http\Controllers\API\AnnouncementController;
use App\Http\Controllers\API\MobileAuthController;
use App\Http\Controllers\API\MobileVerifikasiController;
use App\Http\Controllers\API\MobileVerifikatorController;
use App\Http\Controllers\LocationPickerController;
use App\Http\Controllers\MobileAppController;
use App\Http\Controllers\VerificatorController;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

Route::get('/', [LandingController::class, 'index'])->name('welcome');
Route::get('/landingBlog', [LandingController::class, 'landingBlog'])->name('landingBlog');


// Route::get('/', function () {
//     return view('welcome');
// });

// Route::get('/dashboard', function () {
//     return view('dashboard');
// })->middleware(['auth', 'verified'])->name('dashboard');

Route::get('/dashboard', function () {
    return redirect('/admin');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::get('/panel/{any}/login', function () {
    return redirect('/admin');
})->where('any', '.*');


Route::get('/login', function () {
    return redirect('/admin/login');
})->name('login')->middleware('guest');

// Serve PDF files from documentations directory
Route::get('/documentations/{filename}', function ($filename) {
    $path = base_path('documentations/' . $filename);

    if (!File::exists($path)) {
        abort(404);
    }

    $file = File::get($path);
    $type = File::mimeType($path);

    $response = response($file, 200);
    $response->header('Content-Type', $type);

    return $response;
});

Route::get('/admin/register-error', RegisterError::class)
    ->name('auth.register-error');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
});

Route::get('/verify-skl', [ValidateQRController::class, 'verify']);
Route::get('/verify-logbook', [\App\Http\Controllers\VerificationController::class, 'verifyLogbook']);

// Route untuk mengakses file PDF dari storage
Route::get('/uploads/{npwp}/{noIjin}/dokumen/{filename}', function ($npwp, $noIjin, $filename) {
    $path = 'uploads/' . $npwp . '/' . $noIjin . '/dokumen/' . $filename;
    if (Storage::disk('public')->exists($path)) {
        return response()->file(Storage::disk('public')->path($path));
    }
    abort(404);
})->where('filename', '.*');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/draft-skl/{id}', [App\Http\Controllers\DraftSklController::class, 'generateDraftSkl'])->name('draft-skl.generate');
});

// panel blog
Route::group(['prefix' => 'public', 'as' => 'public.'], function () {
    // Route::get('/about', function () {
    //     return view('velzon.blog.about');
    // });
	Route::get('/{page}/page', [PostPageController::class, 'postPage'])->name('postPage');
	Route::get('/{type}/home', [LandingController::class, 'postsHome'])->name('postsHome');
	Route::get('/{type}/{category}', [LandingController::class, 'categoryHome'])->name('categoryHome');
	Route::get('/{type}/{category}/{slug}', [LandingController::class, 'postArticle'])->name('postArticle');
});

Route::get('/panel/2025/realisasi2025s/{no_ijin}', function ($no_ijin) {
    return Filament::renderHook('panels::2025.realisasi2025s.index', [
        'no_ijin' => $no_ijin,
    ]);
})->name('filament.admin.resources.realisasi2025.index.custom');

Route::get('/updateAllUsers', [UpdateUserController::class, 'updateAllUsers'])
	->name('updateAllUsers');

// Support Ticket Routes
Route::post('/support-ticket/{ticket}/reply', [App\Http\Controllers\SupportTicketController::class, 'reply'])
    ->name('support-ticket.reply')
    ->middleware('auth');

	// Route::get('/log-viewer/{id}', LogViewerDetail::class)
    // ->name('filament.admin.pages.log-viewer-detail');

	Route::get('/log-viewer-advanced/detail/{hash}/{file}', App\Filament\Admin\Pages\LogViewerAdvancedDetail::class)->name('log-viewer-advanced.detail');

// Location Picker Web Routes
Route::prefix('panel/2025')->middleware(['auth', 'verified'])->group(function () {
    Route::get('location-picker/{noRiph}/posted-markers', [LocationPickerController::class, 'getPostedMarkers']);
    Route::get('location-picker/{noRiph}/available-spatials', [LocationPickerController::class, 'getAvailableSpatials']);
    Route::get('location-picker/marker-detail/{kodeSpatial}', [LocationPickerController::class, 'getMarkerDetail']);
    Route::post('location-picker/post-marker/{kodeSpatial}', [LocationPickerController::class, 'postMarker']);
	Route::get('mobile/verifikasi/active', [MobileVerifikasiController::class, 'getActiveVerifications']);
	Route::get('mobile/verifikasi/detail/{id}', [MobileVerifikasiController::class, 'getVerificationDetail']);
});

//panel group
// Route untuk API yang tidak memerlukan autentikasi
Route::group(['prefix' => 'panel/admin/master-spatials', 'as' => 'panel.admin.masterspatials.'], function () {
	Route::post('/getSelectedKabs', [App\Http\Controllers\Thn2025\DataFeederController::class, 'getSelectedKabs'])->name('getSelectedKabs');
	Route::get('/getSelectedKabs', [App\Http\Controllers\Thn2025\DataFeederController::class, 'getSelectedKabs'])->name('getSelectedKabs.get');
	Route::get('/getMarkerInfo/{kodeSpatial}', [App\Http\Controllers\Thn2025\DataFeederController::class, 'getMarkerInfo'])->name('getMarkerInfo');
	Route::get('/searchSpatial', [App\Http\Controllers\Thn2025\DataFeederController::class, 'searchSpatial'])->name('searchSpatial');
});

// Route untuk menyimpan laporan kegagalan KML
Route::post('/panel/admin/save-kml-failures', [App\Http\Controllers\Admin\SpatialController::class, 'saveKmlFailures'])
    ->middleware(['auth', 'verified'])
    ->name('admin.save-kml-failures');

Route::group(['prefix' => 'panel', 'as' => 'panel.', 'middleware' => ['auth', 'verified']], function () {
	Route::group(['prefix' => 'admin', 'as' => 'admin.'], function () {
		Route::get('/announcements', [AnnouncementController::class, 'getAnnouncements']);
		Route::get('/user/profile', [MobileAuthController::class, 'getProfile']);
		Route::get('/{pksId}/realisasi/reports', [DrawSpatialsController::class, 'realisasiReportView'])->name('realisasiReportView');
		Route::group(['prefix' => 'master-spatials', 'as' => 'masterspatials.'], function () {
			Route::get('/uploadSpatials', [SpatialController::class, 'uploadSpatials'])->name('uploadSpatials');
			Route::post('/uploadSpatials', [SpatialController::class, 'storesingle'])->name('storesingle');
			Route::post('/getSelectedKabs', [DataFeederController::class, 'getSelectedKabs'])->name('getSelectedKabs');
			Route::get('/getMarkerInfo/{kodeSpatial}', [DataFeederController::class, 'getMarkerInfo'])->name('getMarkerInfo');
		});
	});
	//admin group
    //Year group
    Route::group(['prefix' => '2025', 'as' => '2025.'], function () {

				// Mobile verification routes for importir
				Route::get('/mobile/verifikator/assigned', [MobileVerifikatorController::class, 'getAssignedVerifications']);
				Route::get('/mobile/verifikator/detail/{id}', [MobileVerifikatorController::class, 'getVerificationDetail']);
				Route::get('/mobile/verifikator/documents/{id}', [MobileVerifikatorController::class, 'getVerificationDocuments']);
				Route::get('/mobile/verifikator/locations/{id}', [MobileVerifikatorController::class, 'getVerificationLocations']);
        Route::group(['namespace' => 'Thn2025'], function () {
            //data feed
            Route::group(['prefix' => 'datafeeder', 'as' => 'datafeeder.'], function () {
				Route::get('/pickSpatials/{noRiph}', [DataFeederController::class, 'pickSpatials'])->name('pickSpatials');
				Route::get('/pickMarker/{kodeSpatial}', [DataFeederController::class, 'pickMarker'])->name('pickMarker');
				Route::get('/getPostedMarker/{noRiph}', [DataFeederController::class, 'getPostedMarker'])->name('getPostedMarker');
				Route::put('/postMarker/{kodeSpatial}', [DataFeederController::class, 'postMarker'])->name('postMarker');
				Route::get('/getSingleMarker/{id}', [DataFeederController::class, 'getSingleMarker'])->name('getSingleMarker');
            });

            //page view
            Route::group(['prefix' => 'report', 'as' => 'report.'], function () {
                Route::get('/e-filling/{noRiph}', [CommitmentController::class, 'index'])->name('efilling');
                Route::get('/single/{id}', [CommitmentController::class, 'singleMap'])->name('singleMap');
            });

            //post put
            Route::group(['prefix' => 'calc', 'as' => 'calc.'], function () {
                Route::get('/fullfill/{record}', [CommitmentRegionController::class, 'calculateFullfilled'])->name('calculateFullfilled');
            });
        });
    });
	Route::group(['prefix' => '2024', 'as' => '2024.'], function () {
		Route::group(['namespace' => 'Thn2024'], function () {
			Route::group(['prefix' => 'draw', 'as' => 'draw.'], function () {
				Route::get('/{noRiph}/{realisasi}', [DrawSpatialsController::class, 'drawMap'])->name('drawMap');
				Route::post('/validateKml', [DrawSpatialsController::class, 'validateKml'])->name('validateKml');
				Route::put('/{noRiph}/{realisasi}/create', [DrawSpatialsController::class, 'saveDraw'])->name('saveDraw');
			});
			Route::get('/pks2024s/{pksId}/realisasi/reports', [DrawSpatialsController::class, 'realisasiReportView'])->name('realisasiReportView');
			Route::get('/pks2024s/{pksId}/realisasi/reports/data', [DrawSpatialsController::class, 'realisasiReportData'])->name('realisasiReportData');
			Route::get('/pks2024s/{realisasiId}/realisasi/reports/single', [DrawSpatialsController::class, 'realisasiReportSingle'])->name('realisasiReportSingle');
		});
	});

});




// Health Check endpoint untuk monitoring
Route::get('/health', [App\Http\Controllers\HealthCheckController::class, 'check'])
    ->name('health.check')
    ->withoutMiddleware(['web'])
    ->middleware('throttle:60,1');

// Mobile App Routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/mobile-apps', [MobileAppController::class, 'index'])->name('mobile-app.index');
    Route::get('/mobile-apps/{mobileApp}/download', [MobileAppController::class, 'download'])->name('mobile-app.download');
});

require __DIR__.'/auth.php';


