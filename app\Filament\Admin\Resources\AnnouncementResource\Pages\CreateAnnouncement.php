<?php

namespace App\Filament\Admin\Resources\AnnouncementResource\Pages;

use App\Filament\Admin\Resources\AnnouncementResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Contracts\Support\Htmlable;

class CreateAnnouncement extends CreateRecord
{
    protected static string $resource = AnnouncementResource::class;
	public static string | Alignment $formActionsAlignment = Alignment::Right;
	public function getTitle(): string|Htmlable
	{
		return 'Pengumuman Baru';
	}
	public function getHeading(): string
	{
		return 'Pengumuman Baru';
	}

	public function getSubheading(): string|Htmlable|null
	{
		return 'Buat pengumuman baru sesuai peran pengguna';
	}
}
