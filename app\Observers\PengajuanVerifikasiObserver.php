<?php

namespace App\Observers;

use App\Models\PengajuanVerifikasi;
use App\Models\Pks2025;
use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class PengajuanVerifikasiObserver
{	
    /**
     * Handle the PengajuanVerifikasi "created" event.
     */
	
	public function created(PengajuanVerifikasi $pengajuanVerifikasi): void
	{
		$jenisMapping = [
			'PVT' => 'Verifikasi Tanam',
			'PVP' => 'Verifikasi Produksi',
			'PVS' => 'Penerbitan SKL',
		];
		$jenis = $pengajuanVerifikasi->kind;
		$jenisLabel = $jenisMapping[$jenis] ?? 'Jenis Tidak Dikenal';

		$registrar = Auth::user();
		
		$kabupatenIds = Pks2025::where('npwp', $registrar->npwp)
			->pluck('kabupaten_id')
			->unique()
			->toArray();
			
		$pusat = User::whereHas('roles', function ($query) {
			$query->whereIn('name', ['Super Admin', 'admin']);
		})->get();

		$dinas = User::whereHas('dataadmin', function ($query) use ($kabupatenIds) {
			$query->whereIn('kabupaten_id', $kabupatenIds);
		})->get();

		if($dinas){
			Notification::make()
				->title("Pengajuan {$jenisLabel}")
				->body("<span class='font-bold text-info-500'>{$registrar->datauser->company_name}</span> telah mengajukan <span class='font-bold text-info-500'>{$jenisLabel}</span> atas komitmen PPRK No: <span class='font-bold text-info-500'>{$pengajuanVerifikasi->no_ijin}</span> yang akan dilaksanakan di wilayah Kabupaten Anda.")
				->sendToDatabase($dinas);
	
		}else{
			Notification::make()
				->title("<span class='text-danger-500'>Pengajuan {$jenisLabel}</span>")
				->body("<span class='font-bold text-info-500'>{$registrar->datauser->company_name}</span> telah mengajukan <span class='font-bold text-info-500'>{$jenisLabel}</span> atas komitmen PPRK No: <span class='font-bold text-info-500'>{$pengajuanVerifikasi->no_ijin}</span>. Namun, <span class='text-danger-500'>akun Dinas</span> terkait untuk wilayah ini <span class='text-danger-500'>belum tersedia</span>.")
				->sendToDatabase($pusat);
		}

		Notification::make()
			->title("Pengajuan {$jenisLabel}")
			->body("<span class='font-bold text-info-500'>{$registrar->datauser->company_name}</span> telah mengajukan <span class='font-bold text-info-500'>{$jenisLabel}</span> atas komitmen PPRK No: <span class='font-bold text-info-500'>{$pengajuanVerifikasi->no_ijin}</span>. Segera tindaklanjuti dengan menugaskan Verifikator untuk melaksanakan {$jenisLabel}.")
			->sendToDatabase($pusat);

		Notification::make()
			->title("Pengajuan {$jenisLabel}")
			->body("Terima kasih telah mengirimkan permohonan <span class='font-bold text-info-500'>{$jenisLabel}</span> untuk PPRK No: <span class='font-bold text-info-500'>{$pengajuanVerifikasi->no_ijin}</span>. <p class='mt-3'>Selanjutnya kami akan menugaskan Verifikator untuk melaksanakan {$jenisLabel}.</p> <p class='font-bold mt-3'>Terima Kasih - Administrator</p>")
			->sendToDatabase($registrar);
	}

    /**
     * Handle the PengajuanVerifikasi "updated" event.
     */
    public function updated(PengajuanVerifikasi $pengajuanVerifikasi): void
    {
		$jenisMapping = [
			'PVT' => 'Verifikasi Tanam',
			'PVP' => 'Verifikasi Produksi',
			'PVS' => 'Penerbitan SKL',
		];
		$jenis = $pengajuanVerifikasi->kind;
		$jenisLabel = $jenisMapping[$jenis] ?? 'Jenis Tidak Dikenal';

		//penugasan verifikator
        if ($pengajuanVerifikasi->wasChanged('status') && $pengajuanVerifikasi->status === '1') {
			$registrar = $pengajuanVerifikasi->user;
			$asigner = Auth::user();
			$pusat = User::whereHas('roles', function ($query) {
				$query->whereIn('name', ['Super Admin', 'admin']);
			})->get();

			Notification::make()
				->title("Penugasan {$jenisLabel} untuk {$pengajuanVerifikasi->no_ijin}")
				->body("Administrator memulai menugaskan Verifikator untuk melaksanakan kegiatan verifikasi atas pengajuan <span class='font-bold text-info-500'>{$jenisLabel}</span> untuk PPRK No: <span class='font-bold text-info-500'>{$pengajuanVerifikasi->no_ijin}</span>. <p class='mt-3'>Para petugas verifikasi kami akan segera mengunjungi lokasi pelaksanaan realisasi komitmen Anda.</p> <p class='font-bold mt-3'>Terima Kasih - Administrator</p>")
				->sendToDatabase($registrar);
		}
		//Petugas ditetapkan
		if ($pengajuanVerifikasi->wasChanged('status') && $pengajuanVerifikasi->status === '2') {
			$registrar = $pengajuanVerifikasi->user;
			$asigner = Auth::user();
			$verifikator = $pengajuanVerifikasi->assignments->map(fn ($assignment) => $assignment->user)->filter();

			$pusat = User::whereHas('roles', function ($query) {
				$query->whereIn('name', ['Super Admin', 'admin']);
			})->get();

			$title = "Petugas {$jenisLabel} Ditetapkan";

			Notification::make()
				->title($title)
				->body("kami telah menetapkan petugas untuk melaksanakan {$jenisLabel} atas Pengajuan No. {$pengajuanVerifikasi->no_pengajuan} untuk PPRK No. <span class='font-bold text-info-500'>{$pengajuanVerifikasi->no_ijin}</span>. <p class='font-bold mt-3'>Terima Kasih - Administrator</p>")
				->sendToDatabase($registrar);

			Notification::make()
				->title($title)
				->body("Anda telah menetapkan petugas {$jenisLabel} atas Pengajuan No. {$pengajuanVerifikasi->no_pengajuan} untuk PPRK No. <span class='font-bold text-info-500'>{$pengajuanVerifikasi->no_ijin}</span>")
				->sendToDatabase($asigner);

			Notification::make()
				->title($title)
				->body("Administrator menetapkan Anda sebagai petugas {$jenisLabel} untuk Pengajuan No. {$pengajuanVerifikasi->no_pengajuan} untuk PPRK No. <span class='font-bold text-info-500'>{$pengajuanVerifikasi->no_ijin}</span>. <p>Selamat Bertugas, Semoga Lancar dan Sukses!.</p><p class='font-bold mt-3'>Terima Kasih - Administrator</p>")
				->sendToDatabase($verifikator);
		}

		//verifikasi dimulai
		if ($pengajuanVerifikasi->wasChanged('status') && $pengajuanVerifikasi->status === '3') {
			$registrar = $pengajuanVerifikasi->user;
			$asigner = Auth::user();
			$verifikator = $pengajuanVerifikasi->assignments->map(fn ($assignment) => $assignment->user)->filter();

			$pusat = User::whereHas('roles', function ($query) {
				$query->whereIn('name', ['Super Admin', 'admin']);
			})->get();

			$title = "Proses {$jenisLabel} Dimulai";

			Notification::make()
				->title($title)
				->body("Petugas Verifikator kami telah memulai proses {$jenisLabel} untuk PPRK No. <span class='font-bold text-info-500'>{$pengajuanVerifikasi->no_ijin}</span><p class='font-bold mt-3'>Terima Kasih - Administrator</p>")
				->sendToDatabase($registrar);

			Notification::make()
				->title($title)
				->body("Proses  {$jenisLabel} untuk PPRK No. <span class='font-bold text-info-500'>{$pengajuanVerifikasi->no_ijin} telah dimulai.</span>.<p class='font-bold mt-3'>SELAMAT BERTUGAS! - Administrator</p>")
				->sendToDatabase($verifikator);

			Notification::make()
				->title($title)
				->body("Pelaksanaan {$jenisLabel} untuk PPRK No. <span class='font-bold text-info-500'>{$pengajuanVerifikasi->no_ijin}</span> telah dimulai.")
				->sendToDatabase($pusat);
		}
		
		if($jenis !== 'PVS')
		{
			Log::info('Jenis Kind: '.$jenis);
			if ($pengajuanVerifikasi->wasChanged('status') && $pengajuanVerifikasi->status === '4') {
				$registrar = $pengajuanVerifikasi->user;
				$asigner = Auth::user();
				$verifikator = $pengajuanVerifikasi->assignments->map(fn ($assignment) => $assignment->user)->filter();

				$pusat = User::whereHas('roles', function ($query) {
					$query->whereIn('name', ['Super Admin', 'admin']);
				})->get();

				$title = "Proses {$jenisLabel} Berakhir";

				Notification::make()
					->title($title)
					->body("Petugas Verifikator kami telah menyelesaikan proses {$jenisLabel} No. {$pengajuanVerifikasi->no_ijin} untuk PPRK No. <span class='font-bold text-info-500'>{$pengajuanVerifikasi->no_ijin}</span>. Terima kasih atas kerjasama dalam memenuhi komitmen tanam/produksi bawang putih.<p class='font-bold mt-3'>Administrator</p>")
					->sendToDatabase($registrar);

				Notification::make()
					->title($title)
					->body("Terima kasih Anda telah menyelesaikan tugas {$jenisLabel} No. {$pengajuanVerifikasi->no_ijin} untuk PPRK No. <span class='font-bold text-info-500'>{$pengajuanVerifikasi->no_ijin}</span>.<p>Sampai jumpa di tugas-tugas berikutnya!.</><p class='font-bold mt-3'>SALAM SUPERS! - Administrator</p>")
					->sendToDatabase($verifikator);

				Notification::make()
					->title($title)
					->body("Seluruh Verifikator telah menyelesaikan tugas {$jenisLabel} No. {$pengajuanVerifikasi->no_ijin} untuk PPRK No. <span class='font-bold text-info-500'>{$pengajuanVerifikasi->no_ijin}</span>.")
					->sendToDatabase($pusat);
			}
		}
		if($jenis === 'PVS')
		{
			if ($pengajuanVerifikasi->wasChanged('status') && $pengajuanVerifikasi->status === '4') {
				$registrar = $pengajuanVerifikasi->user;
				$asigner = Auth::user();
	
				$pusat = User::whereHas('roles', function ($query) {
					$query->whereIn('name', ['Super Admin', 'admin']);
				})->get();
	
				$title = "Pengajuan {$jenisLabel} Direkomendasikan";
	
				Notification::make()
					->title($title)
					->body("Administrator kami telah merekomendasikan Status dan Penerbitan Surat Keterangan Lunas untuk PPRK No.: {$pengajuanVerifikasi->no_ijin} kepada Pimpinan kami.
						<p class='font-bold mt-3'>Administrator</p>")
					->sendToDatabase($registrar);
	
				Notification::make()
					->title($title)
					->body("Rekomendasi Status dan Penerbitan Surat Keterangan Lunas untuk PPRK No.: <span class='font-bold text-info-500'>{$pengajuanVerifikasi->no_ijin}</span> telah dikirimkan kepada Pimpinan Anda untuk mendapatkan persetujuan.")
					->sendToDatabase($pusat);
			}
	
			if ($pengajuanVerifikasi->wasChanged('status') && $pengajuanVerifikasi->status === '5') {
				$registrar = $pengajuanVerifikasi->user;
				$asigner = Auth::user();
				$verifikator = $pengajuanVerifikasi->assignments->map(fn ($assignment) => $assignment->user)->filter();
	
				$pusat = User::whereHas('roles', function ($query) {
					$query->whereIn('name', ['Super Admin', 'admin']);
				})->get();
	
				$title = "Proses {$jenisLabel} Berakhir";
	
				Notification::make()
					->title($title)
					->body("Petugas Verifikator kami telah menyelesaikan proses {$jenisLabel} No. {$pengajuanVerifikasi->no_ijin} untuk PPRK No. <span class='font-bold text-info-500'>{$pengajuanVerifikasi->no_ijin}</span>. Terdapat beberapa catatan untuk segera Anda perbaiki.<p class='font-bold mt-3'>Terima Kasih - Administrator</p>")
					->sendToDatabase($registrar);
	
				Notification::make()
					->title($title)
					->body("Terima kasih Anda telah menyelesaikan tugas {$jenisLabel} No. {$pengajuanVerifikasi->no_ijin} untuk PPRK No. <span class='font-bold text-info-500'>{$pengajuanVerifikasi->no_ijin}</span>.<p>Sampai jumpa di tugas-tugas berikutnya!.</><p class='font-bold mt-3'>SALAM SUPERS! - Administrator</p>")
					->sendToDatabase($verifikator);
	
				Notification::make()
					->title($title)
					->body("Seluruh Verifikator telah menyelesaikan tugas {$jenisLabel} No. {$pengajuanVerifikasi->no_ijin} untuk PPRK No. <span class='font-bold text-info-500'>{$pengajuanVerifikasi->no_ijin}</span>.")
					->sendToDatabase($pusat);
			}
	
			if ($pengajuanVerifikasi->wasChanged('status') && $pengajuanVerifikasi->status === '6') {
				$registrar = $pengajuanVerifikasi->user;
				$asigner = Auth::user();
	
				$pusat = User::whereHas('roles', function ($query) {
					$query->whereIn('name', ['Super Admin', 'admin']);
				})->get();
	
				$title = "Pengajuan {$jenisLabel} Ditolak";
	
				Notification::make()
					->title($title)
					->body("
					<ul class='list-group'>
						<li class='list-group-items flex justify-between'>
							<span class='col-4'>PPRK No:</span>
							<span class='col-7 font-bold text-primary-500'>{$pengajuanVerifikasi->no_ijin}</span>
						</li>
						<li class='list-group-items flex justify-between'>
							<span class='col-4'>Status:</span>
							<span class='col-7 font-bold text-danger-500'>DITOLAK</span>
						</li>
					</ul>")
					->sendToDatabase($registrar);
	
				Notification::make()
					->title($title)
					->body("
					<ul class='list-group'>
						<li class='list-group-items flex justify-between'>
							<span class='col-4'>PPRK No:</span>
							<span class='col-7 font-bold text-primary-500'>{$pengajuanVerifikasi->no_ijin}</span>
						</li>
						<li class='list-group-items flex justify-between'>
							<span class='col-4'>Status:</span>
							<span class='col-7 font-bold text-danger-500'>DITOLAK</span>
						</li>
					</ul>")
					->sendToDatabase($pusat);
			}
	
			if ($pengajuanVerifikasi->wasChanged('status') && $pengajuanVerifikasi->status === '7') {
				$registrar = $pengajuanVerifikasi->user;
				$asigner = Auth::user();
	
				$pusat = User::whereHas('roles', function ($query) {
					$query->whereIn('name', ['Super Admin', 'admin']);
				})->get();
	
				$title = "Pengajuan {$jenisLabel} Disetujui";
	
				Notification::make()
					->title($title)
					->body("
					<ul class='list-group'>
						<li class='list-group-items flex justify-between'>
							<span class='col-4'>PPRK No:</span>
							<span class='col-7 font-bold text-primary-500'>{$pengajuanVerifikasi->no_ijin}</span>
						</li>
						<li class='list-group-items flex justify-between'>
							<span class='col-4'>Status:</span>
							<span class='col-7 font-bold text-info-500'>DISETUJUI</span>
						</li>
					</ul>")
					->sendToDatabase($registrar);
	
				Notification::make()
					->title($title)
					->body("
					<ul class='list-group'>
						<li class='list-group-items flex justify-between'>
							<span class='col-4'>PPRK No:</span>
							<span class='col-7 font-bold text-primary-500'>{$pengajuanVerifikasi->no_ijin}</span>
						</li>
						<li class='list-group-items flex justify-between'>
							<span class='col-4'>Status:</span>
							<span class='col-7 font-bold text-info-500'>DISETUJUI</span>
						</li>
					</ul>")
					->sendToDatabase($pusat);
			}
	
			if ($pengajuanVerifikasi->wasChanged('status') && $pengajuanVerifikasi->status === '8') {
				$registrar = $pengajuanVerifikasi->user;
				$asigner = Auth::user();
	
				$pusat = User::whereHas('roles', function ($query) {
					$query->whereIn('name', ['Super Admin', 'admin']);
				})->get();
	
				$title = "SKL TERBIT";
	
				Notification::make()
					->title($title)
					->body("
					<ul class='list-group'>
						<li class='list-group-items flex justify-between'>
							<span class='col-4'>PPRK No:</span>
							<span class='col-7 font-bold text-primary-500'>{$pengajuanVerifikasi->no_ijin}</span>
						</li>
						<li class='list-group-items flex justify-between'>
							<span class='col-4'>Status:</span>
							<span class='col-7 font-bold text-success-500'>DITERBITKAN</span>
						</li>
					</ul>")
					->sendToDatabase($registrar);
	
				Notification::make()
					->title($title)
					->body("
					<ul class='list-group'>
						<li class='list-group-items flex justify-between'>
							<span class='col-4'>PPRK No:</span>
							<span class='col-7 font-bold text-primary-500'>{$pengajuanVerifikasi->no_ijin}</span>
						</li>
						<li class='list-group-items flex justify-between'>
							<span class='col-4'>Status:</span>
							<span class='col-7 font-bold text-success-500'>DITERBITKAN</span>
						</li>
					</ul>")
					->sendToDatabase($pusat);
			}
		}

    }

    /**
     * Handle the PengajuanVerifikasi "deleted" event.
     */
    public function deleted(PengajuanVerifikasi $pengajuanVerifikasi): void
    {
        //
    }

    /**
     * Handle the PengajuanVerifikasi "restored" event.
     */
    public function restored(PengajuanVerifikasi $pengajuanVerifikasi): void
    {
        //
    }

    /**
     * Handle the PengajuanVerifikasi "force deleted" event.
     */
    public function forceDeleted(PengajuanVerifikasi $pengajuanVerifikasi): void
    {
        //
    }
}
