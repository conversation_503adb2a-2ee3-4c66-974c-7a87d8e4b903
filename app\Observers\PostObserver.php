<?php

namespace App\Observers;

use App\Models\Post;
use App\Models\User;
use Filament\Notifications\Notification;

class PostObserver
{
    /**
     * Handle the Post "created" event.
     */
	public function created(Post $post): void
	{
		// Pastikan hanya post dengan type 'Event' yang diproses
		if ($post->type !== 'Event') {
			return;
		}
	
		// Ambil user dengan role tertentu
		$importir = User::whereHas('roles', function ($query) {
			$query->where('name', 'importir');
		})->get();
	
		$admin = User::whereHas('roles', function ($query) {
			$query->whereIn('name', ['admin', 'direktur', 'spatial', 'verifikator']);
		})->get();
	
		// Gabungkan koleksi user dan ubah menjadi array
		$recipients = $importir->merge($admin);
	
		// Kirim notifikasi hanya jika ada penerima
		if ($recipients->isNotEmpty()) {
			Notification::make()
				->title("Kegiatan (event) Baru akan diselenggarakan")
				->body("<span class='font-bold text-info-500'>Kegiatan baru akan diselenggarakan. Cek informasinya di Halaman Utama</span>")
				->sendToDatabase($recipients->all());
		}
	}
	

    /**
     * Handle the Post "updated" event.
     */
    public function updated(Post $post): void
    {
        //
    }

    /**
     * Handle the Post "deleted" event.
     */
    public function deleted(Post $post): void
    {
        //
    }

    /**
     * Handle the Post "restored" event.
     */
    public function restored(Post $post): void
    {
        //
    }

    /**
     * Handle the Post "force deleted" event.
     */
    public function forceDeleted(Post $post): void
    {
        //
    }
}
