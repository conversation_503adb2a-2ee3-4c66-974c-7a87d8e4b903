<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\MobileAppResource\Pages;
use App\Models\MobileApp;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TagsColumn;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Spatie\Permission\Models\Role;

class MobileAppResource extends Resource
{
    protected static ?string $model = MobileApp::class;

    protected static ?string $navigationIcon = 'heroicon-o-device-phone-mobile';

    protected static ?string $navigationGroup = 'Documentations';

    protected static ?int $navigationSort = 5;

    public static function getNavigationLabel(): string
    {
        return 'Aplikasi Mobile';
    }

    public static function getPluralLabel(): string
    {
        return 'Aplikasi Mobile';
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('is_active', true)->count();
    }

    // public static function canAccess(): bool
    // {
    //     return Auth::user()->hasRole('Super Admin');
    // }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Aplikasi')
                    ->schema([
                        TextInput::make('name')
                            ->label('Nama Aplikasi')
                            ->required()
                            ->maxLength(255),
                        TextInput::make('version')
                            ->label('Versi')
                            ->required()
                            ->maxLength(50),
                        TextInput::make('version_code')
                            ->label('Kode Versi')
                            ->required()
                            ->numeric()
                            ->integer(),
                        Textarea::make('description')
                            ->label('Deskripsi')
                            ->maxLength(65535),
                    ]),
                
                Section::make('File Aplikasi')
                    ->schema([
                        TextInput::make('file_name')
                            ->label('Nama File APK')
                            ->required()
                            ->maxLength(255)
                            ->helperText('Nama file APK di direktori private/mobile_apps')
                            ->disabled(fn ($record) => $record !== null)
                            ->dehydrated(),
                    ]),
                
                Section::make('Minimum Requirements')
                    ->schema([
                        TextInput::make('min_android_version')
                            ->label('Versi Android Minimum')
                            ->maxLength(50),
                        TextInput::make('min_ram')
                            ->label('RAM Minimum (MB)')
                            ->numeric()
                            ->integer(),
                        TextInput::make('min_storage')
                            ->label('Penyimpanan Minimum (MB)')
                            ->numeric()
                            ->integer(),
                        TagsInput::make('required_features')
                            ->label('Fitur yang Diperlukan')
                            ->placeholder('Tambahkan fitur dan tekan Enter'),
                        Textarea::make('recommended_specs')
                            ->label('Spesifikasi yang Direkomendasikan')
                            ->maxLength(65535),
                    ]),
                
                Section::make('Informasi Rilis')
                    ->schema([
                        Textarea::make('release_notes')
                            ->label('Catatan Rilis')
                            ->maxLength(65535),
                    ]),
                
                Section::make('Pengaturan Akses')
                    ->schema([
                        CheckboxList::make('allowed_roles')
                            ->label('Role yang Diizinkan')
                            ->options(function () {
                                // Ambil semua role kecuali Super Admin
                                return Role::where('name', '!=', 'Super Admin')
                                    ->pluck('name', 'name');
                            })
                            ->required(),
                        Toggle::make('is_active')
                            ->label('Aktif')
                            ->default(true),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
			->defaultSort('version', 'desc')
            ->columns([
                TextColumn::make('name')
                    ->label('Nama Aplikasi')
                    ->searchable(),
                TextColumn::make('version')
                    ->label('Versi')
                    ->searchable(),
				TextColumn::make('created_at')
                    ->label('Tanggal Rilis')
					->date(),
                TextColumn::make('min_android_version')
                    ->label('Min Android'),
                TextColumn::make('file_size')
                    ->label('Ukuran File')
                    ->getStateUsing(function (MobileApp $record) {
                        $size = $record->file_size;
                        return $size > 0 ? number_format($size / 1024 / 1024, 2) . ' MB' : 'File tidak ditemukan';
                    })
                    ->color(fn (MobileApp $record) => $record->file_exists ? 'success' : 'danger'),
                TextColumn::make('allowed_roles')
                    ->label('Untuk')
					->badge(),
                IconColumn::make('is_active')
                    ->boolean()
                    ->label('Status'),
            ])
            ->filters([
                // Filter berdasarkan status aktif
                TernaryFilter::make('is_active')
                    ->label('Status')
                    ->placeholder('Semua Status')
                    ->trueLabel('Aktif')
                    ->falseLabel('Tidak Aktif'),
                
                // Filter berdasarkan role
                SelectFilter::make('allowed_roles')
                    ->label('Peran')
                    ->options(function () {
                        return Role::pluck('name', 'name');
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'],
                            fn (Builder $query, $role): Builder => $query->whereJsonContains('allowed_roles', $role)
                        );
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()->iconButton(),
                Tables\Actions\DeleteAction::make()->iconButton(),
                Tables\Actions\Action::make('download')
                    ->label('Unduh')
					->iconButton()
                    ->icon('heroicon-o-arrow-down-tray')
                    ->url(fn (MobileApp $record) => route('mobile-app.download', $record))
                    ->openUrlInNewTab()
                    ->visible(fn (MobileApp $record) => $record->file_exists),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMobileApps::route('/'),
            'create' => Pages\CreateMobileApp::route('/create'),
            'edit' => Pages\EditMobileApp::route('/{record}/edit'),
            'view' => Pages\ViewMobileApp::route('/{record}'),
        ];
    }
}
