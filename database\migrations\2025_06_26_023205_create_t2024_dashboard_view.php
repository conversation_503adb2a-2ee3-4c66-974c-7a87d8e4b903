<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
            CREATE OR REPLACE VIEW `t2024_dashboard` AS
            SELECT 
                p.periodetahun AS periode,
                COUNT(DISTINCT p.no_ijin) AS riph,
                COUNT(DISTINCT c.no_ijin) AS completed,
                SUM(p.luas_wajib_tanam) AS wajibtanam,
                SUM(p.volume_riph) AS wajibpanen,
                SUM(c.luas_tanam) AS realisasitanam,
                SUM(c.volume) AS realisasipanen,
                COUNT(DISTINCT l.id) AS petani
            FROM pull_riphs p
            LEFT JOIN completeds c ON p.no_ijin = c.no_ijin
            LEFT JOIN lokasis l ON p.no_ijin = l.no_ijin
            GROUP BY p.periodetahun;
        ");

        DB::statement("
            CREATE OR REPLACE VIEW view_spatial_lahan_2025 AS
			SELECT
				s.kode_spatial,
				prov.nama AS provinsi,
				kab.nama_kab AS kabupaten,
				kec.nama_kecamatan AS kecamatan,
				des.nama_desa AS desa,
				poktan.nama_kelompok AS nama_poktan,
				s.nama_petani,
				s.latitude,
				s.longitude,
				s.polygon,
				s.luas_lahan,
				s.reserved_by
			FROM
				t2025_master_spatials s
			LEFT JOIN t2025_master_poktans poktan
				ON s.kode_poktan = poktan.kode_poktan
			LEFT JOIN data_provinsis prov
				ON s.provinsi_id = prov.provinsi_id
			LEFT JOIN data_kabupatens kab
				ON s.kabupaten_id = kab.kabupaten_id
			LEFT JOIN data_kecamatans kec
				ON s.kecamatan_id = kec.kecamatan_id
			LEFT JOIN data_desas des
				ON s.kelurahan_id = des.kelurahan_id
			WHERE
				s.is_active = 1;
        ");


    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('t2024_dashboard_view');
    }
};
