<?php

namespace App\Filament\Panel2024\Resources\Commitment2024Resource\Pages;

use App\Filament\Panel2024\Resources\Commitment2024Resource;
use App\Models\AjuVerifProduksi2024;
use App\Models\AjuVerifSkl2024;
use App\Models\AjuVerifTanam2024;
use App\Models\Commitment2024;
use App\Models\DataRealisasi2024;
use App\Models\Lokasi2024;
use App\Models\MasterAnggota2024;
use App\Models\MasterPoktan;
use App\Models\MasterPoktan2024;
use App\Models\Pks2024;
use App\Models\UserDocs2024;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\{Actions, DatePicker, FileUpload, Grid, Group, TextInput, Placeholder, Section, Hidden, Tabs};
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\HtmlString;

class UnduhRiph extends CreateRecord
{
    protected static string $resource = Commitment2024Resource::class;

	protected static ?string $title = 'Sinkronisasi Data';

    public function getHeading(): string
	{
        return 'Sinkronisasi Data RIPH';
	}
	public static string | Alignment $formActionsAlignment = Alignment::Right;

    protected function getFormActions(): array
	{
		$visible = $this->data['nampak'];
		if($visible === 'visible'){
			return [
                $this->getCreateFormAction()->label('Simpan untuk Pelaporan'),
                $this->getCancelFormAction()
			];
		}else{
			return [];
		}
	}

	public function form(Form $form): Form
	{
		return $form
			->schema([
				Hidden::make('visible')->default('hidden'),
				Hidden::make('nampak')->default('hidden'),
				Hidden::make('npwp')
					->default(function () {
						if (Auth::user()->hasRole('importir')) {
							return optional(Auth::user()->datauser)->npwp_company;
						}
						return null;
					}),

				Section::make('Nomor RIPH')
					->aside()
					->description('Isi dengan nomor RIPH yang berlaku')
					->schema([
						TextInput::make('no_ijin')->unique()
							->hiddenLabel()
							->live(onBlur: true)
							->placeholder('Nomor RIPH')
							->helperText('Isi dengan nomor yang berlaku pada periode ini.')
							->validationMessages([
								'unique' => 'Nomor RIPH ini sudah ada dalam database. Hapus data di daftar komitmen terlebih dahulu.'
							])
							->afterStateUpdated(function ($state, $set) {
								$tahun = substr($state, -4);
								if (is_numeric($tahun) && (int)$tahun > 2025) {
									$set('no_ijin', null);
									Notification::make()
										->title('Nomor Tidak Valid')
										->body('Gunakan Nomor RIPH yang berlaku.')
										->danger()
										->send();
								}
							}),

						Actions::make([
							Action::make('Unduh data')
								->icon('heroicon-m-arrow-down-tray')
								->requiresConfirmation()
								->modalHeading('Unduh Data')
								->modalDescription('Anda akan melakukan pengunduhan data. Lanjutkan?')
								->modalSubmitActionLabel('Ya, lanjutkan.')
								->action(function ($get, $set) {
									try {
										$npwp = self::sanitizeNpwp($get('npwp'));
										$noijin = $get('no_ijin');

										if (!self::validateInputs($noijin, $npwp, $set)) return;
										if (!self::validateNoIjin($noijin, $npwp, $set)) return;

										$response = self::callSoapService($npwp, $noijin);

										if ($response) {
											self::processSoapResponse($response, $npwp, $noijin, $set);
										}
									} catch (\Exception $e) {
										self::handleError($e, $npwp ?? '', $noijin ?? '');
									}
								}),
						])->alignment(Alignment::End)->columnSpanFull(),
					]),

				Placeholder::make('hr')
					->columnSpanFull()
					->hiddenLabel()
					->content(new HtmlString('<hr>')),

				Section::make('Informasi Umum')
					->aside()
					->description('Data PPRK')
					->visible(fn($get) => $get('visible') === 'visible')
					->schema([
						Placeholder::make('namaHolder')
							->inlineLabel()
							->label('Pemegang PPRK')
							->columnSpanFull()
							->content(fn($get) => $get('nama')),
						Placeholder::make('hsHolder')
							->inlineLabel()
							->label('Kode HS')
							->columnSpanFull()
							->content(fn($get) => $get('no_hs')),
						Placeholder::make('periodeHolder')
							->inlineLabel()
							->label('Periode PPRK')
							->columnSpanFull()
							->content(fn($get) => $get('periodetahun')),
						Placeholder::make('awalHolder')
							->inlineLabel()
							->label('Mulai Berlaku')
							->columnSpanFull()
							->content(fn($get) => $get('tgl_ijin')),
						Placeholder::make('akhirHolder')
							->inlineLabel()
							->label('Tanggal berakhir')
							->columnSpanFull()
							->content(fn($get) => $get('tgl_akhir')),
						Placeholder::make('volHolder')
							->inlineLabel()
							->label('Volume PPRK')
							->columnSpanFull()
							->content(fn($get) => number_format((float) $get('volume_riph'), 2, ',', '.') . ' ton'),
						Placeholder::make('prdHolder')
							->inlineLabel()
							->label('Volume Komitmen Produksi')
							->columnSpanFull()
							->content(fn($get) => number_format((float) $get('volume_produksi'), 2, ',', '.') . ' ton'),
						Placeholder::make('tanamHolder')
							->inlineLabel()
							->label('Luas Komitmen Tanam')
							->columnSpanFull()
							->content(fn($get) => number_format((float) $get('luas_wajib_tanam'), 2, ',', '.') . ' ha'),
					]),

				Section::make('Benih, Pupuk dan Mulsa')
					->aside()
					->description('Ini adalah total kebutuhan benih, pupuk dan mulsa untuk pelaksanaan Komitmen wajib tanam-produksi Anda.')
					->visible(fn($get) => $get('visible') === 'visible')
					->columns(2)
					->schema([

						Placeholder::make('kebutuhan_benih')
							->inlineLabel()
							->columnStart(1)
							->label('Kebutuhan Benih')
							->extraAttributes(['class' => 'text-end'])
							->content(fn($get) => number_format($get('kebutuhan_benih'), 2, ',', '.') . ' kg'),

						Placeholder::make('stok_mandiri')
							->inlineLabel()
							->columnStart(1)
							->default(0)
							->label('Stok Mandiri')
							->extraAttributes(['class' => 'text-end'])
							->content(fn($get) => number_format($get('stok_mandiri'), 2, ',', '.') . ' kg'),

						Placeholder::make('dari_penangkar')
							->inlineLabel()
							->columnStart(1)
							->label('Beli Penangkar')
							->extraAttributes(['class' => 'text-end'])
							->content(fn($get) => number_format($get('beli_penangkar'), 2, ',', '.') . ' kg'),

						Placeholder::make('organikHolder')
							->inlineLabel()
							->columnStart(1)
							->label('Organik')
							->extraAttributes(['class' => 'text-end'])
							->content(fn($get) => number_format($get('pupuk_organik'), 0, ',', '.') . ' kg'),

						Placeholder::make('dolomitkHolder')
							->inlineLabel()
							->columnStart(1)
							->label('Dolomit')
							->extraAttributes(['class' => 'text-end'])
							->content(fn($get) => number_format($get('dolomit'), 0, ',', '.') . ' kg'),

						Placeholder::make('npkHolder')
							->inlineLabel()
							->columnStart(1)
							->label('NPK')
							->extraAttributes(['class' => 'text-end'])
							->content(fn($get) => number_format($get('npk'), 0, ',', '.') . ' kg'),

						Placeholder::make('zakHolder')
							->inlineLabel()
							->columnStart(1)
							->label('ZA')
							->extraAttributes(['class' => 'text-end'])
							->content(fn($get) => number_format($get('za'), 0, ',', '.') . ' kg'),

						Placeholder::make('mulsakHolder')
							->inlineLabel()
							->columnStart(1)
							->label('mulsa')
							->extraAttributes(['class' => 'text-end'])
							->content(fn($get) => number_format($get('mulsa'), 0, ',', '.') . ' roll'),
					]),
				Group::make()
					->schema([
						Hidden::make('keterangan'),
						Hidden::make('user_id')
							->default(fn() => Auth::user()->id),
						Hidden::make('nama'),
						Hidden::make('no_hs'),
						Hidden::make('periodetahun'),
						Hidden::make('tgl_ijin'),
						Hidden::make('tgl_akhir'),
						Hidden::make('volume_riph'),
						Hidden::make('volume_produksi'),
						Hidden::make('luas_wajib_tanam'),
						Hidden::make('stok_mandiri'),
						Hidden::make('pupuk_organik'),
						Hidden::make('npk'),
						Hidden::make('dolomit'),
						Hidden::make('za'),
						Hidden::make('mulsa'),
					]),
			]);
	}

	//function
	private static function handleError(\Exception $e, string $npwp, string $noijin): void
	{
		self::sendNotification('Error Fetching Data', 'Terjadi kesalahan saat mengunduh data: ' . $e->getMessage(), 'danger');
	}

	private static function sendNotification(string $title, string $body, string $type = 'success'): void
	{
		Notification::make()
			->title($title)
			->{$type}()
			->body($body)
			->send();
	}

	private static function sanitizeNpwp(?string $npwp): string
	{
		return str_replace(['.', '-'], '', $npwp ?? '');
	}

	private static function validateInputs(?string $noijin, string $npwp, $set): bool
	{
		if (empty($noijin) || empty($npwp)) {
			self::sendNotification('Data Tidak Lengkap', 'NPWP dan No Ijin harus diisi.', 'danger');
			return false;
		}
		return true;
	}

	private static function validateNoIjin(string $noijin, string $npwp, $set): bool
	{
		// Cek apakah nomor ijin sudah ada di Commitment2024
		if (Commitment2024::where('no_ijin', $noijin)->exists()) {
			// Cek apakah nomor ijin sudah ada di avskls (sudah memiliki SKL)
			if (AjuVerifSkl2024::where('no_ijin', $noijin)->exists()) {
				$set('no_ijin', null);
				self::sendNotification(
					'Nomor Izin Sudah Memiliki SKL!',
					'RIPH ini sudah memiliki SKL dan tidak dapat diubah.',
					'danger'
				);
				return false;
			}

			// Cek apakah nomor ijin sudah ada di avtanams atau avproduksis
			$hasTanam = AjuVerifTanam2024::where('no_ijin', $noijin)->exists();
			$hasProduksi = AjuVerifProduksi2024::where('no_ijin', $noijin)->exists();

			if ($hasTanam || $hasProduksi) {
				// Tampilkan konfirmasi kepada user
				$message = 'RIPH ini sudah memiliki data verifikasi ';
				$message .= $hasTanam ? 'tanam' : '';
				$message .= ($hasTanam && $hasProduksi) ? ' dan ' : '';
				$message .= $hasProduksi ? 'produksi' : '';
				$message .= '. Melanjutkan akan menghapus data tersebut. Apakah Anda yakin?';

				// Untuk sementara, kita izinkan (return true) karena konfirmasi akan ditangani di UI
				self::sendNotification(
					'Perhatian!',
					$message,
					'warning'
				);
				return true;
			}

			// Jika tidak ada di avtanams dan avproduksis, izinkan untuk melanjutkan
			self::sendNotification(
				'PERHATIAN',
				'Anda akan memperbarui data RIPH ini dengan data terbaru. Perhatian! Seluruh data yang telah dibuat sebelumnya akan dihapus.',
				'warning'
			);
			return true;
		}

		// Jika nomor ijin belum ada di Commitment2024, izinkan untuk melanjutkan
		return true;
	}

	private static function handleInvalidResponse(string $noijin): void
	{
		self::sendNotification('Gagal', 'Format data tidak valid atau respons kosong. (Error 225)', 'danger');
	}

	private static function handleNotFoundResponse(string $noijin): void
	{
		self::sendNotification('GAGAL!', "Data dengan nomor {$noijin} tidak dapat ditemukan di aplikasi/sistem RIPH. Gunakan nomor lain yang berlaku.", 'danger');
	}

	private static function handleUnknownError(): void
	{
		self::sendNotification('Gagal', 'Terjadi kesalahan tidak dikenali. (Error 214)', 'danger');
	}

	private static function callSoapService(string $npwp, string $noijin): ?string
	{
		try {
			$options = array(
				'soap_version' => SOAP_1_1,
				'exceptions' => true,
				'trace' => 1,
				'cache_wsdl' => WSDL_CACHE_MEMORY,
				'connection_timeout' => 25,
				'style' => SOAP_RPC,
				'use' => SOAP_ENCODED,
			);

			$client = new \SoapClient('https://riph.pertanian.go.id/api.php/simethris?wsdl', $options);
			$parameter = array(
				'user' => 'simethris',
				'pass' => 'wsriphsimethris',
				'npwp' => $npwp,
				'nomor' =>  $noijin
			);

			$response = $client->__soapCall('get_riph', $parameter);

			$client = new \SoapClient('https://riph.pertanian.go.id/api.php/simethris?wsdl', [
				'soap_version' => SOAP_1_1,
				'exceptions' => true,
				'trace' => 1,
				'cache_wsdl' => WSDL_CACHE_MEMORY,
				'connection_timeout' => 25,
				'style' => SOAP_RPC,
				'use' => SOAP_ENCODED,
			]);
			self::saveSoapResponse($npwp, $noijin, $response);
			return $response;
		} catch (\SoapFault $e) {
			self::sendNotification('Sinkronisasi Gagal!', 'SOAP gagal. Mencoba Menggunakan data lokal.', 'warning');
			return self::loadLocalJson($npwp, $noijin);
		}
	}

	private static function saveSoapResponse(string $npwp, string $noijin, string $response): void
	{
		$fijin = str_replace(['/', '.'], '', $noijin);
		$filepath = 'uploads/' . $npwp . '/' . $fijin . '.json';
		Storage::disk('public')->put($filepath, json_encode((array)simplexml_load_string($response)));
	}

	private static function loadLocalJson(string $npwp, string $noijin): ?string
	{
		$fijin = str_replace(['/', '.'], '', $noijin);
		$filepath = 'uploads/' . $npwp . '/' . $fijin . '.json';

		if (Storage::disk('public')->exists($filepath)) {
			return Storage::disk('public')->get($filepath);
		}

		self::sendNotification('Data Tidak Tersedia', 'Berkas JSON tidak ditemukan. Proses dibatalkan.', 'danger');
		return null;
	}

	private static function processSoapResponse(string $response, string $npwp, string $noijin, $set): void
	{
		$set('visible', 'visible');
		$set('nampak', 'visible');
		try {
			$xml = simplexml_load_string($response);
			if ($xml === false) {
				$datariph = json_decode($response, true);
			} else {
				$datariph = json_decode(json_encode($xml), true);
			}

			if (!isset($datariph['return_cek'])) {
				self::handleInvalidResponse($noijin);
				return;
			}

			match ($datariph['return_cek']) {
				'R00' => self::handleValidResponse($datariph, $npwp, $noijin, $set),
				'R99' => self::handleNotFoundResponse($noijin),
				default => self::handleUnknownError()
			};
		} catch (\Exception $e) {
			self::handleError($e, $npwp, $noijin);
		}
	}

	private static function handleValidResponse(array $datariph, string $npwp, string $noijin, $set): void
	{
		$wajibTanam = $datariph['riph']['wajib_tanam'];
		self::sendNotification('Pengunduhan', 'Data ditemukan dan berhasil diunduh.', 'success');
		self::setBasicInfo($datariph, $noijin, $set);

		self::setPupukInfo($wajibTanam, $set);
	}

	private static function setBasicInfo(array $datariph, string $noijin, $set): void
	{
		$wajibTanam = $datariph['riph']['wajib_tanam'];
		$persetujuan = $datariph['riph']['persetujuan'];
		$komoditas = self::processKomoditas($datariph['riph']['komoditas']);

		$set('keterangan', (string) $datariph['keterangan']);
		$set('user_id', Auth::user()->id);
		$set('nama', (string) $persetujuan['nama']);
		$set('periodetahun', substr($noijin, -4));
		$set('tgl_ijin', (string) $persetujuan['tgl_ijin']);
		$set('tgl_akhir', (string) $persetujuan['tgl_akhir']);
		$set('no_hs', $komoditas['no_hs'] . ' ' . $komoditas['nama_produk']);
		$set('volume_riph', round((float) $wajibTanam['volume_riph'], 2));
		$set('volume_produksi', round((float) $wajibTanam['volume_produksi'], 2));
		$set('luas_wajib_tanam', round((float) $wajibTanam['luas_wajib_tanam'], 2));

	}

	private static function processKomoditas(array $komoditas): array
	{
		return isset($komoditas['loop'][0]) ? $komoditas['loop'][0] : $komoditas['loop'];
	}

	private static function setPupukInfo(array $wajibTanam, $set): void
	{
		$kebutuhanpupuk = $wajibTanam['kebutuhan_pupuk'] ?? [];
		$mulsa = $wajibTanam['mulsa'] ?? 0;

		$set('kebutuhan_benih', round((float) $wajibTanam['kebutuhan_benih'], 2));
		$set('stok_mandiri', round((float) $wajibTanam['stok_mandiri'], 2));
		$set('beli_penangkar', round((float) $wajibTanam['beli_penangkar'], 2));

		$set('pupuk_organik', round((float) $kebutuhanpupuk['pupuk_organik'], 2));
		$set('npk', round((float) $kebutuhanpupuk['npk'], 2));
		$set('dolomit', round((float) $kebutuhanpupuk['dolomit'], 2));
		$set('za', round((float) $kebutuhanpupuk['za'], 2));
		$set('mulsa', round((float) $mulsa, 2));
	}
	private static function normalizeArray($data): array
	{
		if (is_array($data) && isset($data[0])) {
			return $data;
		}
		return [$data];
	}
	private static function getStringValue($data): string
	{
		if (is_array($data) || (is_object($data) && empty((array)$data))) {
			return '';
		}
		return (string) $data;
	}

	private static function processMasterPoktan(array $daftarPoktan, string $npwp, string $noijin): array
	{
		$poktanList = [];

		if (isset($daftarPoktan['loop'])) {
			$poktanArray = self::normalizeArray($daftarPoktan['loop']);

			foreach ($poktanArray as $poktan) {
				$poktanId = self::getStringValue($poktan['id_poktan'] ?? '');

				if (!empty($poktanId)) {
					$poktanList[$poktanId] = [ // Gunakan id_poktan sebagai key agar unik
						'npwp' => $npwp,
						'poktan_id' => $poktanId,
						'id_kabupaten' => self::getStringValue($poktan['id_kabupaten'] ?? ''),
						'id_kecamatan' => self::getStringValue($poktan['id_kecamatan'] ?? ''),
						'id_kelurahan' => self::getStringValue($poktan['id_kelurahan'] ?? ''),
						'nama_kelompok' => self::getStringValue($poktan['nama_kelompok'] ?? ''),
						'nama_pimpinan' => self::getStringValue($poktan['nama_pimpinan'] ?? ''),
						'hp_pimpinan' => self::getStringValue($poktan['hp_pimpinan'] ?? ''),
					];
				}
			}
		}
		$countPoktan = count($poktanList);
		return [$poktanList, $countPoktan]; // Kembalikan dalam format array indeks numerik
	}

	private static function processMasterAnggota(array $daftarAnggota, string $npwp, string $noijin): array
	{
		$AnggotaList = [];
		if (isset($daftarAnggota['loop'])) {
			$anggotaArray = self::normalizeArray($daftarAnggota['loop']);

			foreach ($anggotaArray as $anggota) {
				$nikAnggota = self::getStringValue($anggota['ktp_petani'] ?? '');

				if (!empty($nikAnggota)) {
					$AnggotaList[$nikAnggota] = [
						'npwp' => $npwp,
						'ktp_petani' => $nikAnggota,
						'id' => self::getStringValue($anggota['id_petani'] ?? ''),
						'anggota_id' => self::getStringValue($anggota['id_petani'] ?? ''),
						'poktan_id' => self::getStringValue($anggota['id_poktan'] ?? ''),
						'nama_petani' => self::getStringValue($anggota['nama_petani'] ?? ''),
						'luas_lahan' => self::getStringValue($anggota['luas_lahan'] ?? ''),
					];
				}
			}
		}
		$countAnggota = count($AnggotaList);
		return [$AnggotaList, $countAnggota];
	}

	protected function mutateFormDataBeforeCreate(array $data): array
	{
		// Pastikan user_id tidak null
		if (empty($data['user_id'])) {
			$data['user_id'] = Auth::user()->id;
		}

		$npwp = $data['npwp'];
		$noijin = $data['no_ijin'];

		$cleanNpwp = str_replace(['.', '-'], '', $npwp ?? '');
		//call for soap
		$options = array(
			'soap_version' => SOAP_1_1,
			'exceptions' => true,
			'trace' => 1,
			'cache_wsdl' => WSDL_CACHE_MEMORY,
			'connection_timeout' => 25,
			'style' => SOAP_RPC,
			'use' => SOAP_ENCODED,
		);

		$client = new \SoapClient('https://riph.pertanian.go.id/api.php/simethris?wsdl', $options);
		$parameter = array(
			'user' => 'simethris',
			'pass' => 'wsriphsimethris',
			'npwp' => $cleanNpwp,
			'nomor' =>  $noijin
		);

		$response = $client->__soapCall('get_riph', $parameter);

		//response processing
		$xml = simplexml_load_string($response);
		if ($xml === false) {
			$datariph = json_decode($response, true);
		} else {
			$datariph = json_decode(json_encode($xml), true);
		}

		if (!isset($datariph['return_cek'])) {
			self::handleInvalidResponse($noijin);
		}
		if ($datariph['return_cek'] === 'R00') {
			// self::sendNotification('Pengunduhan', 'Data ditemukan dan berhasil diunduh.', 'success');

			$wajibTanam = $datariph['riph']['wajib_tanam'] ?? [];
			$masterPoktan = $wajibTanam['kelompoktani'] ?? [];

			$poktanList = [];

			if (is_array($masterPoktan) && isset($masterPoktan['loop'])) {
				$poktanArray = self::normalizeArray($masterPoktan['loop']);

				foreach ($poktanArray as $poktan) {
					$poktanId = self::getStringValue($poktan['id_poktan'] ?? '');

					if (!empty($poktanId)) {
						$poktanList[$poktanId] = [ // Gunakan id_poktan sebagai key agar unik
							'npwp' => $npwp,
							'poktan_id' => $poktanId,
							'id_kabupaten' => self::getStringValue($poktan['id_kabupaten'] ?? ''),
							'id_kecamatan' => self::getStringValue($poktan['id_kecamatan'] ?? ''),
							'id_kelurahan' => self::getStringValue($poktan['id_kelurahan'] ?? ''),
							'nama_kelompok' => self::getStringValue($poktan['nama_kelompok'] ?? ''),
							'nama_pimpinan' => self::getStringValue($poktan['nama_pimpinan'] ?? ''),
							'hp_pimpinan' => self::getStringValue($poktan['hp_pimpinan'] ?? ''),
						];
					}
				}
			}

			$AnggotaList = [];
			if (isset($masterPoktan['loop'])) {
				$anggotaArray = self::normalizeArray($masterPoktan['loop']);

				foreach ($anggotaArray as $anggota) {
					$idPetani = self::getStringValue($anggota['id_petani'] ?? '');
					$nikAnggota = self::getStringValue($anggota['ktp_petani'] ?? '');

					if (!empty($idPetani)) {
						$AnggotaList[$idPetani] = [
							'npwp' => $npwp,
							'ktp_petani' => $nikAnggota,
							'id' => $idPetani,
							'anggota_id' => $idPetani,
							'poktan_id' => self::getStringValue($anggota['id_poktan'] ?? ''),
							'nama_petani' => self::getStringValue($anggota['nama_petani'] ?? ''),
							'luas_lahan' => self::getStringValue($anggota['luas_lahan'] ?? ''),
						];
					}
				}
			}
		}

		// Ambil ID Poktan dengan aman
		$poktanIds = isset($poktanList) ? array_keys($poktanList) : [];
		$masterPoktanData = $poktanList ?? [];
		$anggotaIds = isset($AnggotaList) ? array_keys($AnggotaList) : [];
		$masterAnggotaData = $AnggotaList ?? [];

		// Cek data verifikasi tanam dan produksi
		$hasTanam = AjuVerifTanam2024::where('no_ijin', $noijin)->exists();
		$hasProduksi = AjuVerifProduksi2024::where('no_ijin', $noijin)->exists();

		// Lakukan force delete langsung
		try {
			DB::beginTransaction();

			// Force delete data verifikasi tanam dan produksi
			if ($hasTanam) {
				AjuVerifTanam2024::where('no_ijin', $noijin)->forceDelete();
				Notification::make()
					->title('Data Verifikasi Tanam')
					->body("Data verifikasi tanam untuk RIPH {$noijin} telah dihapus.")
					->warning()
					->send();
			}

			if ($hasProduksi) {
				AjuVerifProduksi2024::where('no_ijin', $noijin)->forceDelete();
				Notification::make()
					->title('Data Verifikasi Produksi')
					->body("Data verifikasi produksi untuk RIPH {$noijin} telah dihapus.")
					->warning()
					->send();
			}

			// Force delete data realisasi dan data terkait lainnya
			DataRealisasi2024::where([
				'npwp_company' => $npwp,
				'no_ijin' => $noijin,
			])->forceDelete();

			Lokasi2024::where('npwp', $npwp)
				->where('no_ijin', $noijin)
				->whereIn('poktan_id', $poktanIds)
				->forceDelete();

			Pks2024::where('npwp', $npwp)
				->where('no_ijin', $noijin)
				->whereIn('poktan_id', $poktanIds)
				->forceDelete();

			MasterAnggota2024::where([
				'npwp' => $npwp,
				'poktan_id' => $poktanIds,
			])->forceDelete();

			MasterAnggota2024::where('npwp', $npwp)
				->whereIn('poktan_id', $poktanIds)
				->forceDelete();

			MasterPoktan2024::where('npwp', $npwp)
				->whereIn('poktan_id', $poktanIds)
				->forceDelete();

			UserDocs2024::where('npwp', $npwp)
				->where('no_ijin', $noijin)
				->forceDelete();

			DB::commit();

			Notification::make()
				->title('Data Lama Dihapus')
				->body("Data RIPH {$noijin} yang sudah ada telah berhasil dihapus secara permanen.")
				->info()
				->send();

		} catch (\Exception $e) {
			DB::rollBack();
			Notification::make()
				->title('Error')
				->body("Terjadi kesalahan saat menghapus data: {$e->getMessage()}")
				->danger()
				->send();
		}

		$newMasterPoktanData = [];
		foreach ($masterPoktanData as $poktan) {
			$newMasterPoktanData[] = [
				'npwp' => $npwp,
				'poktan_id' => $poktan['poktan_id'] ?? '',
				'id_kabupaten' => $poktan['id_kabupaten'] ?? '',
				'id_kecamatan' => $poktan['id_kecamatan'] ?? '',
				'id_kelurahan' => $poktan['id_kelurahan'] ?? '',
				'nama_kelompok' => $poktan['nama_kelompok'] ?? '',
				'nama_pimpinan' => $poktan['nama_pimpinan'] ?? '',
				'hp_pimpinan' => $poktan['hp_pimpinan'] ?? null,
				'created_at' => now(),
				'updated_at' => now(),
			];
		}

		$newPksData = [];
		foreach ($masterPoktanData as $pks) {
			$newPksData[] = [
				'npwp' => $npwp,
				'no_ijin' => $noijin,
				'poktan_id' => $pks['poktan_id'] ?? '',
				'kabupaten_id' => $poktan['id_kabupaten'] ?? '',
				'kecamatan_id' => $poktan['id_kecamatan'] ?? '',
				'kelurahan_id' => $poktan['id_kelurahan'] ?? '',
				'created_at' => now(),
				'updated_at' => now(),
			];
		}

		$newMasterAnggotaData = [];
		foreach ($masterAnggotaData as $anggota) {
			$newMasterAnggotaData[] = [
				'npwp' => $npwp,
				'ktp_petani' => $anggota['ktp_petani'] ?? '',
				'id' => $anggota['id'] ?? '',
				'anggota_id' => $anggota['anggota_id'] ?? '',
				'poktan_id' => $anggota['poktan_id'] ?? '',
				'nama_petani' => $anggota['nama_petani'] ?? '',
				'luas_lahan' => $anggota['luas_lahan'] ?? '',
				'created_at' => now(),
				'updated_at' => now(),
			];
		}

		$newLokasiData = [];
		foreach ($masterAnggotaData as $lokasi) {
			$newLokasiData[] = [
				'npwp' => $npwp,
				'no_ijin' => $noijin,
				'anggota_id' => $lokasi['anggota_id'] ?? '',
				'poktan_id' => $lokasi['poktan_id'] ?? '',
				'luas_lahan' => $lokasi['luas_lahan'] ?? '',
				'created_at' => now(),
				'updated_at' => now(),
			];
		}

		MasterPoktan2024::insert($newMasterPoktanData);
		Pks2024::insert($newPksData);
		MasterAnggota2024::insert($newMasterAnggotaData);
		Lokasi2024::insert($newLokasiData);

		$noijin = $data['no_ijin'];
		Notification::make()
			->title('Data Komitmen')
			->body("Semua data Komitmen {$noijin} telah disimpan.")
			->success()
			->send();

		return $data;
	}
}