<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SupportTicketMessage extends Model
{
	use SoftDeletes;

    protected $fillable = [
        'ticket_id',
        'user_id',
        'message',
        'attachment',
        'parent_id',
    ];

    public function ticket()
    {
        return $this->belongsTo(SupportTicket::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function parent()
    {
        return $this->belongsTo(SupportTicketMessage::class, 'parent_id');
    }

    public function replies()
    {
        return $this->hasMany(SupportTicketMessage::class, 'parent_id');
    }

    public function allReplies()
    {
        return $this->replies()->with('allReplies');
    }

    // Scope untuk mendapatkan pesan root (tanpa parent)
    public function scopeRootMessages($query)
    {
        return $query->whereNull('parent_id');
    }

    // Method untuk mendapatkan level kedalaman pesan
    public function getDepthAttribute()
    {
        $depth = 0;
        $parent = $this->parent;

        while ($parent) {
            $depth++;
            $parent = $parent->parent;
        }

        return $depth;
    }

    // Method untuk mengecek apakah user adalah staff
    public function getIsStaffMessageAttribute()
    {
        return $this->user->hasAnyRole(['admin', 'Super Admin', 'support']);
    }
}
