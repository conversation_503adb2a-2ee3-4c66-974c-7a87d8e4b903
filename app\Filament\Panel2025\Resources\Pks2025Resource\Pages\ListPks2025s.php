<?php

namespace App\Filament\Panel2025\Resources\Pks2025Resource\Pages;

use App\Filament\Panel2025\Resources\Pks2025Resource;
use App\Models\Pks2025;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Actions\{BulkActionGroup, EditAction, ViewAction};
use Filament\Tables\Columns\{ColumnGroup, TextColumn};
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class ListPks2025s extends ListRecords
{
    protected static string $resource = Pks2025Resource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

	protected function getTableQuery(): Builder
	{
		$user = Auth::user();
		return $user->hasRole('dinas') 
			? Pks2025::query()->where('kabupaten_id', Auth::user()->dataadmin->kabupaten_id)->where('status_dinas', 0)
			: Pks2025::query();
	}

	public function table(Table $table): Table
	{
		$isDinas = Auth::user()->hasRole('dinas');
		return $table
			->emptyStateHeading('Belum ada PKS diajukan')
			->poll('10s')
			->columns([
				TextColumn::make('index')
                    ->label('No')
					->rowIndex(),
				TextColumn::make('datauser.company_name')
					->label('Perusahaan')
					->searchable(),
				TextColumn::make('no_ijin')
					->searchable(),
				TextColumn::make('nama_poktan')
					->searchable(),

				ColumnGroup::make('Perjanjian',[
					TextColumn::make('no_perjanjian')
						->label('Nomor')
						->searchable(),
					TextColumn::make('tgl_perjanjian_start')
						->date()
						->label('Mulai')
						->sortable(),
					TextColumn::make('tgl_perjanjian_end')
						->date()
						->label('Akhir')
						->sortable(),
				]),

				TextColumn::make('jumlah_anggota')
					->label('Partisipan')
					->suffix(' orang')
					->numeric()
					->sortable(),
				TextColumn::make('luas_rencana')
					->numeric()
					->suffix(' m2')
					->label('Luas')
					->sortable(),
				TextColumn::make('periode_tanam')
					->searchable(),
				TextColumn::make('kecamatan.nama_kecamatan')
					->searchable(),
				TextColumn::make('desa.nama_desa')
					->searchable(),
				TextColumn::make('berkas_pks')
					->formatStateUsing(fn ($record) => new HtmlString(
						'<a class="font-bold text-info-500" href="' . asset($record->berkas_pks) . '" target="_blank" rel="nofollow noreferrer">Lihat/Unduh</a>'
					)),
				TextColumn::make('status')
					->searchable(),
				TextColumn::make('verif_by')
					->numeric()
					->sortable(),
				TextColumn::make('verif_at')
					->date()
					->sortable(),
			])
			->filters([
				//
			])
			->actions([
				ViewAction::make()->hiddenLabel()->visible(fn ()=> Auth::user()->hasRole('importir')),
				EditAction::make()->hiddenLabel()
					->icon('icon-journal-bookmark-fill')->url(function ($record){
					$isDinas = Auth::user()->hasRole('dinas');
					$isImportir = Auth::user()->hasRole('importir');
					if($isDinas){
						return route('filament.panel2025.resources.pks2025s.verifdinas', ['record' => $record->id]);
					}
				}),
			])
			->bulkActions([
				BulkActionGroup::make([
					// DeleteBulkAction::make(),
				]),
			]);
	}
}
