<?php

namespace App\Filament\Admin\Resources\PengajuansklResource\Pages;

use App\Filament\Admin\Resources\PengajuansklResource;
use App\Models\Commitment2025;
use App\Models\Completed;
use App\Models\MasterSpatial;
use App\Models\Pengajuanskl2025;
use App\Models\PengajuanVerifikasi;
use Filament\Actions;
use Filament\Actions\StaticAction;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\HtmlString;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Endroid\QrCode\Color\Color;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use Illuminate\Support\Facades\Log;
use TCPDF;

class ListPengajuanskl extends ListRecords
{
	protected static string $resource = PengajuansklResource::class;

	public function getHeading(): string
	{
		return 'Daftar Pengajuan Penerbitan SKL';
	}

	public function getSubheading(): ?string
    {
        return 'untuk RIPH Periode setelah 2024';
    }

    protected function getHeaderWidgets(): array
    {
        return [];
    }

	protected function getHeaderActions(): array
	{
		return [
			// Actions\CreateAction::make(),
		];
	}

	public function table(Table $table): Table
	{
		return $table
			->emptyStateHeading('Belum ada pengajuan Status Lunas')
			->columns([
				TextColumn::make('no_pengajuan'),
				TextColumn::make('no_ijin')
					->label('PPRK'),
				TextColumn::make('user.datauser.company_name')
					->label('Pelaku Usaha'),
				TextColumn::make('status')
					->badge()
					->color(function (string $state, $record): string {
						// Cek apakah SKL pernah diperbarui berdasarkan nama file
						$isUpdated = $record->skl_upload && str_contains($record->skl_upload, '_revised_');

						if ($isUpdated) {
							return 'warning'; // Warna khusus untuk SKL yang diperbarui
						}

						$colorMap = [
							'0' => 'warning',  // Baru,
							'1' => 'success',  // Disetujui,
							'2' => 'danger',   // Ditolak,
						];
						return $colorMap[$state] ?? 'secondary';
					})
					->formatStateUsing(function ($state, $record) {
						// Cek apakah SKL pernah diperbarui berdasarkan nama file
						$isUpdated = $record->skl_upload && str_contains($record->skl_upload, '_revised_');

						if ($isUpdated) {
							return 'Diperbarui'; // Label khusus untuk SKL yang diperbarui
						}

						$statusMap = [
							'0' => 'Baru',
							'1' => 'Disetujui',
							'2' => 'Ditolak',
						];
						return $statusMap[$state] ?? 'Tidak Diketahui';
					}),
			])
			->actions([
				Action::make('printSklDraft')
					->hiddenLabel()
					->visible(function ($record) {
						// Tombol ini hanya muncul jika:
						// 1. User adalah admin atau Super Admin
						// 2. Status record adalah 1 (Disetujui)
						// 3. skl_upload belum terisi (belum ada SKL yang diunggah)
						// 4. no_skl atau published_date belum terisi ATAU skl_auto belum terisi
						if (Auth::user()->hasAnyRole(['admin', 'Super Admin']) &&
							$record->status == 1 &&
							is_null($record->skl_upload) &&
							(is_null($record->no_skl) || is_null($record->published_date) || is_null($record->skl_auto))) {
							return true;
						}
						return false;
					})
					->tooltip('Cetak Draft SKL')
					->color('warning')
					->icon('icon-printer-fill')
					->modalHeading('Cetak Draft SKL')
					->modalSubmitAction(fn(StaticAction $action) => $action->label('Cetak'))
					->form([
						TextInput::make('no_skl')
							->label('Nomor SKL')
							->required()
							->live(onBlur: true)
							->inlineLabel()
							->helperText(fn (callable $get) =>
								$get('duplicate_warning')
									? new HtmlString('<span class="text-danger-500">' . $get('duplicate_warning') . '</span>')
									: null
							)
							->afterStateUpdated(function (callable $set, $state) {
								$isDuplicate = Pengajuanskl2025::where('no_skl', $state)->exists() || Completed::where('no_skl', $state)->exists();
								$set('duplicate_warning', $isDuplicate ? 'Nomor SKL sudah digunakan' : '');

								if ($isDuplicate) {
									$set('no_skl', null);
									Notification::make()
										->title('Error')
										->danger()
										->body('Nomor SKL sudah digunakan. Silakan gunakan nomor lain.')
										->send();
								}
							}),

						DatePicker::make('published_date')
							->label('Tanggal SKL')
							->required()
							->inlineLabel(),


					])
					->action(function ($record, array $data) {
						try {
							// Mulai transaksi database
							DB::beginTransaction();

							// Simpan data SKL
							$record->no_skl = $data['no_skl'];
							$record->published_date = $data['published_date'];
							$record->save();

							// Generate SKL
							$result = self::generateDraftSkl($record);

							if ($result instanceof \Illuminate\Http\RedirectResponse) {
								DB::rollBack();
								// Status error ditampilkan melalui notifikasi
								return $result;
							}

							// Commit transaksi
							DB::commit();

							// Status sukses ditampilkan melalui notifikasi

							Notification::make()
								->title('Sukses!')
								->success()
								->body('Berkas SKL berhasil dibuat.')
								->send();

							return redirect(Storage::url($result));
						} catch (\Exception $e) {
							DB::rollBack();

							// Status error ditampilkan melalui notifikasi

							Notification::make()
								->title('Gagal membuat Draft SKL.')
								->danger()
								->body($e->getMessage())
								->send();
							return back();
						}
					})
					->extraModalFooterActions(fn ($action) => [
						$action->getModalSubmitAction()->extraAttributes(['id' => 'submit-skl-button']),
					]),

				Action::make('viewDraftSkl')
					->hiddenLabel()
					->visible(function ($record) {
						// Tombol ini hanya muncul jika:
						// 1. skl_auto sudah terisi (draft SKL sudah dibuat)
						// 2. File draft SKL benar-benar ada di storage
						// 3. User adalah admin atau Super Admin
						return !is_null($record->skl_auto) &&
							Storage::disk('public')->exists($record->skl_auto) &&
							Auth::user()->hasAnyRole(['admin', 'Super Admin']);
					})
					->tooltip('Lihat/Unduh Draft SKL')
					->color('warning')
					->icon('icon-printer-fill')
					->url(fn($record) => url('/' . $record->skl_auto), true), // true untuk membuka di tab baru

				Action::make('editDraftSkl')
					->hiddenLabel()
					->tooltip('Ubah Draft SKL')
					->visible(function ($record) {
						return Auth::user()->hasAnyRole(['admin', 'Super Admin']) &&
							$record->status == 1 &&
							!is_null($record->skl_auto) &&
							is_null($record->skl_upload);
					})
					->color('warning')
					->icon('heroicon-s-pencil-square')
					->modalHeading('Edit Draft SKL')
					->modalSubmitAction(fn(StaticAction $action) => $action->label('Perbarui'))
					->form(function ($record) {
						return [
							TextInput::make('no_skl')
								->label('Nomor SKL')
								->required()
								->default($record->no_skl)
								->live(onBlur: true)
								->inlineLabel()
								->helperText(fn (callable $get) =>
									$get('duplicate_warning')
										? new HtmlString('<span class="text-danger-500">' . $get('duplicate_warning') . '</span>')
										: null
								)
								->afterStateUpdated(function (callable $set, $state, $record) {
									// Cek duplikat kecuali untuk record ini sendiri
									$isDuplicate = Pengajuanskl2025::where('no_skl', $state)
										->where('id', '!=', $record->id)
										->exists() ||
										Completed::where('no_skl', $state)
										->where('no_ijin', '!=', $record->no_ijin)
										->exists();

									$set('duplicate_warning', $isDuplicate ? 'Nomor SKL sudah digunakan' : '');

									if ($isDuplicate) {
										$set('no_skl', null);
										Notification::make()
											->title('Error')
											->danger()
											->body('Nomor SKL sudah digunakan. Silakan gunakan nomor lain.')
											->send();
									}
								}),

							DatePicker::make('published_date')
								->label('Tanggal SKL')
								->required()
								->default($record->published_date)
								->inlineLabel(),
						];
					})
					->action(function ($record, array $data) {
						try {
							// Mulai transaksi database
							DB::beginTransaction();

							// Simpan data SKL baru
							$record->no_skl = $data['no_skl'];
							$record->published_date = $data['published_date'];
							$record->save();

							// Hapus file SKL lama jika ada
							if ($record->skl_auto) {

								// Coba hapus file lama
								try {
									if (Storage::disk('public')->exists($record->skl_auto)) {
										Storage::disk('public')->delete($record->skl_auto);
									} else {
										Log::warning('File SKL lama tidak ditemukan', [
											'path' => $record->skl_auto
										]);
									}
								} catch (\Exception $e) {
									Log::error('Gagal menghapus file SKL lama: ' . $e->getMessage(), [
										'exception' => $e->getMessage()
									]);
								}

								// Set skl_auto ke null untuk memastikan file baru akan dibuat
								$record->skl_auto = null;
								$record->save();
							}

							// Generate SKL baru
							$result = self::generateDraftSkl($record);

							if ($result instanceof \Illuminate\Http\RedirectResponse) {
								DB::rollBack();
								return $result;
							}

							// Commit transaksi
							DB::commit();

							Notification::make()
								->title('Sukses!')
								->success()
								->body('Draft SKL berhasil diperbarui.')
								->send();

							return redirect(Storage::url($result));
						} catch (\Exception $e) {
							DB::rollBack();

							Notification::make()
								->title('Gagal memperbarui Draft SKL.')
								->danger()
								->body($e->getMessage())
								->send();
							return back();
						}
					}),

				/*
					action ini akan menghapus/membebaskan lahan. mengubah status kerjasama dan batasan waktunya
					Tombol ini hanya muncul jika:
					1. User adalah admin atau Super Admin
					2. skl_auto sudah terisi (draft SKL sudah dibuat)
					3. skl_upload belum terisi (belum ada SKL yang diunggah)
				*/
				Action::make('uploadSkl')
					->hiddenLabel()
					->visible(function ($record) {
						if (Auth::user()->hasAnyRole(['admin', 'Super Admin']) &&
							!is_null($record->skl_auto) &&
							is_null($record->skl_upload)) {
							return true;
						}
						return false;
					})
					->tooltip('Unggah SKL')
					->color('warning')
					->icon('icon-cloud-arrow-up-fill')
					->modalHeading('Unggah SKL')
					->modalSubmitAction(fn(StaticAction $action) => $action->label('Unggah'))
					->form([
						FileUpload::make('skl_upload')
							->openable()
							->hiddenLabel()
							->required()
							->maxSize(2048)
							->columnSpan(1)
							->deletable()
							->downloadable()
							->disk('public')
							->visibility('public')
							->panelAspectRatio('5:1')
							->imagePreviewHeight('50')
							->fetchFileInformation(true)
							->directory(function ($record) {
								$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
								$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
								return "uploads/{$cleanNpwp}/{$cleanNoIjin}/dokumen";
							})
							->rules([
								'file',
								'mimetypes:application/pdf',
								'mimes:pdf'
							])
							->validationMessages([
								'mimetypes' => 'Hanya file PDF yang diperbolehkan',
								'mimes' => 'Ekstensi file harus .pdf',
							])
							->getUploadedFileNameForStorageUsing(
								function (TemporaryUploadedFile $file, $record): string {
									$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
									$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

									// Format nama file: [ID]_[NPWP]_[NOIJIN].[ext]
									return 'skl_' . $cleanNpwp . '_' . $cleanNoIjin . '_'.uniqid() . '.' . $file->getClientOriginalExtension();
								}
							)
							->helperText(new HtmlString('<span class="text-danger-500">Maksimal 2MB, format PDF. Pastikan Draft SKL yang akan diunggah telah ditandatangani oleh Pimpinan dan dibubuhi Stempel!</span>'))
					])
					->action(function ($record, array $data) {
						try {
							// Mulai transaksi database
							DB::beginTransaction();

							// Validasi data sebelum memproses
							if (empty($record->no_ijin)) {
								throw new \Exception('Nomor RIPH tidak valid');
							}

							if (empty($record->no_pengajuan)) {
								throw new \Exception('Nomor pengajuan tidak valid');
							}

							// Update record SKL
							$record->skl_upload = $data['skl_upload'];
							$record->save();

							// Update status pengajuan verifikasi
							$pengajuan = PengajuanVerifikasi::where('no_pengajuan', $record->no_pengajuan)->first();
							if ($pengajuan) {
								$pengajuan->status = '8';
								$pengajuan->save();
							} else {
								Log::warning('Pengajuan verifikasi tidak ditemukan', [
									'no_pengajuan' => $record->no_pengajuan
								]);
							}

							// Update status commitment
							$commitment = Commitment2025::where('no_ijin', $record->no_ijin)->first();
							if ($commitment) {
								$commitment->status = 'Lunas';
								$commitment->skl = $record->skl_upload;
								$commitment->save();
							} else {
								Log::warning('Commitment tidak ditemukan', [
									'no_ijin' => $record->no_ijin
								]);
							}

							// Generate URL untuk file SKL
							$fullUrl = url(Storage::url($record->skl_upload));

							// Update atau buat record Completed
							Completed::updateOrCreate(
								[
									'no_ijin' => $record->no_ijin,
								],
								[
									'periodetahun' => $record->commitment->periodetahun ?? null,
									'no_skl' => $record->no_skl,
									'npwp' => $record->npwp,
									'published_date' => $record->published_date,
									'luas_tanam' => $record->detailrealisasitanam ? $record->detailrealisasitanam->sum('value') : 0,
									'volume' => $record->detailrealisasiproduksi ? $record->detailrealisasiproduksi->sum('value') : 0,
									'status' => 'Lunas',
									'skl_upload' => $record->skl_upload,
									'url' => $fullUrl,
								]
							);

							// Update status MasterSpatial (membebaskan lahan yang telah selesai)
							$spatialCount = MasterSpatial::where('reserved_by', $record->no_ijin)->count();

							if ($spatialCount > 0) {
								// Update semua record MasterSpatial terkait dalam satu query
								DB::table('t2025_master_spatials')
									->where('reserved_by', $record->no_ijin)
									->update([
										'status' => 0,
										'reserved_at' => null,
										'reserved_by' => null,
										'updated_at' => now()
									]);
							} else {
								Log::warning('No MasterSpatial records found for this commitment', [
									'no_ijin' => $record->no_ijin
								]);
							}

							// Commit transaksi jika semua operasi berhasil
							DB::commit();

							// Tampilkan notifikasi sukses
							Notification::make()
								->title('SKL Berhasil Diunggah')
								->success()
								->body('File SKL telah berhasil diunggah dan status telah diperbarui.')
								->send();

							// Redirect ke file SKL yang baru diunggah
							return redirect(Storage::url($record->skl_upload));

						} catch (\Exception $e) {
							// Rollback transaksi jika terjadi error
							DB::rollBack();

							Log::error('Gagal mengunggah SKL: ' . $e->getMessage(), [
								'exception' => $e,
								'trace' => $e->getTraceAsString(),
								'record_id' => $record->id ?? null,
								'no_ijin' => $record->no_ijin ?? null
							]);

							// Tampilkan notifikasi error
							Notification::make()
								->title('Gagal Mengunggah SKL')
								->danger()
								->body($e->getMessage())
								->send();

							return back();
						}
					}),

				Action::make('createRevisedSklDraft')
					->hiddenLabel()
					->visible(function ($record) {
						// Hanya tampilkan untuk Super Admin dan jika SKL sudah diunggah
						return Auth::user()->hasRole('Super Admin') &&
							!is_null($record->skl_upload);
					})
					->tooltip('Buat Draft Revisi SKL')
					->color('warning')
					->icon('icon-pencil-square')
					->modalHeading('Buat Draft Revisi SKL')
					->modalDescription(new HtmlString('<div class="text-warning-500 font-bold">Anda akan membuat draft revisi untuk SKL yang sudah diterbitkan. Draft ini perlu dicetak dan ditandatangani sebelum diunggah.</div>'))
					->requiresConfirmation()
					->modalSubmitAction(fn(StaticAction $action) => $action->label('Buat Draft Revisi'))
					->form([
						TextInput::make('no_skl')
							->label('Nomor SKL Baru')
							->required()
							->default(fn ($record) => $record->no_skl)
							->inlineLabel()
							->helperText('Masukkan nomor SKL baru jika perlu diubah'),

						DatePicker::make('published_date')
							->label('Tanggal SKL Baru')
							->required()
							->default(fn ($record) => $record->published_date)
							->inlineLabel(),
					])
					->action(function ($record, array $data) {
						try {
							// Mulai transaksi database
							DB::beginTransaction();

							// Log aktivitas revisi SKL
							Log::warning('Draft revisi SKL dibuat oleh ' . Auth::user()->name, [
								'user_id' => Auth::id(),
								'no_ijin' => $record->no_ijin,
								'old_no_skl' => $record->no_skl,
								'new_no_skl' => $data['no_skl'],
								'old_published_date' => $record->published_date,
								'new_published_date' => $data['published_date'],
								'timestamp' => now()
							]);

							// Simpan data asli sementara
							$oldNoSkl = $record->no_skl;
							$oldPublishedDate = $record->published_date;

							// Perbarui data untuk pembuatan draft
							$record->no_skl = $data['no_skl'];
							$record->published_date = $data['published_date'];

							// Generate draft SKL revisi
							$draftPath = self::generateDraftSkl($record);

							if ($draftPath instanceof \Illuminate\Http\RedirectResponse) {
								DB::rollBack();
								return $draftPath;
							}

							// Kembalikan data asli
							$record->no_skl = $oldNoSkl;
							$record->published_date = $oldPublishedDate;
							$record->save();

							// Commit transaksi
							DB::commit();

							// Simpan session untuk menandai bahwa draft revisi sudah dibuat
							session()->put('draft_revision_created_' . $record->id, true);

							Notification::make()
								->title('Draft Revisi SKL Berhasil Dibuat')
								->success()
								->body('Silakan cetak draft revisi, tandatangani, dan unggah melalui tombol "Unggah Revisi SKL".')
								->send();

							return redirect(Storage::url($draftPath));
						} catch (\Exception $e) {
							DB::rollBack();

							Notification::make()
								->title('Gagal membuat draft revisi SKL.')
								->danger()
								->body($e->getMessage())
								->send();
							return back();
						}
					}),

				Action::make('uploadRevisedSkl')
					->hiddenLabel()
					->visible(function ($record) {
						// Hanya tampilkan untuk Super Admin, jika SKL sudah diunggah, dan setelah draft revisi dibuat
						// Kita menggunakan session untuk menandai bahwa draft revisi sudah dibuat
						return Auth::user()->hasRole('Super Admin') &&
							!is_null($record->skl_upload) &&
							session()->has('draft_revision_created_' . $record->id);
					})
					->tooltip('Unggah Revisi SKL')
					->color('danger')
					->icon('icon-cloud-arrow-up-fill')
					->modalHeading('Unggah Revisi SKL')
					->modalDescription(new HtmlString('<div class="text-danger-500 font-bold">PERHATIAN: Tindakan ini akan mengganti file SKL yang sudah diterbitkan dengan file yang baru. Pastikan file yang diunggah adalah hasil scan dari draft revisi yang sudah ditandatangani dan distempel.</div>'))
					->requiresConfirmation()
					->modalSubmitAction(fn(StaticAction $action) => $action->label('Unggah Revisi'))
					->form([
						FileUpload::make('revised_skl_upload')
							->label('Unggah SKL Revisi')
							->required()
							->maxSize(2048)
							->disk('public')
							->visibility('public')
							->rules([
								'file',
								'mimetypes:application/pdf',
								'mimes:pdf'
							])
							->validationMessages([
								'mimetypes' => 'Hanya file PDF yang diperbolehkan',
								'mimes' => 'Ekstensi file harus .pdf',
							])
							->directory(function ($record) {
								$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
								$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
								return "uploads/{$cleanNpwp}/{$cleanNoIjin}/dokumen";
							})
							->getUploadedFileNameForStorageUsing(
								function (TemporaryUploadedFile $file, $record): string {
									$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
									$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
									return 'skl_' . $cleanNpwp . '_' . $cleanNoIjin . '_revised_'.uniqid() . '.' . $file->getClientOriginalExtension();
								}
							)
							->helperText(new HtmlString('<span class="text-danger-500">Maksimal 2MB, format PDF. Pastikan SKL yang akan diunggah telah ditandatangani oleh Pimpinan dan dibubuhi Stempel!</span>')),
					])
					->action(function ($record, array $data) {
						try {
							// Mulai transaksi database
							DB::beginTransaction();

							// Kita tidak perlu mengambil data draft revisi karena kita hanya mengganti file SKL
							// tanpa mengubah nomor SKL dan tanggal terbit

							// Log aktivitas pembaruan SKL
							Log::warning('SKL diperbarui oleh ' . Auth::user()->name, [
								'user_id' => Auth::id(),
								'no_ijin' => $record->no_ijin,
								'no_skl' => $record->no_skl,
								'published_date' => $record->published_date,
								'old_skl_file' => $record->skl_upload,
								'timestamp' => now()
							]);

							// Hapus file SKL lama jika berbeda dari yang baru
							$oldSklPath = $record->skl_upload;
							if ($oldSklPath && Storage::disk('public')->exists($oldSklPath)) {
								// Buat backup file lama dengan menambahkan suffix
								$backupPath = pathinfo($oldSklPath, PATHINFO_DIRNAME) . '/' .
											pathinfo($oldSklPath, PATHINFO_FILENAME) .
											'_backup_' . date('YmdHis') . '.' .
											pathinfo($oldSklPath, PATHINFO_EXTENSION);

								Storage::disk('public')->copy($oldSklPath, $backupPath);
							}

							// Perbarui data SKL
							$record->skl_upload = $data['revised_skl_upload'];
							$record->save();

							// Perbarui data di tabel Completed jika ada
							$completed = Completed::where('no_ijin', $record->no_ijin)->first();
							if ($completed) {
								$fullUrl = url(Storage::url($record->skl_upload));

								// Pastikan periodetahun diperbarui dari relasi commitment
								$completed->periodetahun = $record->commitment->periodetahun;
								$completed->skl_upload = $data['revised_skl_upload'];
								$completed->url = $fullUrl;
								$completed->save();
							}

							// Commit transaksi
							DB::commit();

							// Hapus session setelah revisi SKL berhasil diunggah
							session()->forget('draft_revision_created_' . $record->id);

							Notification::make()
								->title('SKL Berhasil Diperbarui')
								->success()
								->body('File SKL telah berhasil diperbarui.')
								->send();

							return redirect(Storage::url($data['revised_skl_upload']));
						} catch (\Exception $e) {
							DB::rollBack();

							Notification::make()
								->title('Gagal memperbarui SKL.')
								->danger()
								->body($e->getMessage())
								->send();
							return back();
						}
					}),

				Action::make('viewSkl')
					->hiddenLabel()
					->visible(fn($record) => $record->skl_upload)
					->color('success')
					->icon('icon-award-fill')
					->url(fn($record) => url('/' . $record->skl_upload)),

				EditAction::make()
					->icon('heroicon-o-clipboard-document-check')
					->url(function ($record) {
						if (Auth::user()->hasAnyRole(['admin', 'Super Admin'])) {
							return route('filament.admin.resources.pengajuanskls.sklapprovalview', ['record' => $record->id]);
						}

						if (Auth::user()->hasRole('direktur')) {
							return ($record->status != 0)
								? route('filament.admin.resources.pengajuanskls.sklapprovalview', ['record' => $record->id])
								: route('filament.admin.resources.pengajuanskls.sklapproval', ['record' => $record->id]);
						}
					})->hiddenLabel(),
			]);
	}

	protected static function generateQrCode($noIjin, $npwp)
    {
        try {
            $secretKey = config('app.qr_secret');
            $sanitizedNpwp = preg_replace('/[^A-Za-z0-9]/', '', $npwp);
            $sanitizedNoIjin = preg_replace('/[^A-Za-z0-9]/', '', $noIjin);
            $reversedNoIjin = strrev($sanitizedNoIjin);
            $data = "{$reversedNoIjin}|{$sanitizedNpwp}";
            $hash = hash_hmac('sha256', $data, $secretKey);
            $shortHash = substr($hash, 0, 8);

            $maskedNos = "{$reversedNoIjin}{$shortHash}";
            $url = url("/verify-skl?mask={$maskedNos}&mock={$hash}");

            try {
                $qrCode = new QrCode(
                    data: $url,
                    size: 200,
                    margin: 0,
                    errorCorrectionLevel: ErrorCorrectionLevel::High,
                    foregroundColor: new Color(0, 0, 0),
                    backgroundColor: new Color(255, 255, 255),
                    encoding: new Encoding('UTF-8')
                );

                // Tulis ke PNG
                $writer = new PngWriter();
                $result = $writer->write($qrCode);

                // Konversi ke data URI
                $dataUri = $result->getDataUri();

                return $dataUri;
            } catch (\Exception $endroidException) {
                // Log error Endroid
                Log::warning('Failed to generate QR code with Endroid: ' . $endroidException->getMessage(), [
                    'exception' => $endroidException,
                    'no_ijin' => $noIjin
                ]);
            }
        } catch (\Exception $e) {
            // Log error dan kembalikan string kosong
            Log::error('Failed to generate QR code: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
                'no_ijin' => $noIjin,
                'npwp' => $npwp
            ]);

            return '';
        }
    }

	public static function sklPayload($record)
	{
		try {
			$company = $record->datauser->company_name;
			$npwp = $record->npwp;
			$noSkl = $record->no_skl;
			$tglTerbit = $record->published_date;
			$noIjin = $record->no_ijin;
			$approvedBy = $record->approvedBy->dataadmin->nama;
			$nip = $record->approvedBy->dataadmin->nip;
			$wajibTanam = $record->commitment->luas_wajib_tanam;
			$wajibProduksi = $record->commitment->volume_produksi;
			$realisasiTanam = $record->detailrealisasitanam->sum('value');
			$realisasiProduksi = $record->detailrealisasiProduksi->sum('value');

			// Generate QR Code
			$qrCode = self::generateQrCode($noIjin, $npwp);

			// Jika QR code gagal dibuat, gunakan placeholder
			if (empty($qrCode)) {
				Log::warning('QR Code gagal dibuat, menggunakan placeholder', [
					'no_ijin' => $noIjin,
					'npwp' => $npwp
				]);
				$qrCode = '<div style="width:100px;height:100px;border:1px solid #ccc;text-align:center;line-height:100px;">QR Code</div>';
			}

			return [
				'company' => $company,
				'noSkl' => $noSkl,
				'npwp' => $npwp,
				'noIjin' => $noIjin,
				'approvedBy' => $approvedBy,
				'nip' => $nip,
				'tglTerbit' => $tglTerbit,
				'wajibTanam' => $wajibTanam,
				'wajibProduksi' => $wajibProduksi,
				'realisasiTanam' => $realisasiTanam,
				'realisasiProduksi' => $realisasiProduksi,
				'QrCode' => $qrCode,
				'qrCodeUrl' => url("/verify-skl?no_ijin={$noIjin}"), // URL alternatif untuk template sederhana
			];
		} catch (\Exception $e) {
			Log::error('Error dalam sklPayload: ' . $e->getMessage(), [
				'exception' => $e,
				'trace' => $e->getTraceAsString(),
				'record_id' => $record->id ?? null
			]);

			// Return minimal payload untuk mencegah error
			return [
				'company' => $record->datauser->company_name ?? 'Tidak tersedia',
				'noSkl' => $record->no_skl ?? 'Tidak tersedia',
				'npwp' => $record->npwp ?? 'Tidak tersedia',
				'noIjin' => $record->no_ijin ?? 'Tidak tersedia',
				'approvedBy' => 'Tidak tersedia',
				'nip' => 'Tidak tersedia',
				'tglTerbit' => $record->published_date ?? now(),
				'wajibTanam' => 0,
				'wajibProduksi' => 0,
				'realisasiTanam' => 0,
				'realisasiProduksi' => 0,
				'QrCode' => '<div style="width:100px;height:100px;border:1px solid #ccc;text-align:center;line-height:100px;">QR Code</div>',
				'qrCodeUrl' => url("/verify-skl"),
			];
		}
	}

	/**
     * Membuat PDF dengan metode paling sederhana (tanpa template HTML)
     */
    protected static function createMinimalPdf($outputPath, $payload, $path)
    {
        try {
            // Tingkatkan batas memori dan waktu eksekusi
            ini_set('memory_limit', '512M');
            ini_set('max_execution_time', 300);

            // Buat instance TCPDF
            $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, 'LEGAL', true, 'UTF-8', false);

            // Set informasi dokumen
            $pdf->SetCreator('SIMETHRIS');
            $pdf->SetAuthor('Kementerian Pertanian');
            $pdf->SetTitle('SKL ' . $payload['noIjin']);
            $pdf->SetSubject('Surat Keterangan Lunas');

            // Hapus header dan footer default
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(false);

            // Set margin
            $pdf->SetMargins(15, 15, 15);
            $pdf->SetAutoPageBreak(TRUE, 15);

            // Set font
            $pdf->SetFont('helvetica', 'B', 16);

            // Tambahkan halaman
            $pdf->AddPage();

            // Tambahkan konten langsung tanpa HTML
            $pdf->Cell(0, 10, 'SURAT KETERANGAN LUNAS', 0, 1, 'C');
            $pdf->Cell(0, 10, 'Nomor: ' . $payload['noSkl'], 0, 1, 'C');

            $pdf->Ln(10);

            $pdf->SetFont('helvetica', '', 12);
            $pdf->Cell(0, 10, 'Dengan ini menyatakan bahwa:', 0, 1, 'L');

            $pdf->Ln(5);

            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(50, 10, 'Nama Perusahaan:', 0, 0, 'L');
            $pdf->SetFont('helvetica', '', 12);
            $pdf->Cell(0, 10, $payload['company'], 0, 1, 'L');

            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(50, 10, 'NPWP:', 0, 0, 'L');
            $pdf->SetFont('helvetica', '', 12);
            $pdf->Cell(0, 10, $payload['npwp'], 0, 1, 'L');

            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(50, 10, 'Nomor RIPH:', 0, 0, 'L');
            $pdf->SetFont('helvetica', '', 12);
            $pdf->Cell(0, 10, $payload['noIjin'], 0, 1, 'L');

            $pdf->Ln(10);

            $pdf->MultiCell(0, 10, 'Telah melaksanakan kewajiban tanam dan produksi bawang putih sesuai ketentuan.', 0, 'L');

            $pdf->Ln(10);

            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(50, 10, 'Tanggal:', 0, 0, 'L');
            $pdf->SetFont('helvetica', '', 12);
            $pdf->Cell(0, 10, \Carbon\Carbon::parse($payload['tglTerbit'])->format('d-m-Y'), 0, 1, 'L');

            $pdf->Ln(20);

            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(0, 10, 'Direktur,', 0, 1, 'R');
            $pdf->Ln(15);
            $pdf->Cell(0, 10, 'ttd', 0, 1, 'R');
            $pdf->Cell(0, 10, $payload['approvedBy'], 0, 1, 'R');
            $pdf->SetFont('helvetica', '', 12);
            $pdf->Cell(0, 10, 'NIP. ' . $payload['nip'], 0, 1, 'R');

            $pdf->Ln(20);

            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(50, 10, 'Verifikasi:', 0, 0, 'L');
            $pdf->SetFont('helvetica', '', 12);
            $pdf->Cell(0, 10, url("/verify-skl?no_ijin={$payload['noIjin']}"), 0, 1, 'L');

            // Simpan PDF ke file
            $pdf->Output($outputPath, 'F');

            // Periksa apakah file PDF berhasil dibuat
            if (!file_exists($outputPath)) {
                Log::warning('TCPDF gagal membuat file PDF SKL minimal', [
                    'no_ijin' => $payload['noIjin'],
                    'file_path' => $path
                ]);
                return false;
            }

            return $path;
        } catch (\Exception $e) {
            Log::error('Error pada pembuatan PDF minimal: ' . $e->getMessage(), [
                'no_ijin' => $payload['noIjin'],
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

	/**
     * Mencoba membuat PDF dengan TCPDF
     */
    protected static function tryTCPDF($template, $outputPath, $payload, $path)
    {
        try {
            // Tingkatkan batas memori dan waktu eksekusi
            // ini_set('memory_limit', '512M');
            // ini_set('max_execution_time', 300);

            // Buat instance TCPDF
            $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, 'A4', true, 'UTF-8', false);

            // Set informasi dokumen
            $pdf->SetCreator('SIMETHRIS');
            $pdf->SetAuthor('Kementerian Pertanian');
            $pdf->SetTitle('SKL ' . $payload['noIjin']);
            $pdf->SetSubject('Surat Keterangan Lunas');
            // Hapus header dan footer default
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(false);

            // Set margin
            $pdf->SetMargins(15, 10, 15);
            $pdf->SetAutoPageBreak(TRUE, 10);

            // Set font
            $pdf->SetFont('helvetica', '', 10);

            // Tambahkan halaman
            $pdf->AddPage();

            try {
                // Tambahkan konten HTML
                $pdf->writeHTML($template, true, false, true, false, '');
            } catch (\Exception $htmlException) {
                Log::error('Error saat menambahkan konten HTML: ' . $htmlException->getMessage(), [
                    'exception' => $htmlException->getMessage(),
                    'trace' => $htmlException->getTraceAsString()
                ]);

                // Tambahkan teks sederhana sebagai fallback
                $pdf->AddPage();
                $pdf->Cell(0, 10, 'Error saat membuat SKL dengan format HTML', 0, 1, 'C');
                $pdf->Cell(0, 10, 'Silakan hubungi administrator', 0, 1, 'C');
            }

            // Simpan PDF ke file
            $pdf->Output($outputPath, 'F');

            // Periksa apakah file PDF berhasil dibuat
            if (!file_exists($outputPath)) {
                Log::warning('TCPDF gagal membuat file PDF SKL', [
                    'no_ijin' => $payload['noIjin'],
                    'file_path' => $path
                ]);
                throw new \Exception('Gagal membuat file PDF SKL dengan TCPDF');
            }

            return $path;
        } catch (\Exception $e) {
            Log::error('Error pada TCPDF: ' . $e->getMessage(), [
                'no_ijin' => $payload['noIjin'],
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

	public static function generateDraftSkl($record)
	{
		try {
			// Persiapkan data untuk template
			$payload = self::sklPayload($record);

			// Cek jika payload kosong atau gagal dibuat
			if (empty($payload)) {
				Log::error('Validasi payload SKL gagal', [
					'payload' => $payload,
					'record_id' => $record->id ?? null,
					'no_ijin' => $record->no_ijin ?? null
				]);
				return back()->withErrors(['error' => 'Gagal menghasilkan SKL. Data tidak valid.']);
			}

			// Persiapkan path file
			$npwp = str_replace(['.', '-'], '', $payload['npwp']);
			$noIjin = str_replace(['.', '-', '/'], '', $payload['noIjin']);

			// Tambahkan uniqueId dan timestamp untuk menghindari caching browser
			$uniqueId = uniqid();
			$timestamp = date('YmdHis');
			$fileName = 'draftskl_' . $noIjin . '_' . $uniqueId . '_' . $timestamp . '.pdf';

			$directory = 'uploads/' . $npwp . '/' . $noIjin . '/dokumen';
			$path = $directory . '/' . $fileName;

			// Buat direktori jika belum ada
			if (!Storage::disk('public')->exists($directory)) {
				Storage::disk('public')->makeDirectory($directory);
			}

			// Path lengkap untuk file output
			$outputPath = Storage::disk('public')->path($path);

			// Langsung gunakan template TCPDF yang kompatibel
			try {

				// Render template TCPDF ke HTML
				$template = view('velzon.realisasi.skl-tcpdf', [
					'payload' => $payload,
				])->render();

				// Buat PDF dengan TCPDF
				$result = self::tryTCPDF($template, $outputPath, $payload, $path);

				// Jika gagal, coba dengan metode paling sederhana
				if ($result === false || !file_exists($outputPath)) {
					throw new \Exception('Template TCPDF gagal, mencoba metode paling sederhana');
				}
			} catch (\Exception $templateException) {
				Log::warning('Template TCPDF gagal, mencoba metode paling sederhana: ' . $templateException->getMessage());

				// Buat PDF dengan metode paling sederhana (tanpa template)
				$result = self::createMinimalPdf($outputPath, $payload, $path);

				if ($result === false || !file_exists($outputPath)) {
					throw new \Exception('Gagal membuat PDF dengan metode paling sederhana');
				}
			}

			// Jika berhasil dibuat
			if (file_exists($outputPath)) {
				// Log informasi file yang dibuat

				// Update database
				$update = Pengajuanskl2025::where('no_ijin', $payload['noIjin'])
					->update(['skl_auto' => $path]);

				if (!$update) {
					Log::error('Gagal menyimpan path SKL ke database', [
						'no_ijin' => $payload['noIjin'],
						'path' => $path
					]);

					return back()->withErrors(['error' => 'Gagal menyimpan path SKL.']);
				}

				return $path;
			} else {
				throw new \Exception('Gagal membuat file PDF SKL');
			}
		} catch (\Exception $e) {
			Log::error('Error generating SKL with TCPDF: ' . $e->getMessage(), [
				'exception' => $e,
				'trace' => $e->getTraceAsString()
			]);

			return back()->withErrors(['error' => 'Gagal membuat SKL: ' . $e->getMessage()]);
		}
	}
}
