<?php

namespace App\Filament\Admin\Resources\MasterSpatialResource\Pages;

use App\Filament\Admin\Resources\MasterSpatialResource;
use App\Models\MasterSpatial;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use DOMDocument;
use Filament\Actions;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class CreateMasterSpatial extends CreateRecord
{
    protected static string $resource = MasterSpatialResource::class;
	public function getHeading(): string
	{
		$thisTitle = 'Lokasi Baru';
        return $thisTitle;
	}
    protected static ?string $title = 'Lokasi Baru';

    public function getSubheading(): ?string
    {
		$subTitle = 'Menambahkan Lokasi baru.';
        return $subTitle;
    }

	public function form(Form $form): Form
    {
        return $form
            ->schema([
				Section::make('Uploader')
					->columnSpan(2)
					->schema([
						FileUpload::make('kml_url')
							->hiddenLabel()
							->required()
							->maxSize(2048)
							->deletable()
							->helperText('Maksimal 2MB, format KML')
							->reactive()
							->fetchFileInformation(true)
							->downloadable()
							->multiple()
							// ->panelLayout('grid')
							->disk('public')
							->preserveFilenames()
							->visibility('public')
							->directory(fn () => "uploads/kml")
							->rules([
								'file',
								'mimetypes:application/vnd.google-earth.kml+xml,application/xml,text/xml',
								'mimes:kml,xml',
								'max:2048'
							])
							->validationMessages([
								'mimetypes' => 'Hanya file KML yang diperbolehkan',
								'mimes' => 'Ekstensi file harus .kml atau .xml',
								'max' => 'Ukuran file maksimal 2MB',
							])
							->getUploadedFileNameForStorageUsing(
								fn (TemporaryUploadedFile $file): string => "uploads/kml/" . $file->getClientOriginalName()
							)
							->afterStateUpdated(fn ($state, $set) => self::processKmlFiles($set, $state)),
					]),

				Section::make('processed_data')
					->columnSpan(5)
					->schema([
						TableRepeater::make('InvalidFile')
							->reactive()
							->addable(false)
							->headers([
								Header::make('Kode'),
								Header::make('Petani'),
								Header::make('Luas'),
							])
							->schema([
								TextInput::make('ID_LAHAN'),
								TextInput::make('PETANI'),
								TextInput::make('LUAS_LAHAN'),
							]),

						TableRepeater::make('KmlList')
							->reactive()
							->addable(false)
							->headers([
								Header::make('Kode'),
								Header::make('Desa'),
								Header::make('URL'),
							])
							->schema([
								TextInput::make('kode_spatial'),
								TextInput::make('id_desa'),
								TextInput::make('kml_url'),
							]),

						TableRepeater::make('PoktanList')
							->reactive()
							->addable(false)
							->headers([
								Header::make('Kode'),
								Header::make('Nama'),
								Header::make('Desa'),
							])
							->schema([
								TextInput::make('kode_poktan'),
								TextInput::make('Poktan'),
								TextInput::make('id_desa'),
							]),

						TableRepeater::make('AnggotaList')
							->reactive()
							->addable(false)
							->headers([
								Header::make('NIK'),
								Header::make('Nama'),
								Header::make('Poktan'),
							])
							->schema([
								TextInput::make('nik'),
								TextInput::make('nama'),
								TextInput::make('kode_poktan'),
							])
					]),
            ])->columns(7);
    }

	public function mutateFormDataBeforeSave(array $data): array
	{
		if (isset($data['kml_url'])) {
			logger()->info('Data KML:', ['kml_url' => $data['kml_url']]);
		}
		return $data;
	}

	private static function processKmlFiles($set, $state)
	{
		$results = [];
		if (!$state) {
			Log::error("State kosong, tidak ada file yang diproses.");
			return $results;
		}

		if (is_array($state)) {
			$files = collect($state);
		} else {
			$files = [$state];
		}

		foreach ($files as $file) {
			if ($file instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile) {
				$xmlContent = $file->get();
			} else {
				$fullPath = storage_path("app/public/" . ltrim($file, '/'));
				if (!file_exists($fullPath)) {
					Log::error("File tidak ditemukan: " . $fullPath);
					continue;
				}
				$xmlContent = file_get_contents($fullPath);
			}

			$xml = simplexml_load_string($xmlContent);
			if (!$xml) {
				Log::error("Gagal membaca XML dari file.");
				continue;
			}

			$placemark = $xml->Document->Folder->Placemark ?? null;
			if (!$placemark) {
				Log::error("Placemark tidak ditemukan dalam file.");
				continue;
			}

			$extendedData = [];
			foreach ($placemark->ExtendedData->SchemaData->SimpleData as $simpleData) {
				$extendedData[(string) $simpleData['name']] = (string) $simpleData;
			}
			$idLahan = $extendedData['ID_LAHAN'] ?? null;
			$idDesa = $extendedData['ID_DESA'] ?? null;

			if ($idLahan) {
				$results[] = [
					'kode_spatial' => $idLahan,
					'id_desa' => $idDesa,
				];
			}
		}
		$set('KmlList', $results);
		return $results;
	}

}
