<?php

namespace App\Filament\Admin\Resources\RoleResource\Pages;

use App\Filament\Admin\Resources\RoleResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;

class ViewRole extends ViewRecord
{
    protected static string $resource = RoleResource::class;

	public function getTitle(): string|Htmlable
	{
		$roleName = $this->record ? $this->record->name : '##';
		return 'Peran ' . $roleName;
	}
    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
