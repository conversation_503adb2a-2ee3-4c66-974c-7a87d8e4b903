<?php

namespace App\Filament\Panel2025\Resources\PengajuanVerifikasiResource\Pages;

use App\Filament\Panel2025\Resources\PengajuanVerifikasiResource;
use App\Models\User;
use App\Models\VerificatorAssignment;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Actions;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class EditPengajuanVerifikasi extends EditRecord
{
    protected static string $resource = PengajuanVerifikasiResource::class;
    public static string | Alignment $formActionsAlignment = Alignment::Right;

    protected static ?string $title = 'Pengajuan Verifikasi';
    public function getHeading(): string
	{
        return 'Pengajuan Verifikasi';
	}

    public function getSubheading(): ?string
    {
        $noIjin = $this->record ? $this->record->no_ijin : '##';
        return 'PPRK No: ' . $noIjin;
    }

    protected function getHeaderActions(): array
    {
        return [
            // Actions\ViewAction::make(),
            // Actions\DeleteAction::make(),
        ];
    }

    public function form(Form $form): Form
	{
		return $form
		->schema([
            Section::make('Ringkasan Pengajuan')
                ->aside()
                ->description('Data Rekomendasi Import Produk Hortikultura')
                ->schema([
                    Placeholder::make('Nomor Pengajuan')
                        ->inlineLabel()
                        ->content(fn ($record) => $record->no_pengajuan),
                    Placeholder::make('Pelaku Usaha')
                        ->inlineLabel()
                        ->content(fn ($record) => $record->user->datauser->company_name),
                    Placeholder::make('NPWP')
                        ->inlineLabel()
                        ->label('NPWP')
                        ->content(fn ($record) => $record->npwp),
                    Placeholder::make('No. PPRK')
                        ->inlineLabel()
                        ->label('No. PPRK')
                        ->content(fn ($record) => $record->no_ijin),
                    Placeholder::make('Tanggal Pengajuan')
                        ->inlineLabel()
                        ->label('Tanggal Pengajuan')
                        ->content(fn ($record): string => $record->created_at->toFormattedDateString()),
                ]),

            Section::make('Berkas Pengajuan')
                ->aside()
                ->description('Kelengkapan Berkas Pengajuan')
                ->schema([
                    TableRepeater::make('berkas')
                        ->hiddenLabel()
                        ->addable(false)
                        ->deletable(false)
                        ->relationship(
                            fn () => Auth::user()->hasRole('Super Admin') ? 'berkaspengajuan' : 'userfiles'
                        )
                        ->headers([
                            Header::make('Berkas'),
                            Header::make('Tautan'),
                        ])
                        ->schema([
                            Placeholder::make('kind')
                                ->hiddenLabel()
                                ->content(fn ($record) => [
                                    'spvt' => 'Surat Pengajuan Verifikasi (Tanam)',
                                    'spvp' => 'Surat Pengajuan Verifikasi (Produksi)',
                                    'spskl' => 'Surat Pengajuan Penerbitan SKL',
                                    'sptjmt' => 'Surat Pernyataan Tangggung Jawab Mutlak (Periode Tanam)',
                                    'sptjmp' => 'Surat Pernyataan Tangggung Jawab Mutlak (Periode Produksi)',
                                    'rta' => 'Form Realisasi Tanam',
                                    'rpo' => 'Form Realisasi Produksi',
                                    'spht' => 'Statistik Pertanian Hortikultura (Periode Tanam)',
                                    'sphb' => 'Statistik Pertanian Hortikultura (Periode Produksi)',
                                    'spdst' => 'Surat Pengantar Dinas Telah Selesai Tanam',
                                    'spdsp' => 'Surat Pengantar Dinas Telah Selesai Produksi',
                                    'logbook' => 'Logbook (Tanam/Produksi)',
                                    'la' => 'Laporan Akhir',
                                    'skl' => 'Surat Keterangan Lunas',
                                    'ft' => 'Foto Tanam',
                                    'fp' => 'Foto Produksi',
                                    'pks' => 'Berkas PKS',
                                ][$record->kind] ?? $record->kind),

                            Placeholder::make('Tautan')
                                ->hiddenLabel()
                                ->content(fn ($record) => new HtmlString(
                                    '<a href="/' . e($record->file_url) . '" target="_blank" rel="noopener noreferrer">Buka File</a>'
                                )),
                        ])
                ]),

            Section::make('Penugasan Verifikator (Assignment)')
                ->aside()
                ->visible(fn () => Auth::user()->hasRole('Super Admin'))
                ->description('Penunjukkan Petugas Pelaksana Verifikasi untuk melaksanakan verifikasi.')
                ->schema([
                    Repeater::make('verificatorList')
                    ->relationship('assignments')
                    ->addable(fn () => Auth::user()->hasRole('Super Admin'))
                    ->deletable(fn () => Auth::user()->hasRole('Super Admin'))
                    ->addActionLabel('Tambah Verifikator')
                    ->itemLabel(fn (array $state): ?string =>
                        VerificatorAssignment::find($state['id'])?->user?->name ?? 'Nama Tidak Ditemukan'
                    )
                        ->grid(1)
                        ->columns(3)
                        ->hiddenLabel()
                        ->collapsed()
                        ->schema([
                            FileUpload::make('file')
                                ->hiddenLabel()
                                ->openable()
                                ->deletable(fn () => Auth::user()->hasRole('Super Admin'))
                                // ->required()
                                ->maxSize(2048)
                                ->columnSpan(1)
                                ->downloadable()
                                ->disk('public')
                                ->label('Berkas SK')
                                ->visibility('public')
                                ->panelAspectRatio('2:1')
                                ->fetchFileInformation(true)
                                ->helperText('Maksimal 2MB, format PDF')
                                ->rules([
                                    'file',
                                    'mimetypes:application/pdf',
                                    'mimes:pdf',
                                    'max:2048'
                                ])
                                ->validationMessages([
                                    'mimetypes' => 'Hanya file PDF yang diperbolehkan',
                                    'mimes' => 'Ekstensi file harus .pdf',
                                    'max' => 'Ukuran file maksimal 2MB'
                                ]),
                            Group::make()
                                ->columnSpan(2)
                                ->schema([

                                    Placeholder::make('Verifikator')
                                        ->inlineLabel()
                                        ->visible(fn () => Auth::user()->hasRole('verifikator'))
                                        ->content(fn ($record) => $record->user->name),
                                    Placeholder::make('no sk')
                                        ->inlineLabel()
                                        ->label('NO. SK')
                                        ->visible(fn () => Auth::user()->hasRole('verifikator'))
                                        ->content(fn ($record) => $record->no_sk),
                                    Placeholder::make('tgl sk')
                                        ->inlineLabel()
                                        ->label('Tgl. SK')
                                        ->visible(fn () => Auth::user()->hasRole('verifikator'))
                                        ->content(fn ($record) => $record->tgl_sk),

                                    Select::make('user_id')
                                        ->inlineLabel()
                                        ->visible(fn () => Auth::user()->hasRole('Super Admin'))
                                        ->label('Verifikator')
                                        ->preload()
                                        ->required()
                                        ->options([
                                            User::verifikator()
                                                ->get()
                                                ->pluck('name', 'id')
                                                ->toArray(),
                                        ]),
                                    TextInput::make('no_sk')
                                        ->inlineLabel()
                                        ->visible(fn () => Auth::user()->hasRole('Super Admin'))
                                        ->label('Nomor SK')
                                        ->required(),
                                    DatePicker::make('tgl_sk')
                                        ->inlineLabel()
                                        ->visible(fn () => Auth::user()->hasRole('Super Admin'))
                                        ->label('Tanggal SK')
                                        ->closeOnDateSelection()
                                        ->required(),
                                ])
                        ]),
                ])
        ]);
    }
}
