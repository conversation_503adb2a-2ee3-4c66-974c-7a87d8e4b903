<?php

namespace App\Filament\Panel2025\Resources;

use App\Filament\Panel2025\Resources\Commitment2025Resource\{Pages};
use App\Models\{Commitment2025};
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Auth;

class Commitment2025Resource extends Resource
{
	protected static ?string $model = Commitment2025::class;
	protected static ?string $pluralModelLabel = 'Daftar Komitmen';
	protected static ?int $navigationSort = 1;
	protected static ?string $navigationLabel = 'Daftar Komitmen';
	protected static ?string $navigationIcon = 'heroicon-o-bookmark';

	protected static ?string $currentUser = null;
	public static function boot()
	{
		parent::boot();

		static::$currentUser = Auth::user()?->id ?? 'Guest'; // atau ID

	}

	public static function getGloballySearchableAttributes(): array
	{
		return ['no_ijin', 'npwp','user.datauser.company_name'];
	}

	public static function getGlobalSearchResultDetails($record): array
	{
		return [
			'Nomor Ijin' => $record->no_ijin,
			'NPWP' => $record->npwp,
			'<PERSON><PERSON>' => $record->user->datauser->company_name,
		];
	}

	public static function form(Form $form): Form
	{
		return $form
			->schema([]);
	}

	public static function table(Table $table): Table
	{
		return $table
			->columns([
			]);
	}

	public static function getRelations(): array
	{
		return [
		];
	}

	public static function getPages(): array
	{
		return [
			'index' => Pages\ListCommitment2025s::route('/'),
			'create' => Pages\CreateCommitment2025::route('/create'),
			'view' => Pages\ViewCommitment2025::route('/{record}'),
			'viewlunas' => Pages\ViewlunasCommitment2025::route('/{record}/lunas'),
			'locationConfirm' => Pages\LocationConfirm::route('/{record}/locationconfirm'),
			'daftarPks' => Pages\DaftarPks::route('/{record}/daftarpks'),
			'pengajuanverifikasi' => Pages\PengajuanVerifikasi::route('/{record}/pengajuanverifikasi'),
			'picklocations' => Pages\PickLocations::route('/{record}/picklocations'),

			// 'edit' => Pages\EditCommitment2025::route('/{record}/edit'),
			// 'laporanKegiatan' => Pages\LaporanKegiatan::route('/{record}/laporankegiatan'),
		];
	}

	public static function shouldRegisterNavigation(): bool
	{
		if (Auth::user()->hasRole('importir')) {
			return true;
		}
		return false;
	}
}
