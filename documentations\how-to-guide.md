## Daftar Isi

1. [<PERSON><PERSON><PERSON><PERSON>](#pendahuluan)
2. [<PERSON><PERSON><PERSON>](#memulai-dengan-simethris)
   - [<PERSON><PERSON><PERSON> dan <PERSON>](#registrasi-dan-login)
   - [Memahami Antarmuka Pengguna](#memahami-antarmuka-pengguna)
   - [Panel Tahun](#panel-tahun)
3. [Panduan untuk Importir](#panduan-untuk-importir)
   - [Mendaftarkan Komitmen](#mendaftarkan-komitmen)
   - [Mengelola Data Kelompok Tani](#mengelola-data-kelompok-tani)
   - [Mengelola Data Anggota](#mengelola-data-anggota)
   - [Mengelola Data Spasial](#mengelola-data-spasial)
   - [Melaporkan Realisasi](#melaporkan-realisasi)
   - [Mengajukan Verifikasi](#mengajukan-verifikasi)
   - [Mengajukan SKL](#mengajukan-skl)
4. [Panduan untuk Verifikator](#panduan-untuk-verifikator)
   - [<PERSON><PERSON><PERSON>](#menerima-penugasan)
   - [Melakukan Verifikasi](#melakukan-verifikasi)
   - [Membuat <PERSON>rita Acara](#membuat-berita-acara)
5. [Panduan untuk Admin](#panduan-untuk-admin)
   - [Mengelola Pengguna](#mengelola-pengguna)
   - [Mengelola Master Data](#mengelola-master-data)
   - [Menugaskan Verifikator](#menugaskan-verifikator)
   - [Menerbitkan SKL](#menerbitkan-skl)
6. [Fitur Umum](#fitur-umum)
   - [Menggunakan Peta Spasial](#menggunakan-peta-spasial)
   - [Menggunakan Editor Gambar](#menggunakan-editor-gambar)
   - [Memverifikasi QR Code](#memverifikasi-qr-code)
7. [Pemecahan Masalah](#pemecahan-masalah)
   - [Masalah Umum dan Solusinya](#masalah-umum-dan-solusinya)
   - [Kontak Dukungan](#kontak-dukungan)

## Pendahuluan

Simethris (Sistem Informasi Monitoring dan Evaluasi Tanam Hortikultura dan Realisasi Impor Semusim) adalah aplikasi berbasis web yang dikembangkan untuk Kementerian Pertanian Republik Indonesia. Aplikasi ini bertujuan untuk memantau dan mengevaluasi kegiatan tanam hortikultura dan realisasi impor semusim, khususnya untuk komoditas bawang putih.

Panduan ini akan membantu Anda memahami cara menggunakan Simethris v4.0 sesuai dengan peran Anda dalam sistem, baik sebagai Importir, Verifikator, maupun Admin.

## Memulai dengan Simethris

### Registrasi dan Login

#### Untuk Importir

1. Importir tidak perlu melakukan registrasi manual. Akun akan dibuat secara otomatis berdasarkan data RIPH.
2. Untuk login pertama kali:
   - Buka halaman login Simethris di [https://simethris.hortikultura.pertanian.go.id](https://simethris.hortikultura.pertanian.go.id)
   - Masukkan email yang terdaftar pada data RIPH
   - Masukkan password yang telah diberikan atau gunakan NPWP sebagai password default
   - Klik tombol "Login"
3. Setelah login pertama kali, Anda akan diminta untuk mengganti password.

#### Untuk Verifikator dan Dinas

1. Buka halaman registrasi Simethris di [https://simethris.hortikultura.pertanian.go.id/register](https://simethris.hortikultura.pertanian.go.id/register)
2. Pilih peran "Verifikator" atau "Dinas Kabupaten"
3. Isi formulir registrasi dengan data yang benar:
   - Nama lengkap
   - Username (akan digunakan untuk login)
   - Email (pastikan email aktif)
   - Password (minimal 8 karakter)
4. Klik tombol "Register"
5. Tunggu persetujuan dari Admin sebelum dapat login

#### Login ke Sistem

1. Buka halaman login Simethris di [https://simethris.hortikultura.pertanian.go.id](https://simethris.hortikultura.pertanian.go.id)
2. Masukkan username atau email
3. Masukkan password
4. Klik tombol "Login"

### Memahami Antarmuka Pengguna

Setelah login, Anda akan melihat dashboard yang sesuai dengan peran Anda. Berikut adalah elemen-elemen umum pada antarmuka pengguna:

1. **Sidebar**: Berisi menu navigasi utama
2. **Header**: Berisi nama pengguna, notifikasi, dan tombol logout
3. **Dashboard**: Menampilkan ringkasan informasi dan statistik
4. **Breadcrumb**: Menunjukkan lokasi Anda dalam aplikasi
5. **Footer**: Berisi informasi versi aplikasi dan hak cipta

### Panel Tahun

Simethris v4.0 menggunakan sistem multi-panel berdasarkan tahun:

1. **Panel Admin** (`/admin`): Panel utama untuk administrasi sistem
2. **Panel 2024** (`/panel/2024`): Panel untuk data tahun 2024
3. **Panel 2025** (`/panel/2025`): Panel untuk data tahun 2025 dan seterusnya

Untuk berpindah antar panel, gunakan menu dropdown di sidebar atau header.

## Panduan untuk Importir

### Mendaftarkan Komitmen

1. Dari dashboard, pilih "Panel Realisasi" > "Komitmen"
2. Klik tombol "Tambah Komitmen" di pojok kanan atas
3. Isi formulir komitmen dengan data yang benar:
   - Nomor PPRK/RIPH
   - Tanggal PPRK/RIPH
   - Volume Import
   - Wajib Tanam
   - Wajib Produksi
4. Unggah dokumen pendukung:
   - Surat PPRK/RIPH (format PDF, maks. 2MB)
   - SPTJM (format PDF, maks. 2MB)
   - Form RIPH (format PDF, maks. 2MB)
5. Klik tombol "Simpan"

### Mengelola Data Kelompok Tani

1. Dari dashboard, pilih "Panel Realisasi" > "Kelompok Tani"
2. Untuk menambahkan kelompok tani baru:
   - Klik tombol "Tambah Kelompok Tani"
   - Isi formulir dengan data yang benar:
     - Nama Kelompok Tani
     - Nama Ketua
     - Nomor Kontak
     - Alamat
     - Provinsi, Kabupaten, Kecamatan, Desa (pilih secara berurutan)
   - Klik tombol "Simpan"
3. Untuk mengedit kelompok tani:
   - Klik tombol "Edit" pada baris kelompok tani yang ingin diedit
   - Ubah data yang diperlukan
   - Klik tombol "Simpan"
4. Untuk menghapus kelompok tani:
   - Klik tombol "Hapus" pada baris kelompok tani yang ingin dihapus
   - Konfirmasi penghapusan

### Mengelola Data Anggota

1. Dari dashboard, pilih "Panel Realisasi" > "Anggota Kelompok Tani"
2. Untuk menambahkan anggota baru:
   - Klik tombol "Tambah Anggota"
   - Pilih Kelompok Tani
   - Isi formulir dengan data yang benar:
     - Nama Anggota
     - NIK
     - Nomor Kontak
     - Luas Lahan (ha)
   - Klik tombol "Simpan"
3. Untuk mengedit anggota:
   - Klik tombol "Edit" pada baris anggota yang ingin diedit
   - Ubah data yang diperlukan
   - Klik tombol "Simpan"
4. Untuk menghapus anggota:
   - Klik tombol "Hapus" pada baris anggota yang ingin dihapus
   - Konfirmasi penghapusan

### Mengelola Data Spasial

1. Dari dashboard, pilih "Panel Realisasi" > "Data Spasial"
2. Untuk menambahkan data spasial baru:
   - Klik tombol "Tambah Data Spasial"
   - Pilih Kelompok Tani dan Anggota
   - Isi formulir dengan data yang benar:
     - Luas Lahan (ha)
     - Varietas Tanaman
     - Tanggal Tanam
     - Perkiraan Tanggal Panen
   - Gunakan peta untuk menandai lokasi:
     - Klik tombol "Tandai Lokasi" pada peta
     - Klik pada peta untuk menandai titik lokasi, atau
     - Gunakan tombol "Gambar Polygon" untuk menggambar area tanam
   - Klik tombol "Simpan"
3. Untuk mengedit data spasial:
   - Klik tombol "Edit" pada baris data spasial yang ingin diedit
   - Ubah data yang diperlukan
   - Klik tombol "Simpan"
4. Untuk menghapus data spasial:
   - Klik tombol "Hapus" pada baris data spasial yang ingin dihapus
   - Konfirmasi penghapusan

### Melaporkan Realisasi

1. Dari dashboard, pilih "Panel Realisasi" > "Laporan Realisasi"
2. Untuk menambahkan laporan realisasi baru:
   - Klik tombol "Tambah Laporan"
   - Pilih Komitmen
   - Isi formulir dengan data yang benar:
     - Tanggal Laporan
     - Luas Tanam Realisasi (ha)
     - Volume Produksi Realisasi (ton)
   - Unggah foto bukti kegiatan:
     - Foto Tanam (format JPG/PNG, maks. 2MB)
     - Foto Tumbuh (format JPG/PNG, maks. 2MB)
     - Foto Panen (format JPG/PNG, maks. 2MB)
   - Klik tombol "Simpan"
3. Untuk mengedit laporan realisasi:
   - Klik tombol "Edit" pada baris laporan yang ingin diedit
   - Ubah data yang diperlukan
   - Klik tombol "Simpan"

### Mengajukan Verifikasi

1. Dari dashboard, pilih "Panel Realisasi" > "Pengajuan Verifikasi"
2. Untuk mengajukan verifikasi tanam:
   - Klik tombol "Ajukan Verifikasi Tanam"
   - Pilih Komitmen
   - Pastikan semua data realisasi tanam sudah lengkap
   - Klik tombol "Ajukan"
3. Untuk mengajukan verifikasi produksi:
   - Klik tombol "Ajukan Verifikasi Produksi"
   - Pilih Komitmen
   - Pastikan semua data realisasi produksi sudah lengkap
   - Klik tombol "Ajukan"
4. Pantau status verifikasi di halaman "Pengajuan Verifikasi"

### Mengajukan SKL

1. Dari dashboard, pilih "Panel Realisasi" > "Pengajuan SKL"
2. Untuk mengajukan SKL:
   - Klik tombol "Ajukan SKL"
   - Pilih Komitmen
   - Pastikan verifikasi tanam dan produksi sudah disetujui
   - Klik tombol "Ajukan"
3. Pantau status pengajuan SKL di halaman "Pengajuan SKL"
4. Setelah SKL diterbitkan, Anda dapat mengunduhnya dari halaman "Pengajuan SKL"

## Panduan untuk Verifikator

### Menerima Penugasan

1. Dari dashboard, pilih "Panel Verifikasi" > "Penugasan"
2. Lihat daftar penugasan verifikasi yang diberikan kepada Anda
3. Klik tombol "Terima" untuk menerima penugasan
4. Setelah menerima penugasan, Anda dapat mulai melakukan verifikasi

### Melakukan Verifikasi

1. Dari dashboard, pilih "Panel Verifikasi" > "Verifikasi"
2. Pilih penugasan yang ingin diverifikasi
3. Periksa data realisasi:
   - Data Kelompok Tani
   - Data Anggota
   - Data Spasial
   - Laporan Realisasi
   - Foto Bukti Kegiatan
4. Lakukan verifikasi lapangan jika diperlukan
5. Isi formulir hasil verifikasi:
   - Status Verifikasi (Disetujui/Ditolak)
   - Catatan Verifikasi
   - Rekomendasi
6. Klik tombol "Simpan"

### Membuat Berita Acara

1. Dari dashboard, pilih "Panel Verifikasi" > "Berita Acara"
2. Pilih verifikasi yang ingin dibuatkan berita acara
3. Isi formulir berita acara:
   - Nomor Berita Acara
   - Tanggal Berita Acara
   - Hasil Verifikasi
   - Kesimpulan
4. Unggah dokumen pendukung jika ada
5. Klik tombol "Simpan"
6. Cetak berita acara dengan mengklik tombol "Cetak"

## Panduan untuk Admin

### Mengelola Pengguna

1. Dari dashboard, pilih "Admin" > "Pengguna"
2. Untuk menambahkan pengguna baru:
   - Klik tombol "Tambah Pengguna"
   - Isi formulir dengan data yang benar:
     - Nama
     - Email
     - Username
     - Password
     - Peran (Admin, Verifikator, Dinas, dll)
   - Klik tombol "Simpan"
3. Untuk mengedit pengguna:
   - Klik tombol "Edit" pada baris pengguna yang ingin diedit
   - Ubah data yang diperlukan
   - Klik tombol "Simpan"
4. Untuk menonaktifkan pengguna:
   - Klik tombol "Nonaktifkan" pada baris pengguna yang ingin dinonaktifkan
   - Konfirmasi penonaktifan

### Mengelola Master Data

1. Dari dashboard, pilih "Admin" > "Master Data"
2. Pilih jenis master data yang ingin dikelola:
   - Provinsi
   - Kabupaten
   - Kecamatan
   - Desa
3. Untuk menambahkan data baru:
   - Klik tombol "Tambah Data"
   - Isi formulir dengan data yang benar
   - Klik tombol "Simpan"
4. Untuk mengedit data:
   - Klik tombol "Edit" pada baris data yang ingin diedit
   - Ubah data yang diperlukan
   - Klik tombol "Simpan"
5. Untuk menghapus data:
   - Klik tombol "Hapus" pada baris data yang ingin dihapus
   - Konfirmasi penghapusan
6. Untuk sinkronisasi data wilayah:
   - Klik tombol "Sinkronisasi Data Wilayah"
   - Tunggu proses sinkronisasi selesai

### Menugaskan Verifikator

1. Dari dashboard, pilih "Admin" > "Pengajuan Verifikasi"
2. Pilih pengajuan verifikasi yang ingin ditugaskan
3. Klik tombol "Tugaskan Verifikator"
4. Pilih verifikator yang akan ditugaskan
5. Klik tombol "Simpan"
6. Verifikator yang ditugaskan akan menerima notifikasi

### Menerbitkan SKL

1. Dari dashboard, pilih "Admin" > "Pengajuan SKL"
2. Pilih pengajuan SKL yang ingin diterbitkan
3. Periksa kelengkapan data:
   - Verifikasi Tanam
   - Verifikasi Produksi
   - Berita Acara
4. Klik tombol "Terbitkan SKL"
5. Isi formulir SKL:
   - Nomor SKL
   - Tanggal SKL
   - Keterangan
6. Klik tombol "Simpan"
7. SKL akan diterbitkan dan dapat diunduh oleh importir

## Fitur Umum

### Menggunakan Peta Spasial

1. Buka halaman yang memiliki fitur peta spasial
2. Gunakan kontrol peta untuk navigasi:
   - Klik dan seret untuk menggeser peta
   - Scroll mouse untuk zoom in/out
   - Klik tombol "+" atau "-" untuk zoom in/out
3. Untuk menandai lokasi:
   - Klik tombol "Tandai Lokasi"
   - Klik pada peta untuk menempatkan marker
4. Untuk menggambar polygon:
   - Klik tombol "Gambar Polygon"
   - Klik pada peta untuk membuat titik-titik polygon
   - Klik titik awal untuk menutup polygon
5. Untuk mengedit polygon:
   - Klik polygon yang ingin diedit
   - Seret titik-titik polygon untuk mengubah bentuk
6. Untuk menghapus marker atau polygon:
   - Klik marker atau polygon yang ingin dihapus
   - Klik tombol "Hapus"

### Menggunakan Editor Gambar

1. Saat mengunggah gambar, Anda akan melihat opsi "Edit Gambar"
2. Klik tombol "Edit Gambar" untuk membuka editor
3. Gunakan tools yang tersedia:
   - Crop: Untuk memotong gambar
   - Rotate: Untuk memutar gambar
   - Flip: Untuk membalik gambar
   - Brightness/Contrast: Untuk mengatur kecerahan dan kontras
   - Filters: Untuk menerapkan filter pada gambar
4. Setelah selesai mengedit, klik tombol "Simpan"
5. Gambar yang sudah diedit akan digunakan untuk diunggah

### Memverifikasi QR Code

1. Untuk memverifikasi SKL menggunakan QR Code:
   - Buka aplikasi pemindai QR Code di smartphone
   - Pindai QR Code yang ada pada dokumen SKL
   - Anda akan diarahkan ke halaman verifikasi Simethris
2. Halaman verifikasi akan menampilkan informasi SKL:
   - Nomor SKL
   - Nama Perusahaan
   - Nomor PPRK/RIPH
   - Status Verifikasi
   - Tanggal Terbit
3. Jika QR Code valid, halaman akan menampilkan status "SKL Valid"
4. Jika QR Code tidak valid, halaman akan menampilkan pesan error

## Pemecahan Masalah

### Masalah Umum dan Solusinya

1. **Tidak dapat login**
   - Pastikan username/email dan password benar
   - Periksa apakah akun Anda sudah diaktifkan oleh admin
   - Coba reset password dengan mengklik "Lupa Password"

2. **Tidak dapat mengunggah file**
   - Pastikan ukuran file tidak melebihi batas maksimum (2MB)
   - Pastikan format file sesuai dengan yang diminta
   - Coba kompres file jika terlalu besar

3. **Peta spasial tidak muncul**
   - Pastikan browser Anda mendukung JavaScript
   - Periksa koneksi internet Anda
   - Coba refresh halaman

4. **Data lokasi tidak muncul di dropdown**
   - Pastikan Anda memilih lokasi secara berurutan (provinsi → kabupaten → kecamatan → desa)
   - Jika data tetap tidak muncul, hubungi admin untuk sinkronisasi data wilayah

5. **Error saat mengajukan verifikasi**
   - Pastikan semua data yang diperlukan sudah lengkap
   - Periksa apakah ada persyaratan yang belum terpenuhi
   - Coba lagi nanti atau hubungi admin

### Kontak Dukungan

Jika Anda mengalami masalah yang tidak dapat diselesaikan, silakan hubungi tim dukungan:

- Email: <EMAIL>
- Telepon: (021) XXXXXXXX
- Jam Kerja: Senin-Jumat, 08.00-16.00 WIB

---

Dokumen ini terakhir diperbarui pada: April 2024
