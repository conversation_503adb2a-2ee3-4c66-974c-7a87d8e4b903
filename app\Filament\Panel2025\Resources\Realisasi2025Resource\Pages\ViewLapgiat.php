<?php

namespace App\Filament\Panel2025\Resources\Realisasi2025Resource\Pages;

use App\Filament\Panel2025\Resources\Realisasi2025Resource;
use App\Models\Pks2025;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\{DatePicker, Fieldset, FileUpload, Group, Hidden, Placeholder, Radio, Repeater, Section, Select, Textarea, TextInput};
use Filament\Forms\Form;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class ViewLapgiat extends ViewRecord
{
    protected static string $resource = Realisasi2025Resource::class;
    protected static ?string $title = 'Data Realisasi Komitmen';
	public function getHeading(): string
	{
		return 'Realisasi Komitmen Tanam dan Produksi';
	}

	public function getSubheading(): ?string
	{
		$petak = $this->record->kode_spatial ? $this->record->kode_spatial : '##';
		$noIjin = $this->record ? $this->record->no_ijin : '##';
		return 'Petak: '. $petak.' / PPRK No: ' . $noIjin;
	}


	public function form(Form $form): Form
	{
		return $form
            ->schema(
				function () {
					$record = $this->getRecord();
					if (!$record) {
						return [];
					}
					$kodePoktan = Pks2025::where('no_ijin', $record->no_ijin)
						->where('status_dinas', 2)
						->pluck('kode_poktan')
						->unique()
						->toArray();
					if (!in_array($record->kode_poktan, $kodePoktan)) {
						return [
							Placeholder::make('Restriction')
								->hiddenLabel()
								->columnSpanFull()
								->extraAttributes(['class'=>'text-center uppercase'])
								->content(new HtmlString('Anda belum dapat melakukan pelaporan jika <span class="text-danger-500 font-bold uppercase">Belum Ada PKS yang disetujui</span>'))
						];
					}
				return [
					Section::make('Peta Lokasi')
						->aside()
						->schema([
							Placeholder::make('map')
								->hiddenLabel()
								->columnSpan([
									'sm' => '3',
									'md' => '2',
								])
								->extraAttributes(['class' => 'map-container'])
								->content(function ($record) {
									// Pastikan data spatial memiliki ID untuk identifikasi unik
									$spatialData = $record->spatial->toArray();
									// Tambahkan ID jika belum ada
									if (!isset($spatialData['id'])) {
										$spatialData['id'] = $record->id;
									}
									return view('components.map', ['data' => $spatialData]);
								}),
						]),

					Section::make('Data Peta')
						->aside()
						->schema([
							Group::make()
								->extraAttributes(['class'=>'mb-5'])
								->schema([
									Placeholder::make('kode_lahan')
										->label('Kode Spatial')
										->inlineLabel()
										->content(fn ($record) => new HtmlString('<span class="font-bold">' . $record->kode_spatial . '</span>')),

									Placeholder::make('Luas Lahan')
										->inlineLabel()
										->content(fn ($record)=>number_format($record->luas_lahan,0,',','.').' m2'),

									Placeholder::make('Nama Petani')
										->inlineLabel()
										->content(fn ($record)=>$record->anggota->nama_petani),

									Placeholder::make('NIK Petani')
										->label('NIK')
										->inlineLabel()
										->content(fn ($record)=>$record->anggota->ktp_petani),

									Placeholder::make('Kelompok Tani')
										->inlineLabel()
										->content(fn ($record)=>$record->poktan->nama_kelompok),
								]),
							Group::make()
								->extraAttributes(['class'=>'mb-5'])
								->schema([
									Placeholder::make('Wilayah')
										->inlineLabel()
										->content(fn ($record)=>$record->spatial->kabupaten->nama_kab . ' - ' .$record->spatial->provinsi->nama),

									Placeholder::make('Latitude')
										->inlineLabel()
										->content(fn ($record)=>$record->spatial->latitude . ' LU'),

									Placeholder::make('Longitude')
										->inlineLabel()
										->content(fn ($record)=>$record->spatial->longitude . ' BT'),
								]),

							Group::make()
								->extraAttributes(['class'=>'mb-5'])
								->schema([
									Placeholder::make('Unduh Peta')
										->inlineLabel()
										->content(function ($record) {
											if ($record->spatial && $record->spatial->kml_url) {
												// Gunakan asset() untuk mendapatkan URL yang benar
												$url = asset('storage/' . $record->spatial->kml_url);
												return new HtmlString('<a href="'.$url.'" rel="noreferer nofollow" download><span class="font-bold text-info-500">'.$record->kode_spatial.'</span></a>');
											}
											return new HtmlString('<span class="text-gray-500">File tidak tersedia</span>');
										}),
								]),
							// Placeholder::make('vt_status')
							// 	->hiddenLabel()
							// 	->content(fn ($get) => view('components.status-badge-verifikasi', ['status' => $get('vt_status')])),
						]),

					Section::make('Riwayat Kegiatan')
						->aside()
						->schema([
							Repeater::make('Kegiatan di Lahan')
								->hiddenLabel()
								->relationship('detailrealisasi')
								->reorderable(false)
								->collapsible()
								->collapsed()
								->addable(false)
								->deletable(false)
								->itemLabel(fn (array $state): ?string => match ($state['jenis_keg'] ?? null) {
									'lahan' => 'Persiapan Lahan',
									'benih' => 'Persiapan Benih',
									'mulsa' => 'Pemasangan Mulsa',
									'tanam' => 'Pertanaman',
									'pupuk' => 'Pemupukan',
									'panen' => 'Panen/Produksi',
									'distribusi' => 'Distribusi Hasil',
									'opt' => 'Pengendalian OPT',
									default => '',
								})
								->columns([
									'sm' => 1,
									'md' => 3,
								])
								->schema([
									Select::make('jenis_keg')
										->label('Jenis Kegiatan')
										->inlineLabel()
										->columnSpanFull()
										->reactive()
										->options([
											'Persiapan' => [
												'lahan' => 'Persiapan Lahan',
												'benih' => 'Persiapan Benih',
												'mulsa' => 'Pemasangan Mulsa',
											],
											'tanam' => 'Pertanaman',
											'pupuk' => 'Pemupukan',
											'Produksi' => [
												'panen' => 'Panen',
												'distribusi' => 'Distribusi Hasil'
											],
											'opt' => 'Pengendalian OPT'
										])
										->afterStateUpdated(function ($set, $state){
											$set('value', null);
											if($state === 'lahan'){
												return $set('desc_keg', 'Persiapan Lahan');
											}
											if($state === 'benih'){
												return $set('desc_keg', 'Persiapan Benih');
											}
											if($state === 'mulsa'){
												return $set('desc_keg', 'Pemasangan Mulsa');
											}
											if($state === 'tanam'){
												return $set('desc_keg', 'Kegiatan Pertanaman');
											}
											if($state === 'pupuk'){
												return $set('desc_keg', 'Kegiatan Pemupukan');
											}
											if($state === 'panen'){
												return $set('desc_keg', 'Kegiatan Panen-Produksi');
											}
											if($state === 'distribusi'){
												return $set('desc_keg', 'Distribusi Hasil');
											}
											if($state === 'opt'){
												return $set('desc_keg', 'Pengendalian OPT');
											}
											return 'Tidak ada';
										}),
									Group::make()
										->columnSpanFull()
										->columns(3)
										->schema([
											FileUpload::make('file_url')
												->openable()
												->downloadable()
												->disk('public')
												->previewable(true)
												->visibility('public')
												->label('Bukti Foto/Berkas Kegiatan')
												->imagePreviewHeight('250')
												->panelAspectRatio('1:1')
												->fetchFileInformation(true),

											Fieldset::make('Data')
												->columnSpan(2)
												->columns(1)
												->schema([
													DatePicker::make('tgl_keg')
														->inlineLabel()
														->label('Tanggal Kegiatan'),

													TextInput::make('value')
														->inlineLabel()
														->label(function ($get) {
															switch ($get('jenis_keg')) {
																case 'benih':
																	return 'Volume Benih';
																case 'mulsa':
																	return 'Pemasangan Mulsa';
																case 'tanam':
																	return 'Luas Tanam';
																case 'panen':
																	return 'Produksi';
																default:
																	return '';
															}
														})
														->visible(function ($get) {
															switch ($get('jenis_keg')) {
																case 'benih':
																	return true;
																case 'mulsa':
																	return true;
																case 'tanam':
																	return true;
																case 'panen':
																	return true;
																default:
																	return false;
															}
														})
														->suffix(function ($get) {
															switch ($get('jenis_keg')) {
																case 'mulsa':
																	return 'roll';
																case 'tanam':
																	return 'm2';
																default:
																	return 'kg';
															}
														}),

													Group::make()
														->visible(function ($get) {
															switch ($get('jenis_keg')) {
																case 'distribusi':
																	return true;
																default:
																	return false;
															}
														})
														->schema([
															TextInput::make('dist_benih')
																->label('Disimpan')
																->inlineLabel()
																->suffix('kg'),
															TextInput::make('dist_jual')
																->label('Dijual')
																->inlineLabel()
																->suffix('kg')
														]),

													Group::make()
														->visible(function ($get) {
															switch ($get('jenis_keg')) {
																case 'pupuk':
																	return true;
																default:
																	return false;
															}
														})
														->schema([
															TextInput::make('organik')
																->inlineLabel()
																->label('Pupuk Organik')
																->suffix('kg'),
															TextInput::make('dolomit')
																->inlineLabel()
																->label('Kapur Dolomit')
																->suffix('kg'),
															TextInput::make('npk')
																->inlineLabel()
																->label('Pupuk NPK')
																->suffix('kg'),
															TextInput::make('za')
																->inlineLabel()
																->label('Pupuk ZA')
																->suffix('kg'),
														]),
													Textarea::make('keg_note')
														->label('Catatan Kegiatan')
														->autosize()
												])
										])
								]),
						]),
				];
			}
		);
	}
}
