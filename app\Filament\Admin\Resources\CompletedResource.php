<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\CompletedResource\Pages;
use App\Models\Completed;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class CompletedResource extends Resource
{
    protected static ?string $model = Completed::class;
    protected static ?string $title = 'Daftar SKL';
	public static function getNavigationGroup(): ?string
	{
		return 'SKL';
	}
    public static function getPluralModelLabel(): string
    {
        return 'Daftar SKL';
    }

    protected static ?string $pluralModelLabel = 'Daftar SKL';

    protected static ?int $navigationSort = -1;
    protected static ?string $navigationLabel = 'Daftar SKL';
    protected static ?string $navigationIcon = 'icon-skl';

	public static function shouldRegisterNavigation(): bool
	{
		$user = Auth::user();

		if ($user->hasAnyRole(['Super Admin', 'admin', 'direktur'])) {
			return true;
		}

		return $user->hasRole('importir') && $user->completed->isNotEmpty();
	}

	public static function getGloballySearchableAttributes(): array
	{
		return ['no_skl', 'no_ijin', 'npwp', 'datauser.company_name'];
	}

	public static function getGlobalSearchResultDetails(Model $record): array
	{
		return [
			'SKL' => $record->no_skl,
			'No. RIPH' => $record->no_ijin,
			'NPWP' => $record->npwp,
			'Perusahaan' => $record->datauser->company_name,
		];
	}

    public static function form(Form $form): Form
    {
        return $form
			->columns(2)
            ->schema([
				Fieldset::make()
					->columns(1)
					->visible(fn ($record) => $record->periodetahun === '2025')
					->columnSpan(1)
					->schema([
						FileUpload::make('skl_upload')
							->openable()
							->hiddenLabel()
							->deletable(false)
							->panelAspectRatio('1:1')
					]),
				Fieldset::make()
					->columns(1)
					->columnSpan(fn ($record) => $record->periodetahun === '2025' ? 1 : 2)
					->schema([
						TextInput::make('no_ijin')
							->inlineLabel()
							->label('No. PPRK'),
						TextInput::make('periodetahun')
							->inlineLabel()
							->label('Periode'),
						TextInput::make('Pelaku Usaha')
							->inlineLabel()
							->formatStateUsing(fn ($record)=>$record->datauser->company_name),
						TextInput::make('npwp')
							->inlineLabel()
							->label('NPWP')
							->formatStateUsing(fn ($record)=>$record->datauser->company_name),
						DatePicker::make('published_date')
							->label('Tanggal Terbit')
							->inlineLabel(),
						TextInput::make('luas_tanam')
							->label('Luas Realisasi')
							->inlineLabel()
							->suffix(function ($record){
								if($record->periodetahun === '2025')
								{
									return 'm2';
								}
								return 'ha';
							})
							->numeric(),
						TextInput::make('volume')
							->label('Volume Realisasi')
							->inlineLabel()
							->suffix(function ($record){
								if($record->periodetahun === '2025')
								{
									return 'kg';
								}
								return 'ton';
							})
							->numeric(),
						Placeholder::make('url')
							->label('Berkas SKL')
							->hidden(fn ($record) => $record->periodetahun === '2025')
							->inlineLabel()
							->extraAttributes(['class'=>'text-info-500'])
							->content(fn ($record) => new HtmlString('<a href="'.$record->url.'" class="font-bold" target="_blank" rel="nofollow noreferer">Lihat/Unduh</a>')),
					]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
			->defaultGroup('periodetahun')
			->groupingSettingsHidden()
			->deferLoading() //async load
			->groups([
				Group::make('periodetahun')
					->collapsible()->titlePrefixedWithLabel(false),
			])
            ->columns([
                TextColumn::make('no_skl')
					->label('Nomor SKL')
                    ->searchable(),
                TextColumn::make('periodetahun')
					->label('Periode')
                    ->searchable(),
                TextColumn::make('no_ijin')
					->label('No. RIPH/PPRK')
                    ->searchable(),
                TextColumn::make('datauser.company_name')
					->label('Pelaku Usaha')
                    ->searchable(),
                TextColumn::make('published_date')
					->label('Tanggal Terbit')
                    ->date()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('periodetahun')
                    ->label('Periode Tahun')
                    ->options(function () {
                        // Ambil nilai unik dari kolom periodetahun
                        return Completed::query()
                            ->whereNotNull('periodetahun') // Filter out null values
                            ->where('periodetahun', '!=', '') // Filter out empty strings
                            ->distinct()
                            ->orderBy('periodetahun', 'desc') // Urutkan dari terbaru
                            ->pluck('periodetahun')
                            ->filter() // Remove any null/empty values from the collection
                            ->mapWithKeys(function ($periodetahun) {
                                return [$periodetahun => $periodetahun];
                            })
                            ->toArray();
                    })
                    ->placeholder('Semua Periode')
            ])
            ->actions([
                ViewAction::make()->modalHeading('Data SKL')->hiddenLabel()->icon('icon-skl')->color('success'),
                // EditAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    // DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCompleteds::route('/'),
            // 'view' => Pages\ViewCompleted::route('/{record}'),
            // 'create' => Pages\CreateCompleted::route('/create'),
            // 'edit' => Pages\EditCompleted::route('/{record}/edit'),
        ];
    }
}
