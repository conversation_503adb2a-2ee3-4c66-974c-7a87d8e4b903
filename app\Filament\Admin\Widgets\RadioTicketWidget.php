<?php

namespace App\Filament\Admin\Widgets;

use Filament\Forms\Components\Radio;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Widgets\Widget;

class RadioTicketWidget extends Widget implements HasForms
{
	use InteractsWithForms;
	public string $mode = 'list';
    protected static string $view = 'filament.admin.widgets.radio-ticket-widget';

	public function form(Form $form): Form
	{
		return $form
			->schema([
 			Radio::make('mode')
				->hiddenLabel()
                ->options([
                    'list' => 'Daftar Tiket',
                    'form' => 'Buka Tiket baru',
                ])
                ->default('list')
                ->reactive(),
			]);
	}
}
