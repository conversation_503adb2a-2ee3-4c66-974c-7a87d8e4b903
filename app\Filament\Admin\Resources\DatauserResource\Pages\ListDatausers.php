<?php

namespace App\Filament\Admin\Resources\DatauserResource\Pages;

use App\Filament\Admin\Resources\DatauserResource;
use App\Models\DataUser;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class ListDatausers extends ListRecords
{
    protected static string $resource = DatauserResource::class;

    protected function getHeaderActions(): array
    {
        $model = static::getModel();
        $hasRecord = $model::where('user_id', auth()->id())->exists();
        if ($hasRecord) {
            return [];
        }
        return [
            Actions\CreateAction::make()->label('Pengguna Baru')->icon('heroicon-o-user-plus')->hidden(),
        ];
    }

    protected function getFormActions(): array
	{
		$visible = $this->data['visible'];
		if($visible === 'visible'){
			return [
                $this->getCreateFormAction(),
                $this->getCancelFormAction()
			];
		}else{
			return [];
		}
	}

	protected function getTableQuery(): Builder
	{
		$user = Auth::user();
		return $user->hasRole('importir') 
			? DataUser::query()->where('user_id', auth()->id())
			: DataUser::query();
	}
}
