<?php

namespace App\Filament\Admin\Widgets;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class NewUser extends Widget
{
    protected static ?int $sort = 0;

    protected static bool $isLazy = false;

    /**
     * @var view-string
     */
    protected static string $view = 'filament.admin.resources.warning';
	public static function canView(): bool
	{
		$user = Auth::user();
	
		return $user->hasRole('importir') && is_null($user->datauser);
	}
	
}
