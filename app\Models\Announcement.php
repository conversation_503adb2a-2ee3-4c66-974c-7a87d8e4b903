<?php

namespace App\Models;

use App\Observers\AnnouncementObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Spatie\Permission\Models\Role;

#[ObservedBy([AnnouncementObserver::class])]
class Announcement extends Model
{
    protected $fillable = ['role_id','title', 'content', 'is_active', 'published_at','read_by', 'priority'];

    protected $casts = [
        'is_active' => 'boolean',
        'published_at' => 'datetime',
		'read_by' => 'array',
    ];

	public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

	public function isReadBy(): bool
    {
        return in_array(Auth::id(), $this->read_by ?? []);
    }

	public function markAsRead(): void
    {
        $user = Auth::user();
		if ($user->hasAnyRole(['admin', 'Super Admin'])) {
			return;
		}

		$readBy = $this->read_by ?? [];

		if (!in_array($user->id, $readBy)) {
			$readBy[] = $user->id;
			$this->update(['read_by' => $readBy]);
		}
    }
}
