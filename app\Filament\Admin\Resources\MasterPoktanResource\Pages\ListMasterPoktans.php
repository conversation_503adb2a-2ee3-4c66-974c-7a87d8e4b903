<?php

namespace App\Filament\Admin\Resources\MasterPoktanResource\Pages;

use App\Filament\Admin\Resources\MasterPoktanResource;
use App\Models\MasterPoktan;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class ListMasterPoktans extends ListRecords
{
    protected static string $resource = MasterPoktanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
	
	protected function getTableQuery(): Builder
	{
		$user = Auth::user();
		return $user->hasRole('dinas') 
			? MasterPoktan::query()->where('kabupaten_id', Auth::user()->dataadmin->kabupaten_id)
			: MasterPoktan::query();
	}
}
