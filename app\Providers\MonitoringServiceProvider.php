<?php

namespace App\Providers;

use App\Console\Commands\CleanGuestSessions;
use App\Http\Middleware\LogMonitoringAccess;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use Illuminate\Console\Scheduling\Schedule;

class MonitoringServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register middleware
        $this->app['router']->aliasMiddleware('log-monitoring', LogMonitoringAccess::class);
        
        // Register commands
        $this->commands([
            CleanGuestSessions::class,
        ]);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Apply middleware to all routes
        $this->app['router']->pushMiddlewareToGroup('web', LogMonitoringAccess::class);
        
        // Schedule commands
        $this->app->booted(function () {
            $schedule = $this->app->make(Schedule::class);
            
            // Jalankan pembersihan session guest setiap hari pada jam 1 pagi
            $schedule->command('sessions:clean-guests --days=1')
                ->dailyAt('01:00')
                ->appendOutputTo(storage_path('logs/sessions-cleanup.log'));
        });
    }
}
