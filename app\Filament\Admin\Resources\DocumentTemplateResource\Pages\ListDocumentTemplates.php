<?php

namespace App\Filament\Admin\Resources\DocumentTemplateResource\Pages;

use App\Filament\Admin\Resources\DocumentTemplateResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Facades\Auth;

class ListDocumentTemplates extends ListRecords
{
    protected static string $resource = DocumentTemplateResource::class;

    protected function getHeaderActions(): array
    {
        if(Auth::user()->hasAnyRole(['admin', 'Super Admin'])){
            return [
                Actions\CreateAction::make()->label('Unggah Template')->icon('icon-cloud-arrow-up-fill')->modalHeading('Unggah Template Dokumen'),
            ];
        }
        return [];
    }
}
