<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\MasterPenangkar2024;
use App\Models\User;

class MasterPenangkar2024Policy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any MasterPenangkar2024');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, MasterPenangkar2024 $masterpenangkar2024): bool
    {
        return $user->checkPermissionTo('view MasterPenangkar2024');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create MasterPenangkar2024');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, MasterPenangkar2024 $masterpenangkar2024): bool
    {
        return $user->checkPermissionTo('update MasterPenangkar2024');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, MasterPenangkar2024 $masterpenangkar2024): bool
    {
        return $user->checkPermissionTo('delete MasterPenangkar2024');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any MasterPenangkar2024');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, MasterPenangkar2024 $masterpenangkar2024): bool
    {
        return $user->checkPermissionTo('restore MasterPenangkar2024');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any MasterPenangkar2024');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, MasterPenangkar2024 $masterpenangkar2024): bool
    {
        return $user->checkPermissionTo('replicate MasterPenangkar2024');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder MasterPenangkar2024');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, MasterPenangkar2024 $masterpenangkar2024): bool
    {
        return $user->checkPermissionTo('force-delete MasterPenangkar2024');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any MasterPenangkar2024');
    }
}
