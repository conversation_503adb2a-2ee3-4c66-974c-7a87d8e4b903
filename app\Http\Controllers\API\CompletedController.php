<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\SKLResources;
use App\Models\Completed;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CompletedController extends Controller
{
	public function getSkls()
	{
		if (Auth::user()->hasRole('importir')) {
			$completeds = Completed::all();
		} else {
			$completeds = Completed::withoutGlobalScopes(['npwp'])->get();
		}
		return response()->json($completeds);
	}

	public function getSKL(Request $request)
	{
		$about = [
			'Surat Keterangan Lunas Wajib Tanam Simethris 4',
		];

		try {
			// Validate incoming request
			$validated = $request->validate([
				'noIjin' => 'nullable|string',
			]);
			$no_riph = $validated['noIjin'] ?? null;

			// Format 'noIjin' (if provided) to match the desired pattern
			$ijin = null;
			if ($no_riph) {
				$ijin = Str::substr($no_riph, 0, 4) . '/' . Str::substr($no_riph, 4, 2) . '.' . Str::substr($no_riph, 6, 3) . '/' .
					Str::substr($no_riph, 9, 1) . '/' . Str::substr($no_riph, 10, 2) . '/' . Str::substr($no_riph, 12, 4);
			}

			// Start building the query
			$query = Completed::select(
				'id',
				'no_skl',
				'periodetahun',
				'no_ijin',
				'npwp',
				'published_date',
				'luas_tanam',
				'volume',
				'status',
				'url',
				'created_at'
			);

			if (!is_null($ijin)) {
				$query->where('no_ijin', $ijin);
			}
			$completedRecords = $query->get();

			// Cek apakah hasil query kosong
			if ($completedRecords->isEmpty()) {
				return response()->json([
					'success' => false,
					'Status' => 'NOT_FOUND',
					'message' => 'Data SKL dengan nomor RIPH tersebut tidak ditemukan',
					'Tentang' => $about,
					'data' => []
				], 200);
			}

			// Jika ada data, lanjutkan seperti biasa
			$sklResources = new SKLResources($completedRecords);

			return response()->json([
				'success' => true,
				'Status' => 'SUCCESS',
				'Tentang' => $about,
				'data' => $sklResources,
			], 200);
		} catch (\Exception $e) {
			// Error handling
			return response()->json([
				'success' => false,
				'error' => $e->getMessage(),
				'message' => 'Failed to fetch skl data.',
				'Status' => 'FAIL',
				'Tentang' => $about,
				'data' => [],
			], 500);
		}
	}
}
