<?php

namespace App\Filament\Pages\Auth;

use Filament\Pages\SimplePage;
use Illuminate\Contracts\Support\Htmlable;
use Filament\Actions\Action;
use Filament\Facades\Filament;

class RegisterError extends SimplePage
{
    protected static string $view = 'filament.pages.auth.register-error';

    public ?string $errorMessage = null;
    public ?string $errorDetail = null;
    public ?string $errorTrace = null;

    public function mount(?string $errorMessage = null, ?string $errorDetail = null, ?string $errorTrace = null): void
    {
        $this->errorMessage = $errorMessage ?? session('error_message');
        $this->errorDetail = $errorDetail ?? session('error_detail');
        $this->errorTrace = $errorTrace ?? session('error_trace');

        if (!$this->errorMessage) {
            $this->redirect(Filament::getLoginUrl());
            return;
        }
    }

    public function getTitle(): string | Htmlable
    {
        return 'Registrasi Gagal';
    }

    public function getHeading(): string | Htmlable
    {
        return 'Registrasi Gagal';
    }

    protected function getHeaderActions(): array
    {
        return [
            $this->getLoginAction(),
            $this->getRegisterAction(),
        ];
    }

    protected function getLoginAction(): Action
    {
        return Action::make('login')
            ->label('Masuk')
            ->url(Filament::getLoginUrl())
            ->color('primary');
    }

    protected function getRegisterAction(): Action
    {
        return Action::make('register')
            ->label('Coba Daftar Lagi')
            ->url(Filament::getRegistrationUrl())
            ->color('gray');
    }
}
