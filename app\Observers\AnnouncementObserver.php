<?php

namespace App\Observers;

use App\Models\Announcement;
use App\Models\User;
use Filament\Notifications\Notification;

class AnnouncementObserver
{
    /**
     * Handle the Announcement "created" event.
     */
    public function created(Announcement $announcement): void
    {
        $users = User::whereHas('roles', function ($query) use ($announcement) {
			$query->where('name', $announcement->role->name);
		})->get();
		
		foreach ($users as $user) {
			Notification::make()
				->title('Pengumuman Baru')
				->body("Administrator telah membuat pengumuman baru bersifat {$announcement->priority}")
				->sendToDatabase($user);
		}
    }

    /**
     * Handle the Announcement "updated" event.
     */
    public function updated(Announcement $announcement): void
    {
        //
    }

    /**
     * Handle the Announcement "deleted" event.
     */
    public function deleted(Announcement $announcement): void
    {
        //
    }

    /**
     * Handle the Announcement "restored" event.
     */
    public function restored(Announcement $announcement): void
    {
        //
    }

    /**
     * Handle the Announcement "force deleted" event.
     */
    public function forceDeleted(Announcement $announcement): void
    {
        //
    }
}
