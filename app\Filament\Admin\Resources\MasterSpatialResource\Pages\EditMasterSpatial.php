<?php

namespace App\Filament\Admin\Resources\MasterSpatialResource\Pages;

use App\Filament\Admin\Resources\MasterSpatialResource;
use Filament\Actions;
use Filament\Forms\Components\{Fieldset, Group, Placeholder, Section, Textarea, Toggle};
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class EditMasterSpatial extends EditRecord
{
    protected static string $resource = MasterSpatialResource::class;
	public static string | Alignment $formActionsAlignment = Alignment::Right;

	public function getTitle(): string | Htmlable
	{
		return 'Perubahan Data Lahan';
	}
	
	public function getSubheading(): ?string
	{
		return 'untuk lokasi lahan: ' . $this->record?->kode_spatial;
	}

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

	public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Data Spatial')
                    ->description('Data Spatial Pemetaan Lahan')
					->aside()
                    ->schema([
						Placeholder::make('map')
							->label('')
							->content(function ($record) {
								// Ambil API key dari database atau env
								$mapKey = \App\Models\ForeignApi::where('status', 1)->select('key')->first()?->key ?? env('GOOGLE_MAPS_API_KEY');

								// Siapkan data untuk view
								$polygon = $record->polygon;
								if ($polygon) {
									// Coba parse polygon jika berbentuk JSON string
									$polygonData = json_decode($polygon, true);

									// Jika gagal parse (hasil null dan bukan karena string 'null'), gunakan string asli
									if ($polygonData === null && $polygon !== 'null') {
										$polygonData = $polygon;
									}
								} else {
									$polygonData = null;
								}

								$viewData = [
									'mapKey' => $mapKey,
									'latitude' => $record->latitude,
									'longitude' => $record->longitude,
									'kodeSpatial' => $record->kode_spatial,
									'polygon' => $polygonData,
								];

								// Render view
								return new HtmlString(
									view('filament.admin.resources.master-spatial-resource.pages.spatial-map', $viewData)->render()
								);
							}),
                        Group::make()
                            ->schema([
                                Placeholder::make('latitude')
                                    ->inlineLabel()
                                    ->content(fn ($record)=>$record->latitude),
                                Placeholder::make('longitude')
                                    ->inlineLabel()
                                    ->content(fn ($record)=>$record->longitude),
                                Placeholder::make('polygon')
                                    ->inlineLabel()
									->extraAttributes(['class' => 'text-xs truncate'])
                                    ->content(fn ($record) => new HtmlString(
                                        '<p>' . $record->polygon . '</p>'
                                    )),
                                Placeholder::make('altitude')
                                    ->inlineLabel()
                                    ->content(fn ($record)=>$record->altitude),
                                Placeholder::make('luas_lahan')
                                    ->inlineLabel()
                                    ->content(fn ($record)=>number_format($record->luas_lahan,0,',','.') . ' m2'),
								
                                Placeholder::make('kml_url')
                                    ->label('Unduh Berkas KML')
                                    ->inlineLabel()
                                    ->content(fn ($record) => new HtmlString(
                                        '<a class="font-bold text-info-500" href="' . asset($record->kml_url) . '" target="_blank" rel="nofollow noreferrer" download>Klik untuk mengunduh</a>'
									)),
                            ])
                    ]),

                Section::make('Data Pengelola')
                    ->aside()
                    ->description('Petani pemilik/pengelola/penggarap lahan ini.')
                    ->schema([
                        Placeholder::make('kode_poktan')
                            ->label('Kelompok Tani')
                            ->inlineLabel()
                            ->content(fn ($record)=>$record->masterpoktan?->nama_kelompok),
                        Placeholder::make('nama_petani')
                            ->inlineLabel()
                            ->label('Nama Petani')
                            ->content(fn ($record)=>$record->nama_petani),
                        Placeholder::make('ktp_petani')
                            ->inlineLabel()
                            ->label('NIK')
                            ->content(fn ($record)=>$record->ktp_petani),
                    ]),

                Section::make('Wilayah')
                    ->aside()
                    ->description('Domisili Administratif')
                    ->schema([
                        Placeholder::make('provinsi')
                            ->inlineLabel()
                            ->content(fn ($record)=>$record->provinsi->nama),
                        Placeholder::make('kabupaten')
                            ->inlineLabel()
                            ->content(fn ($record)=>$record->kabupaten->nama_kab),
                        Placeholder::make('kecamatan')
                            ->inlineLabel()
                            ->content(fn ($record)=>$record->kecamatan->nama_kecamatan),
                        Placeholder::make('desa')
                            ->inlineLabel()
                            ->content(fn ($record)=>$record->desa->nama_desa)
					]),
				Section::make('Perubahan Data')
					->aside()
					->description('Data yang akan diubah')
					->schema([
						Toggle::make('is_active')
							->inline()
							->inlineLabel(true)
							->label(fn ($record) => $record->is_active === 1 ? 'Status Lahan: Aktif' : 'Status Lahan: Tidak Aktif'),
						Textarea::make('catatan')
							->inlineLabel()
							->rows(3)
							->placeholder('Catatan tambahan tentang lahan ini...')
					])
            ]);
    }
}
