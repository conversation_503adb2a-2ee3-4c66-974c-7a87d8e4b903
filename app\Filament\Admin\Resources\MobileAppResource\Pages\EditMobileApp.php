<?php

namespace App\Filament\Admin\Resources\MobileAppResource\Pages;

use App\Filament\Admin\Resources\MobileAppResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Storage;

class EditMobileApp extends EditRecord
{
    protected static string $resource = MobileAppResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Actions\Action::make('exportMetadata')
                ->label('Ekspor Metadata')
                ->icon('heroicon-o-document-arrow-down')
                ->action(function () {
                    $this->exportMetadata();
                })
                ->requiresConfirmation()
                ->modalHeading('Ekspor Metadata ke JSON')
                ->modalDescription('Sistem akan mengekspor metadata aplikasi ini ke file JSON di direktori yang sama dengan file APK. Lanjutkan?')
                ->modalSubmitActionLabel('Ekspor'),
        ];
    }

    protected function exportMetadata()
    {
        $app = $this->record;
        $fileName = pathinfo($app->file_name, PATHINFO_FILENAME) . '.json';
        $filePath = 'mobile_apps/' . $fileName;

        // Siapkan data untuk ekspor
        $metadata = [
            'description' => $app->description,
            'min_android_version' => $app->min_android_version,
            'min_ram' => $app->min_ram,
            'min_storage' => $app->min_storage,
            'required_features' => $app->required_features,
            'recommended_specs' => $app->recommended_specs,
            'release_notes' => $app->release_notes,
        ];

        // Hapus nilai null
        $metadata = array_filter($metadata, function ($value) {
            return $value !== null;
        });

        // Simpan ke file JSON
        try {
            Storage::disk('public')->put($filePath, json_encode($metadata, JSON_PRETTY_PRINT));
            Notification::make()
                ->title('Metadata berhasil diekspor')
                ->body('File disimpan sebagai ' . $fileName)
                ->success()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Gagal mengekspor metadata')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
}
