<?php

namespace App\Filament\Panel2025\Resources\UserfileResource\RelationManagers;

use App\Models\Realisasi2025;
use App\Models\Userfile;
use Filament\Forms;
use Filament\Forms\Components\{Actions, DatePicker, FileUpload, Grid, Group, Placeholder, Select, TextInput};
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Filament\Tables\Actions\{Action as TableAction, BulkActionGroup, CreateAction,EditAction,DeleteAction, DeleteBulkAction};
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Endroid\QrCode\Color\Color;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use TCPDF;


class UserfilesRelationManager extends RelationManager
{
    protected static string $relationship = 'userfiles';
    protected static ?string $title = 'Kelengkapan Dokumen';
    public function getHeading(): string
	{
        return 'Daftar Data PPRK';
	}
    public static string | Alignment $formActionsAlignment = Alignment::Right;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('kind')
                    ->label('Jenis Berkas')
                    ->required()
                    ->reactive()
                    ->options([
                        'spvt' => 'Surat Pengajuan Verifikasi (Tanam)',
                        'spvp' => 'Surat Pengajuan Verifikasi (Produksi)',
                        'spskl' => 'Surat Pengajuan Penerbitan SKL',
                        'sptjmt' => 'Surat Pernyataan Tangggung Jawab Mutlak (Periode Tanam)',
                        'sptjmp' => 'Surat Pernyataan Tangggung Jawab Mutlak (Periode Produksi)',
                        'rta' => 'Form Realisasi Tanam',
                        'rpo' => 'Form Realisasi Produksi',
                        'spht' => 'Statistik Pertanian Hortikultura (Periode Tanam)',
                        'sphb' => 'Statistik Pertanian Hortikultura (Periode Produksi)',
                        'spdst' => 'Surat Pengantar Dinas Telah Selesai Tanam',
                        'spdsp' => 'Surat Pengantar Dinas Telah Selesai Produksi',
                        'logbook' => 'Logbook (Tanam/Produksi)',
                        'la' => 'Laporan Akhir',
                        // 'skl' => 'Surat Keterangan Lunas',
                        // 'ft' => 'Foto Tanam',
                        // 'fp' => 'Foto Produksi',
                        // 'pks' => 'Berkas PKS',
                    ]),

                    Actions::make([
                        Action::make('Logbook')
                            ->label('Generate Logbook')
                            ->icon('icon-journal-bookmark-fill')
                            ->tooltip('klik untuk men-generate Logbook dengan')
                            ->color('success')
                            ->requiresConfirmation()
                            ->action(function () {
                                $record = $this->getOwnerRecord();
                                $result = self::generateLogbookTanamWithTCPDF($record);

                                // Jika gagal, tampilkan notifikasi error
                                if ($result instanceof \Illuminate\Http\RedirectResponse && $result->getSession()->get('errors')) {
                                    Notification::make()
                                        ->title('Gagal membuat logbook dengan TCPDF.')
                                        ->danger()
                                        ->body($result->getSession()->get('errors')->first())
                                        ->send();
                                } else {
                                    Notification::make()
                                        ->title('Sukses!')
                                        ->success()
                                        ->body('Berkas logbook berhasil dibuat dengan TCPDF.')
                                        ->send();
                                }
                            })
                            ->visible(fn ($get) => in_array($get('kind'), ['logbook'])),
                        Action::make('laporanAkhir')
                            ->label('Generate Laporan Akhir')
                            ->icon('icon-journal-bookmark-fill')
                            ->tooltip('klik untuk men-generate Laporan Akhir secara otomatis')
                            ->color('info')
                            ->requiresConfirmation()
                            ->action(function () {
                                $record = $this->getOwnerRecord();
                                $result = self::generateLaporanAkhir($record);
                                if ($result instanceof \Illuminate\Http\RedirectResponse && $result->getSession()->get('errors')) {
                                    Notification::make()
                                        ->title('Gagal membuat Laporan Akhir.')
                                        ->danger()
                                        ->body($result->getSession()->get('errors')->first())
                                        ->send();
                                } else {
                                    Notification::make()
                                        ->title('Sukses!')
                                        ->success()
                                        ->body('Berkas laporan akhir berhasil dibuat.')
                                        ->send();
                                }
                            })
                            ->visible(fn ($get) => in_array($get('kind'), ['la'])),
                    ])->fullWidth(),
					FileUpload::make('file_url')
						->visible(fn ($get) => !in_array($get('kind'), ['logbook', 'la']))
						->reactive()
						->openable()
						->required()
						->moveFiles()
						->maxSize(2048)
						->downloadable()
						->disk('public')
						->previewable(true)
						->visibility('public')
						->label('Dokumen')
						->imagePreviewHeight('150')
						->panelAspectRatio('2:1')
						->fetchFileInformation(true)
						->helperText('Maksimal 2MB, format portable document')
						->directory(function ($record) {
							$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $this->getOwnerRecord()->npwp);
							$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $this->getOwnerRecord()->no_ijin);
							return "uploads/{$cleanNpwp}/{$cleanNoIjin}/dokumen";
						})
						->rules([
							'file',
							'mimetypes:application/pdf',
							'mimes:pdf'
						])
						->validationMessages([
							'mimetypes' => 'Hanya file PDF yang diperbolehkan',
							'mimes' => 'Ekstensi file harus .pdf',
						])
						->getUploadedFileNameForStorageUsing(
							function (TemporaryUploadedFile $file, $get, $record): string {
								$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $this->getOwnerRecord()->npwp);
								$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $this->getOwnerRecord()->no_ijin);
								$kind = $get('kind');
								// Format nama file: [ID]_[NPWP]_[NOIJIN].[ext]
								return $kind .'_'. $cleanNpwp . '_' . $cleanNoIjin . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
							}
						),
            ])->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('kind')
            ->columns([
                TextColumn::make('kind')
                    ->label('Dokumen')
                    ->formatStateUsing(fn ($state) => self::getDocumentLabel($state))
                    ->tooltip(fn ($state) => self::getDocumentLabel($state)),

                    TextColumn::make('created_at')
                        ->label('Tanggal dibuat')
                        ->since()
                        ->dateTooltip(),
                    TextColumn::make('updated_at')
                        ->label('Tanggal diubah')
                        ->since()
                        ->dateTooltip(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                CreateAction::make()
                    ->label('Unggah Berkas')
                    ->modalHeading('Unggah Berkas')
                    ->modalWidth('md')
                    ->tooltip('Unggah berkas/dokumen kegiatan selain Logbook.')
                    ->icon('icon-cloud-upload')
                    ->using(function (array $data) {
                        $kind = $data['kind'] ?? null;
                        $fileUrl = $data['file_url'] ?? null;
                        $noIjin = $this->getOwnerRecord()->no_ijin;
                        $fileName = '';
                        if ($fileUrl) {
                            $parts = explode('/', $fileUrl);
                            $fileName = end($parts);
                        }
                        $userfile = new Userfile();
                        $userfile->kind = $kind;
                        $userfile->no_ijin = $noIjin;
                        $userfile->file_code = $fileName;
                        $userfile->file_url = $fileUrl;
                        $userfile->save();



                        return $userfile;
                    }),
            ])
            ->actions([
				TableAction::make('viewPdf')
                    ->label('Lihat Berkas')
                    ->hiddenLabel()
                    ->tooltip('Lihat berkas')
                    ->color('info')
                    ->url(function ($record) {
                        // Ekstrak npwp, noIjin, dan filename dari file_url
                        $parts = explode('/', $record->file_url);
                        if (count($parts) >= 4) {
                            $npwp = $parts[1];
                            $noIjin = $parts[2];
                            $filename = end($parts);
                            $url = '/uploads/' . $npwp . '/' . $noIjin . '/dokumen/' . $filename;



                            return $url;
                        }
                        $url = '/storage/' . $record->file_url;



                        return $url;
                    }, true)
					->extraAttributes(['rel' => 'nofollor noreferer'])
                    ->icon('icon-binoculars-fill'),
                EditAction::make()
					->hiddenLabel()
					->modalHeading('Ubah Berkas')
					->modalWidth('md')
					->tooltip('Ubah berkas')
                    ->using(function ($record, array $data) {
                        // Update record
                        $record->update($data);
                        return $record;
                    }),
                DeleteAction::make()
                    ->hiddenLabel()
                    ->tooltip('Hapus berkas')
                    ->using(function ($record) {
                        // Hapus record
                        $record->delete();

                        return true;
                    }),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    // DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getDocumentKinds(): array
    {
        return [
            'spvt' => 'Surat Pengajuan Verifikasi (Tanam)',
            'spvp' => 'Surat Pengajuan Verifikasi (Produksi)',
            'spskl' => 'Surat Pengajuan Penerbitan SKL',
            'sptjmt' => 'Surat Pernyataan Tangggung Jawab Mutlak (Periode Tanam)',
            'sptjmp' => 'Surat Pernyataan Tangggung Jawab Mutlak (Periode Produksi)',
            'rta' => 'Form Realisasi Tanam',
            'rpo' => 'Form Realisasi Produksi',
            'spht' => 'Statistik Pertanian Hortikultura (Periode Tanam)',
            'sphb' => 'Statistik Pertanian Hortikultura (Periode Produksi)',
            'spdst' => 'Surat Pengantar Dinas Telah Selesai Tanam',
            'spdsp' => 'Surat Pengantar Dinas Telah Selesai Produksi',
            'logbook' => 'Logbook (Tanam/Produksi)',
            'la' => 'Laporan Akhir',
            'skl' => 'Surat Keterangan Lunas',
            'ft' => 'Foto Tanam',
            'fp' => 'Foto Produksi',
            'pks' => 'Berkas PKS',
        ];
    }

    public static function getDocumentLabel(?string $key): string
    {
        return self::getDocumentKinds()[$key] ?? $key;
    }
    protected static function generateQrCode($noIjin, $npwp)
    {
        try {
            $secretKey = config('app.key');
            $sanitizedNpwp = preg_replace('/[^A-Za-z0-9]/', '', $npwp);
            $sanitizedNoIjin = preg_replace('/[^A-Za-z0-9]/', '', $noIjin);
            $reversedNoIjin = strrev($sanitizedNoIjin);
            $data = "{$reversedNoIjin}|{$sanitizedNpwp}";
            $hash = hash_hmac('sha256', $data, $secretKey);
            $shortHash = substr($hash, 0, 8);

            $maskedNos = "{$reversedNoIjin}{$shortHash}";
            $url = url("/verify-logbook?mask={$maskedNos}&hash={$hash}");
            try {
                $qrCode = new QrCode(
                    data: $url,
                    size: 200,
                    margin: 0,
                    errorCorrectionLevel: ErrorCorrectionLevel::High,
                    foregroundColor: new Color(0, 0, 0),
                    backgroundColor: new Color(255, 255, 255),
                    encoding: new Encoding('UTF-8')
                );

                // Tulis ke PNG
                $writer = new PngWriter();
                $result = $writer->write($qrCode);

                // Konversi ke data URI
                $dataUri = $result->getDataUri();

                // Log sukses
                Log::info('QR Code berhasil dibuat dengan Endroid', [
                    'no_ijin' => $noIjin,
                    'url' => $url
                ]);

                return $dataUri;
            } catch (\Exception $endroidException) {
                // Log error Endroid
                Log::warning('Failed to generate QR code with Endroid: ' . $endroidException->getMessage(), [
                    'exception' => $endroidException,
                    'no_ijin' => $noIjin
                ]);
            }
        } catch (\Exception $e) {
            // Log error dan kembalikan string kosong
            Log::error('Failed to generate QR code: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
                'no_ijin' => $noIjin,
                'npwp' => $npwp
            ]);

            return '';
        }
    }

    public static function logbookPayload($record)
    {
        $data = $record;
        $company = $data->nama;
        $npwp = $data->npwp;
        $noIjin = $data->no_ijin;
        $userfiles = Userfile::where('no_ijin', $data->no_ijin)->get() ?? new Userfile();
        $spatials = Realisasi2025::where('no_ijin', $data->no_ijin)
			->get() ?? new Userfile();
		// dd($spatials);
		// $luasTanam = $record
        $mapkey = env('GOOGLE_MAPS_API_KEY');

        // Generate QR Code
        $qrCode = self::generateQrCode($noIjin, $npwp);

        $payload = [
            'company' => $company,
            'npwp'=>$npwp,
            'noIjin'=>$noIjin,
            'periode'=>$data->periodetahun,
            'dokumen'=>$userfiles,
            'spatials'=>$spatials,
            'mapkey' => $mapkey,
            'qrCode' => $qrCode,
        ];
        return $payload;
    }

    /**
     * Mendapatkan pesan error validasi
     */
    protected function getErrorMessages()
    {
        $errors = session()->get('errors');
        if (!$errors) {
            return [];
        }

        $messages = [];
        foreach ($errors->getBags() as $bag => $error) {
            $messages[$bag] = $error->all();
        }

        return $messages;
    }

    /**
     * Mencoba membuat PDF dengan TCPDF
     */
    protected static function tryTCPDF($template, $outputPath, $payload, $path)
    {
        try {
            Log::info('Mencoba membuat PDF dengan TCPDF', [
                'no_ijin' => $payload['noIjin'],
                'file_path' => $path
            ]);

            // Buat instance TCPDF
            $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, 'A4', true, 'UTF-8', false);

            // Set informasi dokumen
            $pdf->SetCreator('SIMETHRIS');
            $pdf->SetAuthor('Kementerian Pertanian');
            $pdf->SetTitle('Logbook Tanam ' . $payload['noIjin']);
            $pdf->SetSubject('Logbook Tanam');

            // Hapus header dan footer default
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(false);

            // Set margin
            $pdf->SetMargins(15, 15, 15);
            $pdf->SetAutoPageBreak(TRUE, 15);

            // Set font
            $pdf->SetFont('helvetica', '', 10);

            // Tambahkan halaman
            $pdf->AddPage();

            // Tambahkan konten HTML
            $pdf->writeHTML($template, true, false, true, false, '');

            // Simpan PDF ke file
            $pdf->Output($outputPath, 'F');

            // Periksa apakah file PDF berhasil dibuat
            if (!file_exists($outputPath)) {
                Log::warning('TCPDF gagal membuat file PDF', [
                    'no_ijin' => $payload['noIjin'],
                    'file_path' => $path
                ]);
                throw new \Exception('Gagal membuat file PDF dengan TCPDF');
            }

            Log::info('PDF berhasil dibuat dengan TCPDF', [
                'no_ijin' => $payload['noIjin'],
                'file_path' => $path
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Error pada TCPDF: ' . $e->getMessage(), [
                'no_ijin' => $payload['noIjin'],
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    protected static function generateLogbookTanamWithTCPDF($record)
    {
        try {
            // Persiapkan data untuk template
            $payload = self::logbookPayload($record);

            // Cek jika payload kosong atau gagal dibuat
            if (empty($payload) || !isset($payload['spatials']) || !isset($payload['npwp']) || !isset($payload['noIjin'])) {
                Log::error('Validasi payload logbook gagal', [
                    'payload' => $payload,
                    'record_id' => $record->id ?? null,
                    'no_ijin' => $record->no_ijin ?? null
                ]);
                return back()->withErrors(['error' => 'Gagal menghasilkan logbook. Data tidak valid.']);
            }

            // Render template ke HTML
            $template = view('velzon.realisasi.logbook-tcpdf', ['payload' => $payload])->render();

            // Persiapkan path file
            $npwp = str_replace(['.', '-'], '', $payload['npwp']);
            $noIjin = str_replace(['.', '-', '/'], '', $payload['noIjin']);
            $timestamp = time();
            $fileName = 'logbook_tanam_' . $noIjin . '_' . $timestamp . '.pdf';
            $directory = 'uploads/' . $npwp . '/' . $noIjin. '/dokumen';
            $path = $directory . '/' . $fileName;

            // Buat direktori jika belum ada
            if (!Storage::disk('public')->exists($directory)) {
                Storage::disk('public')->makeDirectory($directory);
            }

            // Path lengkap untuk file output
            $outputPath = Storage::disk('public')->path($path);

            // Buat instance TCPDF
            $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, 'A4', true, 'UTF-8', false);

            // Set informasi dokumen
            $pdf->SetCreator('SIMETHRIS');
            $pdf->SetAuthor('Kementerian Pertanian');
            $pdf->SetTitle('Logbook Tanam ' . $payload['noIjin']);
            $pdf->SetSubject('Logbook Tanam');

            // Hapus header dan footer default
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(false);

            // Set margin
            $pdf->SetMargins(15, 15, 15);
            $pdf->SetAutoPageBreak(TRUE, 15);

            // Set font
            $pdf->SetFont('helvetica', '', 10);

            // Tambahkan halaman
            $pdf->AddPage();

            // Tambahkan konten HTML
            $pdf->writeHTML($template, true, false, true, false, '');

            // Simpan PDF ke file
            $pdf->Output($outputPath, 'F');

            // Periksa apakah file berhasil dibuat
            if (file_exists($outputPath)) {
                // Log informasi file yang dibuat
                Log::info('File PDF berhasil dibuat dan disimpan.', [
                    'no_ijin' => $payload['noIjin'],
                    'file_path' => $path,
                    'file_size' => filesize($outputPath) . ' bytes',
                    'url' => '/uploads/' . $npwp . '/' . $noIjin . '/dokumen/' . $fileName,
                    'timestamp' => $timestamp
                ]);

                // Simpan file ke database
                Userfile::updateOrCreate(
                    [
                        'no_ijin' => $payload['noIjin'],
                        'kind' => 'logbook',
                    ],
                    [
                        'file_code' => $fileName,
                        'file_url' => $path,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]
                );

                return redirect()->back()->with('success', 'Berkas logbook berhasil dibuat.');
            } else {
                throw new \Exception('Gagal membuat file PDF dengan TCPDF');
            }
        } catch (\Exception $e) {
            Log::error('Error generating logbook with TCPDF: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            return back()->withErrors(['error' => 'Gagal membuat logbook dengan TCPDF: ' . $e->getMessage()]);
        }
    }

    public static function generateLogbookTanam($record)
    {
        $payload = self::logbookPayload($record);

        // Cek jika payload kosong atau gagal dibuat
        if (empty($payload) || !isset($payload['spatials']) || !isset($payload['npwp']) || !isset($payload['noIjin'])) {
            Log::error('Validasi payload logbook gagal', [
                'payload' => $payload,
                'record_id' => $record->id ?? null,
                'no_ijin' => $record->no_ijin ?? null
            ]);
            return back()->withErrors(['error' => 'Gagal menghasilkan logbook. Data tidak valid.']);
        }

        $npwp = str_replace(['.', '-'], '', $payload['npwp']);
        $noIjin = str_replace(['.', '-', '/'], '', $payload['noIjin']);
        $timestamp = time();
        $fileName = 'logbook_tanam_' . $noIjin . '_' . $timestamp . '.pdf';
        $directory = 'uploads/' . $npwp . '/' . $noIjin. '/dokumen';
        $path = $directory . '/' . $fileName;

        $template = view('velzon.realisasi.logbook', [
            'payload' => $payload,
        ])->render();

        // Buat footer untuk PDF
        $footerText = 'Autogenerated by Simethris v4.0 @2025. Dicetak pada ' . now()->format('d M Y H:i:s');

        // Tambahkan footer ke HTML template
        $template .= '<div style="margin: 0 auto; text-align: center; margin-top: 20px;">
            <span style="font-size: 10px; font-family: Courier New, Courier, monospace; color: #666;">
                ' . $footerText . '
            </span>
        </div>';

        // Pastikan direktori ada sebelum menyimpan file
        if (!Storage::disk('public')->exists($directory)) {
            Storage::disk('public')->makeDirectory($directory);
        }

        try {
            // Simpan HTML ke file sementara
            $tempHtmlPath = Storage::disk('public')->path($directory . '/temp_logbook.html');
            file_put_contents($tempHtmlPath, $template);

            // Buat direktori output jika belum ada
            $outputDir = dirname(Storage::disk('public')->path($path));
            if (!file_exists($outputDir)) {
                mkdir($outputDir, 0755, true);
            }

            $outputPath = Storage::disk('public')->path($path);
            $success = false;

            // Coba dengan TCPDF terlebih dahulu
            try {
                $success = self::tryTCPDF($template, $outputPath, $payload, $path);
            } catch (\Exception $tcpdfException) {
                Log::error('Error pada TCPDF: ' . $tcpdfException->getMessage(), [
                    'no_ijin' => $payload['noIjin'],
                    'exception' => $tcpdfException->getMessage(),
                    'trace' => $tcpdfException->getTraceAsString()
                ]);
            }

            // Jika TCPDF gagal, coba dengan DOMPDF
            if (!$success) {
                try {
                    Log::info('Mencoba membuat PDF dengan DOMPDF', [
                        'no_ijin' => $payload['noIjin'],
                        'file_path' => $path
                    ]);

                    $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadHTML($template);
                    $pdf->setPaper('legal', 'portrait');
                    $pdf->setOptions([
                        'isHtml5ParserEnabled' => true,
                        'isRemoteEnabled' => true,
                        'isPhpEnabled' => true,  // Aktifkan PHP untuk memproses kode PHP dalam template
                        'defaultFont' => 'sans-serif',
                        'dpi' => 150,
                        'defaultPaperSize' => 'legal',
                        'margin_top' => 4,
                        'margin_right' => 0,
                        'margin_bottom' => 4,
                        'margin_left' => 0,
                        'enable_remote' => true,
                        'enable_css_float' => true,
                        'enable_javascript' => false,
                        'font_height_ratio' => 1.1,
                        'chroot' => public_path(),  // Penting untuk akses file lokal
                        'tempDir' => sys_get_temp_dir()  // Gunakan direktori temp sistem
                    ]);

                    Storage::disk('public')->put($path, $pdf->output());

                    if (file_exists(Storage::disk('public')->path($path))) {
                        $success = true;
                        Log::info('PDF berhasil dibuat dengan DOMPDF', [
                            'no_ijin' => $payload['noIjin'],
                            'file_path' => $path
                        ]);
                    }
                } catch (\Exception $pdfException) {
                    Log::error('Error pada DOMPDF: ' . $pdfException->getMessage(), [
                        'no_ijin' => $payload['noIjin'],
                        'exception' => $pdfException->getMessage(),
                        'trace' => $pdfException->getTraceAsString()
                    ]);
                }
            }

            // Hapus file HTML sementara
            if (file_exists($tempHtmlPath)) {
                unlink($tempHtmlPath);
            }

            // Jika semua metode gagal
            if (!$success && !file_exists(Storage::disk('public')->path($path))) {
                throw new \Exception('Gagal membuat file PDF dengan semua metode yang tersedia');
            }

            // Log informasi file yang dibuat
            Log::info('File PDF berhasil dibuat dan disimpan', [
                'no_ijin' => $payload['noIjin'],
                'file_path' => $path,
                'file_size' => filesize(Storage::disk('public')->path($path)) . ' bytes',
                'url' => '/uploads/' . $npwp . '/' . $noIjin . '/dokumen/' . $fileName,
                'timestamp' => $timestamp
            ]);

            Userfile::updateOrCreate(
                [
                    'no_ijin' => $payload['noIjin'],
                    'kind' => 'logbook',
                ],
                [
                    'file_code' => $fileName,
                    'file_url' => $path,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );
        } catch (\Exception $e) {
            Log::error('Gagal membuat logbook: ' . $e->getMessage(), [
                'no_ijin' => $payload['noIjin'],
                'npwp' => $payload['npwp'],
                'file' => $fileName,
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            return back()->withErrors(['error' => 'Gagal membuat PDF logbook.']);
        }
        return redirect()->back()->with('success', 'Berkas berhasil dibuat.');
    }

    public static function generateLaporanAkhir($record)
    {
        try {
            // Validasi data
            if (empty($record) || !isset($record->no_ijin) || !isset($record->npwp)) {
                Log::error('Validasi data laporan akhir gagal', [
                    'record_id' => $record->id ?? null,
                    'no_ijin' => $record->no_ijin ?? null
                ]);
                return back()->withErrors(['error' => 'Gagal menghasilkan laporan akhir. Data tidak valid.']);
            }

            // Persiapan data untuk laporan akhir
            $npwp = str_replace(['.', '-'], '', $record->npwp);
            $noIjin = str_replace(['.', '-', '/'], '', $record->no_ijin);
            $timestamp = time();
            $fileName = 'laporan_akhir_' . $noIjin . '_' . $timestamp . '.pdf';
            $directory = 'uploads/' . $npwp . '/' . $noIjin. '/dokumen';
            $path = $directory . '/' . $fileName;

            // Pastikan direktori ada
            if (!Storage::disk('public')->exists($directory)) {
                Storage::disk('public')->makeDirectory($directory);
            }

            // Log proses pembuatan PDF dimulai
            Log::info('Memulai proses pembuatan PDF untuk laporan akhir', [
                'no_ijin' => $record->no_ijin,
                'file_path' => $path
            ]);

            // Untuk sementara, kita hanya log bahwa fitur belum tersedia
            Log::info('Fitur laporan akhir belum diimplementasikan sepenuhnya', [
                'no_ijin' => $record->no_ijin
            ]);

            // Contoh implementasi dasar menggunakan template sederhana
            // Ini hanya template dasar, perlu dikembangkan lebih lanjut
            $template = '<html><body>';
            $template .= '<h1 style="text-align: center;">Laporan Akhir</h1>';
            $template .= '<h2 style="text-align: center;">' . $record->nama . '</h2>';
            $template .= '<p style="text-align: center;">NPWP: ' . $record->npwp . '</p>';
            $template .= '<p style="text-align: center;">No. Ijin: ' . $record->no_ijin . '</p>';
            $template .= '<p style="text-align: center;">Periode: ' . ($record->periodetahun ?? 'N/A') . '</p>';
            $template .= '<p style="text-align: center;">Laporan ini masih dalam pengembangan.</p>';
            $template .= '</body></html>';

            // Tambahkan footer
            $footerText = 'Autogenerated by Simethris v4.0 @2025. Dicetak pada ' . now()->format('d M Y H:i:s');
            $template .= '<div style="margin: 0 auto; text-align: center; margin-top: 20px;">';
            $template .= '<span style="font-size: 10px; font-family: Courier New, Courier, monospace; color: #666;">';
            $template .= $footerText;
            $template .= '</span></div>';

            try {
                // Simpan HTML ke file sementara
                $tempHtmlPath = Storage::disk('public')->path($directory . '/temp_laporan.html');
                file_put_contents($tempHtmlPath, $template);

                // Buat direktori output jika belum ada
                $outputDir = dirname(Storage::disk('public')->path($path));
                if (!file_exists($outputDir)) {
                    mkdir($outputDir, 0755, true);
                }

                $outputPath = Storage::disk('public')->path($path);
                $success = false;

                // Coba dengan TCPDF terlebih dahulu
                try {
                    $payload = ['noIjin' => $record->no_ijin, 'npwp' => $record->npwp];
                    $success = self::tryTCPDF($template, $outputPath, $payload, $path);
                } catch (\Exception $tcpdfException) {
                    Log::error('Error pada TCPDF (laporan akhir): ' . $tcpdfException->getMessage(), [
                        'no_ijin' => $record->no_ijin,
                        'exception' => $tcpdfException->getMessage(),
                        'trace' => $tcpdfException->getTraceAsString()
                    ]);
                }

                // Jika TCPDF gagal, coba dengan DOMPDF
                if (!$success) {
                    try {
                        Log::info('Mencoba membuat PDF laporan akhir dengan DOMPDF', [
                            'no_ijin' => $record->no_ijin,
                            'file_path' => $path
                        ]);

                        $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadHTML($template);
                        $pdf->setPaper('legal', 'portrait');
                        $pdf->setOptions([
                            'isHtml5ParserEnabled' => true,
                            'isRemoteEnabled' => true,
                            'isPhpEnabled' => true,  // Aktifkan PHP untuk memproses kode PHP dalam template
                            'defaultFont' => 'sans-serif',
                            'dpi' => 150,
                            'defaultPaperSize' => 'legal',
                            'margin_top' => 4,
                            'margin_right' => 0,
                            'margin_bottom' => 4,
                            'margin_left' => 0,
                            'enable_remote' => true,
                            'enable_css_float' => true,
                            'enable_javascript' => false,
                            'font_height_ratio' => 1.1,
                            'chroot' => public_path(),  // Penting untuk akses file lokal
                            'tempDir' => sys_get_temp_dir()  // Gunakan direktori temp sistem
                        ]);

                        Storage::disk('public')->put($path, $pdf->output());

                        if (file_exists(Storage::disk('public')->path($path))) {
                            $success = true;
                            Log::info('PDF laporan akhir berhasil dibuat dengan DOMPDF', [
                                'no_ijin' => $record->no_ijin,
                                'file_path' => $path
                            ]);
                        }
                    } catch (\Exception $pdfException) {
                        Log::error('Error pada DOMPDF (laporan akhir): ' . $pdfException->getMessage(), [
                            'no_ijin' => $record->no_ijin,
                            'exception' => $pdfException->getMessage(),
                            'trace' => $pdfException->getTraceAsString()
                        ]);
                    }
                }

                // Hapus file HTML sementara
                if (file_exists($tempHtmlPath)) {
                    unlink($tempHtmlPath);
                }

                // Jika semua metode gagal
                if (!$success && !file_exists(Storage::disk('public')->path($path))) {
                    throw new \Exception('Gagal membuat file PDF laporan akhir dengan semua metode yang tersedia');
                }

                // Log informasi file yang dibuat
                Log::info('File PDF laporan akhir berhasil dibuat dan disimpan', [
                    'no_ijin' => $record->no_ijin,
                    'file_path' => $path,
                    'file_size' => filesize(Storage::disk('public')->path($path)) . ' bytes',
                    'url' => '/uploads/' . $npwp . '/' . $noIjin . '/dokumen/' . $fileName,
                    'timestamp' => $timestamp
                ]);

                // Simpan file ke database
                Userfile::updateOrCreate(
                    [
                        'no_ijin' => $record->no_ijin,
                        'kind' => 'la',
                    ],
                    [
                        'file_code' => $fileName,
                        'file_url' => $path,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]
                );

                return redirect()->back()->with('success', 'Berkas laporan akhir berhasil dibuat.');
            } catch (\Exception $e) {
                Log::error('Gagal membuat laporan akhir: ' . $e->getMessage(), [
                    'no_ijin' => $record->no_ijin,
                    'npwp' => $record->npwp,
                    'exception' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e; // Re-throw untuk ditangkap oleh catch di luar
            }
        } catch (\Exception $e) {
            Log::error('Gagal membuat laporan akhir: ' . $e->getMessage(), [
                'no_ijin' => $record->no_ijin ?? null,
                'npwp' => $record->npwp ?? null,
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            return back()->withErrors(['error' => 'Gagal membuat PDF laporan akhir.']);
        }
    }

}

