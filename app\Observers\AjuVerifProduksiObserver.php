<?php

namespace App\Observers;

use App\Models\AjuVerifProduksi2024;
use App\Models\User;
use Filament\Notifications\Notification;

class AjuVerifProduksiObserver
{
    /**
     * Handle the AjuVerifProduksi2024 "created" event.
     */
    public function created(AjuVerifProduksi2024 $ajuVerifProduksi2024): void
    {
        $pusat = User::whereHas('roles', function ($query) {
			$query->whereIn('name', ['Super Admin', 'admin', 'verifikator']);
		})->get();

		$noIjin = $ajuVerifProduksi2024->no_ijin;
		$periode = $ajuVerifProduksi2024->commitment->periodetahun;
		$company = $ajuVerifProduksi2024->commitment->user->datauser->company_name;

		Notification::make()
			->title('Verifikasi Produksi')

			->body("<p class='mb-3'><span class='text-info-500 font-bold'>{$company}</span> telah mengajukan Verifikasi Produksi Komitmen No.: <span class='text-info-500 font-bold'>{$noIjin}</span>, untuk periode {$periode}.</p>
			<p class='mb-3 mt-5'>Segera tindaklanjut.</p>")
			->sendToDatabase($pusat);
    }

    /**
     * Handle the AjuVerifProduksi2024 "updated" event.
     */
    public function updated(AjuVerifProduksi2024 $ajuVerifProduksi2024): void
    {

		$noIjin = $ajuVerifProduksi2024->no_ijin;
		$periode = $ajuVerifProduksi2024->commitment->periodetahun;
        if ($ajuVerifProduksi2024->wasChanged('status') && $ajuVerifProduksi2024->status === '1') {
			$company = $ajuVerifProduksi2024->commitment->user->datauser->company_name;
			
			$pusat = User::whereHas('roles', function ($query) {
				$query->whereIn('name', ['Super Admin', 'admin', 'verifikator']);
			})->get();

			Notification::make()
				->title('Verifikasi Produksi')

				->body("<p class='mb-3'><span class='text-info-500 font-bold'>{$company}</span> telah mengajukan Verifikasi Produksi Komitmen No.: <span class='text-info-500 font-bold'>{$noIjin}</span>, untuk periode {$periode}.</p>
				<p class='mb-3 mt-5'>Segera tindaklanjut.</p>")
				->sendToDatabase($pusat);
		}

        if ($ajuVerifProduksi2024->wasChanged('status') && $ajuVerifProduksi2024->status === '2') {
			$registrar = $ajuVerifProduksi2024->commitment->user;
			$pusat = User::whereHas('roles', function ($query) {
				$query->whereIn('name', ['Super Admin', 'admin']);
			})->get();

			Notification::make()
				->title('Verifikasi Produksi')
				->body("<p class='mb-3'>Verifikasi Produksi Komitmen No.: <span class='text-info-500 font-bold'>{$noIjin}</span> periode {$periode} telah dimulai.</p>")
				->sendToDatabase($pusat);
			Notification::make()
				->title('Verifikasi Produksi')
				->body("<p class='mb-3'>Verifikasi Produksi Komitmen No.: <span class='text-info-500 font-bold'>{$noIjin}</span> periode {$periode} telah dimulai.</p>")
				->sendToDatabase($registrar);
		}

        if ($ajuVerifProduksi2024->wasChanged('status') && $ajuVerifProduksi2024->status === '3') {
			$registrar = $ajuVerifProduksi2024->commitment->user;
			$pusat = User::whereHas('roles', function ($query) {
				$query->whereIn('name', ['Super Admin', 'admin']);
			})->get();

			Notification::make()
				->title('Verifikasi Produksi')
				->body("<p class='mb-3'>Verifikasi Produksi Komitmen No.: <span class='text-danger-500 font-bold'>{$noIjin}</span> periode {$periode} telah berakhir.</p>
				<p class='mb-3 mt-5'>Segera perbaiki untuk kemudian ajukan verifikasi kembali.</p>")
				->sendToDatabase($registrar);

			Notification::make()
				->title('Verifikasi Produksi')
				->body("<p class='mb-3'>Verifikasi Produksi Komitmen No.: <span class='text-info-500 font-bold'>{$noIjin}</span> periode {$periode} telah berakhir.</p>")
				->sendToDatabase($pusat);
		}

        if ($ajuVerifProduksi2024->wasChanged('status') && $ajuVerifProduksi2024->status === '4') {
			$registrar = $ajuVerifProduksi2024->commitment->user;
			$pusat = User::whereHas('roles', function ($query) {
				$query->whereIn('name', ['Super Admin', 'admin']);
			})->get();

			Notification::make()
				->title('Verifikasi Produksi')
				->body("<p class='mb-3'>Verifikasi Produksi Komitmen No.: <span class='text-success-500 font-bold'>{$noIjin}</span> periode {$periode} telah berakhir dengan status <span class='text-success-500 font-bold'>SELESAI</span>.</p>
				<p class='mb-3 mt-5'>Silahkan melanjutkan pelaporan realisasi produksi.</p>")
				->sendToDatabase($registrar);

			Notification::make()
				->title('Verifikasi Produksi')
				->body("<p class='mb-3'>Verifikasi Produksi Komitmen No.: <span class='text-success-500 font-bold'>{$noIjin}</span> periode {$periode} telah berakhir.</p>")
				->sendToDatabase($pusat);
		}
    }

    /**
     * Handle the AjuVerifProduksi2024 "deleted" event.
     */
    public function deleted(AjuVerifProduksi2024 $ajuVerifProduksi2024): void
    {
        //
    }

    /**
     * Handle the AjuVerifProduksi2024 "restored" event.
     */
    public function restored(AjuVerifProduksi2024 $ajuVerifProduksi2024): void
    {
        //
    }

    /**
     * Handle the AjuVerifProduksi2024 "force deleted" event.
     */
    public function forceDeleted(AjuVerifProduksi2024 $ajuVerifProduksi2024): void
    {
        //
    }
}
