<?php

namespace App\Filament\Pages\Auth;

use App\Models\BotLog;
use App\Models\DataAdministrator;
use App\Models\MasterKabupaten;
use App\Models\MasterProvinsi;
use Dan<PERSON><PERSON>rin\LivewireRateLimiting\Exceptions\TooManyRequestsException;
use Dan<PERSON><PERSON>rin\LivewireRateLimiting\WithRateLimiting;
use Exception;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Events\Auth\Registered;
use Filament\Facades\Filament;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Http\Responses\Auth\Contracts\RegistrationResponse;
use Filament\Notifications\Auth\VerifyEmail;
use Filament\Notifications\Notification;
use Filament\Pages\Concerns\CanUseDatabaseTransactions;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Pages\SimplePage as BasePage;
use Illuminate\Auth\EloquentUserProvider;
use Illuminate\Auth\SessionGuard;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;
use Filament\Pages\Auth\Register as BaseRegister;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * @property Form $form
 */
class Register extends BasePage
{
    use CanUseDatabaseTransactions;
    use InteractsWithFormActions;
    use WithRateLimiting;

    /**
     * @var view-string
     */
    protected static string $view = 'filament-panels::pages.auth.register';

    /**
     * @var array<string, mixed> | null
     */
    public ?array $data = [];

    protected string $userModel;

    public function mount(): void
    {
        if (Filament::auth()->check()) {
            redirect()->intended(Filament::getUrl());
        }

        $this->callHook('beforeFill');

        $this->form->fill();

        $this->callHook('afterFill');
    }

    public function register(): ?RegistrationResponse
    {
        try {
            $this->rateLimit(2);
        } catch (TooManyRequestsException $exception) {
            $this->getRateLimitedNotification($exception)?->send();

            return null;
        }

        $user = $this->wrapInDatabaseTransaction(function () {
            $this->callHook('beforeValidate');

            $data = $this->form->getState();

            $this->callHook('afterValidate');

            $data = $this->mutateFormDataBeforeRegister($data);

            $this->callHook('beforeRegister');

            $user = $this->handleRegistration($data);

            if (!$user) {
                return null;
            }

            $this->form->model($user)->saveRelationships();

            $this->callHook('afterRegister');

            return $user;
        });

        if (!$user) {
            return null;
        }

        event(new Registered($user));

        // $this->sendEmailVerificationNotification($user);

        Filament::auth()->login($user);

        session()->regenerate();

        return app(RegistrationResponse::class);
    }

    protected function getRateLimitedNotification(TooManyRequestsException $exception): ?Notification
    {
        return Notification::make()
            ->title(__('filament-panels::pages/auth/register.notifications.throttled.title', [
                'seconds' => $exception->secondsUntilAvailable,
                'minutes' => $exception->minutesUntilAvailable,
            ]))
            ->body(array_key_exists('body', __('filament-panels::pages/auth/register.notifications.throttled') ?: []) ? __('filament-panels::pages/auth/register.notifications.throttled.body', [
                'seconds' => $exception->secondsUntilAvailable,
                'minutes' => $exception->minutesUntilAvailable,
            ]) : null)
            ->danger();
    }

    /**
     * @param  array<string, mixed>  $data
     */
    protected function handleRegistration(array $data): ?Model
    {
		// Mulai transaksi database di awal metode
		DB::beginTransaction();

		try {
			// Validasi bot detection
			if (!empty($data['full_name'])) {
				Log::info('Bot Detected', [
					'ip' => request()->ip(),
					'user_agent' => request()->userAgent(),
					'referer' => request()->headers->get('referer'),
					'method' => request()->method(),
					'url' => request()->fullUrl(),
					'accept_language' => request()->header('accept-language'),
					'timestamp' => now()->toDateTimeString(),
				]);

				BotLog::create([
					'ip' => request()->ip(),
					'user_agent' => request()->userAgent(),
					'referer' => request()->headers->get('referer'),
					'method' => request()->method(),
					'url' => request()->fullUrl(),
					'accept_language' => request()->header('accept-language'),
					'created_at' => now(),
				]);

				DB::commit();

				Notification::make()
					->title('Failed Registration')
					->body('Bot Detected.')
					->danger()
					->send();
				return null;
			}

			// Validasi dinas kabupaten
			if ($data['role_id'] == 6) {
				$existingData = DataAdministrator::where('kabupaten_id', $data['kabupaten_id'])
					->whereHas('user', function ($query) {
						$query->whereHas('roles', function ($q) {
							$q->where('name', 'dinas'); // Pastikan role bernama "dinas"
						});
					})
					->exists();
				if ($existingData) {
					DB::commit(); // Commit untuk menyelesaikan transaksi

					Notification::make()
						->title('Registrasi Gagal')
						->body('Dinas untuk Kabupaten yang sama sudah terdaftar.')
						->danger()
						->send();
					return null;
				}
			}

			// Validasi field jabatan
			// if (empty($data['jabatan'])) {
			// 	throw new \Exception('Field Jabatan harus diisi');
			// }

			// Buat user
			$user = $this->getUserModel()::create([
				'name' => $data['name'],
				'username' => $data['username'],
				'email' => $data['email'],
				'password' => $data['password'],
				'status' => 'Baru',
			]);

			// Konversi role_id ke nama role dan tentukan guard secara eksplisit
			$roleName = $data['role_id'] == 4 ? 'verifikator' : 'dinas';

			// Gunakan DB langsung untuk menetapkan role
			DB::table('model_has_roles')->insert([
				'role_id' => $data['role_id'],
				'model_type' => get_class($user),
				'model_id' => $user->id
			]);


			// Buat data administrator
			DataAdministrator::create([
				'user_id' => $user->id,
				'nama' => $data['name'],
				'nip' => $data['nip'],
				'jabatan' => $data['jabatan'],
				'nama_dinas' => $data['nama_dinas'] ?? null,
				'provinsi_id' => $data['provinsi_id'] ?? null,
				'kabupaten_id' => $data['kabupaten_id'] ?? null,
			]);

			// Commit transaksi jika semua operasi berhasil
			DB::commit();

			return $user;
		} catch (\Exception $e) {
			// Rollback transaksi jika terjadi error
			DB::rollBack();

			// Log error
			Log::error('Registration failed: ' . $e->getMessage());
			Log::error('Stack trace: ' . $e->getTraceAsString());

			// Simpan detail error ke session
			session([
				'error_message' => 'Registrasi gagal',
				'error_detail' => $e->getMessage(),
				'error_trace' => $e->getTraceAsString()
			]);

			// Gunakan cara yang benar untuk redirect di Livewire
			return $this->redirect(route('auth.register-error'));
			// Kode di bawah ini tidak akan dieksekusi karena redirect di atas
			return null;
		}
    }

    protected function sendEmailVerificationNotification(Model $user): void
    {
        if (! $user instanceof MustVerifyEmail) {
            return;
        }

        if ($user->hasVerifiedEmail()) {
            return;
        }

        if (! method_exists($user, 'notify')) {
            $userClass = $user::class;

            throw new Exception("Model [{$userClass}] does not have a [notify()] method.");
        }

        $notification = app(VerifyEmail::class);
        $notification->url = Filament::getVerifyEmailUrl($user);

        $user->notify($notification);
    }

    public function form(Form $form): Form
    {
        return $form;
    }

    /**
     * @return array<int | string, string | Form>
     */
    protected function getForms(): array
    {
        return [
            'form' => $this->form(
                $this->makeForm()
                    ->schema([
                        $this->getRoleComponent(),
                        Section::make()
                            ->hiddenLabel()
                            ->hidden(fn ($get)=>$get('role_id') === null)
                            ->schema([
                                $this->getProfileGroup(),
                            ]),
                        Section::make()
                            ->hiddenLabel()
                            ->hidden(fn ($get)=>$get('role_id') === null)
                            ->schema([
                                $this->getNameFormComponent(),
                                $this->getEmailFormComponent(),
                                $this->getPasswordFormComponent(),
                                $this->getPasswordConfirmationFormComponent(),
                            ])
                    ])
                    ->statePath('data'),
            ),
        ];
    }

    protected function getRoleComponent(): Component
    {
        return Select::make('role_id')
                ->label('Register sebagai:')
                ->options([
                    4 => 'Verifikator',
                    6 => 'Dinas Kabupaten',
                ])
                ->reactive()
                ->required();
    }

    protected function getProfileGroup(): Group
    {
        return Group::make()
            ->schema([
				TextInput::make('full_name')
					->hiddenLabel()
					->extraAttributes([
						'style' => 'position:absolute; left:-9999px;',
						'tabindex' => '-1',
						'autocomplete' => 'off',
					])
					->dehydrated(true),
                TextInput::make('name')
                    ->label(__('filament-panels::pages/auth/register.form.name.label'))
                    ->required()
                    ->maxLength(255),
                TextInput::make('nip')
                    ->label('NIP')
                    ->required()
                    ->maxLength(255),
				TextInput::make('jabatan')
                    ->label('Jabatan')
                    ->required()
                    ->maxLength(255),
                TextInput::make('nama_dinas')
                    ->label('Nama Dinas')
                    ->required()
                    ->maxLength(255)
                    ->hidden(fn ($get)=>$get('role_id') == 4),
                Select::make('provinsi_id')
                    ->hidden(fn ($get)=>$get('role_id') == 4)
                    ->searchable()
                    ->options(function () {
                        return MasterProvinsi::pluck('nama', 'provinsi_id')->toArray();
                    })
                    ->reactive()
                    ->afterStateUpdated(fn ($set) => $set('kabupaten_id', null))
                    ->required(),

                Select::make('kabupaten_id')
                    ->hidden(fn ($get)=>$get('role_id') == 4)
                    ->searchable()
                    ->options(function ($get) {
                        $provinsiId = $get('provinsi_id');
                        if (!$provinsiId) {
                            return [];
                        }
                        return MasterKabupaten::where('provinsi_id', $provinsiId)->pluck('nama_kab', 'kabupaten_id')->toArray();
                    })
                    ->required()
                    ->reactive(),
				]);
    }

    protected function getNameFormComponent(): Component
    {
        return TextInput::make('username')
            ->label('Nama Pengguna/username')
			->unique($this->getUserModel())
            ->required()
            ->maxLength(255)
            ->helperText('Digunakan untuk mengakses aplikasi');
    }

    protected function getEmailFormComponent(): Component
    {
        return TextInput::make('email')
            ->label(__('filament-panels::pages/auth/register.form.email.label'))
            ->email()
			->unique($this->getUserModel())
            ->required()
            ->maxLength(255)
            ->unique($this->getUserModel());
    }

    protected function getPasswordFormComponent(): Component
    {
        return TextInput::make('password')
            ->label(__('filament-panels::pages/auth/register.form.password.label'))
            ->password()
            ->revealable(filament()->arePasswordsRevealable())
            ->required()
            ->rule(Password::default())
            ->dehydrateStateUsing(fn ($state) => Hash::make($state))
            ->same('passwordConfirmation')
            ->validationAttribute(__('filament-panels::pages/auth/register.form.password.validation_attribute'));
    }

    protected function getPasswordConfirmationFormComponent(): Component
    {
        return TextInput::make('passwordConfirmation')
            ->label(__('filament-panels::pages/auth/register.form.password_confirmation.label'))
            ->password()
            ->revealable(filament()->arePasswordsRevealable())
            ->required()
            ->dehydrated(false);
    }

    public function loginAction(): Action
    {
        return Action::make('login')
            ->link()
            ->label(__('filament-panels::pages/auth/register.actions.login.label'))
            ->url(filament()->getLoginUrl());
    }

    protected function getUserModel(): string
    {
        if (isset($this->userModel)) {
            return $this->userModel;
        }

        /** @var SessionGuard $authGuard */
        $authGuard = Filament::auth();

        /** @var EloquentUserProvider $provider */
        $provider = $authGuard->getProvider();

        return $this->userModel = $provider->getModel();
    }

    public function getTitle(): string | Htmlable
    {
        return 'Registrasi Pengguna';
    }

    public function getHeading(): string | Htmlable
    {
        return 'Registrasi akun';
    }

    /**
     * @return array<Action | ActionGroup>
     */
    protected function getFormActions(): array
    {
        return [
            $this->getRegisterFormAction(),
        ];
    }

    public function getRegisterFormAction(): Action
    {
        return Action::make('register')
            ->label('Register Akun Dinas/Verifikator')
            ->submit('register');
    }

    protected function hasFullWidthFormActions(): bool
    {
        return true;
    }

    /**
     * @param  array<string, mixed>  $data
     * @return array<string, mixed>
     */
    protected function mutateFormDataBeforeRegister(array $data): array
    {
        return $data;
    }
}
