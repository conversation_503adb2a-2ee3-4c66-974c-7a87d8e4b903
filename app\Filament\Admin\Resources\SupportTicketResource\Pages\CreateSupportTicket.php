<?php

namespace App\Filament\Admin\Resources\SupportTicketResource\Pages;

use App\Filament\Admin\Resources\SupportTicketResource;
use Filament\Actions;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Enums\Alignment;

class CreateSupportTicket extends CreateRecord
{
    protected static string $resource = SupportTicketResource::class;
	protected static ?string $title = null;
	protected ?string $heading = '';
    public static string | Alignment $formActionsAlignment = Alignment::Right;

	// protected function getFormActions(): array
    // {
    //     return [
    //         CreateAction::make('createAnother')
    //             ->label('Kirim tiket'),
    //     ];
    // }
}
