<?php

namespace App\Filament\Admin\Resources\CmsCategoryResource\Pages;

use App\Filament\Admin\Resources\CmsCategoryResource;
use App\Models\CmsCategory;
use Illuminate\Support\Str;
use Filament\Actions;
use Filament\Forms\Components\{ColorPicker, FileUpload, Grid, Section, Select, Textarea, TextInput, Toggle};
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use TomatoPHP\FilamentIcons\Components\IconPicker;

class EditCmsCategory extends EditRecord
{
    protected static string $resource = CmsCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected static ?string $title = 'Perbarui Kategori';

    public function getHeading(): string
	{
		// $sales_no = $this->record ? $this->record->sales_no : 'N/A';
        return 'Perbarui Kategori';
	}

    public function getSubheading(): ?string
    {
        $category = $this->record ? $this->record->name : '##';
        return 'Kategori ' . $category;
    }
}
