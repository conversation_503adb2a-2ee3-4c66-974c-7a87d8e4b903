<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CleanGuestSessions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sessions:clean-guests {--days=1 : Number of days to keep}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old guest sessions from the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        $cutoff = Carbon::now()->subDays($days)->timestamp;
        
        $this->info("Cleaning guest sessions older than {$days} days...");
        
        // Hitung jumlah session sebelum pembersihan
        $beforeCount = DB::table('sessions')->count();
        $this->info("Total sessions before cleanup: {$beforeCount}");
        
        // Hitung jumlah session guest sebelum pembersihan
        $guestBeforeCount = DB::table('sessions')->whereNull('user_id')->count();
        $this->info("Guest sessions before cleanup: {$guestBeforeCount}");
        
        // Hitung jumlah session monitoring sebelum pembersihan
        $monitoringBeforeCount = DB::table('sessions')
            ->whereNull('user_id')
            ->where('user_agent', 'like', '%Uptime-Kuma%')
            ->count();
        $this->info("Monitoring sessions before cleanup: {$monitoringBeforeCount}");
        
        // Hapus session guest yang sudah tidak aktif
        $deleted = DB::table('sessions')
            ->whereNull('user_id')
            ->where('last_activity', '<', $cutoff)
            ->delete();
        
        $this->info("Deleted {$deleted} old guest sessions.");
        
        // Hitung jumlah session setelah pembersihan
        $afterCount = DB::table('sessions')->count();
        $this->info("Total sessions after cleanup: {$afterCount}");
        
        // Log hasil pembersihan
        Log::info("Guest sessions cleanup completed", [
            'deleted' => $deleted,
            'before_count' => $beforeCount,
            'after_count' => $afterCount,
            'cutoff_days' => $days,
        ]);
        
        return Command::SUCCESS;
    }
}
