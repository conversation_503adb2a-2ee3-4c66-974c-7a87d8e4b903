<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\MasterAnggotaResource\Pages;
use App\Models\MasterAnggota;
use App\Models\MasterDesa;
use App\Models\MasterKabupaten;
use App\Models\MasterKecamatan;
use App\Models\MasterPoktan;
use Filament\Forms;
use Filament\Forms\Components\{Select};
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\{TextColumn};
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class MasterAnggotaResource extends Resource
{
    protected static ?string $model = MasterAnggota::class;

    protected static ?string $modelLabel = 'Master Anggota Poktan';
    protected static ?string $pluralModelLabel = 'Daftar Anggota';

	protected static ?string $navigationGroup = 'Data Induk';
    protected static ?string $navigationLabel = 'Petani';
    protected static ?int $navigationSort = 4;
    protected static ?string $navigationIcon = 'heroicon-o-users';

	public static function shouldRegisterNavigation(): bool
	{
		$user = Auth::user();

		if ($user->hasAnyRole(['Super Admin', 'admin'])) {
			return true;
		}

		return false;

		// return $user->hasRole('importir') && $user->anggota2024->isNotEmpty();
	}

	public static function getGloballySearchableAttributes(): array
	{
		if(Auth::user()->hasAnyRole(['admin','Super Admin','dinas'])) {
			return ['nama_petani', 'ktp_petani'];
		}
		return [];
	}

	public static function getGlobalSearchResultDetails($record): array
	{
		return [
			'Nama Petani' => $record->nama_petani,
			'KTP' => $record->ktp_petani,
		];
	}

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('kode_poktan')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('nama_petani')
                    ->maxLength(255),
                Forms\Components\TextInput::make('ktp_petani')
                    ->required()
                    ->maxLength(18),
                Forms\Components\TextInput::make('hp_petani')
                    ->maxLength(20),
                Forms\Components\Textarea::make('alamat_petani')
                    ->columnSpanFull(),
                Select::make('provinsi_id')
                    ->label('Provinsi')
                    ->relationship(name: 'provinsi', titleAttribute: 'nama')
                    ->searchable()
                    ->preload() // Provinsi jumlahnya sedikit, jadi bisa preload
                    ->live() // Gunakan live() untuk memastikan komponen dirender ulang
                    ->afterStateUpdated(function (callable $set) {
                        $set('kabupaten_id', null);
                        $set('kecamatan_id', null);
                        $set('kelurahan_id', null);
                    })
                    ->required(),

                Select::make('kabupaten_id')
                    ->label('Kabupaten/Kota')
                    ->options(function (callable $get) {
                        $provinsiId = $get('provinsi_id');
                        if (!$provinsiId) {
                            return [];
                        }

                        return MasterKabupaten::query()
                            ->where('provinsi_id', $provinsiId)
                            ->orderBy('nama_kab')
                            ->pluck('nama_kab', 'kabupaten_id')
                            ->toArray();
                    })
                    ->searchable()
                    ->live() // Gunakan live() untuk memastikan komponen dirender ulang
                    ->afterStateUpdated(function (callable $set) {
                        $set('kecamatan_id', null);
                        $set('kelurahan_id', null);
                    })
                    ->disabled(fn (callable $get) => !$get('provinsi_id'))
                    ->placeholder(fn (callable $get) =>
                        $get('provinsi_id')
                            ? 'Pilih Kabupaten/Kota'
                            : 'Pilih Provinsi terlebih dahulu'
                    )
                    ->required(),

                Select::make('kecamatan_id')
                    ->label('Kecamatan')
                    ->options(function (callable $get) {
                        $kabupatenId = $get('kabupaten_id');
                        if (!$kabupatenId) {
                            return [];
                        }

                        return MasterKecamatan::query()
                            ->where('kabupaten_id', $kabupatenId)
                            ->orderBy('nama_kecamatan')
                            ->pluck('nama_kecamatan', 'kecamatan_id')
                            ->toArray();
                    })
                    ->searchable()
                    ->live() // Gunakan live() untuk memastikan komponen dirender ulang
                    ->afterStateUpdated(function (callable $set) {
                        $set('kelurahan_id', null);
                    })
                    ->disabled(fn (callable $get) => !$get('kabupaten_id'))
                    ->placeholder(fn (callable $get) =>
                        $get('kabupaten_id')
                            ? 'Pilih Kecamatan'
                            : 'Pilih Kabupaten/Kota terlebih dahulu'
                    )
                    ->required(),

                Select::make('kelurahan_id')
                    ->label('Desa/Kelurahan')
                    ->options(function (callable $get) {
                        $kecamatanId = $get('kecamatan_id');
                        if (!$kecamatanId) {
                            return [];
                        }

                        return MasterDesa::query()
                            ->where('kecamatan_id', $kecamatanId)
                            ->orderBy('nama_desa')
                            ->pluck('nama_desa', 'kelurahan_id')
                            ->toArray();
                    })
                    ->searchable()
                    ->live() // Gunakan live() untuk memastikan komponen dirender ulang
                    ->disabled(fn (callable $get) => !$get('kecamatan_id'))
                    ->placeholder(fn (callable $get) =>
                        $get('kecamatan_id')
                            ? 'Pilih Desa/Kelurahan'
                            : 'Pilih Kecamatan terlebih dahulu'
                    )
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
				TextColumn::make('index')
                    ->label('No')
					->rowIndex(),
                TextColumn::make('nama_petani')
					->label('Nama')
                    ->searchable(),
                TextColumn::make('masterpoktan.nama_kelompok')
                    ->searchable(),
                TextColumn::make('ktp_petani')
					->label('NIK')
                    ->searchable(),
                TextColumn::make('hp_petani')
					->label('No. HP')
                    ->searchable(),
                TextColumn::make('provinsi.nama')
                    ->sortable()->hidden(fn () => Auth::user()->hasRole('dinas')),
                TextColumn::make('kabupaten.nama_kab')
                    ->sortable()->hidden(fn () => Auth::user()->hasRole('dinas')),
                TextColumn::make('kecamatan.nama_kecamatan')
                    ->sortable()->visible(fn () => Auth::user()->hasRole('dinas')),
            ])
            ->filters([
				SelectFilter::make('kecamatan_id')
					->label('Kecamatan')
					->options(function () {
						return MasterKecamatan::where('kabupaten_id', optional(Auth::user()->dataadmin)->kabupaten_id)
							->pluck('nama_kecamatan', 'kecamatan_id')
							->toArray();
					})
					->searchable()
					->preload()->visible(fn () => Auth::user()->hasRole('dinas')),
				SelectFilter::make('kode_poktan')
					->label('Kelompok Tani')
					->options(function () {
						return MasterPoktan::where('kabupaten_id', optional(Auth::user()->dataadmin)->kabupaten_id)
							->pluck('nama_kelompok', 'kode_poktan')
							->toArray();
					})
					->searchable()
					->preload()->visible(fn () => Auth::user()->hasRole('dinas')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMasterAnggotas::route('/'),
            'create' => Pages\CreateMasterAnggota::route('/create'),
            'view' => Pages\ViewMasterAnggota::route('/{record}'),
            'edit' => Pages\EditMasterAnggota::route('/{record}/edit'),
        ];
    }
}
