<?php

namespace App\Http\Controllers\Thn2024;

use App\Http\Controllers\Controller;
use App\Models\Commitment2024;
use App\Models\DataRealisasi2024;
use App\Models\ForeignApi;
use App\Models\FotoProduksi2024;
use App\Models\FotoTanam2024;
use App\Models\Lokasi2024;
use App\Models\MasterAnggota2024;
use App\Models\Pks2024;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DrawSpatialsController extends Controller
{
    public function drawMap($noRiph,$realisasi)
	{
		$noIjin = vsprintf('%s/%s.%s/%s/%s/%s', [
			substr($noRiph, 0, 4),
			substr($noRiph, 4, 2),
			substr($noRiph, 6, 3),
			substr($noRiph, 9, 1),
			substr($noRiph, 10, 2),
			substr($noRiph, 12, 4),
		]);

		$mapKey = ForeignApi::where('status', 1)->select('key')->first();

		$lokasi = DataRealisasi2024::findOrFail($realisasi);
		$pageTitle = 'Panel Pemetaan';
		$modul = 'Pemilihan Lokasi Komitmen Tanam';
		$pageFooter = 'Footer';

		return view('velzon.efilling.drawMap', compact(
			'modul',
			'pageTitle',
			'pageFooter',
			'mapKey',
			'noIjin',
			'noRiph',
			'realisasi',
			'lokasi',
		));
	}

	public function validateKml(Request $request)
	{
		Log::info('[KML Validation] Memulai validasi file.');

		// Validasi awal file
		$request->validate([
			'file' => 'required|file|mimes:kml,xml|max:1024', // max 1MB
		]);

		$uploadedFile = $request->file('file');
		$originalName = $uploadedFile->getClientOriginalName();
		$size = $uploadedFile->getSize();

		Log::info("[KML Validation] File diterima: {$originalName} ({$size} bytes)");

		// Ambil isi file
		$content = $uploadedFile->get();
		$xmlText = $request->file('file')->get();

		// Log isi awal file (maksimal 300 karakter untuk keamanan/log sanitasi)
		Log::debug('[KML Validation] Potongan isi file:', [
			'snippet' => substr($xmlText, 0, 300)
		]);

		// Deteksi tag berbahaya
		if (preg_match('/<(script|NetworkLink|!DOCTYPE)/i', $xmlText, $match)) {
			Log::warning("[KML Validation] File diblokir. Terdeteksi tag berbahaya: <{$match[1]}>");

			return response()->json([
				'success' => false,
				'reason' => "File KML mengandung tag berbahaya (<{$match[1]}>)",
			]);
		}

		// Validasi struktur XML
		libxml_use_internal_errors(true);
		$xml = simplexml_load_string($xmlText);

		if (!$xml) {
			$errorMsg = collect(libxml_get_errors())->pluck('message')->implode('; ');
			libxml_clear_errors();

			Log::error("[KML Validation] Parsing XML gagal: {$errorMsg}");

			return response()->json([
				'success' => false,
				'reason' => 'File tidak dapat diurai sebagai XML. ' . $errorMsg,
			]);
		}

		Log::info("[KML Validation] Validasi berhasil untuk file: {$originalName}");

		return response()->json([
			'success' => true,
			'message' => 'File KML valid.',
		]);
	}

	public function saveDraw(Request $request, $noRiph, $realisasi)
    {
        // Log awal pemanggilan method
        Log::info('DrawSpatialsController::saveDraw called', [
            'noRiph' => $noRiph,
            'realisasi' => $realisasi,
            'request_method' => $request->method(),
            'request_url' => $request->fullUrl(),
            'request_ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'request_headers' => $request->header(),
        ]);

        // Log request data
        Log::info('Request data received', [
            'all_data' => $request->all(),
            'content_type' => $request->header('Content-Type'),
            'content_length' => $request->header('Content-Length'),
        ]);

        // Format noIjin dari noRiph
        $noIjin = vsprintf('%s/%s.%s/%s/%s/%s', [
            substr($noRiph, 0, 4),
            substr($noRiph, 4, 2),
            substr($noRiph, 6, 3),
            substr($noRiph, 9, 1),
            substr($noRiph, 10, 2),
            substr($noRiph, 12, 4),
        ]);
        Log::info('Formatted noIjin', ['noIjin' => $noIjin]);

        try {
            // Validasi data
            Log::info('Validating request data');
            $validatedData = $request->validate([
                'lat'   => 'required|numeric|min:-90|max:90',
                'lng'   => 'required|numeric|min:-180|max:180',
                'poly'  => 'required|json',
                'area'  => 'required|numeric|min:0',
            ]);
            Log::info('Validation passed', ['validatedData' => $validatedData]);

            // Mulai transaksi database
            Log::info('Starting database transaction');
            DB::beginTransaction();

            try {
                // Cari data realisasi
                Log::info('Finding DataRealisasi2024 record', ['realisasi_id' => $realisasi]);
                $dataRealisasi = DataRealisasi2024::findOrFail($realisasi);
                Log::info('DataRealisasi2024 record found', [
                    'id' => $dataRealisasi->id,
                    'no_ijin' => $dataRealisasi->no_ijin,
                    'anggota_id' => $dataRealisasi->anggota_id,
                    'current_lat' => $dataRealisasi->latitude,
                    'current_lng' => $dataRealisasi->longitude,
                    'current_area' => $dataRealisasi->luas_kira,
                ]);

                // Cari commitment dengan mengatasi masalah spasi dan format
                $trimmedNoIjin = trim($noIjin); // Menghilangkan spasi di awal dan akhir
                Log::info('Finding Commitment2024 record', [
                    'original_no_ijin' => $noIjin,
                    'trimmed_no_ijin' => $trimmedNoIjin
                ]);

                // Coba cari dengan no_ijin yang sudah di-trim (exact match)
                $commitment = Commitment2024::where('no_ijin', $trimmedNoIjin)->first();

                // Jika tidak ditemukan, coba cari dengan no_ijin dari data realisasi yang sudah di-trim (exact match)
                if (!$commitment && $dataRealisasi->no_ijin) {
                    $trimmedRealisasiNoIjin = trim($dataRealisasi->no_ijin);
                    Log::info('Trying with realisasi no_ijin (exact match)', [
                        'original_realisasi_no_ijin' => $dataRealisasi->no_ijin,
                        'trimmed_realisasi_no_ijin' => $trimmedRealisasiNoIjin
                    ]);
                    $commitment = Commitment2024::where('no_ijin', $trimmedRealisasiNoIjin)->first();
                }

                // Jika masih tidak ditemukan, coba cari dengan LIKE untuk no_ijin yang sudah di-trim
                if (!$commitment) {
                    Log::info('Trying with LIKE query for trimmed no_ijin', [
                        'search_pattern' => '%' . $trimmedNoIjin . '%'
                    ]);
                    $commitment = Commitment2024::where('no_ijin', 'LIKE', '%' . $trimmedNoIjin . '%')->first();
                }

                // Jika masih tidak ditemukan, coba cari dengan LIKE untuk no_ijin dari data realisasi yang sudah di-trim
                if (!$commitment && $dataRealisasi->no_ijin) {
                    Log::info('Trying with LIKE query for realisasi no_ijin', [
                        'search_pattern' => '%' . $trimmedRealisasiNoIjin . '%'
                    ]);
                    $commitment = Commitment2024::where('no_ijin', 'LIKE', '%' . $trimmedRealisasiNoIjin . '%')->first();
                }

                // Jika masih tidak ditemukan, coba cari dengan LIKE untuk bagian-bagian dari no_ijin
                if (!$commitment) {
                    // Hapus karakter non-alphanumeric dari no_ijin untuk pencarian yang lebih fleksibel
                    $cleanNoIjin = preg_replace('/[^a-zA-Z0-9]/', '', $trimmedNoIjin);
                    Log::info('Trying with LIKE query for cleaned no_ijin', [
                        'cleaned_no_ijin' => $cleanNoIjin,
                        'search_pattern' => '%' . $cleanNoIjin . '%'
                    ]);

                    // Cari dengan pattern yang lebih fleksibel
                    $commitment = Commitment2024::whereRaw('REPLACE(REPLACE(REPLACE(no_ijin, "/", ""), ".", ""), " ", "") LIKE ?', ['%' . $cleanNoIjin . '%'])->first();
                }

                // Jika masih tidak ditemukan, log error dan return 404
                if (!$commitment) {
                    Log::error('Commitment not found after trying multiple search methods', [
                        'original_no_ijin' => $noIjin,
                        'trimmed_no_ijin' => $trimmedNoIjin,
                        'realisasi_no_ijin' => $dataRealisasi->no_ijin,
                        'trimmed_realisasi_no_ijin' => isset($trimmedRealisasiNoIjin) ? $trimmedRealisasiNoIjin : null,
                        'cleaned_no_ijin' => isset($cleanNoIjin) ? $cleanNoIjin : null
                    ]);
                    return response()->json(['error' => 'Commitment tidak ditemukan'], 404);
                }
                Log::info('Commitment2024 record found', [
                    'id' => $commitment->id,
                    'no_ijin' => $commitment->no_ijin,
                ]);

                // Update data realisasi
                Log::info('Updating DataRealisasi2024 record', [
                    'id' => $dataRealisasi->id,
                    'new_lat' => $validatedData['lat'],
                    'new_lng' => $validatedData['lng'],
                    'new_area' => $validatedData['area'],
                    'poly_length' => strlen($validatedData['poly']),
                ]);

                $dataRealisasi->update([
                    'latitude'  => $validatedData['lat'],
                    'longitude' => $validatedData['lng'],
                    'polygon'   => $validatedData['poly'],
                    'luas_kira' => $validatedData['area'],
                ]);

                // Commit transaksi
                Log::info('Committing database transaction');
                DB::commit();

                // Log sukses
                Log::info('Data successfully updated', [
                    'realisasi_id' => $dataRealisasi->id,
                    'updated_data' => [
                        'latitude' => $dataRealisasi->latitude,
                        'longitude' => $dataRealisasi->longitude,
                        'luas_kira' => $dataRealisasi->luas_kira,
                        'polygon_length' => strlen($dataRealisasi->polygon),
                    ]
                ]);

                // Return response sukses
                return response()->json([
                    'success' => true,
                    'message' => 'Data berhasil diperbarui',
                    'data' => [
                        'realisasi_id' => $dataRealisasi->id,
                        'latitude' => $dataRealisasi->latitude,
                        'longitude' => $dataRealisasi->longitude,
                        'luas_kira' => $dataRealisasi->luas_kira,
                    ]
                ], 200);
            } catch (\Exception $e) {
                // Rollback transaksi jika terjadi error
                Log::error('Error in database transaction, rolling back', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                ]);
                DB::rollBack();
                throw $e;
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Log error validasi
            Log::error('Validation error', [
                'errors' => $e->errors(),
                'request_data' => $request->all(),
            ]);
            return response()->json([
                'error' => 'Validasi gagal',
                'details' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            // Log error umum
            Log::error('Unhandled exception in saveDraw', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'request_data' => $request->all(),
            ]);
            return response()->json([
                'error' => 'Terjadi kesalahan',
                'details' => $e->getMessage()
            ], 500);
        }
    }

	public function realisasiReportView(Request $request, $pksId)
	{
		$pageTitle = 'Panel Pemetaan';
		$modul = 'Daftar Lokasi Realisasi Komitmen';
		$pageFooter = 'Footer';
		$mapKey = ForeignApi::where('status', 1)->select('key')->first();

		return view('velzon.realisasi.realisasiReport2024', compact(
			'modul',
			'pageTitle',
			'pageFooter',
			'mapKey',
			'pksId'
		));
	}

	public function realisasiReportData(Request $request, $pksId)
	{
		$decryptedId = Crypt::decryptString($pksId, config('app.qr_secret'));

		$Pks = Pks2024::where('id', $decryptedId)
			->select(['id', 'no_ijin', 'poktan_id'])
			->firstOrFail();

		$realisasi = DataRealisasi2024::where('pks_id', $Pks->id)
			->select('id', 'anggota_id', 'nama_lokasi')
			->get()
			->map(function ($item) {
				$item->nama_petani = MasterAnggota2024::where('anggota_id', $item->anggota_id)->value('nama_petani');
				$item->ktp_petani = MasterAnggota2024::where('anggota_id', $item->anggota_id)->value('ktp_petani');
				$item->encryptedId = Crypt::encryptString($item->id, config('app.qr_secret'));
				unset($item->id,$item->anggota_id);
				return $item;
			});

		$jumlahTitik = DataRealisasi2024::where('pks_id', $Pks->id)
			->count();

		$realisasiTanam = DataRealisasi2024::where('pks_id', $Pks->id)
			->sum('luas_lahan');
		$realisasiPanen = DataRealisasi2024::where('pks_id', $Pks->id)
			->sum('volume');

		unset($Pks->id, $Pks->poktan_id);
		$data = [
			'pks' => $Pks,
			'jumlahTitik'=>$jumlahTitik,
			'realisasiTanam'=>number_format($realisasiTanam,2),
			'realisasiPanen' =>number_format($realisasiPanen,2),
			'realisasi' => $realisasi,
		];

		return response()->json($data);
	}

	public function realisasiReportSingle(Request $request, $realisasiId)
	{
		$decryptedId = Crypt::decryptString($realisasiId, config('app.qr_secret'));

		$dataRealisasi = DataRealisasi2024::findOrFail($decryptedId);
		$lokasi = Lokasi2024::where('no_ijin', $dataRealisasi->no_ijin)
			->where('anggota_id', $dataRealisasi->anggota_id)
			->select('id','poktan_id','anggota_id','luas_lahan')
			->first();

		$fotoTanam = FotoTanam2024::where('realisasi_id', $dataRealisasi->id)->get();
		$fotoPanen = FotoProduksi2024::where('realisasi_id', $dataRealisasi->id)->get();

		$anggota = MasterAnggota2024::where('anggota_id', $dataRealisasi->anggota_id)
			->select('nama_petani','ktp_petani')
			->firstOrFail();

		unset($lokasi->id, $lokasi->poktan_id, $lokasi->anggota_id);
		unset($dataRealisasi->id, $dataRealisasi->poktan_id, $dataRealisasi->anggota_id);
		$data = [
			'datarealisasi' => $dataRealisasi,
			'lokasi' => $lokasi,
			'anggota' => $anggota,
			'fotoTanam' => $fotoTanam,
			'fotoPanen' => $fotoPanen,
		];
		return response()->json($data);
	}
}
