<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\Varietas;
use App\Models\User;

class VarietasPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any Varietas');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Varietas $varietas): bool
    {
        return $user->checkPermissionTo('view Varietas');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create Varietas');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Varietas $varietas): bool
    {
        return $user->checkPermissionTo('update Varietas');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Varietas $varietas): bool
    {
        return $user->checkPermissionTo('delete Varietas');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any Varietas');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Varietas $varietas): bool
    {
        return $user->checkPermissionTo('restore Varietas');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any Varietas');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, Varietas $varietas): bool
    {
        return $user->checkPermissionTo('replicate Varietas');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder Varietas');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Varietas $varietas): bool
    {
        return $user->checkPermissionTo('force-delete Varietas');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any Varietas');
    }
}
