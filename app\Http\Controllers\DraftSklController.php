<?php

namespace App\Http\Controllers;

use App\Filament\Admin\Resources\AjuVerifSkl2024Resource\Pages\GenerateDraftSklAction;
use App\Models\AjuVerifSkl2024;
use Illuminate\Http\Request;

class DraftSklController extends Controller
{
    public function generateDraftSkl($id)
    {
        // Cek otorisasi
        if (!auth()->user()->hasAnyRole(['admin', 'Super Admin', 'direktur'])) {
            abort(403, 'Unauthorized action.');
        }

        // Ambil data pengajuan
        $record = AjuVerifSkl2024::findOrFail($id);

        // Generate dan tampilkan PDF
        return GenerateDraftSklAction::handle($record);
    }
}
