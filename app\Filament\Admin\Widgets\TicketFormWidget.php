<?php

namespace App\Filament\Admin\Widgets;

use App\Models\SupportDepartement;
use App\Models\SupportTicket;
use Filament\Forms\Components\{FileUpload, Hidden, RichEditor, Select, TextInput};
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class TicketFormWidget extends Widget implements HasForms
{
	use InteractsWithForms;

	public string $mode = 'list';
    protected static string $view = 'filament.admin.widgets.ticket-form-widget';

	public ?array $data = [];
	public function mount(): void
	{
		$this->form->fill();
	}

	public function form(Form $form): Form
	{
        return $form
			->columns(2)
			->statePath('data')
            ->schema([
                Hidden::make('user_id')
                    ->default(Auth::user()->id),
                TextInput::make('subject')
                    ->required()
                    ->maxLength(255),
                Select::make('related_service')
					->label('Layanan terkait')
					->searchable()
					->options(function () {
						$user = Auth::user();

						$commitmentIjin = $user->commitment()
							->pluck('no_ijin')
							->filter()
							->mapWithKeys(fn ($ijin) => [$ijin => $ijin]);

						$oldCommitmentIjin = $user->oldCommitment()
							->pluck('no_ijin')
							->filter()
							->mapWithKeys(fn ($ijin) => [$ijin => $ijin]);

						return $commitmentIjin->merge($oldCommitmentIjin)->unique();
					}),
                RichEditor::make('message')
                    ->required()->label('Isi Pesan')
                    ->columnSpanFull(),

                Select::make('department_id')
                    ->required()
					->label('Departement')
					->options(fn () => SupportDepartement::pluck('name', 'id')),
                Select::make('priority')
                    ->required()
					->options(['low', 'medium', 'high', 'urgent']),
                FileUpload::make('attachment')->columnSpanFull(),

                Hidden::make('last_replied_by'),
            ]);
	}

	public function create(): void
	{
		$data = $this->form->getState();

		try {
			SupportTicket::create($data);

			Notification::make()
				->title('Tiket layanan bantuan berhasil dikirimkan')
				->success()
				->send();

			// Reset form
			$this->form->fill();

			// Dispatch event to refresh other widgets
			$this->dispatch('ticket-created');
		} catch (\Exception $e) {
			Notification::make()
				->title('Error')
				->body($e->getMessage())
				->danger()
				->send();
		}
	}
}
