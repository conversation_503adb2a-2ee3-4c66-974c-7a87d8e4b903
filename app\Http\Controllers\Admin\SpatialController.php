<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class SpatialController extends Controller
{
    /**
     * Menyimpan laporan kegagalan unggah KML
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveKmlFailures(Request $request)
    {
        try {
            // Validasi request
            $request->validate([
                'data' => 'required|array',
            ]);
            
            $data = $request->input('data');
            
            // Pastikan direktori ada
            $directory = 'uploads/kml/not_processed';
            if (!Storage::disk('public')->exists($directory)) {
                Storage::disk('public')->makeDirectory($directory, 0755, true);
            }
            
            // Generate nama file dengan timestamp
            $timestamp = now()->format('Y-m-d_H-i-s');
            $filename = "kml_failures_{$timestamp}.json";
            $path = "{$directory}/{$filename}";
            
            // Simpan data JSON ke file
            Storage::disk('public')->put($path, json_encode($data, JSON_PRETTY_PRINT));
            
            // Log aktivitas
            Log::info('KML failure report saved', [
                'user_id' => auth()->id(),
                'filename' => $filename,
                'failed_files_count' => count($data['failures'] ?? [])
            ]);
            
            // Return response
            return response()->json([
                'success' => true,
                'message' => 'Laporan kegagalan berhasil disimpan',
                'filename' => $filename
            ]);
        } catch (\Exception $e) {
            // Log error
            Log::error('Error saving KML failure report: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            
            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Gagal menyimpan laporan kegagalan: ' . $e->getMessage()
            ], 500);
        }
    }
}
