<?php

namespace App\Filament\Panel2025\Resources\Realisasi2025Resource\Pages;

use App\Filament\Panel2025\Resources\Realisasi2025Resource;
use App\Models\PengajuanVerifikasi;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Forms\Components\{Checkbox, DatePicker, Fieldset, FileUpload, Grid, Group, Hidden, Placeholder, Radio, Section, Select, Textarea, TextInput};
use Filament\Forms\Components\Actions\Action as ActionsAction;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class VerifikasiPetak extends EditRecord
{
	protected static string $resource = Realisasi2025Resource::class;
	public static string | Alignment $formActionsAlignment = Alignment::Right;

    public ?string $noPengajuan = null;
	public function mount(string | int $record): void
	{
		parent::mount($record);

		$this->noPengajuan = request('pengajuan');
	}


	// protected static ?string $title = 'Verifikasi Data Realisasi';

	public function getTitle(): string
	{
		$noPengajuan = $this->noPengajuan;
		$pengajuan = PengajuanVerifikasi::where('no_pengajuan', $noPengajuan)->first();
		$jenis = $pengajuan->kind;
		if($jenis === 'PVT'){
			return 'Verifikasi Tanam';
		}
		if($jenis === 'PVP'){
			return 'Verifikasi Produksi';
		}
		return 'Verifikasi'; // Default return value
	}

	public function getHeading(): string
	{
		$noPengajuan = $this->noPengajuan;
		$pengajuan = PengajuanVerifikasi::where('no_pengajuan', $noPengajuan)->first();
		$jenis = $pengajuan->kind;
		if($jenis === 'PVT'){
			return 'Verifikasi Realisasi Komitmen Tanam';
		}
		if($jenis === 'PVP'){
			return 'Verifikasi Realisasi Komitmen Produksi';
		}
		return 'Verifikasi'; // Default return value
	}

	public function getSubheading(): ?string
	{
		$petak = $this->record->kode_spatial ? $this->record->kode_spatial : '##';
		$noIjin = $this->record ? $this->record->no_ijin : '##';
		return 'Petak: '. $petak.' / PPRK No: ' . $noIjin;
	}

	protected function getHeaderActions(): array
	{
		return [
			// Actions\ViewAction::make()->label('Lihat Peta')->icon('icon-geo-alt-fill')->color('info'),
			// Actions\DeleteAction::make(),
			Action::make('gotomap')
				->hidden()
				->icon('icon-geo-alt-fill')
				->color('info')
				->label('Lihat Peta Lokasi')
				->url(fn ($record) => route('panel.2025.report.singleMap', $record->id) ),
		];
	}

	public function form(Form $form): Form
	{
		return $form
			->columns(3)
            ->schema([
				Section::make()
					->hiddenLabel()
					->schema([
						Placeholder::make('map')
							->label('Peta')
							->hiddenLabel()
							// Tidak perlu live dan reactive karena kita menggunakan wire:ignore
							->columnSpan([
								'sm' => '3',
								'md' => '2',
							])
							->extraAttributes(['class' => 'map-container'])
							->content(function ($record) {
								// Pastikan data spatial memiliki ID untuk identifikasi unik
								$spatialData = $record->spatial->toArray();
								// Tambahkan ID jika belum ada
								if (!isset($spatialData['id'])) {
									$spatialData['id'] = $record->id;
								}
								return view('components.map', ['data' => $spatialData]);
							}),
						Fieldset::make('Data Peta')
							->columnSpan([
								'sm' => '3',
								'md' => '1',
							])->columns(1)
							->schema([
								Group::make()
									->extraAttributes(['class'=>'mb-5'])
									->schema([
										Placeholder::make('kode_lahan')
											->label('Kode Spatial')
											->inlineLabel()
											->content(fn ($record) => new HtmlString('<span class="font-bold">' . $record->kode_spatial . '</span>')),

										Placeholder::make('Luas Lahan')
											->inlineLabel()
											->content(fn ($record)=>number_format($record->luas_lahan,0,',','.').' m2'),

										Placeholder::make('Nama Petani')
											->inlineLabel()
											->content(fn ($record)=>$record->anggota->nama_petani),

										Placeholder::make('NIK Petani')
											->label('NIK')
											->inlineLabel()
											->content(fn ($record)=>$record->anggota->ktp_petani),

										Placeholder::make('Kelompok Tani')
											->inlineLabel()
											->content(fn ($record)=>$record->poktan->nama_kelompok),
									]),
								Group::make()
									->extraAttributes(['class'=>'mb-5'])
									->schema([
										Placeholder::make('Wilayah')
											->inlineLabel()
											->content(fn ($record)=>$record->spatial->kabupaten->nama_kab . ' - ' .$record->spatial->provinsi->nama),

										// Placeholder::make('Latitude')
										// 	->inlineLabel()
										// 	->content(fn ($record)=>$record->spatial->latitude . ' LU'),

										// Placeholder::make('Longitude')
										// 	->inlineLabel()
										// 	->content(fn ($record)=>$record->spatial->longitude . ' BT'),
									]),

								Group::make()
									->extraAttributes(['class'=>'mb-5'])
									->schema([
										Placeholder::make('Unduh Peta')
											->inlineLabel()
											->content(function ($record) {
												if ($record->spatial && $record->spatial->kml_url) {
													// Gunakan asset() untuk mendapatkan URL yang benar
													$url = asset('storage/' . $record->spatial->kml_url);
													return new HtmlString('<a href="'.$url.'" rel="noreferer nofollow" download><span class="font-bold text-info-500">'.$record->kode_spatial.'</span></a>');
												}
												return new HtmlString('<span class="text-gray-500">File tidak tersedia</span>');
											}),
									]),
								Placeholder::make('vt_status')
									->hiddenLabel()
									->content(fn ($get) => view('components.status-badge-verifikasi', ['status' => $get('vt_status')])),
							])
					])->columns(3),

				Section::make('Riwayat Kegiatan')
					->schema([
						TableRepeater::make('Kegiatan di Lahan')
							->hiddenLabel()
							->relationship('detailrealisasi')
							->reorderable(false)
							->addable(false)
							->deletable(false)
							->headers([
								Header::make('Tahap'),
								Header::make('Tanggal'),
								Header::make('Realisasi'),
								// Header::make('Diperiksa'),
							])
							->schema([
								Placeholder::make('desc_keg')
									->hiddenLabel()
									->visible(function ($get, $record){
										$noPengajuan = $this->noPengajuan;
										$pengajuan = PengajuanVerifikasi::where('no_pengajuan', $noPengajuan)->first();
										$kind = $pengajuan->kind;
										$jenisKeg = $record->jenis_keg;
										if ($kind === 'PVT' && ($jenisKeg === 'panen' || $jenisKeg === 'distribusi'))
										{
											return false;
										}
										return true;
									})
									->content(fn($record)=>$record->desc_keg),
								Placeholder::make('tgl_keg')->hiddenLabel()
									->visible(function ($get, $record){
										$noPengajuan = $this->noPengajuan;
										$pengajuan = PengajuanVerifikasi::where('no_pengajuan', $noPengajuan)->first();
										$kind = $pengajuan->kind;
										$jenisKeg = $record->jenis_keg;
										if ($kind === 'PVT' && ($jenisKeg === 'panen' || $jenisKeg === 'distribusi'))
										{
											return false;
										}
										return true;
									})
									->content(fn ($record) => Carbon::parse($record->tgl_keg)->translatedFormat('d F Y')),
								Placeholder::make('value')
									->hiddenLabel()
									->visible(function ($get, $record){
										$noPengajuan = $this->noPengajuan;
										$pengajuan = PengajuanVerifikasi::where('no_pengajuan', $noPengajuan)->first();
										$kind = $pengajuan->kind;
										$jenisKeg = $record->jenis_keg;
										if ($kind === 'PVT' && ($jenisKeg === 'panen' || $jenisKeg === 'distribusi'))
										{
											return false;
										}
										return true;
									})
									->content(function ($record) {
										if (!$record) return null; // Menghindari error jika $record kosong

										switch ($record->jenis_keg) {
											case 'pupuk':
												$output = collect([
													'Organik' => $record->organik,
													'NPK' => $record->npk,
													'Dolomit' => $record->dolomit,
													'ZA' => $record->za,
												])
												->filter() // Hapus nilai null/kosong
												->map(fn($value, $key) => "<li><strong>{$key}:</strong> " . number_format($value, 0, ',', '.') . " kg</li>")
												->implode('');

												return new HtmlString("<ul class='list-disc ml-4'>{$output}</ul>");

											case 'mulsa':
												return new HtmlString(number_format($record->value, 0, ',', '.') . ' roll');

											case 'tanam':
												return new HtmlString(number_format($record->value, 0, ',', '.') . ' m2');

											case 'distribusi':
												$output = collect([
													'Disimpan (benih)' => $record->dist_benih,
													'Dijual' => $record->dist_jual,
												])
												->filter()
												->map(fn($value, $key) => "<li><strong>{$key}:</strong> " . number_format($value, 0, ',', '.') . " kg</li>")
												->implode('');

												return new HtmlString("<ul class='list-disc ml-4'>{$output}</ul>");

											default:
												if (in_array($record->jenis_keg, ['lahan', 'opt'])) {
													return null;
												}
												return new HtmlString(number_format($record->value, 0, ',', '.') . ' kg');
										}
									}),
								// Checkbox::make('status')
								// 	->inline()
								// 	->visible(function ($get, $record){
								// 		$noPengajuan = $this->noPengajuan;
								// 		$pengajuan = PengajuanVerifikasi::where('no_pengajuan', $noPengajuan)->first();
								// 		$kind = $pengajuan->kind;
								// 		$jenisKeg = $record->jenis_keg;
								// 		if ($kind === 'PVT' && ($jenisKeg === 'panen' || $jenisKeg === 'distribusi'))
								// 		{
								// 			return false;
								// 		}
								// 		return true;
								// 	})
							]),
					]),

				Section::make('Verifikasi')
					->aside()
					->description('Hasil verifikasi yang dilakukan')
					->visible()
					->schema([
						Fieldset::make('Verifikasi Tanam')
							->visible()
							->columnSpan(1)
							->schema([
								DatePicker::make('vt_at')
									->label('Tanggal')
									->inlineLabel()
									->required()
									->columnSpanFull(),
								Select::make('vt_status')
									->label('Hasil Verifikasi')
									->inlineLabel()
									->required()
									->reactive()
									->columnSpanFull()
									->options([
										'Sesuai' => 'Sesuai',
										'Tidak Sesuai' => 'Tidak Sesuai'
									]),
								Textarea::make('vt_note')
									->label('Catatan Verifikasi')
									->required(fn ($get)=>$get('vt_status') === 'Tidak Sesuai')
									->inlineLabel()
									->columnSpanFull()
									->autosize(),
							]),

						Fieldset::make('Verifikasi Produksi')
							->visible(function () {
								$noPengajuan = $this->noPengajuan;
								$pengajuan = PengajuanVerifikasi::where('no_pengajuan', $noPengajuan)->first();
								if($pengajuan->kind === 'PVP')
								{
									return true;
								}
								return false;
							})
							->columnSpan(1)
							->schema([
								DatePicker::make('vp_at')
									->label('Tanggal')
									->inlineLabel()
									->required()
									->columnSpanFull(),
								Select::make('vp_status')
									->label('Hasil Verifikasi')
									->inlineLabel()
									->columnSpanFull()
									->required()
									->reactive()
									->options([
										'Sesuai' => 'Sesuai',
										'Tidak Sesuai' => 'Tidak Sesuai'
									]),
								Textarea::make('vp_note')
									->label('Catatan Verifikasi')
									->required(fn ($get)=>$get('vp_status') === 'Tidak Sesuai')
									->inlineLabel()
									->columnSpanFull()
									->autosize(),
							])
					]),
			]);
	}

	// Tambahkan metode untuk menangani event Livewire
	public function refreshMap(): void
	{
		// Metode ini akan dipanggil oleh Livewire setelah repeater diperbarui
		// Emit event untuk me-refresh peta
		$record = $this->getRecord();
		if ($record && $record->spatial) {
			$spatialData = $record->spatial->toArray();
			if (!isset($spatialData['id'])) {
				$spatialData['id'] = $record->id;
			}
			$this->dispatch('refreshMap', $spatialData);
		}
	}

	protected function handleRecordUpdate(Model $record, array $data): Model
	{
		$noPengajuan = $this->noPengajuan;
		$pengajuan = PengajuanVerifikasi::where('no_pengajuan', $noPengajuan)->first();
		$jenis = $pengajuan->kind;
		if($jenis === 'PVT'){
			$record->vt_at = $data['vt_at'];
			$record->vt_by = Auth::user()->id;
			$record->vt_note = $data['vt_note'];
			$record->vt_status = $data['vt_status'];
		}

		if($jenis === 'PVP')
		{
			$record->vp_at = $data['vp_at'];
			$record->vp_by = Auth::user()->id;
			$record->vp_note = $data['vp_note'];
			$record->vp_status = $data['vp_status'];
		}

		$record->update($data);

		// Refresh peta setelah update
		$this->refreshMap();

		return $record;
	}

}
