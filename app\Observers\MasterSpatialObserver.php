<?php

namespace App\Observers;

use App\Models\MasterSpatial;
use Illuminate\Support\Facades\Log;

class MasterSpatialObserver
{
    /**
     * Handle the MasterSpatial "created" event.
     */
    public function created(MasterSpatial $masterSpatial): void
    {
        //
    }

    /**
     * Handle the MasterSpatial "updated" event.
     */
    public function updated(MasterSpatial $masterSpatial): void
    {
        //
    }

    /**
     * Handle the MasterSpatial "deleted" event.
     */
    public function deleted(MasterSpatial $masterSpatial): void
    {
        //
    }

    /**
     * Handle the MasterSpatial "restored" event.
     */
    public function restored(MasterSpatial $masterSpatial): void
    {
        //
    }

    /**
     * Handle the MasterSpatial "force deleted" event.
     */
    public function forceDeleted(MasterSpatial $masterSpatial): void
    {
        //
    }
}
