<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\MobileApp;
use App\Models\User;

class MobileAppPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any MobileApp');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, MobileApp $mobileapp): bool
    {
        return $user->checkPermissionTo('view MobileApp');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create MobileApp');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, MobileApp $mobileapp): bool
    {
        return $user->checkPermissionTo('update MobileApp');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, MobileApp $mobileapp): bool
    {
        return $user->checkPermissionTo('delete MobileApp');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any MobileApp');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, MobileApp $mobileapp): bool
    {
        return $user->checkPermissionTo('restore MobileApp');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any MobileApp');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, MobileApp $mobileapp): bool
    {
        return $user->checkPermissionTo('replicate MobileApp');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder MobileApp');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, MobileApp $mobileapp): bool
    {
        return $user->checkPermissionTo('force-delete MobileApp');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any MobileApp');
    }
}
