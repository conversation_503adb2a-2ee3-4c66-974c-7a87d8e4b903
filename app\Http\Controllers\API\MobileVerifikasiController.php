<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\PengajuanVerifikasi;
use App\Models\VerificatorAssignment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class MobileVerifikasiController extends Controller
{
    /**
     * Get active verifications for the logged-in user
     */
    public function getActiveVerifications()
    {
        $user = Auth::user();

        // Check if user is importir
        if (!$user->hasRole('importir')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        // Get active verifications for the user
        // For PVT/PVP, active statuses are 0-3
        // For PVS, active statuses are 0-7 (excluding 8 which is completed/lunas)
        $verifications = PengajuanVerifikasi::where('npwp', $user->npwp)
            ->where(function($query) {
                $query->where(function($q) {
                    // For PVT/PVP, only show status 0-3
                    $q->whereIn('kind', ['PVT', 'PVP'])
                      ->whereIn('status', ['0', '1', '2', '3']);
                })->orWhere(function($q) {
                    // For PVS, show status 0-7 (excluding 8 which is completed/lunas)
                    $q->where('kind', 'PVS')
                      ->whereIn('status', ['0', '1', '2', '3', '4', '5', '6', '7']);
                });
            })
            ->orderBy('created_at', 'desc')
            ->get();

        // Transform the data for mobile display
        $result = $verifications->map(function ($verification) {
            $kindMap = [
                'PVT' => 'Verifikasi Tanam',
                'PVP' => 'Verifikasi Produksi',
                'PVS' => 'Penerbitan SKL',
            ];

            // Base status map for all verification types
            $baseStatusMap = [
                '0' => 'Diajukan',
                '1' => 'Penugasan Verifikator',
                '2' => 'Penetapan Verifikator',
                '3' => 'Proses Verifikasi',
            ];

            // Status map specific to verification type
            if ($verification->kind === 'PVS') {
                $statusMap = array_merge($baseStatusMap, [
                    '4' => 'Direkomendasikan',
                    '5' => 'Perbaikan',
                    '6' => 'Ditolak',
                    '7' => 'Disetujui',
                    '8' => 'Diterbitkan/Lunas',
                ]);
            } else {
                // For PVT and PVP
                $statusMap = array_merge($baseStatusMap, [
                    '4' => 'Selesai',
                    '5' => 'Perbaikan',
                ]);
            }

            // Calculate progress percentage based on status and kind
            if ($verification->kind === 'PVS') {
                $progressMap = [
                    '0' => 10,  // Diajukan
                    '1' => 20,  // Penugasan Verifikator
                    '2' => 30,  // Penetapan Verifikator
                    '3' => 40,  // Proses Verifikasi
                    '4' => 60,  // Direkomendasikan
                    '5' => 50,  // Perbaikan
                    '6' => 70,  // Ditolak
                    '7' => 90,  // Disetujui
                    '8' => 100, // Diterbitkan/Lunas
                ];
            } else {
                // For PVT and PVP
                $progressMap = [
                    '0' => 20,  // Diajukan
                    '1' => 40,  // Penugasan Verifikator
                    '2' => 60,  // Penetapan Verifikator
                    '3' => 80,  // Proses Verifikasi
                    '4' => 100, // Selesai
                    '5' => 90,  // Perbaikan
                ];
            }

            // Get verifikator name if assigned
            $verifikatorName = null;
            if ($verification->status >= '1') {
                $assignment = VerificatorAssignment::where('pengajuan_id', $verification->id)
                    ->with('user')
                    ->first();

                if ($assignment && $assignment->user) {
                    $verifikatorName = $assignment->user->name;
                }
            }

            return [
                'id' => $verification->id,
                'no_pengajuan' => $verification->no_pengajuan,
                'kind' => $verification->kind,
                'kind_text' => $kindMap[$verification->kind] ?? 'Tidak Diketahui',
                'status' => $verification->status,
                'status_text' => $statusMap[$verification->status] ?? 'Tidak Diketahui',
                'progress' => $progressMap[$verification->status] ?? 0,
                'created_at' => $verification->created_at->format('Y-m-d H:i:s'),
                'created_at_formatted' => $verification->created_at->format('d F Y'),
                'verifikator' => $verifikatorName,
                'note' => $verification->note,
            ];
        });

        return response()->json([
            'success' => true,
            'message' => 'Active verifications retrieved successfully',
            'data' => $result
        ]);
    }

    /**
     * Get verification detail by ID
     */
    public function getVerificationDetail($id)
    {
        $user = Auth::user();

        // Check if user is importir
        if (!$user->hasRole('importir')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        // Get verification detail
        $verification = PengajuanVerifikasi::where('id', $id)
            ->where('npwp', $user->npwp)
            ->first();

        if (!$verification) {
            return response()->json([
                'success' => false,
                'message' => 'Verification not found'
            ], 404);
        }

        // Get assignment information
        $assignment = VerificatorAssignment::where('pengajuan_id', $verification->id)
            ->with('user')
            ->first();

        $kindMap = [
            'PVT' => 'Verifikasi Tanam',
            'PVP' => 'Verifikasi Produksi',
            'PVS' => 'Penerbitan SKL',
        ];

        // Base status map for all verification types
        $baseStatusMap = [
            '0' => 'Diajukan',
            '1' => 'Penugasan Verifikator',
            '2' => 'Penetapan Verifikator',
            '3' => 'Proses Verifikasi',
        ];

        // Status map specific to verification type
        if ($verification->kind === 'PVS') {
            $statusMap = array_merge($baseStatusMap, [
                '4' => 'Direkomendasikan',
                '5' => 'Perbaikan',
                '6' => 'Ditolak',
                '7' => 'Disetujui',
                '8' => 'Diterbitkan/Lunas',
            ]);
        } else {
            // For PVT and PVP
            $statusMap = array_merge($baseStatusMap, [
                '4' => 'Selesai',
                '5' => 'Perbaikan',
            ]);
        }

        // Calculate progress percentage based on status and kind
        if ($verification->kind === 'PVS') {
            $progressMap = [
                '0' => 10,  // Diajukan
                '1' => 20,  // Penugasan Verifikator
                '2' => 30,  // Penetapan Verifikator
                '3' => 40,  // Proses Verifikasi
                '4' => 60,  // Direkomendasikan
                '5' => 50,  // Perbaikan
                '6' => 70,  // Ditolak
                '7' => 90,  // Disetujui
                '8' => 100, // Diterbitkan/Lunas
            ];
        } else {
            // For PVT and PVP
            $progressMap = [
                '0' => 20,  // Diajukan
                '1' => 40,  // Penugasan Verifikator
                '2' => 60,  // Penetapan Verifikator
                '3' => 80,  // Proses Verifikasi
                '4' => 100, // Selesai
                '5' => 90,  // Perbaikan
            ];
        }

        // Format the response
        $result = [
            'id' => $verification->id,
            'no_pengajuan' => $verification->no_pengajuan,
            'no_ijin' => $verification->no_ijin,
            'kind' => $verification->kind,
            'kind_text' => $kindMap[$verification->kind] ?? 'Tidak Diketahui',
            'status' => $verification->status,
            'status_text' => $statusMap[$verification->status] ?? 'Tidak Diketahui',
            'progress' => $progressMap[$verification->status] ?? 0,
            'created_at' => $verification->created_at->format('Y-m-d H:i:s'),
            'created_at_formatted' => $verification->created_at->format('d F Y'),
            'note' => $verification->note,
            'verif_at' => $verification->verif_at ? date('Y-m-d H:i:s', strtotime($verification->verif_at)) : null,
            'verif_at_formatted' => $verification->verif_at ? date('d F Y', strtotime($verification->verif_at)) : null,
            'metode' => $verification->metode,
            'assignment' => $assignment ? [
                'id' => $assignment->id,
                'verifikator_id' => $assignment->user_id,
                'verifikator_name' => $assignment->user->name ?? null,
                'no_sk' => $assignment->no_sk,
                'tgl_sk' => $assignment->tgl_sk ? date('Y-m-d', strtotime($assignment->tgl_sk)) : null,
                'tgl_sk_formatted' => $assignment->tgl_sk ? date('d F Y', strtotime($assignment->tgl_sk)) : null,
                'note' => $assignment->myNote,
            ] : null,
            'files' => [
                'berita_acara' => $verification->fileBa,
                'nota_dinas' => $verification->fileNdhp,
                'laporan' => $verification->report_url,
            ],
        ];

        return response()->json([
            'success' => true,
            'message' => 'Verification detail retrieved successfully',
            'data' => $result
        ]);
    }
}
