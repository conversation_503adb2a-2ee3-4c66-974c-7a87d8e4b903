<?php

namespace App\Filament\Panel2025\Resources;

use App\Filament\Panel2025\Resources\Pks2025Resource\Pages;
use App\Filament\Panel2025\Resources\Pks2025Resource\RelationManagers;
use App\Models\Pks2025;
use App\Models\Varietas;
use Filament\Forms;
use Filament\Forms\Components\{DatePicker, FileUpload, Placeholder, Radio, Section, Select, Textarea, TextInput};
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\{Action as TableAction, BulkActionGroup,EditAction,ViewAction, };
use Filament\Tables\Columns\{ColumnGroup,TextColumn};
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\{Builder,SoftDeletingScope};
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class Pks2025Resource extends Resource
{
	protected static ?string $model = Pks2025::class;
	protected static ?string $pluralModelLabel = 'PKS';
	protected static ?int $navigationSort = 2;

	public static function getNavigationLabel(): string
	{
		$navLabel = 'Daftar PKS';
		if(Auth::user()->hasAnyRole(['dinas']))
		{
			$navLabel = 'Verifikasi PKS';
		}
		return $navLabel;
	}
	protected static ?string $navigationIcon = 'heroicon-o-bookmark';

	public static function getNavigationBadge(): ?string
	{
		if(static::getModel()::where('status_dinas', '0')->count() > 0)
		{
			return static::getModel()::where('status_dinas', '0')->count();
		}
		return '';
	}

	public static function getNavigationBadgeColor(): ?string
	{
		return 'warning';
	}

	public static function form(Form $form): Form
	{
		return $form
			->schema([
				Section::make('Berkas PKS')
					->aside()
					->description('Unggah berkas PKS  (Wajib)')
					->schema([
						FileUpload::make('berkas_pks')
							->openable()
							->hiddenLabel()
							->required(fn ($get) => $get('status_dinas') === '0') // Mandatory jika status_dinas = 0
							->maxSize(2048)
							->columnSpan(1)
							->downloadable()
							->deletable()
							->label('Berkas PKS')
							->visibility('public')
							->panelAspectRatio('5:1')
							->imagePreviewHeight('50')
							->fetchFileInformation(true)
							->helperText(fn ($get) => 
								$get('status_dinas') === '0' 
									? null
									: 'Maksimal 2MB, format PDF'
							)
							->disk('public')
							->directory(function ($record) {
								$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
								$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
								return "uploads/{$cleanNpwp}/{$cleanNoIjin}/dokumen/pks";
							})
							->rules([
								'file',
								'mimetypes:application/pdf',
								'mimes:pdf',
								'max:2048'
							])
							->validationMessages([
								'mimetypes' => 'Hanya file PDF yang diperbolehkan',
								'mimes' => 'Ekstensi file harus .pdf',
								'max' => 'Ukuran file maksimal 2MB',
								'required' => 'Berkas PKS wajib diunggah ketika status Final',
							])
							->getUploadedFileNameForStorageUsing(
								function (TemporaryUploadedFile $file, $get, $record): string {
									$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
									$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

									// Format nama file: [ID]_[NPWP]_[NOIJIN].[ext]
									return 'pks_'.$record->id . '_' . $cleanNpwp . '_' . $cleanNoIjin . '_'.uniqid() . '.' . $file->getClientOriginalExtension();
								}
							),
					]),

				Section::make('Data PKS')
					->aside()
					->description('Data Isian Detail PKS')
					->schema([
						Placeholder::make('Kelompoktani')
							->inlineLabel()
							->content(fn ($record)=>$record->nama_poktan),
						Placeholder::make('Jumlah Lokasi dipilih')
							->inlineLabel()
							->content(fn ($record)=>$record->jumlah_anggota .' titik'),
						Placeholder::make('Total Luas dipilih')
							->inlineLabel()
							->content(fn ($record)=> number_format($record->luas_rencana,0,',','.') . ' m2') ,
						TextInput::make('no_perjanjian')
							->required()
							->inlineLabel()
							->maxLength(255),
						DatePicker::make('tgl_perjanjian_start')
							->inlineLabel()
							->label('Tanggal Mulai')
							->closeOnDateSelection()
							->required(),
						DatePicker::make('tgl_perjanjian_end')
							->inlineLabel()
							->label('Tanggal Berakhir')
							->closeOnDateSelection()
							->required(),
						Select::make('varietas_tanam')
							->inlineLabel()
							->label('Varietas')
							->required()
							->options(fn () => Varietas::orderBy('nama_varietas')->pluck('nama_varietas', 'id'))
							->preload(),
						TextInput::make('periode_tanam')
							->required()
							->inlineLabel()
							->maxLength(255)
							->helperText('contoh: Januari - Agustus'),

						Radio::make('status_dinas')
							->label('Status Dokumen')
							->inlineLabel()
							->options([
								null => 'Draft',
								0 => 'Final',
							])
							->descriptions([
								null => 'Dokumen masih dalam keadaan DRAFT. Tidak akan terkirim ke Dinas Kabupaten terkait.',
								0 => 'Saat disimpan, Dokumen Dikirim langsung ke Dinas Kabupaten Terkait untuk dilakukan pemeriksaan.',
							]),
					]),
			])->columns(3);
	}

	public static function table(Table $table): Table
	{
		$isDinas = Auth::user()->hasRole('dinas');
		return $table
			->emptyStateHeading('Belum ada PKS diajukan')
			->poll('10s')
			->columns([
				TextColumn::make('index')
                    ->label('No')
					->rowIndex(),
				TextColumn::make('user.datauser.company_name')
					->label('Perusahaan')
					->searchable(),
				TextColumn::make('no_ijin')
					->searchable(),
				TextColumn::make('nama_poktan')
					->searchable(),

				ColumnGroup::make('Perjanjian',[
					TextColumn::make('no_perjanjian')
						->label('Nomor')
						->searchable(),
					TextColumn::make('tgl_perjanjian_start')
						->date()
						->label('Mulai')
						->sortable(),
					TextColumn::make('tgl_perjanjian_end')
						->date()
						->label('Akhir')
						->sortable(),
				]),

				TextColumn::make('jumlah_anggota')
					->label('Partisipan')
					->suffix(' orang')
					->numeric()
					->sortable(),
				TextColumn::make('luas_rencana')
					->numeric()
					->suffix(' m2')
					->label('Luas')
					->sortable(),
				TextColumn::make('periode_tanam')
					->searchable(),
				TextColumn::make('kecamatan.nama_kecamatan')
					->searchable(),
				TextColumn::make('desa.nama_desa')
					->searchable(),
				TextColumn::make('berkas_pks')
					->formatStateUsing(fn ($record) => new HtmlString(
						'<a class="font-bold text-info-500" href="' . asset($record->berkas_pks) . '" target="_blank" rel="nofollow noreferrer" download>Unduh</a>'
					)),
				TextColumn::make('status')
					->searchable(),
				TextColumn::make('verif_by')
					->numeric()
					->sortable(),
				TextColumn::make('verif_at')
					->date()
					->sortable(),
			])
			->filters([
				//
			])
			->actions([
				ViewAction::make()->hiddenLabel()->visible(fn ()=> Auth::user()->hasRole('importir')),
				EditAction::make()->hiddenLabel()
						->icon('icon-journal-bookmark-fill')->url(function ($record){
					$isDinas = Auth::user()->hasRole('dinas');
					$isImportir = Auth::user()->hasRole('importir');
					if($isDinas){
						return route('filament.panel2025.resources.pks2025s.verifdinas', ['record' => $record->id]);
					}
				}),
			])
			->bulkActions([
				BulkActionGroup::make([
					// DeleteBulkAction::make(),
				]),
			]);
	}

	public static function getRelations(): array
	{
		return [
			//
		];
	}

	public static function getPages(): array
	{
		return [
			'index' => Pages\ListPks2025s::route('/'),
			'create' => Pages\CreatePks2025::route('/create'),
			'view' => Pages\ViewPks2025::route('/{record}'),
			'edit' => Pages\EditPks2025::route('/{record}/edit'),
			'verifdinas' => Pages\DinasEditPks2025::route('/{record}/verifikasi-dinas'),
			'verifpusat' => Pages\VerifPks2025::route('/{noijin}/verifikasi-pks'),
			'verifpks' => Pages\PusatEditPks2025::route('/{record}/verifikasi'),
		];
	}

	public static function shouldRegisterNavigation(): bool
	{
		$user = Auth::user();
		if ($user->hasRole('dinas')) {
			return true;
		}
		return false;
	}
}
