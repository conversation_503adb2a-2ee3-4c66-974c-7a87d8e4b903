<?php

namespace App\Models;

use \DateTimeInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class DataUser extends Model
{
	use SoftDeletes, LogsActivity;
	public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
			->logFillable('*')
			->logOnlyDirty();
    }

	public $table = 'data_users';

	protected $dates = [
		'created_at',
		'updated_at',
		'deleted_at',
	];

	// di table lama tidak ada penanggungjawab
	//di table lama npwp = npwp_company
	protected $fillable = [
		'user_id',
		'name',
		'email',
		'mobile_phone',
		'fix_phone',
		'company_name',
		'pic_name',
        'penanggungjawab',
		'jabatan',
		'npwp_company',
		'nib_company',
		'address_company',
		'provinsi',
		'kabupaten',
		'kecamatan',
		'desa',
		'kodepos',
		'fax',
		'ktp',
		'ktp_image',
		'assignment',
		'avatar',
		'logo',
		'email_company',
		'created_at',
		'updated_at',
		'deleted_at',
	];

	protected static function booted()
	{
		static::addGlobalScope('npwp', function (Builder $builder) {
			if (Auth::check()) {
				$user = Auth::user();

				if ($user->hasAnyRole(['admin', 'direktur', 'Super Admin', 'verifikator','dinas'])) {
					$builder->where('id', '!=', 3);
				}
				else {
					$builder->where('npwp_company', $user->npwp);
				}
			}
		});
	}

	protected function serializeDate(DateTimeInterface $date)
	{
		return $date->format('Y-m-d H:i:s');
	}

    public function user():BelongsTo
    {
        return $this->belongsTo(User::class);
    }
    public function myprovinsi():BelongsTo
    {
        return $this->belongsTo(MasterProvinsi::class, 'provinsi', 'provinsi_id');
    }
    public function mykabupaten():BelongsTo
    {
        return $this->belongsTo(MasterKabupaten::class, 'kabupaten', 'kabupaten_id');
    }
    public function mykecamatan():BelongsTo
    {
        return $this->belongsTo(MasterKecamatan::class, 'kecamatan', 'kecamatan_id');
    }
    public function mydesa():BelongsTo
    {
        return $this->belongsTo(MasterDesa::class, 'desa', 'kelurahan_id');
    }
}
