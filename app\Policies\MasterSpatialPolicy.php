<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\MasterSpatial;
use App\Models\User;

class MasterSpatialPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any MasterSpatial');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, MasterSpatial $masterspatial): bool
    {
        return $user->checkPermissionTo('view MasterSpatial');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create MasterSpatial');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, MasterSpatial $masterspatial): bool
    {
        return $user->checkPermissionTo('update MasterSpatial');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, MasterSpatial $masterspatial): bool
    {
        return $user->checkPermissionTo('delete MasterSpatial');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any MasterSpatial');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, MasterSpatial $masterspatial): bool
    {
        return $user->checkPermissionTo('restore MasterSpatial');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any MasterSpatial');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, MasterSpatial $masterspatial): bool
    {
        return $user->checkPermissionTo('replicate MasterSpatial');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder MasterSpatial');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, MasterSpatial $masterspatial): bool
    {
        return $user->checkPermissionTo('force-delete MasterSpatial');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any MasterSpatial');
    }
}
