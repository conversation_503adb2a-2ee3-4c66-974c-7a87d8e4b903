<?php

namespace App\Filament\Admin\Pages;

use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;
use League\CommonMark\Environment\Environment;
use League\CommonMark\Extension\GithubFlavoredMarkdownExtension;
use League\CommonMark\Extension\CommonMark\CommonMarkCoreExtension;
use League\CommonMark\MarkdownConverter;

class TermsAndConditions extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';
    protected static ?string $navigationLabel = 'Syarat dan Ketentuan';
    protected static ?string $title = 'Syarat dan Ketentuan';
    protected static ?string $navigationGroup = 'Documentations';
    protected static ?int $navigationSort = 6;

    public static function shouldRegisterNavigation(): bool
    {
        return true; // Visible to all users
    }

    protected static string $view = 'filament.admin.pages.terms-and-conditions';

    public function getViewData(): array
    {
        $markdownPath = base_path('documentations/terms-and-conditions.md');

        if (!file_exists($markdownPath)) {
            return [
                'termsAndConditionsHtml' => '<div class="text-red-500">Terms and Conditions file not found. Please make sure the file exists at: ' . $markdownPath . '</div>',
            ];
        }

        $markdownContent = file_get_contents($markdownPath);

        if (empty($markdownContent)) {
            return [
                'termsAndConditionsHtml' => '<div class="text-red-500">Terms and Conditions file is empty.</div>',
            ];
        }

        // Create a new environment with GFM extension
        $environment = new Environment([
            'html_input' => 'strip',
            'allow_unsafe_links' => false,
        ]);

        // Add the CommonMark core extension and GFM extension
        $environment->addExtension(new CommonMarkCoreExtension());
        $environment->addExtension(new GithubFlavoredMarkdownExtension());

        // Create a new converter using the configured environment
        $converter = new MarkdownConverter($environment);

        // Convert the markdown to HTML
        $result = $converter->convert($markdownContent);
        $html = $result->getContent();

        return [
            'termsAndConditionsHtml' => $html,
        ];
    }
}
