<?php

namespace Database\Seeders;

use App\Filament\Admin\Resources\Shield\RoleResource;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $roles = ["Super Admin", "admin", "direktur", "verifikator", "spatial", "dinas","importir", "tamu"];

        foreach ($roles as $key => $role) {
            $roleCreated = (new (Role::getModel()))->create(
                [
                    'name' => $role,
                    'guard_name' => 'web',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );
        }
    }
}
