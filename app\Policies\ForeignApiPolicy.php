<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\ForeignApi;
use App\Models\User;

class ForeignApiPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any ForeignApi');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ForeignApi $foreignapi): bool
    {
        return $user->checkPermissionTo('view ForeignApi');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create ForeignApi');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ForeignApi $foreignapi): bool
    {
        return $user->checkPermissionTo('update ForeignApi');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ForeignApi $foreignapi): bool
    {
        return $user->checkPermissionTo('delete ForeignApi');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any ForeignApi');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, ForeignApi $foreignapi): bool
    {
        return $user->checkPermissionTo('restore ForeignApi');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any ForeignApi');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, ForeignApi $foreignapi): bool
    {
        return $user->checkPermissionTo('replicate ForeignApi');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder ForeignApi');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, ForeignApi $foreignapi): bool
    {
        return $user->checkPermissionTo('force-delete ForeignApi');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any ForeignApi');
    }
}
