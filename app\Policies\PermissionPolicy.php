<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\Response;
use Spatie\Permission\Models\Permission;

class PermissionPolicy
{
    /**
     * Determine whether the permission can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any Permission');
    }

    /**
     * Determine whether the permission can view the model.
     */
    public function view(User $user, Permission $model): bool
    {
        return $user->checkPermissionTo('view Permission');
    }

    /**
     * Determine whether the permission can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create Permission');
    }

    /**
     * Determine whether the permission can update the model.
     */
    public function update(User $user, Permission $model): bool
    {
        return $user->checkPermissionTo('update Permission');
    }

    /**
     * Determine whether the permission can delete the model.
     */
    public function delete(User $user, Permission $model): bool
    {
        return $user->checkPermissionTo('delete Permission');
    }

    /**
     * Determine whether the permission can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any Permission');
    }

    /**
     * Determine whether the permission can restore the model.
     */
    public function restore(User $user, Permission $model): bool
    {
        return $user->checkPermissionTo('restore Permission');
    }

    /**
     * Determine whether the permission can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any Permission');
    }

    /**
     * Determine whether the permission can replicate the model.
     */
    public function replicate(User $user, Permission $model): bool
    {
        return $user->checkPermissionTo('replicate Permission');
    }

    /**
     * Determine whether the permission can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder Permission');
    }

    /**
     * Determine whether the permission can permanently delete the model.
     */
    public function forceDelete(User $user, Permission $model): bool
    {
        return $user->checkPermissionTo('force-delete Permission');
    }

    /**
     * Determine whether the permission can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any Permission');
    }
}
