<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\Realisasi2025;
use App\Models\User;

class Realisasi2025Policy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any Realisasi2025');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Realisasi2025 $realisasi2025): bool
    {
        return $user->checkPermissionTo('view Realisasi2025');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create Realisasi2025');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Realisasi2025 $realisasi2025): bool
    {
        return $user->checkPermissionTo('update Realisasi2025');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Realisasi2025 $realisasi2025): bool
    {
        return $user->checkPermissionTo('delete Realisasi2025');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any Realisasi2025');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Realisasi2025 $realisasi2025): bool
    {
        return $user->checkPermissionTo('restore Realisasi2025');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any Realisasi2025');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, Realisasi2025 $realisasi2025): bool
    {
        return $user->checkPermissionTo('replicate Realisasi2025');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder Realisasi2025');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Realisasi2025 $realisasi2025): bool
    {
        return $user->checkPermissionTo('force-delete Realisasi2025');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any Realisasi2025');
    }
}
