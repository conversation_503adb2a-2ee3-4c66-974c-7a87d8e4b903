<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\BotLog;
use App\Models\User;

class BotLogPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any BotLog');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, BotLog $botlog): bool
    {
        return $user->checkPermissionTo('view BotLog');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create BotLog');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, BotLog $botlog): bool
    {
        return $user->checkPermissionTo('update BotLog');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, BotLog $botlog): bool
    {
        return $user->checkPermissionTo('delete BotLog');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any BotLog');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, BotLog $botlog): bool
    {
        return $user->checkPermissionTo('restore BotLog');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any BotLog');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, BotLog $botlog): bool
    {
        return $user->checkPermissionTo('replicate BotLog');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder BotLog');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, BotLog $botlog): bool
    {
        return $user->checkPermissionTo('force-delete BotLog');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any BotLog');
    }
}
