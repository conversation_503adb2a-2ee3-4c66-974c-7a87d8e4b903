<?php

namespace App\Filament\Panel2025\Resources;

use App\Filament\Panel2025\Resources\Penangkar2025Resource\Pages;
use App\Filament\Admin\Resources\Penangkar2025Resource\RelationManagers;
use App\Models\Penangkar2025;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class Penangkar2025Resource extends Resource
{
    protected static ?string $model = Penangkar2025::class;
    protected static bool $shouldRegisterNavigation = false;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('nama_penangkar')
                    ->maxLength(255),
                Forms\Components\TextInput::make('nama_pimpinan')
                    ->maxLength(255),
                Forms\Components\TextInput::make('hp_pimpinan')
                    ->maxLength(255),
                Forms\Components\Textarea::make('alamat')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('npwp')
                    ->maxLength(50),
                Forms\Components\TextInput::make('no_ijin')
                    ->maxLength(255),
                Forms\Components\TextInput::make('varietas')
                    ->maxLength(255),
                Forms\Components\TextInput::make('ketersediaan')
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nama_penangkar')
                    ->searchable(),
                Tables\Columns\TextColumn::make('nama_pimpinan')
                    ->searchable(),
                Tables\Columns\TextColumn::make('hp_pimpinan')
                    ->searchable(),
                Tables\Columns\TextColumn::make('npwp')
                    ->searchable(),
                Tables\Columns\TextColumn::make('no_ijin')
                    ->searchable(),
                Tables\Columns\TextColumn::make('varietas')
                    ->searchable(),
                Tables\Columns\TextColumn::make('ketersediaan')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPenangkar2025s::route('/'),
            'create' => Pages\CreatePenangkar2025::route('/create'),
            'view' => Pages\ViewPenangkar2025::route('/{record}'),
            'edit' => Pages\EditPenangkar2025::route('/{record}/edit'),
        ];
    }
}
