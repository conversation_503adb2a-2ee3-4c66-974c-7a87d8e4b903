<?php

namespace App\Filament\Panel2024\Resources\DataRealisasi2024Resource\Pages;

use App\Filament\Panel2024\Resources\DataRealisasi2024Resource;
use App\Models\Commitment2024;
use Filament\Actions\Action as ActionsAction;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;
use Filament\Forms\Components\{Actions, DatePicker, Fieldset, FileUpload, Group, Placeholder, Repeater, Section, Textarea, TextInput};
use Filament\Forms\Components\Actions\Action;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Form;
use Illuminate\Support\HtmlString;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class FotoTanam extends EditRecord
{
    protected static string $resource = DataRealisasi2024Resource::class;
	protected static ?string $title = 'Foto Tanam';
    public function getHeading(): string
	{
        // $noPks = $this->record ? $this->record->masterpoktan->nama_kelompok : '#';
        return 'Foto Tanam';
	}
    public function getSubheading(): ?string
    {
        $realisasi = $this->record ? $this->record->nama_lokasi. ' / ' .  $this->record->no_ijin : '##';
        return 'Lokasi: ' . $realisasi;
    }

    public static string | Alignment $formActionsAlignment = Alignment::Right;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\ViewAction::make(),
            // Actions\DeleteAction::make(),
        ];
    }

	protected function getFormActions(): array
	{
		return [
			$this->getSaveFormAction(),
			ActionsAction::make('back')
            ->label('Kembali')
            ->color('success')
            ->url(fn () => route('filament.panel2024.resources.pks2024s.edit', $this->record->pks->id)),
			$this->getCancelFormAction(),
		];
	}

	public function form(Form $form): Form
    {
        return $form
            ->schema([
				Section::make('Data Lokasi')
					->columns(3)
					->schema([
						Placeholder::make('nama_lokasi')
							->inlineLabel()
							->content(fn ($record) => new HtmlString('<span class="font-bold">'.$record->nama_lokasi.'</span>') ),
						Placeholder::make('nama_anggota')
							->inlineLabel()
							->content(fn ($record) => new HtmlString('<span class="font-bold">'.$record->masteranggota->nama_petani.'</span>') ),
						Placeholder::make('nama_kelompok')
							->inlineLabel()
							->content(fn ($record) => new HtmlString('<span class="font-bold">'.$record->masterkelompok->nama_kelompok.'</span>') ),
						Placeholder::make('luas_kira')
							->inlineLabel()
							->label('Luas Lahan')
							->content(fn ($record) => new HtmlString('<span class="font-bold">'.
								number_format($record->luas_kira,3,',','.')
								.' ha</span>')
							),
						Placeholder::make('mulai_tanam')
							->inlineLabel()
							->content(fn ($record): string => \Carbon\Carbon::parse($record->mulai_tanam)->toFormattedDateString()),
						Placeholder::make('akhir_tanam')
							->inlineLabel()
							->content(fn ($record): string => \Carbon\Carbon::parse($record->akhir_tanam)->toFormattedDateString()),
					]),

				Section::make()
					->schema([
						Repeater::make('unggahFoto')
							->hiddenLabel()
							->grid(3)
							->relationship('fototanam')
							->maxItems(3)
							->schema([
								FileUpload::make('url')
									->openable()
									->imageEditor()
									->required()
									->hiddenLabel()
									->live()
									->maxSize(2048)
									->downloadable()
									->deletable()
									->visibility('public')
									->panelAspectRatio('1:1')
									->fetchFileInformation(true)
									->helperText('Maksimal 2MB, format gambar')
									->disk('public')
									->rules([
										'mimetypes:image/jpeg,image/png',
										'mimes:jpg,jpeg,png',
									])
									->validationMessages([
										'mimetypes' => 'Hanya file gambar (JPG, JPEG, PNG) yang diperbolehkan',
										'mimes' => 'Ekstensi file harus .jpg, .jpeg atau .png',
									])
									->directory(function ($get) {
										$npwp = $get('../../npwp_company');
										$noIjin = $get('../../no_ijin');
										$commitment = Commitment2024::where('no_ijin', $noIjin)->select('periodetahun')->first();
										$tahun = $commitment->periodetahun;
										$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $npwp);
										return "uploads/{$cleanNpwp}/{$tahun}";
									})
									->getUploadedFileNameForStorageUsing(
										function (TemporaryUploadedFile $file, $get, $set): string {
											$id = $get('../../id');
											$noIjin = $get('../../no_ijin');
											$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $noIjin);
											$filename = 'foto_tanam_'. $id . '_' .$cleanNoIjin . '_'.uniqid().'.' . $file->getClientOriginalExtension();
											return $filename;
										}
									)
							])
					])
            ]);
    }
}
