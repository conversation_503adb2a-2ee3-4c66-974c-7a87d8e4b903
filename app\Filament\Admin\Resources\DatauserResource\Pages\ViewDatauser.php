<?php

namespace App\Filament\Admin\Resources\DatauserResource\Pages;

use App\Filament\Admin\Resources\DatauserResource;
use App\Models\MasterKabupaten;
use App\Models\MasterProvinsi;
use Filament\Actions;
use Filament\Forms\Components\{Fieldset, Section, Select, TextInput};
use Filament\Forms\Form;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Auth;

class ViewDatauser extends ViewRecord
{
    protected static string $resource = DatauserResource::class;
    protected static ?string $title = 'Profil Saya';

    public function getHeading(): string
	{
		$user = Auth::user();
	
		return $user->hasAnyRole(['admin','Super Admin']) 
			? 'Profil Perusahaan'
			: 'username: ' . $user->username;
	}
    // protected static string $view = 'realisasi.view-map';

    public function getSubheading(): ?string
    {
        return Auth::user()->hasAnyRole(['admin','Super Admin']) ? $this->record->company_name : 'username: ' . Auth::user()->username;
    }

	public static function getNavigationLabel(): string
	{
        return Auth::user()->hasAnyRole(['admin','Super Admin']) ? 'Daftar Profil Pengguna' : static::$pluralModelLabel ?? 'Profile Anda';
	}

	public function form(Form $form): Form
	{
		return $form
		->schema([
            Section::make('Data Pengguna')
                ->aside()
                ->description('Profil akun Pengguna')
                ->visible(fn ($get) => $get('user_id') === Auth::user()->id)
                ->schema([
                    TextInput::make('user_id')
                        ->label('ID Pengguna')
                        ->inlineLabel()
                        ->columnSpanFull()
                        ->afterStateHydrated(function ($set, $state){
                            $user = auth()->user();
                            $userId = $user->id;
                            $set('user_id', $userId);
                        }),
                    TextInput::make('name')->label('Nama Lengkap')->inlineLabel()->columnSpanFull(),
                    TextInput::make('mobile_phone')->label('No. HP')->inlineLabel()->columnSpanFull(),
                    TextInput::make('email')->inlineLabel()->columnSpanFull(),
                    TextInput::make('ktp')->label('KTP')->inlineLabel()->columnSpanFull(),
                ]),

            Section::make('Data Perusahaan')
                ->aside()
                ->description('Data profil perusahaan')
                ->visible(fn ($get) => $get('user_id') === Auth::user()->id)
                ->schema([
                    TextInput::make('npwp_company')->label('NPWP')->inlineLabel()->columnSpanFull(),
                    TextInput::make('nib_company')->label('NIB')->inlineLabel()->columnSpanFull(),
                    TextInput::make('company_name')->label('Nama Perusahaan')->inlineLabel()->columnSpanFull(),
                    TextInput::make('address_company')->label('Alamat')->inlineLabel()->columnSpanFull(),
                    Select::make('provinsi')
                        ->searchable()
                        ->options([
                            MasterProvinsi::query()
                            ->pluck('nama', 'provinsi_id')
                            ->toArray()
                        ])
                        ->reactive()
                        ->inlineLabel()
                        ->columnSpanFull()
                        ->afterStateUpdated(fn (callable $set, $state) =>
                            $set('kabupaten', null)
                        ),
                    Select::make('kabupaten')
                        ->searchable()
                        ->preload()
                        ->options(fn ($get) =>
                            MasterKabupaten::where('provinsi_id', $get('provinsi'))
                                ->pluck('nama_kab', 'kabupaten_id')
                                ->toArray()
                        )
                        ->inlineLabel()
                        ->reactive()
                        ->columnSpanFull(),
                    TextInput::make('kodepos')->inlineLabel()->columnSpanFull(),
                    TextInput::make('fix_phone')->label('Nomor Telp')->inlineLabel()->columnSpanFull(),
                    TextInput::make('fax')->inlineLabel()->columnSpanFull(),
                    TextInput::make('email_company')->label('Email Perusahaan')->inlineLabel()->columnSpanFull(),
                    TextInput::make('penanggungjawab')->inlineLabel()->columnSpanFull(),
                    TextInput::make('jabatan')->inlineLabel()->columnSpanFull(),
                ]),
        ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            // Actions\EditAction::make(),
        ];
    }
}
