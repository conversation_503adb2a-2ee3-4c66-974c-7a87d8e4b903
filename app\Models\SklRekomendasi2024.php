<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

class SklRekomendasi2024 extends Model
{
	use HasFactory, SoftDeletes;
	public $table = 'skls';

	protected $dates = [
		'created_at',
		'updated_at',
		'deleted_at',
		'published_date',
	];

	protected $fillable = [
		'pengajuan_id',
		'no_pengajuan',
		'no_skl',
		'npwp',
		'no_ijin',
		'submit_by',
		'published_date',
		'qrcode',
		'nota_attch',
		'publisher',
		'approved_by',
		'approved_at',
		'skl_upload',
		'skl_auto',
	];

	//relationship
	public function pengajuan()
	{
		return $this->belongsTo(AjuVerifSkl2024::class, 'pengajuan_id', 'id');
	}

	public function datauser()
	{
		return $this->belongsTo(DataUser::class, 'npwp', 'npwp_company');
	}

	public function commitment()
	{
		return $this->belongsTo(Commitment2024::class, 'no_ijin', 'no_ijin');
	}

	public function completed()
	{
		return $this->belongsTo(Completed::class, 'no_skl', 'no_skl');
	}

	public function submitBy()
	{
		return $this->belongsTo(User::class, 'submit_by', 'id');
	}

	public function approvedBy()
	{
		return $this->belongsTo(User::class, 'approved_by', 'id');
	}
}
