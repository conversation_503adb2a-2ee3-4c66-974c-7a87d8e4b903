<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\AjuVerifTanam2024;
use App\Models\User;

class AjuVerifTanam2024Policy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any AjuVerifTanam2024');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, AjuVerifTanam2024 $ajuveriftanam2024): bool
    {
        return $user->checkPermissionTo('view AjuVerifTanam2024');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create AjuVerifTanam2024');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, AjuVerifTanam2024 $ajuveriftanam2024): bool
    {
        return $user->checkPermissionTo('update AjuVerifTanam2024');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, AjuVerifTanam2024 $ajuveriftanam2024): bool
    {
        return $user->checkPermissionTo('delete AjuVerifTanam2024');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any AjuVerifTanam2024');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, AjuVerifTanam2024 $ajuveriftanam2024): bool
    {
        return $user->checkPermissionTo('restore AjuVerifTanam2024');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any AjuVerifTanam2024');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, AjuVerifTanam2024 $ajuveriftanam2024): bool
    {
        return $user->checkPermissionTo('replicate AjuVerifTanam2024');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder AjuVerifTanam2024');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, AjuVerifTanam2024 $ajuveriftanam2024): bool
    {
        return $user->checkPermissionTo('force-delete AjuVerifTanam2024');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any AjuVerifTanam2024');
    }
}
