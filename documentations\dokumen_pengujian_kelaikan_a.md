# Dokumen Pengujian Kelaikan A - Pengujian Fungsional SIMETHRIS

*Versi: 1.0*  
*Tanggal: Desember 2024*  
*Sistem: SIMETHRIS (Sistem Informasi Monitoring dan Eva<PERSON>asi <PERSON>am Hortikultura dan Realisasi Impor Semusim)*

## Daftar Isi

1. [Pendahuluan](#pendahuluan)
2. [<PERSON><PERSON>](#ruang-lingkup-pengujian)
3. [Strategi <PERSON>an](#strategi-pengujian)
4. [Skenario Pengujian](#skenario-pengujian)
5. [Prosedur Pengujian](#prosedur-pengujian)
6. [Kriteria Keberhasilan](#kriteria-keberhasilan)
7. [<PERSON><PERSON><PERSON>an](#pelaporan)

## 1. Pendahuluan

### 1.1 Tujuan Dokumen
Dokumen ini menjelaskan prosedur pengujian fungsional untuk memastikan semua fitur SIMETHRIS berfungsi sesuai dengan spesifikasi yang telah ditetapkan.

### 1.2 Definisi dan Istil<PERSON>
- **SIMETHRIS**: Sistem Informasi Monitoring dan Eva<PERSON> Hortikultura dan Realisasi Impor Semusim
- **RIPH**: Rekomendasi Impor Produk Hortikultura
- **SKL**: Surat Keterangan Lunas
- **SPTJM**: Surat Pernyataan Tanggung Jawab Mutlak

### 1.3 Referensi
- Spesifikasi Kebutuhan Sistem SIMETHRIS v4.0
- Manual Pengguna SIMETHRIS
- Dokumentasi Teknis SIMETHRIS

## 2. Ruang Lingkup Pengujian

### 2.1 Fitur yang Diuji
- Sistem Autentikasi dan Otorisasi
- Manajemen Komitmen Tanam dan Produksi
- Manajemen Data Master (Poktan, Anggota, Spasial)
- Sistem Pelaporan Realisasi
- Proses Verifikasi
- Penerbitan SKL
- Integrasi Peta Spasial
- API Mobile
- Sistem Notifikasi

### 2.2 Fitur yang Tidak Diuji
- Infrastruktur server
- Keamanan jaringan
- Backup dan recovery (akan diuji di Dokumen B)

## 3. Strategi Pengujian

### 3.1 Pendekatan Pengujian
- **Black Box Testing**: Pengujian berdasarkan spesifikasi fungsional
- **User Acceptance Testing**: Pengujian dari perspektif pengguna akhir
- **Integration Testing**: Pengujian integrasi antar modul
- **API Testing**: Pengujian endpoint API

### 3.2 Lingkungan Pengujian
- **Environment**: Staging/Testing Environment
- **Browser**: Chrome, Firefox, Safari, Edge (versi terbaru)
- **Device**: Desktop, Tablet, Mobile
- **Database**: MySQL/MariaDB (copy dari production)

### 3.3 Data Pengujian
- Data master wilayah (provinsi, kabupaten, kecamatan, desa)
- Data pengguna test untuk setiap role
- Data komitmen dan realisasi sample
- File dokumen test (PDF, gambar)

## 4. Skenario Pengujian

### 4.1 Pengujian Autentikasi (AUTH-001 s/d AUTH-005)

#### AUTH-001: Login Importir
**Tujuan**: Memastikan importir dapat login dengan kredensial yang valid
**Prasyarat**: Akun importir sudah terdaftar
**Langkah**:
1. Buka halaman login SIMETHRIS
2. Masukkan email dan password yang valid
3. Klik tombol "Masuk"
**Hasil yang Diharapkan**: 
- Berhasil login dan diarahkan ke dashboard
- Menu sesuai dengan role importir

#### AUTH-002: Login Verifikator
**Tujuan**: Memastikan verifikator dapat login dan mengakses panel verifikasi
**Prasyarat**: Akun verifikator sudah terdaftar
**Langkah**:
1. Login dengan kredensial verifikator
2. Verifikasi akses ke panel verifikasi
**Hasil yang Diharapkan**: 
- Berhasil login
- Dapat mengakses menu verifikasi

#### AUTH-003: Login Admin
**Tujuan**: Memastikan admin dapat login dan mengakses semua panel
**Prasyarat**: Akun admin sudah terdaftar
**Langkah**:
1. Login dengan kredensial admin
2. Verifikasi akses ke semua panel (Admin, 2024, 2025)
**Hasil yang Diharapkan**: 
- Berhasil login
- Dapat mengakses semua panel administrasi

#### AUTH-004: Logout
**Tujuan**: Memastikan proses logout berfungsi dengan benar
**Langkah**:
1. Login dengan akun valid
2. Klik menu logout
**Hasil yang Diharapkan**: 
- Session berhasil dihapus
- Diarahkan ke halaman login

#### AUTH-005: Akses Tanpa Login
**Tujuan**: Memastikan halaman yang memerlukan autentikasi tidak dapat diakses tanpa login
**Langkah**:
1. Akses URL dashboard tanpa login
**Hasil yang Diharapkan**: 
- Diarahkan ke halaman login

### 4.2 Pengujian Manajemen Komitmen (COMMIT-001 s/d COMMIT-010)

#### COMMIT-001: Tambah Komitmen Baru
**Tujuan**: Memastikan importir dapat menambah komitmen baru
**Prasyarat**: Login sebagai importir
**Langkah**:
1. Masuk ke panel tahun yang sesuai
2. Klik menu "Komitmen"
3. Klik tombol "Tambah Komitmen"
4. Isi semua field yang diperlukan
5. Upload dokumen RIPH
6. Simpan komitmen
**Hasil yang Diharapkan**: 
- Komitmen berhasil disimpan
- Status komitmen "Draft"
- Dokumen RIPH terupload

#### COMMIT-002: Edit Komitmen
**Tujuan**: Memastikan komitmen dapat diedit sebelum disubmit
**Prasyarat**: Komitmen dengan status "Draft" tersedia
**Langkah**:
1. Buka komitmen yang akan diedit
2. Ubah data komitmen
3. Simpan perubahan
**Hasil yang Diharapkan**: 
- Perubahan berhasil disimpan
- Data terupdate sesuai perubahan

#### COMMIT-003: Submit Komitmen
**Tujuan**: Memastikan komitmen dapat disubmit untuk verifikasi
**Prasyarat**: Komitmen lengkap dengan dokumen
**Langkah**:
1. Buka komitmen yang akan disubmit
2. Klik tombol "Submit"
3. Konfirmasi submit
**Hasil yang Diharapkan**: 
- Status berubah menjadi "Submitted"
- Komitmen tidak dapat diedit lagi

#### COMMIT-004: Upload Dokumen Pendukung
**Tujuan**: Memastikan dokumen dapat diupload dengan benar
**Prasyarat**: Komitmen sudah dibuat
**Langkah**:
1. Buka detail komitmen
2. Upload dokumen SPTJM
3. Upload dokumen lainnya
**Hasil yang Diharapkan**: 
- File berhasil diupload
- File dapat didownload kembali
- Format file sesuai ketentuan

#### COMMIT-005: Validasi Data Komitmen
**Tujuan**: Memastikan validasi data berfungsi dengan benar
**Langkah**:
1. Coba simpan komitmen dengan data tidak lengkap
2. Coba upload file dengan format tidak sesuai
**Hasil yang Diharapkan**: 
- Muncul pesan error yang jelas
- Data tidak tersimpan jika tidak valid

### 4.3 Pengujian Data Master (MASTER-001 s/d MASTER-008)

#### MASTER-001: Tambah Data Poktan
**Tujuan**: Memastikan data kelompok tani dapat ditambahkan
**Prasyarat**: Login sebagai importir, komitmen sudah ada
**Langkah**:
1. Masuk ke menu "Kelompok Tani"
2. Klik "Tambah Poktan"
3. Isi data poktan lengkap
4. Simpan data
**Hasil yang Diharapkan**: 
- Data poktan berhasil disimpan
- Data muncul di daftar poktan

#### MASTER-002: Edit Data Poktan
**Tujuan**: Memastikan data poktan dapat diedit
**Langkah**:
1. Pilih poktan yang akan diedit
2. Ubah data poktan
3. Simpan perubahan
**Hasil yang Diharapkan**: 
- Perubahan berhasil disimpan
- Data terupdate

#### MASTER-003: Hapus Data Poktan
**Tujuan**: Memastikan data poktan dapat dihapus
**Prasyarat**: Poktan tidak memiliki anggota
**Langkah**:
1. Pilih poktan yang akan dihapus
2. Klik tombol hapus
3. Konfirmasi penghapusan
**Hasil yang Diharapkan**: 
- Data poktan terhapus
- Tidak muncul di daftar

#### MASTER-004: Tambah Data Anggota
**Tujuan**: Memastikan anggota poktan dapat ditambahkan
**Prasyarat**: Data poktan sudah ada
**Langkah**:
1. Masuk ke detail poktan
2. Klik "Tambah Anggota"
3. Isi data anggota
4. Simpan data
**Hasil yang Diharapkan**: 
- Data anggota berhasil disimpan
- Anggota terkait dengan poktan yang benar

#### MASTER-005: Import Data Excel
**Tujuan**: Memastikan data dapat diimport dari file Excel
**Prasyarat**: File template Excel tersedia
**Langkah**:
1. Download template Excel
2. Isi data sesuai template
3. Upload file Excel
4. Proses import
**Hasil yang Diharapkan**: 
- Data berhasil diimport
- Data sesuai dengan file Excel

### 4.4 Pengujian Pemetaan Spasial (SPATIAL-001 s/d SPATIAL-006)

#### SPATIAL-001: Tambah Lokasi di Peta
**Tujuan**: Memastikan lokasi dapat ditandai di peta
**Prasyarat**: Data anggota sudah ada
**Langkah**:
1. Masuk ke menu "Data Spasial"
2. Klik "Tambah Lokasi"
3. Tandai lokasi di peta
4. Isi data luas lahan
5. Simpan lokasi
**Hasil yang Diharapkan**: 
- Lokasi berhasil disimpan
- Koordinat tersimpan dengan benar
- Luas lahan terhitung otomatis

#### SPATIAL-002: Edit Lokasi
**Tujuan**: Memastikan lokasi dapat diedit
**Langkah**:
1. Pilih lokasi yang akan diedit
2. Ubah posisi atau luas
3. Simpan perubahan
**Hasil yang Diharapkan**: 
- Perubahan berhasil disimpan
- Koordinat terupdate

#### SPATIAL-003: Upload File KML
**Tujuan**: Memastikan file KML dapat diupload
**Prasyarat**: File KML valid tersedia
**Langkah**:
1. Klik "Upload KML"
2. Pilih file KML
3. Preview peta
4. Simpan data
**Hasil yang Diharapkan**: 
- File KML berhasil diproses
- Lokasi muncul di peta
- Data koordinat tersimpan

### 4.5 Pengujian Realisasi (REAL-001 s/d REAL-008)

#### REAL-001: Tambah Laporan Realisasi
**Tujuan**: Memastikan laporan realisasi dapat ditambahkan
**Prasyarat**: Data komitmen dan lokasi sudah ada
**Langkah**:
1. Masuk ke menu "Realisasi"
2. Klik "Tambah Realisasi"
3. Pilih lokasi dan periode
4. Isi data kegiatan
5. Upload foto bukti
6. Simpan realisasi
**Hasil yang Diharapkan**: 
- Data realisasi berhasil disimpan
- Foto terupload dengan benar
- Status realisasi "Draft"

#### REAL-002: Edit Realisasi
**Tujuan**: Memastikan realisasi dapat diedit
**Langkah**:
1. Pilih realisasi yang akan diedit
2. Ubah data realisasi
3. Simpan perubahan
**Hasil yang Diharapkan**: 
- Perubahan berhasil disimpan
- Data terupdate

### 4.6 Pengujian Verifikasi (VERIF-001 s/d VERIF-010)

#### VERIF-001: Penugasan Verifikator
**Tujuan**: Memastikan admin dapat menugaskan verifikator
**Prasyarat**: Login sebagai admin, komitmen submitted tersedia
**Langkah**:
1. Masuk ke panel admin
2. Pilih komitmen untuk verifikasi
3. Tugaskan ke verifikator
4. Simpan penugasan
**Hasil yang Diharapkan**: 
- Verifikator berhasil ditugaskan
- Notifikasi terkirim ke verifikator

#### VERIF-002: Verifikasi Dokumen
**Tujuan**: Memastikan verifikator dapat memverifikasi dokumen
**Prasyarat**: Login sebagai verifikator, penugasan tersedia
**Langkah**:
1. Masuk ke panel verifikasi
2. Buka penugasan verifikasi
3. Review dokumen
4. Berikan status verifikasi
5. Simpan hasil verifikasi
**Hasil yang Diharapkan**: 
- Status dokumen terupdate
- Komentar verifikasi tersimpan

### 4.7 Pengujian SKL (SKL-001 s/d SKL-005)

#### SKL-001: Pengajuan SKL
**Tujuan**: Memastikan importir dapat mengajukan SKL
**Prasyarat**: Verifikasi tanam dan produksi approved
**Langkah**:
1. Masuk ke menu "Pengajuan SKL"
2. Pilih komitmen untuk SKL
3. Submit pengajuan
**Hasil yang Diharapkan**: 
- Pengajuan SKL berhasil disubmit
- Status berubah menjadi "Pengajuan SKL"

#### SKL-002: Penerbitan SKL
**Tujuan**: Memastikan admin dapat menerbitkan SKL
**Prasyarat**: Pengajuan SKL tersedia
**Langkah**:
1. Login sebagai admin
2. Review pengajuan SKL
3. Generate SKL dengan QR Code
4. Approve penerbitan
**Hasil yang Diharapkan**: 
- SKL berhasil diterbitkan
- QR Code terbuat dengan benar
- PDF SKL dapat didownload

### 4.8 Pengujian API Mobile (API-001 s/d API-010)

#### API-001: Login Mobile
**Tujuan**: Memastikan API login mobile berfungsi
**Langkah**:
1. POST ke `/api/mobile/login`
2. Kirim kredensial valid
**Hasil yang Diharapkan**: 
- Response 200 OK
- Token autentikasi diterima

#### API-002: Get Commitments
**Tujuan**: Memastikan API dapat mengambil data komitmen
**Prasyarat**: Token valid tersedia
**Langkah**:
1. GET ke `/api/mobile/realisasi/commitments`
2. Sertakan token di header
**Hasil yang Diharapkan**: 
- Response 200 OK
- Data komitmen sesuai user

## 5. Prosedur Pengujian

### 5.1 Persiapan Pengujian
1. Setup environment testing
2. Prepare test data
3. Siapkan akun test untuk setiap role
4. Backup database production untuk testing

### 5.2 Eksekusi Pengujian
1. Jalankan test case sesuai urutan
2. Dokumentasikan hasil setiap test
3. Screenshot untuk test yang gagal
4. Catat bug yang ditemukan

### 5.3 Pelaporan Bug
1. Gunakan template bug report
2. Sertakan langkah reproduksi
3. Lampirkan screenshot/video
4. Tentukan prioritas bug

## 6. Kriteria Keberhasilan

### 6.1 Kriteria Pass
- Semua test case utama (Priority 1) berhasil
- Minimal 95% test case Priority 2 berhasil
- Tidak ada bug critical atau high

### 6.2 Kriteria Fail
- Ada test case Priority 1 yang gagal
- Ditemukan bug critical
- Performa tidak memenuhi standar

## 7. Pelaporan

### 7.1 Test Summary Report
- Total test case executed
- Pass/Fail ratio
- Bug summary by severity
- Recommendation

### 7.2 Deliverables
- Test execution report
- Bug report
- Test evidence (screenshots)
- Sign-off document
