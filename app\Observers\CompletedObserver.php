<?php

namespace App\Observers;

use App\Models\Completed;
use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class CompletedObserver
{
    /**
     * Handle the Completed "created" event.
     */
    public function created(Completed $completed): void
    {
        $pusat = User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['Super Admin', 'admin', 'direktur']);
        })->get();

		$registrar = $completed->user;
        $noIjin = $completed->no_ijin;
        $periode = $completed->periodetahun;
        $noSkl = $completed->no_skl;
        $company = $completed->datauser->company_name;

        Notification::make()
            ->title('SKL Terbit!')
            ->body("
                <p class='mb-3'>Surat Keterangan Lunas (SKL) atas nama <span class='text-info-500 font-bold'>{$company}</span> untuk:</p>
                <p class='mb-0 mt-2'>No. RIPH: {$noIjin}</p>
                <p class='mb-0'>Periode: {$periode}</p>
                <p class='mb-0'>No. SKL: {$noSkl}</p>
                <p class='mb-3 mt-5'>Telah diterbitkan. Segera Ucapkan selamat kepada Suksesor.</p>
            ")
            ->sendToDatabase($pusat);

        Notification::make()
            ->title('SKL Telah Terbit!!')
            ->body("
                <p class='mb-3'>SELAMAT!</p>
                <p class='mb-3'>Surat Keterangan Lunas (SKL) atas nama <span class='text-info-500 font-bold'>{$company}</span> untuk:</p>
                <p class='mb-0 mt-2'>No. RIPH: {$noIjin}</p>
                <p class='mb-0'>Periode: {$periode}</p>
                <p class='mb-0'>No. SKL: {$noSkl}</p>
                <p class='mb-3 mt-5'>Telah diterbitkan. Terima Kasih atas kerjasama dan upaya Anda untuk mensukseskan program ini.</p>
            ")
            ->sendToDatabase($registrar);
    }

    /**
     * Handle the Completed "updated" event.
     */
    public function updated(Completed $completed): void
    {
        //
    }

    /**
     * Handle the Completed "deleted" event.
     */
    public function deleted(Completed $completed): void
    {
        //
    }

    /**
     * Handle the Completed "restored" event.
     */
    public function restored(Completed $completed): void
    {
        //
    }

    /**
     * Handle the Completed "force deleted" event.
     */
    public function forceDeleted(Completed $completed): void
    {
        //
    }
}
