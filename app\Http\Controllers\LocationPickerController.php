<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Commitment2025;
use App\Models\CommitmentRegion;
use App\Models\MasterSpatial;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class LocationPickerController extends Controller
{
    public function getPostedMarkers($noRiph)
    {
        $noIjin = preg_replace('/(\d{4})(\D{2})(\d+)(\D)(\d{2})(\d{4})/', '$1/$2.$3/$4/$5/$6', $noRiph);
        $myMarkers = MasterSpatial::where('reserved_by', $noIjin)->get();
        $data = [];
        foreach ($myMarkers as $marker) {
            $data[] = [
                'kdSpatial'  => $marker->kode_spatial,
                'petani' => $marker->anggota->nama_petani,
                'ktp' => $marker->anggota->ktp_petani,
                'poktan' => $marker->masterpoktan->nama_kelompok,
                'kabupaten_id' => $marker->kabupaten_id,
                'kabupaten_nama' => $marker->kabupaten->nama_kab,
                'kecamatan_id' => $marker->kecamatan_id,
                'nama_kecamatan' => $marker->kecamatan->nama_kecamatan,
                'luas' => $marker->luas_lahan,
            ];
        }
        return response()->json($data);
    }

    public function getAvailableSpatials($noRiph)
    {
        $npwp = Auth::user()->datauser->npwp_company;
        $noIjin = preg_replace('/(\d{4})(\D{2})(\d+)(\D)(\d{2})(\d{4})/', '$1/$2.$3/$4/$5/$6', $noRiph);
        $commitment = Commitment2025::where('no_ijin', $noIjin)->select('id','luas_wajib_tanam')->first();
		$myCommitment = $commitment->luas_wajib_tanam;
		Log::info($commitment);
        $myQuota = CommitmentRegion::where('no_ijin', $noIjin)
            ->with(['kabupaten:kabupaten_id,nama_kab'])
            ->select('kabupaten_id','quota')
            ->get();

        $quotas = (float) $myQuota->sum('quota');
        $commitmentId = $commitment->id;
        $kabupatenIds = $myQuota->pluck('kabupaten_id');

        $query = MasterSpatial::where('is_active', 1)->whereIn('kabupaten_id', $kabupatenIds);

        $queryCount = $query->where('status', 0)->count();
        $querySum = $query->where('status', 0)->sum('luas_lahan');

        $regionData = CommitmentRegion::where('no_ijin', $noIjin)->select('kabupaten_id', 'quota', 'status')->get();
        $regionList = [];
        foreach ($regionData as $region) {
            $sumLuasLahan = MasterSpatial::where('kabupaten_id', $region->kabupaten_id)->sum('luas_lahan');
            $regionList[] = [
                'kabupaten_id' => $region->kabupaten_id,
                'kabupaten_nama' => $region->kabupaten->nama_kab,
                'quota' => $region->quota,
                'available' => $sumLuasLahan,
            ];
        }

        $mySpatials = MasterSpatial::where('is_active', 1)->whereIn('kabupaten_id', $kabupatenIds);
        $spatials = $mySpatials->get()->map(function ($item) {
            return [
                'id' => $item->id,
                'kode_spatial' => e($item->kode_spatial),
                'nama_petani'  => $item->anggota?->nama_petani ?? $item->nama_petani,
                'ktp_petani'  => $item->anggota?->ktp_petani ?? $item->ktp_petani,
                'latitude' => $item->latitude,
                'longitude' => $item->longitude,
                'luas_lahan' => $item->luas_lahan,
                'kabupaten_id' => $item->kabupaten_id,
                'kecamatan_id' => $item->kecamatan_id,
                'status' => $item->status,
            ];
        });

        return response()->json([
            'commitmentId' => $commitmentId,
            'myCommitment' => $myCommitment,
            'myQuota' => $quotas,
            'availableMarker' => $queryCount,
            'availableLuas' => $querySum,
            'regionList' => $regionList,
            'data' => $spatials,
        ]);
    }

    public function getMarkerDetail($kodeSpatial)
    {
        $lokasi = MasterSpatial::where('kode_spatial', $kodeSpatial)->first();

        $data = [
            'id'            => $lokasi->id,
            'kode_spatial'  => e($lokasi->kode_spatial),
            'ktp_petani'    => e($lokasi->ktp_petani),
            'nama_petani'   => e($lokasi->nama_petani),
            'poktan'        => e($lokasi->masterpoktan->nama_kelompok),
            'latitude'      => $lokasi->latitude,
            'longitude'     => $lokasi->longitude,
            'polygon'       => $lokasi->polygon,
            'altitude'      => $lokasi->altitude,
            'luas_lahan'    => $lokasi->luas_lahan,
            'kabupaten'     => $lokasi->kabupaten->nama_kab,
            'kecamatan'     => $lokasi->kecamatan->nama_kecamatan,
            'kml_url'       => e($lokasi->kml_url),
            'is_active'     => $lokasi->is_active,
            'status'        => $lokasi->status,
            'reserved_by'   => $lokasi->reserved_by,
            'reserved_at'   => $lokasi->reserved_at,
        ];

        return response()->json([
            'success' => true,
            'data' => $data,
        ]);
    }

    public function postMarker(Request $request, $kodeSpatial)
    {
        // Jika request adalah JSON, ambil data dari JSON body
        if ($request->isJson()) {
            $data = $request->json()->all();
            $validatedData = [
                'kode_spatial' => $data['kode_spatial'] ?? null,
                'status' => $data['status'] ?? null,
                'reserved_by' => $data['reserved_by'] ?? null,
                'reserved_at' => $data['reserved_at'] ?? null,
            ];
        } else {
            // Jika request adalah form data, ambil data dari form
            $validatedData = $request->validate([
                'kode_spatial' => 'required|string',
                'status' => 'required|integer',
                'reserved_by' => 'nullable|string',
                'reserved_at' => 'nullable|date',
            ]);
        }

        $maxRetries = 3;
        $attempts = 0;

        while ($attempts < $maxRetries) {
            try {
                DB::beginTransaction();
                $marker = MasterSpatial::where('kode_spatial', $kodeSpatial)->lockForUpdate()->first();

                if ($marker) {
                    $marker->update($validatedData);
                    $message = 'Data berhasil diperbarui';
                } else {
                    MasterSpatial::create($validatedData);
                    $message = 'Data berhasil disimpan';
                }

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'received_data' => $validatedData,
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                usleep(500000);

                $attempts++;

                if ($attempts >= $maxRetries) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Gagal menyimpan data setelah beberapa percobaan',
                        'error' => $e->getMessage(),
                    ], 500);
                }
            }
        }
    }
}
