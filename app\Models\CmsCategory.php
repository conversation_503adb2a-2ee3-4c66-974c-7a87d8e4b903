<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CmsCategory extends Model
{
	use SoftDeletes;

	protected $table = 'categories';

	protected $fillable = [
        'type',
        'for',
        'parent_id',
        'name',
        'slug',
        'description',
        'icon',
        'color',
        'is_active',
        'show_in_menu',
        'feature_image',
        'cover_image',
        'created_at',
        'updated_at'
    ];

    public function children()
    {
        return $this->hasMany(CmsCategory::class, 'parent_id');
    }
    public function parent()
    {
        return $this->belongsTo(CmsCategory::class, 'parent_id');
    }

    public function posts()
    {
        return $this->hasMany(Post::class, 'category_id', 'id');
    }
}
