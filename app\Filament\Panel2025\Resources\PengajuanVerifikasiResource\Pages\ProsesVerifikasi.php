<?php

namespace App\Filament\Panel2025\Resources\PengajuanVerifikasiResource\Pages;

use App\Filament\Panel2025\Resources\PengajuanVerifikasiResource;
use App\Models\Pengajuanskl2025;
use App\Models\PengajuanVerifikasi;
use App\Models\Pks2025;
use App\Models\Realisasi2025;
use App\Models\User;
use App\Models\Userfile;
use App\Models\VerificatorAssignment;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\{Actions, Fieldset, FileUpload, Hidden, TextInput, Placeholder, Radio, Repeater, Section, Select, Tabs, Textarea};
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

use Filament\Support\Enums\Alignment;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\HtmlString;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class ProsesVerifikasi extends EditRecord
{
	protected static string $resource = PengajuanVerifikasiResource::class;
	public static string | Alignment $formActionsAlignment = Alignment::Right;

	protected static ?string $title = 'Proses Verifikasi';
	public function getHeading(): string
	{
		return 'Proses Verifikasi';
	}

	public ?int $pengajuanId = null;

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $this->pengajuanId = $data['id']; // Simpan ID pengajuan
        return $data;
    }

	public function getSubheading(): ?string
	{
		$noIjin = $this->record ? $this->record->no_ijin : '##';
		return 'Komitmen Tanam-Produksi PPRK No: ' . $noIjin;
	}

	protected function getHeaderActions(): array
	{
		return [
			// Actions\ViewAction::make(),
			// Actions\DeleteAction::make(),
		];
	}

	public function form(Form $form): Form
	{
		return $form
			->schema([
				Tabs::make('Tabs')
					->tabs([
						Tab::make('Data Pengajuan')
							->schema([
								Section::make('Data Pengajuan')
									->aside()
									->description('Data Rekomendasi Import Produk Hortikultura')
									->schema([
										Placeholder::make('Nomor Pengajuan')
											->inlineLabel()
											->content(fn($record) => $record->no_pengajuan),
										Placeholder::make('No. PPRK')
											->inlineLabel()
											->label('No. PPRK')
											->content(fn($record) => $record->no_ijin),
										Placeholder::make('NPWP')
											->inlineLabel()
											->label('NPWP')
											->content(fn($record) => $record->npwp),
										Placeholder::make('Pelaku Usaha')
											->inlineLabel()
											->content(fn($record) => $record->user->datauser->company_name),
										Placeholder::make('Tanggal Pengajuan')
											->inlineLabel()
											->label('Tanggal Pengajuan')
											->content(fn($record): string => $record->created_at->format('d M Y')),
									]),

								Section::make('Status Verifikasi')
									->aside()
									->description('Status Verifikasi Tanam dan Produksi sebelumnya')
									->schema([
										TableRepeater::make('previouseVerification')
											->hiddenLabel()
											->relationship('hasInCommon')
											->addable(false)
											->deletable(false)
											->headers([
												Header::make('Verifikasi'),
												Header::make('Nomor'),
												Header::make('Status'),
											])
											->schema([
												Placeholder::make('Verifikasi')
													->hiddenLabel()
													->content(function ($record) {
														$kind = [
															'PVT' => 'Tanam',
															'PVP' => 'Produksi',
														];
														return $kind[$record->kind] ?? 'Tidak Diketahui';
													}),
												Placeholder::make('no_pengajuan')
													->hiddenLabel()
													->content(fn ($record) => $record->no_pengajuan),
												Placeholder::make('status')
													->hiddenLabel()
													->content(fn($get) => view('components.badge-verifikasi', ['status' => $get('status')])),
											])
										]),

								Section::make('Verifikator')
									->aside()
									->description('Petugas Verifikasi Tanam dan Produksi sebelumnya')
									->schema([
										TableRepeater::make('previouseVerificators')
											->hiddenLabel()
											->relationship('assignmentsInCommitment')
											->addable(false)
											->deletable(false)
											->headers([
												Header::make('Verifikasi'),
												Header::make('Nomor'),
												Header::make('Verifikator'),
												Header::make('Catatan'),
											])
											->schema([
												Placeholder::make('Verifikasi')
													->hiddenLabel()
													->content(function ($record) {
														$kind = [
															'PVT' => 'Tanam',
															'PVP' => 'Produksi',
														];
														return $kind[$record->pengajuan->kind] ?? 'Tidak Diketahui';
													}),
												Placeholder::make('no_pengajuan')
													->hiddenLabel()
													->content(fn ($record) => $record->kode_pengajuan),
												Placeholder::make('user_id')
													->hiddenLabel()
													->content(fn($record) => $record->user->name),

												Placeholder::make('myNote')
													->hiddenLabel()
													->content(fn ($record) => new HtmlString('<p>'.$record->myNote.'</p>'))
											])
									]),
							]),

						Tab::make('Berkas Kelengkapan')
							->badge(function ($record) {
								$countRealisasi = Userfile::where('no_ijin', $record->no_ijin)->whereNull('status')->count();
								return $countRealisasi;
							})
							->badgeColor('warning')
							->schema([
								Section::make('Pemeriksaan Berkas')
									->aside()
									->description('Pemeriksaan Berkas-berkas Pengajuan Verifikasi')
									->schema([
										TableRepeater::make('berkas')
											->addable(false)
											->deletable(false)
											->relationship('userfiles')
											// ->streamlined()
											->headers([
												Header::make('Berkas'),
												Header::make('Tautan'),
												Header::make('Status'),
											])
											->schema([
												Placeholder::make('kind')
													->hiddenLabel()
													->content(fn($record) => [
														'spvt' => 'Surat Pengajuan Verifikasi (Tanam)',
														'spvp' => 'Surat Pengajuan Verifikasi (Produksi)',
														'spskl' => 'Surat Pengajuan Penerbitan SKL',
														'sptjmt' => 'Surat Pernyataan Tangggung Jawab Mutlak (Periode Tanam)',
														'sptjmp' => 'Surat Pernyataan Tangggung Jawab Mutlak (Periode Produksi)',
														'rta' => 'Form Realisasi Tanam',
														'rpo' => 'Form Realisasi Produksi',
														'spht' => 'Statistik Pertanian Hortikultura (Periode Tanam)',
														'sphb' => 'Statistik Pertanian Hortikultura (Periode Produksi)',
														'spdst' => 'Surat Pengantar Dinas Telah Selesai Tanam',
														'spdsp' => 'Surat Pengantar Dinas Telah Selesai Produksi',
														'logbook' => 'Logbook (Tanam/Produksi)',
														'la' => 'Laporan Akhir',
														'skl' => 'Surat Keterangan Lunas',
														'ft' => 'Foto Tanam',
														'fp' => 'Foto Produksi',
														'pks' => 'Berkas PKS',
													][$record->kind] ?? $record->kind),
												Placeholder::make('Tautan')
													->hiddenLabel()
													->extraAttributes(['class'=>'text-info-500 font-bold'])
													->content(fn($record) => new HtmlString(
														'<a class="" href="/' . e($record->file_url) . '" target="_blank" rel="noopener noreferrer">Buka File</a>'
													)),
												Placeholder::make('status')
													->hiddenLabel()
													->content(fn($get) => view('components.status-badge-verifikasi', ['status' => $get('status')])),

											])
									]),
							]),

						Tab::make('Perjanjian Kerjasama')
							->badge(function ($record) {
								$countRealisasi = Pks2025::where('no_ijin', $record->no_ijin)->whereNull('status')->count();
								return $countRealisasi;
							})
							->badgeColor('warning')
							->schema([
								Section::make('Perjanjian Kerjasama')
									->aside()
									->description('Pemeriksaan Berkas dan Data Perjanjian antara Pelaku Usaha dengan Kelompok Tani Mitra.')
									->schema([
										TableRepeater::make('berkaspks')
											->addable(false)
											->deletable(false)
											->relationship('pks')
											// ->streamlined()
											->headers([
												Header::make('Kelompok Tani'),
												Header::make('No. Perjanjian'),
												Header::make('Tautan'),
												Header::make('Status'),
												Header::make('Rinci'),
											])
											->schema([
												Placeholder::make('poktan')
													->hiddenLabel()
													->content(fn($record) => $record->nama_poktan),
												Placeholder::make('no_Perjanjian')
													->hiddenLabel()
													->content(fn($record) => $record->no_perjanjian),
												Placeholder::make('Tautan')
													->hiddenLabel()
													->extraAttributes(['class' => 'text-info-500'])
													->content(fn($record) => new HtmlString(
														'<a class="text-info-500 font-bold" href="/' . e($record->berkas_pks) . '" target="_blank" rel="noopener noreferrer">Buka File</a>'
													)),
												Placeholder::make('statuspks')
													->hiddenLabel()
													->content(fn($get) => view('components.status-badge-verifikasi', ['status' => $get('status')])),

												Actions::make([
													Action::make('detail')
														->hiddenLabel()
														->iconButton()
														->color('warning')
														->form([
															Placeholder::make('poktan')
																->inlineLabel()
																->content(fn($record) => $record->nama_poktan),
															Placeholder::make('no_Perjanjian')
																->inlineLabel()
																->content(fn($record) => $record->no_perjanjian),
															Placeholder::make('masa_berlaku')
																->inlineLabel()
																->content(
																	fn($record) => ($record->tgl_perjanjian_start ?
																		Carbon::parse($record->tgl_perjanjian_start)->format('d M Y') : '-') .
																		' s.d ' .
																		($record->tgl_perjanjian_end ?
																			Carbon::parse($record->tgl_perjanjian_end)->format('d M Y') : '-')
																),
															Placeholder::make('memberCount')
																->inlineLabel()
																->label('Anggota')
																->content(fn($record) => $record->jumlah_anggota),
															Placeholder::make('luas_rencana')
																->inlineLabel()
																->label('Luas Rencana')
																->content(fn($record) => number_format($record->luas_rencana, 0, ',', '.') . ' m2'),
															Placeholder::make('varietas')
																->inlineLabel()
																->label('Rencana Varietas')
																->content(fn($record) => $record->varietas?->nama_varietas ?? '-')
														])
														->icon('icon-layout-text-window-reverse')
														->slideOver()
														->modal()
														->modalSubmitAction(false)
														->modalWidth('md')
														->modalCancelAction(fn($action) => $action->label('Tutup'))
														->modalHeading(fn($record) => 'Detail Perjanjian ' . ($record->no_perjanjian ?? $record->nama_poktan)),
												])->alignCenter(),
												// Placeholder::make('detail')
												//     ->hiddenLabel()
												//     ->extraAttributes(['class'=>'text-center'])
												//     ->content(fn ($record) => $record->no_perjanjian),
											])
									]),
							]),

						Tab::make('Realisasi Komitmen Tanam dan Produksi')
							->badge(function ($record) {
								$countRealisasi = Realisasi2025::where('no_ijin', $record->no_ijin)->whereNull('vt_status')->count();
								return $countRealisasi;
							})
							->badgeColor('warning')
							->schema([
								Section::make('Realisasi Komitmen Tanam dan Produksi')
									->aside()
									->description('Pemeriksaan Data Realisasi Komitmen Tanam dan Produksi.')
									->schema([
										Actions::make([
											Action::make('Realisasi')
												// ->hiddenLabel()
												->label('Kunjungi halaman daftar lokasi')
												// ->visible(fn() => Auth::user()->hasRole('verifikator'))
												->icon('icon-map-fill')
												// ->iconButton()
												->tooltip('Verifikasi realisasi tanam produksi')
												// ->button()
												->color('info')
												->url(function ($record) {
													if(Auth::user()->hasRole('importir'))
													{
														return route('filament.panel2025.resources.realisasi2025s.daftarrealisasi', [
															'noijin' => preg_replace('/[\/.\-]/', '', $record->no_ijin)
														]);
													}
													return route('filament.panel2025.resources.realisasi2025s.daftarlokasi', [
													'record' => $record->id,
													'pengajuan' => $record->no_pengajuan,
													'noijin' => preg_replace('/[\/.\-]/', '', $record->no_ijin)
													]);
												})
										]),
									]),
							]),
						Tab::make('Ringkasan Pemeriksaan')
							->schema([
								Section::make('Ringkasan Pemeriksaan')
									->visible(fn ($record) => $record->kind !== 'PVS')
									->aside()
									->description('Ringkasan hasil pemeriksaan serta penetapan hasil.')
									->schema([
										Repeater::make('kesimpulan')
											->relationship('authAssignments')
											->hiddenLabel()
											->addable(false)
											->deletable(false)
											->schema([
												Textarea::make('myNote')
													->label('Catatan Anda')
													->autosize()
													->placeholder('catatan, opini atau pendapat serta rekomendasi Anda sebagai verifikator atas hasil verifikasi yang telah dilaksanakan.'),
											]),

										Hidden::make('kind'),
										Hidden::make('verif_by')->formatStateUsing(fn () => Auth::user()->id),
										Hidden::make('verif_at')->formatStateUsing(fn () => today()),
										Hidden::make('no_ijin'),
										Hidden::make('npwp'),
										Hidden::make('no_pengajuan'),
										Hidden::make('id'),
										Hidden::make('no_pengajuan'),
										Fieldset::make('Hasil Pemeriksaan Akhir')
											->visible(function ($record){
												$firstVerifikator = $record->assignments()
													->orderBy('user_id')
													->first()?->user_id;
												return Auth::id() === $firstVerifikator;
											})
											->schema([
												Radio::make('status')
													->label('Status Verifikasi')
													->options([
														'4' => 'Selesai',
														'5' => 'Perbaikan',
													])
													->descriptions([
														'4' => 'Pemeriksaan telah selesai dilaksanakan. Data dinyatakan Sesuai',
														'5' => 'Pemeriksaan telah selesai dilaksanakan, dengan catatan perbaikan yang harus dilaksanakan oleh Pelaku Usaha',
													]),
												Textarea::make('note')
													->label('Catatan')
													->placeholder('kesimpulan hasil pemeriksaan')
													->autosize(),

												FileUpload::make('fileBa')
													->openable()
													->required()
													->maxSize(2048)
													->downloadable()
													->disk('public')
													->deletable()
													->visibility('public')
													->panelAspectRatio('4:1')
													->imagePreviewHeight('100')
													->fetchFileInformation(true)
													->label('Unggah Berita Acara')
													->helperText('Berita Acara Hasil Pemeriksaan. Maksimal 2MB, format PDF')
													->directory(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
														return "uploads/{$cleanNpwp}/{$cleanNoIjin}/dokumen";
													})
													->rules([
														'file',
														'mimetypes:application/pdf',
														'mimes:pdf',
														'max:2048'
													])
													->validationMessages([
														'mimetypes' => 'Hanya file PDF yang diperbolehkan',
														'mimes' => 'Ekstensi file harus .pdf',
													])
													->getUploadedFileNameForStorageUsing(
														function (TemporaryUploadedFile $file, $get, $record): string {
															$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
															$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

															// Format nama file: [ID]_[NPWP]_[NOIJIN].[ext]
															return 'bap_' . $record->id . '_' . $cleanNpwp . '_' . $cleanNoIjin . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
														}
													),

												FileUpload::make('fileNdhp')
													->openable()
													// ->required()
													->maxSize(2048)
													->downloadable()
													->disk('public')
													->deletable()
													->visibility('public')
													->panelAspectRatio('4:1')
													->imagePreviewHeight('100')
													->fetchFileInformation(true)
													->label('Unggah Nota Dinas')
													->helperText('Nota Dinas Hasil Pemeriksaan. Maksimal 2MB, format PDF')
													->directory(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
														return "uploads/{$cleanNpwp}/{$cleanNoIjin}/dokumen";
													})
													->rules([
														'file',
														'mimetypes:application/pdf',
														'mimes:pdf',
														'max:2048'
													])
													->validationMessages([
														'mimetypes' => 'Hanya file PDF yang diperbolehkan',
														'mimes' => 'Ekstensi file harus .pdf',
													])
													->getUploadedFileNameForStorageUsing(
														function (TemporaryUploadedFile $file, $get, $record): string {
															$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
															$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

															// Format nama file: [ID]_[NPWP]_[NOIJIN].[ext]
															return 'ndhp_' . $record->id . '_' . $cleanNpwp . '_' . $cleanNoIjin . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
														}
													),
											])
									]),

								Section::make('Rekomendasi')
									->visible(fn ($record) => $record->kind === 'PVS')
									->aside()
									->description('Rekomendasi Penerbitan Surat Keterangan Lunas.')
									->schema([
										Placeholder::make('disclaimer')
											->hiddenLabel()
											->content(new HtmlString('Berdasarkan hasil pemeriksaan pada tahap <span class="font-bold">Verifikasi Tanam dan Produksi</span>, maka Pengajuan ini:')),

										Select::make('status')
											->inlineLabel()
											->label('Dinyatakan')
											->reactive()
											->live()
											->options([
												'4' => 'Direkomendasikan Terbit',
												'5' => 'Perbaiki Data',
											]),
										Placeholder::make('disclaimer')
											->hiddenLabel()
											->content(function ($get){
												if($get('status') === '4')
												{
													return new HtmlString('Selanjutnya, Rekomendasi ini akan <span class="font-bold">diteruskan kepada Pimpinan</span> untuk mendapatkan persetujuan Status Lunas serta Penerbitan Surat Keterangan Lunas');
												}
												if($get('status') === '5')
												{
													return new HtmlString('Selanjutnya, Rekomendasi beserta catatan di bawah ini akan diteruskan kepada <span class="font-bold">diteruskan kepada Pelaku Usaha</span> untuk dilakukan perbaikian sebagaimana mestinya.');
												}
												return '';
											}),

										Textarea::make('note')
											->label('Catatan')
											->reactive()
											->required(fn ($get)=> $get('status') === '5')
											->autosize(),
										Hidden::make('kind'),
										Hidden::make('verif_by')->formatStateUsing(fn () => Auth::user()->id),
										Hidden::make('verif_at')->formatStateUsing(fn () => today()),
										Hidden::make('no_ijin'),
										Hidden::make('npwp'),
										Hidden::make('no_pengajuan'),
										Hidden::make('id'),
										Hidden::make('no_pengajuan'),
									])
							])
					])->contained(false)
			])->columns(1);
	}

	protected function mutateFormDataBeforeSave(array $data): array
	{
		$kind = $data['kind'];
		$pengajuanId = $data['id'];
		$status = $data['status'];
		$noIjin = $data['no_ijin'];
		$npwp = $data['npwp'];
		$noPengajuan = $data['no_pengajuan'];
		$submitBy = $data['verif_by'];
		$note = isset($data['note']) ? $data['note'] : null;

		if($kind === 'PVS' && $status === '4') {
			try {
				$pengajuan = new Pengajuanskl2025();
				$pengajuan->pengajuan_id = $pengajuanId;
				$pengajuan->no_pengajuan = $noPengajuan;
				$pengajuan->npwp = $npwp;
				$pengajuan->no_ijin = $noIjin;
				$pengajuan->submit_by = $submitBy;
				$pengajuan->status = '0';
				$pengajuan->note = $note;

				$pengajuan->save();

			} catch (\Exception $e) {
				// Log error
				Log::error("Failed to create Pengajuanskl2025: " . $e->getMessage());

				// Optional: Notify user
				Notification::make()
					->title('Error saving data')
					->danger()
					->send();

				// Optional: Throw exception to rollback transaction
				throw $e;
			}
		}

		return $data;
	}
}
