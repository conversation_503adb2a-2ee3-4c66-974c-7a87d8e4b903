<?php

namespace App\Providers;

use App\Models\User;
use Illuminate\Support\ServiceProvider;

use App\Policies\RolePolicy;
use App\Policies\PermissionPolicy;
use App\Providers\FileUploadFixServiceProvider;
use Filament\Http\Middleware\Authenticate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\URL;
use Spatie\Activitylog\Models\Activity;
use Spatie\Browsershot\Browsershot;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use TomatoPHP\FilamentCms\Facades\FilamentCMS;
use TomatoPHP\FilamentCms\Services\Contracts\CmsAuthor;

class AppServiceProvider extends ServiceProvider
{
	public function register(): void
	{
		Gate::policy(Role::class, RolePolicy::class);
		Gate::policy(Permission::class, PermissionPolicy::class);
	}

	/**
	 * Bootstrap any application services.
	 */
	public function boot(): void
	{
		URL::forceScheme('https');
		// if (config('app.env') !== 'local') {
		// }

		// // Register FileUploadFixServiceProvider for production environment
		// if (config('app.env') === 'production') {
		// 	$this->app->register(FileUploadFixServiceProvider::class);
		// }
		if (config('app.env') === 'production') {
			putenv('NODE_PATH=' . config('browsershot.node_module_path'));
			putenv('PATH=$PATH:' . config('browsershot.include_path'));
			if (config('browsershot.puppeteer_cache_dir')) {
				putenv('PUPPETEER_CACHE_DIR=' . config('browsershot.puppeteer_cache_dir'));
			}
		}

		Authenticate::redirectUsing(function (Request $request) {
			return route('filament.admin.auth.login');
		});


		Gate::before(function (User $user, string $ability) {
			return $user->isSuperAdmin() ? true : null;
		});
	}

	public $singletons = [
		\Filament\Http\Responses\Auth\Contracts\LoginResponse::class => \App\Http\Responses\LoginResponse::class,
		\Filament\Http\Responses\Auth\Contracts\LogoutResponse::class => \App\Http\Responses\LogoutResponse::class,
	];
}
