<?php

namespace App\Filament\Panel2025\Resources;

use App\Filament\Panel2025\Resources\PengajuanVerifikasiResource\Pages;
use App\Filament\Panel2025\Resources\PengajuanVerifikasiResource\Pages\VerificationReport;
use App\Filament\Panel2025\Resources\PengajuanVerifikasiResource\RelationManagers;
use App\Filament\Panel2025\Resources\Realisasi2025Resource\Pages\VerifikasiPetak;
use App\Models\PengajuanVerifikasi;
use Filament\Actions\StaticAction;
use Filament\Forms;
use Filament\Forms\Components\{DatePicker, Textarea, TextInput};
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\{Action as TableAction, BulkActionGroup, DeleteBulkAction, EditAction,ViewAction};
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class PengajuanVerifikasiResource extends Resource
{
    protected static ?string $model = PengajuanVerifikasi::class;
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationLabel(): string
    {
        $user = Auth::user();
        if ($user->hasRole('verifikator')) {
        return 'Daftar Tugas Verifikasi';
        }else{
            return 'Daftar Pengajuan Verifikasi';
        }
    }

    public static function shouldRegisterNavigation(): bool
    {
        $user = Auth::user();

        if ($user->hasRole('importir')) {
            return $user->commitment()->whereHas('ajuverif')->exists();
        }
        return true;
    }

	public static function getNavigationBadge(): ?string
	{
		if (!Auth::check()) {
			return null;
		}
	
		$user = Auth::user();
		$count = 0; // Default count
	
		if ($user->hasAnyRole(['admin', 'Super Admin'])) {
			$count = static::getModel()::where('status', '0')->count();
		} elseif ($user->hasAnyRole(['importir'])) {
			$count = static::getModel()::whereIn('status', ['0', '1', '2'])->count();
		} else {
			$count = static::getModel()::whereHas('authAssignments', function ($query) use ($user) {
				$query->where('status', '1')->where('user_id', $user->id);
			})->count();
		}
	
		return $count > 0 ? (string) $count : null;
	}
	
	

	public static function getNavigationBadgeColor(): ?string
	{
		return 'warning';
	}
	
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
				//
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPengajuanVerifikasis::route('/'),
            'assignment' => Pages\VerifikatorAssignment::route('/{record}/assignment'),
            'verifikasi' => Pages\ProsesVerifikasi::route('/{record}/verifikasi'),
            'verifSpatial' => VerifikasiPetak::route('/{record}/{pengajuan}/verifikasi/{spatial}'),
            'verifReport' => VerificationReport::route('/{record}/report'),
            // 'create' => Pages\CreatePengajuanVerifikasi::route('/create'),
            // 'view' => Pages\ViewPengajuanVerifikasi::route('/{record}'),
            // 'edit' => Pages\EditPengajuanVerifikasi::route('/{record}/edit'),
        ];
    }
}
