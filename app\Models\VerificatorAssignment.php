<?php

namespace App\Models;

use App\Observers\AssignmentObserver;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Notifications\Notifiable;

#[ObservedBy([AssignmentObserver::class])]
class VerificatorAssignment extends Model
{
    use Notifiable;

	public $table = 't2025_assignment_verifikasis';

	protected $fillable = [
		'tcode',
		'pengajuan_id',
		'kode_pengajuan',
		'no_ijin',
		'user_id',
		'no_sk',
		'tgl_sk',
		'file',
		'myNote',
	];

	public function user()
	{
		return $this->belongsTo(User::class);
	}

	public function pengajuan()
	{
		return $this->belongsTo(PengajuanVerifikasi::class, 'pengajuan_id', 'id');
	}
}
