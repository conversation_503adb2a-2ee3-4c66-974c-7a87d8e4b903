<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\MasterPoktanResource\Pages;
use App\Models\MasterDesa;
use App\Models\MasterKabupaten;
use App\Models\MasterKecamatan;
use App\Models\MasterPoktan;
use App\Models\MasterProvinsi;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\{ViewAction,EditAction};
use Filament\Tables\Columns\{TextColumn};
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class MasterPoktanResource extends Resource
{
    protected static ?string $model = MasterPoktan::class;

    protected static ?string $modelLabel = 'Master Poktan';
    protected static ?string $pluralModelLabel = 'Daftar Kelompok Tani';

	protected static ?string $navigationGroup = 'Data Induk';
    protected static ?string $navigationLabel = 'Kelompok Tani';
    protected static ?int $navigationSort = 3;
    protected static ?string $navigationIcon = 'heroicon-o-user-group';

	public static function shouldRegisterNavigation(): bool
    {
        $user = Auth::user();
        if ($user->hasAnyRole(['Super Admin', 'admin'])) {
			return true;
        }else{
        	return false;
        }
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('kode_poktan')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('alamat')
                    ->columnSpanFull(),
                Select::make('provinsi_id')
                    ->label('Provinsi')
                    ->options(function () {
                        return MasterProvinsi::query()
                            ->orderBy('nama')
                            ->pluck('nama', 'provinsi_id')
                            ->toArray();
                    })
                    ->searchable()
                    ->live() // Gunakan live() untuk memastikan komponen dirender ulang
                    ->afterStateUpdated(function (callable $set) {
                        $set('kabupaten_id', null);
                        $set('kecamatan_id', null);
                        $set('kelurahan_id', null);
                    })
                    ->required(),

                Select::make('kabupaten_id')
                    ->label('Kabupaten/Kota')
                    ->options(function (callable $get) {
                        $provinsiId = $get('provinsi_id');
                        if (!$provinsiId) {
                            return [];
                        }

                        return MasterKabupaten::query()
                            ->where('provinsi_id', $provinsiId)
                            ->orderBy('nama_kab')
                            ->pluck('nama_kab', 'kabupaten_id')
                            ->toArray();
                    })
                    ->searchable()
                    ->live() // Gunakan live() untuk memastikan komponen dirender ulang
                    ->afterStateUpdated(function (callable $set) {
                        $set('kecamatan_id', null);
                        $set('kelurahan_id', null);
                    })
                    ->disabled(fn (callable $get) => !$get('provinsi_id'))
                    ->placeholder(fn (callable $get) =>
                        $get('provinsi_id')
                            ? 'Pilih Kabupaten/Kota'
                            : 'Pilih Provinsi terlebih dahulu'
                    )
                    ->required(),

                Select::make('kecamatan_id')
                    ->label('Kecamatan')
                    ->options(function (callable $get) {
                        $kabupatenId = $get('kabupaten_id');
                        if (!$kabupatenId) {
                            return [];
                        }

                        return MasterKecamatan::query()
                            ->where('kabupaten_id', $kabupatenId)
                            ->orderBy('nama_kecamatan')
                            ->pluck('nama_kecamatan', 'kecamatan_id')
                            ->toArray();
                    })
                    ->searchable()
                    ->live() // Gunakan live() untuk memastikan komponen dirender ulang
                    ->afterStateUpdated(function (callable $set) {
                        $set('kelurahan_id', null);
                    })
                    ->disabled(fn (callable $get) => !$get('kabupaten_id'))
                    ->placeholder(fn (callable $get) =>
                        $get('kabupaten_id')
                            ? 'Pilih Kecamatan'
                            : 'Pilih Kabupaten/Kota terlebih dahulu'
                    )
                    ->required(),

                Select::make('kelurahan_id')
                    ->label('Desa/Kelurahan')
                    ->options(function (callable $get) {
                        $kecamatanId = $get('kecamatan_id');
                        if (!$kecamatanId) {
                            return [];
                        }

                        return MasterDesa::query()
                            ->where('kecamatan_id', $kecamatanId)
                            ->orderBy('nama_desa')
                            ->pluck('nama_desa', 'kelurahan_id')
                            ->toArray();
                    })
                    ->searchable()
                    ->live() // Gunakan live() untuk memastikan komponen dirender ulang
                    ->disabled(fn (callable $get) => !$get('kecamatan_id'))
                    ->placeholder(fn (callable $get) =>
                        $get('kecamatan_id')
                            ? 'Pilih Desa/Kelurahan'
                            : 'Pilih Kecamatan terlebih dahulu'
                    )
                    ->required(),
                Forms\Components\TextInput::make('nama_kelompok')
                    ->maxLength(255),
                Forms\Components\TextInput::make('nama_pimpinan')
                    ->maxLength(255),
                Forms\Components\TextInput::make('hp_pimpinan')
                    ->maxLength(255),
                Forms\Components\TextInput::make('status')
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
				TextColumn::make('index')
                    ->label('No')
					->rowIndex(),
                TextColumn::make('nama_kelompok')
                    ->searchable(),
                TextColumn::make('nama_pimpinan')
                    ->searchable(),
				TextColumn::make('provinsi.nama')
					->searchable()->hidden(fn () => Auth::user()->hasRole('dinas')),
				TextColumn::make('kabupaten.nama_kab')
					->searchable()->hidden(fn () => Auth::user()->hasRole('dinas')),
				TextColumn::make('kecamatan.nama_kecamatan')
					->searchable()->visible(fn () => Auth::user()->hasRole('dinas')),
                TextColumn::make('status')
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMasterPoktans::route('/'),
            'create' => Pages\CreateMasterPoktan::route('/create'),
            'view' => Pages\ViewMasterPoktan::route('/{record}'),
            'edit' => Pages\EditMasterPoktan::route('/{record}/edit'),
        ];
    }
}
