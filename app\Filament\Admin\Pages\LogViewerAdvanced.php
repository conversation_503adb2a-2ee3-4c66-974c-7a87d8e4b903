<?php

namespace App\Filament\Admin\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Notifications\Notification;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Filament\Forms\Components\Section;
use Illuminate\Support\Facades\Log;

class LogViewerAdvanced extends Page
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-magnifying-glass';
    protected static ?string $navigationLabel = 'System Log Viewer';
    protected static string $view = 'filament.admin.pages.log-viewer-advanced';
    protected static ?string $navigationGroup = 'System';
	protected static ?string $title = 'Log Viewer';
    protected static ?int $navigationSort = 5;

    public ?array $data = [];
    public $logFiles = [];
    public $selectedFile = null;
    public $perPage = 10;
    public $searchQuery = '';
    public $logLevels = ['ALERT', 'CRITICAL', 'DEBUG', 'ERROR', 'EMERGENCY', 'INFO', 'NOTICE', 'WARNING'];
    public $envValues = [];
    public $selectedLevel = null;
    public $selectedEnv = null;
    public $dateFrom = null;
    public $dateTo = null;
    public $sortField = 'timestamp';
    public $sortDirection = 'desc';

    public static function canAccess(): bool
    {
        return Auth::user()?->hasAnyRole(['admin', 'Super Admin']);
    }

    public function mount(): void
    {
        // Set default sorting to timestamp descending (newest first)
        $this->sortField = 'timestamp';
        $this->sortDirection = 'desc';

        $this->loadLogFiles();
        $this->selectedFile = $this->logFiles[0] ?? null;

        if (empty($this->logFiles)) {
            Notification::make()
                ->title('Informasi')
                ->body('Tidak ada file log yang ditemukan')
                ->info()
                ->send();
        } else {
            $this->loadEnvValues();
        }

        // Initialize form data with default values
        $this->form->fill([
            'selectedFile' => $this->selectedFile,
            'selectedLevel' => $this->selectedLevel,
            'selectedEnv' => $this->selectedEnv,
            'dateFrom' => $this->dateFrom,
            'dateTo' => $this->dateTo,
            'searchQuery' => $this->searchQuery,
        ]);
    }

    protected function loadLogFiles(): void
    {
        $path = storage_path('logs');
        $files = File::glob($path . '/*.log');
		// Log::alert('ini contoh log untuk alert');
		// Log::critical('ini contoh log untuk critical');
		// Log::debug('ini contoh log untuk debug');
		// Log::emergency('ini contoh log untuk emergency');
		// Log::error('ini contoh log untuk error');
		// Log::info('ini contoh log untuk info');
		// Log::notice('ini contoh log untuk notice');
		// Log::warning('ini contoh log untuk warning');

        // Create array with file info for sorting
        $fileData = [];
        foreach ($files as $file) {
            $fileData[] = [
                'path' => $file,
                'name' => basename($file),
                'modified' => File::lastModified($file)
            ];
        }

        // Sort files by last modified time (newest first)
        usort($fileData, function ($a, $b) {
            return $b['modified'] <=> $a['modified'];
        });

        // Extract just the filenames
        $this->logFiles = array_map(function ($file) {
            return $file['name'];
        }, $fileData);

        // Load unique environment values from the selected log file
        if (!empty($this->selectedFile)) {
            $this->loadEnvValues();
        }
    }

    protected function loadEnvValues(): void
    {
        $path = storage_path('logs/' . $this->selectedFile);
        if (!File::exists($path)) {
            $this->envValues = [];
            return;
        }

        // Read only the first part of the file to extract environment values
        // Mengurangi ukuran yang dibaca menjadi 500KB untuk menghemat memori
        $content = file_get_contents($path, false, null, 0, min(File::size($path), 500 * 1024)); // Read max 500KB

        $pattern = '/\[(\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}\.?\d*(?:[\+-]\d{2}:\d{2})?)\]\s+(\w+)\.(\w+):/';
        preg_match_all($pattern, $content, $matches, PREG_SET_ORDER);

        $environments = [];
        $maxEnvs = 20; // Batasi jumlah environment yang diproses

        foreach ($matches as $match) {
            $env = $match[2] ?? '';
            if (!empty($env) && !in_array($env, $environments)) {
                $environments[] = $env;

                // Batasi jumlah environment untuk menghemat memori
                if (count($environments) >= $maxEnvs) {
                    break;
                }
            }
        }

        sort($environments);
        $this->envValues = $environments;

        // Bersihkan variabel yang tidak digunakan untuk menghemat memori
        unset($content, $matches);
    }

    protected function getFormSchema(): array
    {
        return [
            Section::make()
                ->columns(4)
                ->schema([
                    TextInput::make('searchQuery')
                        ->label('Cari')
                        ->placeholder('Cari dalam log...')
                        ->reactive()
                        ->columnSpanFull(),

                    Select::make('selectedFile')
                        ->label('File Log')
                        ->options(array_combine($this->logFiles, $this->logFiles))
                        ->reactive()
                        ->columnSpan(2),

                    Select::make('selectedLevel')
                        ->label('Level')
                        ->options(array_combine($this->logLevels, $this->logLevels))
                        ->placeholder('Semua Level')
                        ->reactive()
                        ->columnSpan(1),

                    Select::make('selectedEnv')
                        ->label('Environment')
                        ->options(empty($this->envValues) ? [] : array_combine($this->envValues, $this->envValues))
                        ->placeholder('Semua Environment')
                        ->reactive()
                        ->columnSpan(1),

                    DatePicker::make('dateFrom')
                        ->label('Dari Tanggal')
                        ->placeholder('Dari tanggal...')
                        ->reactive()
                        ->columnSpan(2),

                    DatePicker::make('dateTo')
                        ->label('Sampai Tanggal')
                        ->placeholder('Sampai tanggal...')
                        ->reactive()
                        ->columnSpan(2),
                ])
        ];
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema($this->getFormSchema())
            ->statePath('data')
            ->live();
    }

    public $page = 1;

    public function resetPage(): void
    {
        // Reset pagination without causing infinite loops
        $this->page = 1;
    }

    public function setPage($page): void
    {
        $this->page = (int) $page;
    }

    public function gotoPage($page)
    {
        $this->page = (int) $page;
    }

    // This method is called when the form state changes
    public function updatedData($value, $path): void
    {
        // Sync form data with class properties
        if ($path === 'selectedFile') {
            $this->selectedFile = $value;
            $this->loadEnvValues();
            $this->resetPage();
        } elseif ($path === 'selectedLevel') {
            $this->selectedLevel = $value;
            $this->resetPage();
        } elseif ($path === 'selectedEnv') {
            $this->selectedEnv = $value;
            $this->resetPage();
        } elseif ($path === 'dateFrom') {
            $this->dateFrom = $value;
            $this->resetPage();
        } elseif ($path === 'dateTo') {
            $this->dateTo = $value;
            $this->resetPage();
        } elseif ($path === 'searchQuery') {
            $this->searchQuery = $value;
            $this->resetPage();
        }
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }

        // Reset halaman saat mengubah pengurutan
        $this->resetPage();
    }

    public function getPaginatedLogs(): LengthAwarePaginator
    {
        $items = $this->parseLogFile();

        // Sort items
        usort($items, function ($a, $b) {
            $aValue = $a[$this->sortField] ?? '';
            $bValue = $b[$this->sortField] ?? '';

            if ($this->sortField === 'timestamp') {
                $aValue = strtotime($aValue);
                $bValue = strtotime($bValue);
            }

            if ($this->sortDirection === 'asc') {
                return $aValue <=> $bValue;
            } else {
                return $bValue <=> $aValue;
            }
        });

        // Gunakan properti page internal daripada mengambil dari request
        $currentPage = $this->page;

        return new LengthAwarePaginator(
            array_slice($items, ($currentPage - 1) * $this->perPage, $this->perPage),
            count($items),
            $this->perPage,
            $currentPage,
            ['path' => request()->url()]
        );
    }

    protected function parseLogFile(): array
    {
        if (empty($this->selectedFile)) {
            return [];
        }

        $path = storage_path('logs/' . $this->selectedFile);

        if (!File::exists($path)) {
            return [];
        }

        // Membatasi ukuran file yang dibaca untuk menghindari masalah performa
        $fileSize = File::size($path);
        $maxSize = 1 * 1024 * 1024; // 1MB - Mengurangi ukuran maksimum untuk mencegah memory exhausted

        // Get values from form data or class properties
        $selectedLevel = $this->data['selectedLevel'] ?? $this->selectedLevel;
        $selectedEnv = $this->data['selectedEnv'] ?? $this->selectedEnv;
        $searchQuery = $this->data['searchQuery'] ?? $this->searchQuery;
        $dateFrom = $this->data['dateFrom'] ?? $this->dateFrom;
        $dateTo = $this->data['dateTo'] ?? $this->dateTo;

        // Gunakan file_get_contents dengan offset dan length untuk membaca file secara bertahap
        if ($fileSize > $maxSize) {
            // Jika file terlalu besar, hanya baca bagian akhir
            $content = file_get_contents($path, false, null, max(0, $fileSize - $maxSize), $maxSize);
            // Pastikan kita mulai dari baris log yang lengkap
            $content = substr($content, strpos($content, '[') ?: 0);
        } else {
            $content = file_get_contents($path);
        }

        $pattern = '/\[(\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}\.?\d*(?:[\+-]\d{2}:\d{2})?)\]\s+(\w+)\.(\w+):\s+(.*?)(?=\[\d{4}-\d{2}-\d{2}|$)/s';

        preg_match_all($pattern, $content, $matches, PREG_SET_ORDER);

        $entries = [];
        $maxEntries = 1000; // Batasi jumlah entri untuk mencegah memory exhausted
        $count = 0;

        foreach ($matches as $match) {
            // Batasi jumlah entri yang diproses
            if ($count >= $maxEntries) {
                break;
            }

            $timestamp = $match[1] ?? '-';
            $env = $match[2] ?? 'local';
            $level = strtoupper($match[3] ?? 'INFO');

            // Batasi panjang pesan untuk menghemat memori
            $message = $match[4] ?? '';
            if (strlen($message) > 1000) {
                $message = substr($message, 0, 1000) . '... [truncated]';
            }

            // Filter by level if selected
            if ($selectedLevel && $level !== $selectedLevel) {
                continue;
            }

            // Filter by environment if selected
            if ($selectedEnv && $env !== $selectedEnv) {
                continue;
            }

            // Filter by search query
            if ($searchQuery &&
                !Str::contains(strtolower($message), strtolower($searchQuery)) &&
                !Str::contains(strtolower($env), strtolower($searchQuery)) &&
                !Str::contains(strtolower($level), strtolower($searchQuery)) &&
                !Str::contains(strtolower($timestamp), strtolower($searchQuery))) {
                continue;
            }

            // Filter by date range
            if ($dateFrom || $dateTo) {
                try {
                    $logDate = Carbon::parse($timestamp);

                    if ($dateFrom) {
                        $fromDate = Carbon::parse($dateFrom)->startOfDay();
                        if ($logDate->lt($fromDate)) {
                            continue;
                        }
                    }

                    if ($dateTo) {
                        $toDate = Carbon::parse($dateTo)->endOfDay();
                        if ($logDate->gt($toDate)) {
                            continue;
                        }
                    }
                } catch (\Exception) {
                    // Skip date filtering if parsing fails
                }
            }

            $entries[] = [
                'timestamp' => $timestamp,
                'env' => $env,
                'level' => $level,
                'message' => trim($message),
                'hash' => md5($timestamp . $env . $level . substr($message, 0, 100)), // Gunakan hanya sebagian pesan untuk hash
            ];

            $count++;
        }

        // Bersihkan variabel yang tidak digunakan untuk menghemat memori
        unset($content, $matches);

        return $entries;
    }

    public function downloadLogFile()
    {
        if (empty($this->selectedFile)) {
            Notification::make()
                ->title('Kesalahan')
                ->body('Tidak ada file log yang dipilih')
                ->danger()
                ->send();
            return;
        }

        $path = storage_path('logs/' . $this->selectedFile);

        if (!File::exists($path)) {
            Notification::make()
                ->title('Kesalahan')
                ->body('File log tidak ditemukan')
                ->danger()
                ->send();
            return;
        }

        return response()->download($path);
    }

    public function clearLogFile()
    {
        if (empty($this->selectedFile)) {
            Notification::make()
                ->title('Kesalahan')
                ->body('Tidak ada file log yang dipilih')
                ->danger()
                ->send();
            return;
        }

        $path = storage_path('logs/' . $this->selectedFile);

        if (!File::exists($path)) {
            Notification::make()
                ->title('Kesalahan')
                ->body('File log tidak ditemukan')
                ->danger()
                ->send();
            return;
        }

        // Clear the file content but keep the file
        file_put_contents($path, '');

        Notification::make()
            ->title('Berhasil')
            ->body('File log berhasil dibersihkan')
            ->success()
            ->send();

        // Refresh the page
        return redirect()->route('filament.admin.pages.log-viewer-advanced');
    }
}
