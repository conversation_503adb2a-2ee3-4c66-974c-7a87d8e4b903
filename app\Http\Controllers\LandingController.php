<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\CmsCategory;
use App\Models\MasterAnggota;
use App\Models\MasterSpatial;
use App\Models\User;
use App\Models\Post;
use Illuminate\Http\Request;

class LandingController extends Controller
{
    public function index()
	{
		$pageTitle = 'Simethris 4.0 @2025';
		$modul = 'Landing';

		$blogs = Post::where('posts.type', 'Artikel')
			->where('posts.is_published', true)
			->orderBy('posts.created_at', 'desc')
			->limit(3)
			->get();

		$news = Post::where('posts.type', 'Berita')
			->where('posts.is_published', true)
			->orderBy('posts.created_at', 'desc')
			->limit(3)
			->get();

		$events = Post::where('posts.type', 'Event')
			->where('posts.is_published', true)
			->orderBy('posts.created_at', 'desc')
			->limit(3)
			->get();

		$upcomingEvents = Post::where('type', 'Event')
			->where('is_published', true)
			->where('date_start', '>', today())
			->orderBy('date_start', 'asc')
			->limit(3)
			->get();

		$about = Post::where('title', 'About Welcome')->first();

		$spatialCount=MasterSpatial::count();
		$spatialSum=MasterSpatial::sum('luas_lahan');
		$luasSpatial = $spatialSum / 10000;
		$anggotaCount=MasterAnggota::count();
		$importirCount = User::whereHas('roles', function ($query) {
			$query->where('name', 'importir');
		})->get()->count();

		return view('welcome', compact(
			'modul',
			'pageTitle',
			'blogs',
			'news',
			'events',
			'upcomingEvents',
			'spatialCount',
			'luasSpatial',
			'anggotaCount',
			'importirCount',
			'about',
		));
	}

	public function postsHome($type)
	{
		$pageTitle = 'Simethris 4.0 @2025';
		$modul = $type;
	
		$articlesQuery = Post::where('type', $type)
			->where('is_published', true);
	
		$articles = $articlesQuery->get();
		$latest = $articlesQuery->latest('published_at')->first();
	
		$topThree = $articlesQuery
			->where('id', '!=', optional($latest)->id) // Mengecualikan $latest
			->latest('published_at')
			->limit(3)
			->get();
	
		$categories = CmsCategory::where('for', $type)->where('is_active', true)->get();
		
		return view('velzon.blog.blog-home', compact(
			'modul',
			'pageTitle',
			'latest',
			'topThree',
			'articles',
			'categories'
		));
	}

	public function categoryHome($type, $category)
	{
		$pageTitle = 'Simethris 4.0 @2025';
	
		$otherCategories = CmsCategory::where('for', $type)->where('slug', '!=', $category)->get();
		$category = CmsCategory::where('for', $type)
			->where('slug', $category)
			->where('is_active', true)
			->first();
	
		if (!$category) {
			abort(404, 'Category not found');
		}
	
		$modul = $category->for . ' ' . $category->name;
		
		return view('velzon.blog.category-home', compact(
			'modul',
			'pageTitle',
			'category',
			'otherCategories'
		));
	}

	public function postArticle($type, $category, $slug)
	{
		$article = Post::where('slug', $slug)->firstOrFail();
		$category = $article->category->slug;
		$pageTitle = $article->title . '|| Simethris 4.0 @2025';
		$modul = $article->type .' || '.$article->title;
		$categories = CmsCategory::where('for', $article->type)->where('is_active', true)->get();

		if($type === 'Event'){
			
			return view('velzon.blog.event-starter', compact(
				'modul',
				'pageTitle',
				'article',
				'categories',
			));
		}

		return view('velzon.blog.blog-starter', compact(
			'modul',
			'pageTitle',
			'article',
			'categories',
		));
	}
}
