<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\MasterProvinsiResource\Pages;
use App\Filament\Admin\Resources\MasterProvinsiResource\RelationManagers;
use App\Filament\Admin\Resources\MasterProvinsiResource\RelationManagers\KabupatenRelationManager;
use App\Models\MasterProvinsi;
use Filament\Forms;
use Filament\Forms\Components\{Grid, Group, Section, Textarea, TextInput};
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\{TextColumn};
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MasterProvinsiResource extends Resource
{
    protected static ?string $model = MasterProvinsi::class;
    protected static ?string $modelLabel = 'Master Provinsi';
    protected static ?string $pluralModelLabel = 'Daftar Provinsi';

	protected static ?string $navigationGroup = 'Data Induk';
    protected static ?string $navigationLabel = 'Provinsi';
    protected static ?int $navigationSort = 0;
    protected static ?string $navigationIcon = 'heroicon-o-globe-asia-australia';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('provinsi_id')
                    ->required()
                    ->maxLength(2),
                TextInput::make('kode_dagri'),
                TextInput::make('nama')
                    ->required()
                    ->columnSpanFull(),
                Grid::make([
                    'default' => 1,
                    'sm' => 1,
                    'lg' => 2,
                ])->schema([
                    Group::make([
                        TextInput::make('lat')
                            ->columnSpanFull(),
                        TextInput::make('lng')
                            ->columnSpanFull(),
                    ])->columnStart(0),
                    Group::make([
                            Textarea::make('polygon')
                                ->rows(5)
                                ->columnSpanFull(),
                    ])->columnStart(0),
                ])->columnStart(0),
            ])->columns(2);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('provinsi_id')
                    ->label('Kode BPS')
                    ->searchable(),
                TextColumn::make('kode_dagri')
                    ->label('Kemendagri')
                    ->searchable(),
                TextColumn::make('nama')
                    ->label('Provinsi')
                    ->searchable(),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->hiddenLabel(),
                Tables\Actions\EditAction::make()->modalHeading('Perbarui Provinsi')->hiddenLabel(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            KabupatenRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMasterProvinsis::route('/'),
            // 'create' => Pages\CreateMasterProvinsi::route('/create'),
            'view' => Pages\ViewMasterProvinsi::route('/{record}'),
            // 'edit' => Pages\EditMasterProvinsi::route('/{record}/edit'),
        ];
    }
}
