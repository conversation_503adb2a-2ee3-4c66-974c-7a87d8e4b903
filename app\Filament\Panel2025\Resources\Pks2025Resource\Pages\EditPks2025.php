<?php

namespace App\Filament\Panel2025\Resources\Pks2025Resource\Pages;

use App\Filament\Panel2025\Resources\Pks2025Resource;
use App\Models\Commitment2025;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;

class EditPks2025 extends EditRecord
{
    protected static string $resource = Pks2025Resource::class;
    protected static ?string $title = 'PKS';
    public function getHeading(): string
	{
        $noPks = $this->record ? $this->record->no_perjanjian : '#';
        return 'PKS No: '.$noPks;
	}
    public function getSubheading(): ?string
    {
        $noIjin = $this->record ? $this->record->no_ijin : '##';
        return 'untuk PPRK No: ' . $noIjin;
    }

    protected function getRedirectUrl(): string
    {
        $noIjin = $this->record->no_ijin;
        $commitmentId = Commitment2025::where('no_ijin', $noIjin)->select('id')->first()->id;
        return url(route('filament.panel2025.resources.commitment2025s.view', $commitmentId));
    }

    public static string | Alignment $formActionsAlignment = Alignment::Right;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\ViewAction::make(),
            // Actions\DeleteAction::make(),
        ];
    }

	// protected function getFormActions(): array
	// {
    //     return [];
	// }
}
