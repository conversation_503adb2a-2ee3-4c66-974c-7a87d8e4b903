<?php

namespace App\Filament\Panel2025\Resources\Realisasi2025Resource\Pages;

use App\Filament\Panel2025\Resources\Realisasi2025Resource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;

class EditRealisasi2025 extends EditRecord
{
    protected static string $resource = Realisasi2025Resource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\ViewAction::make(),
            // Actions\DeleteAction::make(),
        ];
    }

    public function getHeading(): string
	{
        $spatial = $this->record ? $this->record->kode_spatial : '##';
        return 'Kode Lokasi: ' . $spatial;
	}

    public static string | Alignment $formActionsAlignment = Alignment::Right;

    public function getSubheading(): ?string
    {
        $petani = $this->record->anggota->nama_petani;
        $poktan = $this->record->poktan->nama_kelompok;
        $luas = $this->record->luas_lahan;
        return 'Petani: ' .$petani . ' / ' . $poktan .'. <PERSON><PERSON>: ' . number_format($luas,0,',','.') . ' m2';
    }
}
