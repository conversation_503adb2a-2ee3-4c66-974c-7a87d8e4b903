<?php

namespace App\Filament\Panel2025\Resources\Commitment2025Resource\Pages;

use App\Filament\Panel2025\Resources\Commitment2025Resource;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Actions\Action;
// use Filament\Actions;
use Filament\Forms\Components\{Actions, Hidden, Placeholder, Repeater, Section, Select, TextInput};
use Filament\Forms\Components\Actions\Action as ActionsAction;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Routing\Route;

class LaporanKegiatan extends EditRecord
{
    protected static string $resource = Commitment2025Resource::class;
    protected static ?string $title = 'Laporan Perkembangan';

    protected function getHeaderActions(): array
    {
        return [];
    }

    protected function getFormActions(): array
	{
        return [];
	}

    public function getHeading(): string
	{
        return 'Laporan Realisasi Komitmen Tanam dan Produksi ';
	}

    public function getSubheading(): ?string
    {
        $noIjin = $this->record ? $this->record->no_ijin : '##';
        return 'untuk PPRK No: ' . $noIjin;
    }

    public function form(Form $form): Form
	{
		return $form
		->schema([
            Section::make()
                ->schema([
                    TextInput::make('cari')
                        ->label('Cari Lokasi')
                        ->live()
                        // ->inlineLabel()
                        ->prefixIcon('icon-search')
                        ->placeholder('cari data')
                        ->afterStateUpdated(fn ($state, callable $set, callable $get) => 
                            $set('lokasikegiatan', collect($get('lokasikegiatan'))
                                ->filter(fn ($item) => str_contains(strtolower($item['kode_spatial']), strtolower($state)))
                                ->toArray()
                            )
                        ),
                    Repeater::make('lokasikegiatan')
                        ->grid(3)
                        ->columns(3)
                        ->reorderableWithButtons()
                        ->reorderableWithDragAndDrop()
                        ->itemLabel(fn (array $state): ?string => $state['kode_spatial'] ?? null)
                        ->hiddenLabel()
                        ->addable(false)
                        ->deletable(false)->collapsed()
                        ->relationship('realisasi')
                        ->schema([
                            Hidden::make('id'),
                            Placeholder::make('Nama Anggota')
                                ->inlineLabel()
                                ->columnSpanFull()
                                ->content(fn ($record) => $record->anggota->nama_petani . ' / '.$record->anggota->ktp_petani),
                            Placeholder::make('Kelompok Tani')
                                ->inlineLabel()
                                ->columnSpanFull()
                                ->content(fn ($record) => $record->poktan->nama_kelompok),
                            Placeholder::make('Wilayah')
                                ->inlineLabel()
                                ->columnSpanFull()
                                ->content(fn ($record) => $record->spatial->kecamatan->nama_kecamatan . ' - ' .$record->spatial->kabupaten->nama_kab),
                            Placeholder::make('Luas Tanam')
                                ->inlineLabel()
                                ->columnSpanFull()
                                ->content(fn ($record) => number_format($record->luas_tanam,0,',','.') . ' / ' .number_format($record->luas_lahan,0,',','.') . ' m2'),
        
                            Placeholder::make('Produksi')
                                ->inlineLabel()
                                ->columnSpanFull()
                                ->content(fn ($record) => number_format($record->volume,0,',','.').' kg'),
                                
                        ])
                        ->extraItemActions([
                            ActionsAction::make('Realisasi')
                                ->hiddenLabel()
                                ->icon('icon-journal-richtext')
                                ->iconButton()
                                ->color('warning')
                                ->action(function (array $arguments, Repeater $component) {
                                    $itemData = $component->getItemState($arguments['item']);
                                    // dd($itemData['id']);
                                    if (isset($itemData['id'])) {
                                        return redirect()->route('filament.panel2025.resources.realisasi2025s.edit', $itemData['id']);
                                    }
                                }),

                            ActionsAction::make('Peta')
                                ->hiddenLabel()
                                ->icon('icon-map-fill')
                                ->iconButton()
                                ->color('success')
                                ->action(function (array $arguments, Repeater $component) {
                                    $itemData = $component->getItemState($arguments['item']);
                                    // dd($itemData['id']);
                                    if (isset($itemData['id'])) {
                                        return redirect()->route('panel.2025.report.singleMap', $itemData['id']);
                                    }
                                }),
                        ])

                ])
        ]);
    }
}
