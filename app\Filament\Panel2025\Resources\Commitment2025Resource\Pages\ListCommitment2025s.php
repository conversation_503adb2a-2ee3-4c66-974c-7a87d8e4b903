<?php

namespace App\Filament\Panel2025\Resources\Commitment2025Resource\Pages;

use App\Filament\Panel2025\Resources\Commitment2025Resource;
use App\Models\ActionLog;
use App\Models\Commitment2025;
use App\Models\Completed;
use App\Models\DetailRealisasi2025;
use App\Models\PengajuanVerifikasi;
use App\Models\Pks2025;
use App\Models\Realisasi2025;
use App\Models\Userfile;
use Filament\Actions;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Table;
use Filament\Tables;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\Action as TableAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ListCommitment2025s extends ListRecords
{
	protected static string $resource = Commitment2025Resource::class;

	protected function getHeaderActions(): array
	{
		return [
			Actions\CreateAction::make()
				->label('Tambah PPRK')
				->visible(fn () => Auth::user()->hasRole('importir')),
		];
	}

	public function table(Table $table): Table
	{
		return $table
			->columns([
				TextColumn::make('no_ijin')
					->label('No. PPRK')
					->searchable()
					->sortable(),
				TextColumn::make('volume_riph')
					->label('Volume RIPH')
					->numeric()
					->alignEnd()
					->suffix('ton')->description(function ($state) {
						return 'setara ' . number_format($state/1000,2,',','.') . ' ton';
					})
					->sortable(),
				TextColumn::make('luas_wajib_tanam')
					->label('Komitmen Tanam')
					->numeric()
					->alignEnd()
					->suffix('m2')->description(function ($state) {
						return 'setara ' . number_format($state/10000,2,',','.') . ' ha';
					})
					->sortable(),
				TextColumn::make('volume_produksi')
					->label('Komitmen Produksi')
					->numeric()
					->alignEnd()
					->suffix('kg')
					->description(function ($state) {
						return 'setara ' . number_format($state/1000,2,',','.') . ' ton';
					})
					->sortable(),
				TextColumn::make('status')
					->label('Status')
					->badge()
					->color(fn (string $state): string => match ($state) {
						'Lunas' => 'success',
						'Belum Lunas' => 'danger',
					})
					->searchable()
					->sortable(),
			])
			->filters([
				SelectFilter::make('status')
					->options([
						'Lunas' => 'Lunas',
						'Belum Lunas' => 'Belum Lunas',
					])
					->label('Status'),
			])
			->actions([
				TableAction::make('skl')
					->label('SKL')
					->hiddenLabel()
					->icon('icon-award-fill')
					->visible(fn ($record) => $record->skl)
					->url(fn($record) => url('/'.$record->skl)),

				TableAction::make('e-filling')
					->label('e-Filling')
					->hiddenLabel()
					->visible(function ($record) {
						return $record->status !== 'Lunas' && self::isOwner($record) && !self::checkPksCompletion($record);
					})
					->tooltip('Pilih Lokasi Tanam')
					->url(fn($record) => '/panel/2025/location-picker-page?noRiph=' . preg_replace('/[\/.\-]/', '', $record->no_ijin))
					->icon('heroicon-s-map-pin'),

				TableAction::make('memberConfirm')
					->label('Konfirmasi Kelompok Tani')
					->hiddenLabel()
					->color('warning')
					->icon('icon-person-fill-check')
					->tooltip('Konfirmasi Lokasi Tanam')
					->visible(function ($record) {
						return $record->status !== 'Lunas' && self::isOwner($record) && self::hasMemberToConfirm($record) && !self::checkPksCompletion($record);
					})
					->url(fn($record) => route('filament.panel2025.resources.commitment2025s.locationConfirm', ['record' => $record->id])),

				TableAction::make('daftarpks')
					->label('Isi data PKS')
					->hiddenLabel()
					->visible(function ($record) {
						return $record->status !== 'Lunas' && self::isOwner($record);
					})
					->tooltip('Data PKS')
					->url(fn($record) => route('filament.panel2025.resources.commitment2025s.daftarPks', ['record' => $record->id]))
					->icon('icon-journal-bookmark-fill'),

				TableAction::make('reporting')
					->hiddenLabel()
					->tooltip('Lihat Daftar lokasi')
					->visible(function ($record) {
						return $record->status !== 'Lunas' && self::isOwner($record);
					})
					->color('success')
					->url(fn($record) => route('filament.panel2025.resources.realisasi2025s.daftarrealisasi', ['noijin' => preg_replace('/[\/.\-]/', '', $record->no_ijin)]))
					->icon('icon-ui-checks'),

				TableAction::make('documents')
					->label('Unggah Berkas Kelengkapan')
					->hiddenLabel()
					->visible(function ($record) {
						if ($record->status === 'Lunas') {
							return false; // Jika status "Lunas", tombol tidak tampak ke siapapun
						}
						return Auth::user()->hasRole('importir');
					})
					->tooltip('Unggah berkas kelengkapan')
					->color('success')
					->url(fn($record) => route('filament.panel2025.resources.userfiles.berkastanam', ['record' => $record->id]))
					->icon('icon-file-arrow-up-fill'),

				TableAction::make('ajuveriftanam')
					->label('Verifikasi Tanam')
					->hiddenLabel()
					->visible(function ($record) {
						return $record->status !== 'Lunas' && self::allowToApplyVerifTanam($record);
					})
					->tooltip('Ajukan Verifikasi Tanam')
					->color('info')
					->icon('icon-person-raised-hand')
					->requiresConfirmation()
					->modalHeading('Ajukan Verifikasi Tanam')
					->modalDescription('Anda akan mengajukan verifikasi, lanjutkan?')
					->action(function ($record) {
						$kind = 'PVT';
						DB::beginTransaction();
						try {
							$noIjin = $record->no_ijin;
							$npwp   = $record->npwp;

							$pengajuan = new PengajuanVerifikasi();
							$pengajuan->kind = $kind;
							$pengajuan->no_ijin = $noIjin;
							$pengajuan->npwp = $npwp;
							$pengajuan->status = 0;

							$pengajuan->save();

							$newId = $pengajuan->id;
							$noAju = $pengajuan->no_pengajuan;
							DB::commit();
							Notification::make()
								->title('Pengajuan Verifikasi Tanam')
								->body('Permohonan Verifikasi Tanam berhasil diajukan, dengan Nomor Pengajuan: ' . $noAju)
								->success()
								->send();

							return redirect(route('filament.panel2025.resources.pengajuan-verifikasis.verifReport', ['record' => $newId]));
						} catch (\Exception $e) {
							DB::rollBack();
							Notification::make()
								->title('Pengajuan Verifikasi')
								->body('Terjadi kesalahan: ' . $e->getMessage())
								->danger()
								->send();
						}
					}),

				TableAction::make('ajuverifproduksi')
					->label('Verifikasi Produksi')
					->hiddenLabel()
					->visible(function ($record) {
						return $record->status !== 'Lunas' && self::allowToApplyVerifProduksi($record);
					})
					->tooltip('Ajukan Verifikasi Produksi')
					->color('warning')
					->icon('icon-person-raised-hand')
					->requiresConfirmation()
					->modalHeading('Ajukan Verifikasi Produksi')
					->modalDescription('Anda akan mengajukan verifikasi, lanjutkan?')
					->action(function ($record) {
						$kind = 'PVP';
						DB::beginTransaction();
						try {
							$noIjin = $record->no_ijin;
							$npwp   = $record->npwp;

							$pengajuan = new PengajuanVerifikasi();
							$pengajuan->kind = $kind;
							$pengajuan->no_ijin = $noIjin;
							$pengajuan->npwp = $npwp;
							$pengajuan->status = 0;

							$pengajuan->save();

							$newId = $pengajuan->id;
							$noAju = $pengajuan->no_pengajuan;
							DB::commit();
							Notification::make()
								->title('Pengajuan Verifikasi Produksi')
								->body('Permohonan Verifikasi Produksi berhasil diajukan, dengan Nomor Pengajuan: ' . $noAju)
								->success()
								->send();

							return redirect(route('filament.panel2025.resources.pengajuan-verifikasis.verifReport', ['record' => $newId]));
						} catch (\Exception $e) {
							DB::rollBack();
							Notification::make()
								->title('Pengajuan Verifikasi')
								->body('Terjadi kesalahan: ' . $e->getMessage())
								->danger()
								->send();
						}
					}),
				TableAction::make('ajuSkl')
					->label('Aju SKL')
					->hiddenLabel()
					->visible(function ($record) {
						return $record->status !== 'Lunas' && self::allowToApplySKL($record);
					})
					->tooltip('Ajukan SKL')
					->color('success')
					->icon('icon-person-raised-hand')
					->requiresConfirmation()
					->modalHeading('Ajukan Penerbitan SKL')
					->modalDescription('Anda akan mengajukan SKL, lanjutkan?')
					->action(function ($record) {
						$kind = 'PVS';
						DB::beginTransaction();
						try {
							$noIjin = $record->no_ijin;
							$npwp   = $record->npwp;

							$pengajuan = new PengajuanVerifikasi();
							$pengajuan->kind = $kind;
							$pengajuan->no_ijin = $noIjin;
							$pengajuan->npwp = $npwp;
							$pengajuan->status = 0;

							$pengajuan->save();

							$newId = $pengajuan->id;
							$noAju = $pengajuan->no_pengajuan;
							DB::commit();
							Notification::make()
								->title('Pengajuan Keterangan Lunas')
								->body('Permohonan Penerbitan SKL berhasil diajukan, dengan Nomor Pengajuan: ' . $noAju)
								->success()
								->send();

							return redirect(route('filament.panel2025.resources.pengajuan-verifikasis.verifReport', ['record' => $newId]));
						} catch (\Exception $e) {
							DB::rollBack();
							Notification::make()
								->title('Pengajuan SKL')
								->body('Terjadi kesalahan: ' . $e->getMessage())
								->danger()
								->send();
						}
					}),

				EditAction::make()
					->hiddenLabel()
					->visible(function ($record) {
						return $record->status !== 'Lunas' && self::isOwner($record);
					})
					->tooltip('Pembaruan Data PPRK')
					->hidden(fn ($record) => $record->mySpatials->contains(fn ($spatial) => $spatial->status != 0))
					->modalHeading('Pembaruan Data PPRK'),

				DeleteAction::make()
					->iconButton()
					->visible(function ($record) {
						return $record->completed == null && self::isOwner($record);
					})
					->requiresConfirmation()
					->modalHeading(fn ($record) => "Hapus Data {$record->no_ijin}")
					->modalDescription(fn ($record) => "Anda akan menghapus data PPRK {$record->no_ijin}. Seluruh data terkait beserta turunannya akan terhapus. Tindakan ini tidak dapat dibatalkan. Anda yakin akan melakukan ini?")
					->modalSubmitActionLabel('Ya, Hapus Semua Data')
					->form([
						Textarea::make('reason')
							->label('Alasan Penghapusan')
							->required()
							->autosize()
							->placeholder('Berikan alasan penghapusan data')
							->columnSpanFull()
					])
					->action(function ($data, Commitment2025 $record):void {
						
						$noIjin = $record->no_ijin;
						$npwp = $record->npwp;

						if (Completed::where('no_ijin', $noIjin)->exists()) {
							Notification::make()
								->title('Tidak Dapat Menghapus')
								->body("PPRK No: {$noIjin} sudah memiliki SKL dan tidak dapat dihapus.")
								->danger()
								->send();
							return;
						}
						try {
							DB::beginTransaction();

							$record->load([
								'myRegions',
								'myPenangkars',
								'pks',
								'realisasi',
								'userfiles',
								'ajuverif',
							]);

							collect([
								$record->myRegions,
								$record->myPenangkars,
								$record->pks,
								$record->realisasi,
								$record->userfiles,
								$record->ajuverif,
							])->each(fn ($rel) => $rel?->each->forceDelete());

							DetailRealisasi2025::where([
								'npwp' => $npwp,
								'no_ijin' => $noIjin,
							])->forceDelete();

							ActionLog::create([
								'log_type' => 'Delete',
								'model_type' => get_class($record),
								'model_id' => $record->id,
								'npwp' => $record->npwp,
								'no_ijin' => $record->no_ijin,
								'request_by' => Auth::id(),
								'data' => $data,
								'metadata' => $record->toArray(),
							]);

							DB::table('t2025_master_spatials')
								->where('reserved_by', $record->no_ijin)
								->update([
									'status' => 0,
									'reserved_at' => null,
									'reserved_by' => null,
									'updated_at' => now()
								]);
							// Hapus data commitment
							$record->forceDelete();
							DB::commit();
							Notification::make()
								->title('Data Berhasil Dihapus')
								->body("Data RIPH {$noIjin} beserta semua data terkait telah berhasil dihapus secara permanen.")
								->success()
								->send();

						} catch (\Exception $e) {
							DB::rollBack();
							Notification::make()
								->title('Error')
								->body("Terjadi kesalahan saat menghapus data: {$e->getMessage()}")
								->danger()
								->send();
						}
					})
					/*
						can delete if no:
						return $this->hasOne(Completed::class, 'no_ijin', 'no_ijin');

						db transaction
						force delete
						return $this->hasMany(Penangkar2025::class, 'no_ijin', 'no_ijin');
						return $this->hasMany(MasterSpatial::class, 'reserved_by', 'no_ijin');
						return $this->hasMany(Pks2025::class, 'no_ijin', 'no_ijin');
						return $this->hasMany(Realisasi2025::class, 'no_ijin', 'no_ijin');
						return $this->hasMany(Userfile::class, 'no_ijin', 'no_ijin');
						return $this->hasMany(PengajuanVerifikasi::class, 'no_ijin', 'no_ijin');
						return $this->hasMany(DetailRealisasi2025::class, 'no_ijin', 'no_ijin')->where('jenis_keg', 'panen');
						return $this->hasMany(DetailRealisasi2025::class, 'no_ijin', 'no_ijin')->where('jenis_keg', 'distribusi');
						return $this->hasMany(DetailRealisasi2025::class, 'no_ijin', 'no_ijin')->where('jenis_keg', 'pupuk');
						return $this->hasMany(DetailRealisasi2025::class, 'no_ijin', 'no_ijin')->where('jenis_keg', 'mulsa');
						return $this->hasMany(VerificatorAssignment::class, 'no_ijin', 'no_ijin')->where('kode_pengajuan', $latestPengajuan->no_pengajuan);
						return $this->hasMany(VerificatorAssignment::class, 'no_ijin', 'no_ijin')->where('kode_pengajuan', $latestPengajuan->no_pengajuan);

						record delete action to
							ActionLog::create([
								'log_type' => 'Delete',
								'model_type' => get_class($record),
								'model_id' => $record->id,
								'npwp' => $record->npwp,
								'no_ijin' => $record->no_ijin,
								'request_by' => Auth::id(),
								'data' => $data['reason'],
								'metadata' => $record->toArray(),
							]);

						last to delete
						return $this->hasMany(CommitmentRegion::class, 'no_ijin', 'no_ijin');
					*/
					
					,
			])
			->bulkActions([
				Tables\Actions\BulkActionGroup::make([
					// Tables\Actions\DeleteBulkAction::make(),
				]),
			]);
	}

	public static function isOwner($record)
	{
		return Auth::user()->hasRole('importir') && $record->user_id === Auth::id();
	}

	public static function hasMemberToConfirm($record)
	{
		return DB::table('t2025_master_spatials')->where('reserved_by', $record->no_ijin)->exists();
	}

	public static function checkPksCompletion($record)
	{
		$pksList = Pks2025::where('no_ijin', $record->no_ijin)
			->select('berkas_pks')
			->get();
		if ($pksList->isEmpty()) {
			return false;
		}
		if ($pksList->contains('berkas_pks', null)) {
			return false;
		}
		return true;
	}

	public static function checkFileTanamCompletion($record)
	{
		$requiredKinds = [
			'spvt',
			'sptjmt',
			'rta',
			'sphtanam',
			'spht',
			'spdst',
			'logbook',
			'la',
		];

		$files = Userfile::where('no_ijin', $record->no_ijin)
			->whereIn('kind', $requiredKinds)
			->pluck('file_url', 'kind');
		if ($files->count() !== count($requiredKinds)) {
			return false;
		}

		if ($files->contains(null)) {
			return false;
		}

		return true;
	}

	public static function checkFileProduksiCompletion($record)
	{
		$requiredKinds = [
			'spvt',
			'spvp',
			'sptjmt',
			'sptjmp',
			'rta',
			'rpo',
			'spht',
			'sphb',
			'spdst',
			'spdsp',
			'logbook',
			'la'
		];

		$files = Userfile::where('no_ijin', $record->no_ijin)
			->whereIn('kind', $requiredKinds)
			->pluck('file_url', 'kind');
		if ($files->count() !== count($requiredKinds)) {
			return false;
		}

		if ($files->contains(null)) {
			return false;
		}

		return true;
	}

	public static function checkDataTanamCompletion($record)
	{
		$realisasis = Realisasi2025::where('no_ijin', $record->no_ijin)
			->select('id', 'no_ijin')
			->get();

		$detailRealisasis = DetailRealisasi2025::whereIn('realisasi_id', $realisasis->pluck('id'))
			->where('jenis_keg', 'tanam')
			->select('jenis_keg', 'value')
			->get();

		$luasTanam = $detailRealisasis->sum('value');

		$wajibTanam = $record->luas_wajib_tanam;

		if ($wajibTanam == 0) {
			return false;
		}
		if (($luasTanam / $wajibTanam) < 0.1) {
			return false;
		}

		return true;
	}

	public static function checkDataProduksiCompletion($record)
	{
		$realisasis = Realisasi2025::where('no_ijin', $record->no_ijin)
			->select('id', 'no_ijin')
			->get();

		$detailRealisasis = DetailRealisasi2025::whereIn('realisasi_id', $realisasis->pluck('id'))
			->where('jenis_keg', 'panen')
			->select('jenis_keg', 'value')
			->get();

		$panen = $detailRealisasis->sum('value');

		$wajibPanen = $record->volume_produksi;

		if ($wajibPanen == 0) {
			return false;
		}
		if (($panen / $wajibPanen) < 0.95) {
			return false;
		}

		return true;
	}

	public static function checkVerifTanam($record)
	{
		$verifikasi = PengajuanVerifikasi::where('no_ijin', $record->no_ijin)
			->where('kind', 'PVT')
			->select('no_ijin', 'status')
			->latest('created_at')
			->first();

		if (!$verifikasi) {
			return true;
		}

		if (in_array($verifikasi->status, ['0', '1', '2', '3','5'])) {
			return false;
		}

		return true;
	}

	public static function checkVerifProduksi($record)
	{
		$verifikasi = PengajuanVerifikasi::where('no_ijin', $record->no_ijin)
			->where('kind', 'PVP')
			->select('no_ijin', 'status')
			->latest('created_at')
			->first();

		if (!$verifikasi) {
			return true;
		}

		if (in_array($verifikasi->status, ['0', '1', '2', '3', '5'])) {
			return false;
		}

		return true;
	}

	public static function checkVerifSkl($record)
	{
		$verifikasi = PengajuanVerifikasi::where('no_ijin', $record->no_ijin)
			->where('kind', 'PVS')
			->select('no_ijin', 'status')
			->latest('created_at')
			->first();

		if (!$verifikasi) {
			return true;
		}

		if (in_array($verifikasi->status, ['0', '1', '2', '3','5'])) {
			return false;
		}

		return true;
	}

	public static function checkIsCompleted($record)
	{
		return !DB::table('completeds')->where('no_ijin', $record->no_ijin)->exists();
	}

	public static function allowToApplyVerifTanam($record)
	{
		self::isOwner($record);
		self::checkDataTanamCompletion($record);
		self::checkPksCompletion($record);
		self::checkFileTanamCompletion($record);
		self::checkVerifTanam($record);
		self::checkIsCompleted($record);
	}

	public static function allowToApplyVerifProduksi($record)
	{
		self::isOwner($record);
		self::checkDataProduksiCompletion($record);
		self::checkPksCompletion($record);
		self::checkFileProduksiCompletion($record);
		self::checkVerifProduksi($record);
		self::checkIsCompleted($record);
	}

	public static function allowToApplySKL($record)
	{
		self::isOwner($record);

		$verifikasi = PengajuanVerifikasi::where('no_ijin', $record->no_ijin)
			->where('kind', 'PVP')
			->select('no_ijin', 'status')
			->latest('created_at')
			->first();

		if ($verifikasi && $verifikasi->status !== '4') {
			return false;
		}

		self::checkPksCompletion($record);

		self::checkDataTanamCompletion($record);
		self::checkFileTanamCompletion($record);
		self::checkVerifTanam($record);

		self::checkDataProduksiCompletion($record);
		self::checkFileProduksiCompletion($record);
		self::checkVerifProduksi($record);

		self::checkVerifSkl($record);
		self::checkIsCompleted($record);
	}
}
