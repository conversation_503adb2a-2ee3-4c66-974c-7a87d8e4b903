<?php

namespace App\Http\Controllers;

use App\Models\SupportTicket;
use App\Models\SupportTicketMessage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class SupportTicketController extends Controller
{
    public function reply(Request $request, $ticketId)
    {
        $ticket = SupportTicket::findOrFail($ticketId);

        $request->validate([
            'message' => 'required|string',
            'attachment' => 'nullable|file|max:5120|mimes:jpg,jpeg,png,pdf,doc,docx,txt',
        ]);

        $user = Auth::user();
        
        // Validasi permission
        if (!$this->canReply($user, $ticket)) {
            return back()->with('error', 'Anda tidak memiliki izin untuk membalas tiket ini.');
        }

        $data = [
            'ticket_id' => $ticket->id,
            'user_id' => $user->id,
            'message' => $request->message,
            'parent_id' => $request->parent_id,
        ];

        // Handle file upload
        if ($request->hasFile('attachment')) {
            $file = $request->file('attachment');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('support-attachments', $filename, 'public');
            $data['attachment'] = $path;
        }

        // Create message
        SupportTicketMessage::create($data);

        // Update ticket
        $ticket->update([
            'last_replied_by' => $user->hasAnyRole(['admin', 'Super Admin', 'support']) ? 'staff' : 'user',
            'last_activity_at' => now(),
            'status' => $ticket->status === 'closed' ? 'open' : $ticket->status,
        ]);

        return back()->with('success', 'Balasan berhasil dikirim.');
    }

    private function canReply($user, $ticket)
    {
        // Cek apakah tiket masih bisa di-reply
        if (in_array($ticket->status, ['closed'])) {
            return false;
        }
        
        // User yang membuat tiket bisa reply
        if ($ticket->user_id === $user->id) {
            return true;
        }
        
        // Staff yang di-assign bisa reply
        if ($ticket->staff_id === $user->id) {
            return true;
        }
        
        // Admin dan Super Admin bisa reply
        if ($user->hasAnyRole(['admin', 'Super Admin'])) {
            return true;
        }
        
        return false;
    }
}
