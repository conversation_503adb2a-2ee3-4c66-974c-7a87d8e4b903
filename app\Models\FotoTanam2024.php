<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class FotoTanam2024 extends Model
{
	use HasFactory, SoftDeletes, LogsActivity;
	public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
			->logFillable('*')
			->logOnlyDirty();
    }

	public $table = 'foto_tanams';

	protected $fillable = [
		'realisasi_id',
		'filename',
		'url',
	];

	public function datarealisasi()
	{
		return $this->belongsTo(DataRealisasi2024::class, 'realisasi_id');
	}
}
