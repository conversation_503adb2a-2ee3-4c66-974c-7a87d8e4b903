<?php

namespace App\Filament\Panel2025\Pages;

use App\Models\Commitment2025;
use App\Models\CommitmentRegion;
use App\Models\MasterSpatial;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

class LocationPickerPage extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-map-pin';
    protected static string $view = 'filament.panel2025.pages.location-picker-page';
    protected static bool $shouldRegisterNavigation = false;

    public ?string $noRiph = null;
    public ?string $noIjin = null;

	public function getTitle(): string
	{
		return 'Pemilihan Lokasi Tanam';
	}

	public function getHeading(): string
	{
		return 'Pemilihan Lokasi Tanam';
	}

	// public function getSubheading(): ?string
	// {
	// 	$noIjin = $this->record ? $this->record->no_ijin : '##';
	// 	return 'untuk PPRK No: ' . $noIjin;
	// }

    public static function getRoutes(): \Illuminate\Routing\Router
    {
        return Route::group([
            'middleware' => ['auth:sanctum'],
        ], function () {
            // Halaman utama
            Route::get('location-picker-page', static::class)
                ->name(static::getRouteName());
        });
    }

    public function mount()
    {
        // Validasi akses
        if (!Auth::check()) {
            return redirect()->route('logout');
        }

        if(!Auth::user()->hasRole('importir')){
            Notification::make()
                ->danger()
                ->title('Akses Ditolak!')
                ->body('Anda tidak memiliki izin untuk mengakses data di halaman tujuan.')
                ->send();

            return redirect()->back();
        }

        // Ambil noRiph dari query string
        $noRiph = request()->query('noRiph');
        if (!$noRiph) {
            Notification::make()
                ->danger()
                ->title('Parameter Tidak Valid')
                ->body('Parameter noRiph tidak ditemukan.')
                ->send();

            return redirect()->back();
        }

        // Format noRiph menjadi noIjin
        $this->noRiph = $noRiph;
        $this->noIjin = vsprintf('%s/%s.%s/%s/%s/%s', [
            substr($noRiph, 0, 4),
            substr($noRiph, 4, 2),
            substr($noRiph, 6, 3),
            substr($noRiph, 9, 1),
            substr($noRiph, 10, 2),
            substr($noRiph, 12, 4),
        ]);
    }

    protected function getHeaderActions(): array
    {
        return [];
    }
}
