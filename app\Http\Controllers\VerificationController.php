<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\Userfile;
use App\Models\Commitment2025;

class VerificationController extends Controller
{
    /**
     * Verifikasi logbook melalui QR Code
     */
    public function verifyLogbook(Request $request)
    {
        try {
            // Ambil parameter dari URL
            $mask = $request->input('mask');
            $hash = $request->input('hash');
            
            if (!$mask || !$hash) {
                return view('verification.invalid', [
                    'message' => 'Parameter verifikasi tidak lengkap.'
                ]);
            }
            
            // Ekstrak shortHash (8 karakter terakhir dari mask)
            $shortHash = substr($mask, -8);
            $reversedNoIjin = substr($mask, 0, -8);
            
            // Reverse kembali no_ijin
            $noIjin = strrev($reversedNoIjin);
            
            // Cari data commitment berdasarkan no_ijin
            $commitment = Commitment2025::where('no_ijin', 'like', '%' . $noIjin . '%')->first();
            
            if (!$commitment) {
                Log::warning('Verifikasi logbook: No ijin tidak ditemukan', [
                    'mask' => $mask,
                    'reversed_no_ijin' => $reversedNoIjin,
                    'no_ijin' => $noIjin
                ]);
                
                return view('verification.invalid', [
                    'message' => 'Dokumen tidak ditemukan.'
                ]);
            }
            
            // Ambil NPWP dari commitment
            $npwp = $commitment->npwp;
            $sanitizedNpwp = preg_replace('/[^A-Za-z0-9]/', '', $npwp);
            $sanitizedNoIjin = preg_replace('/[^A-Za-z0-9]/', '', $noIjin);
            $reversedNoIjinCheck = strrev($sanitizedNoIjin);
            
            // Buat data untuk verifikasi hash
            $secretKey = config('app.key');
            $data = "{$reversedNoIjinCheck}|{$sanitizedNpwp}";
            $calculatedHash = hash_hmac('sha256', $data, $secretKey);
            
            // Verifikasi hash
            if ($hash !== $calculatedHash) {
                Log::warning('Verifikasi logbook: Hash tidak valid', [
                    'provided_hash' => $hash,
                    'calculated_hash' => $calculatedHash
                ]);
                
                return view('verification.invalid', [
                    'message' => 'Kode verifikasi tidak valid.'
                ]);
            }
            
            // Cari file logbook
            $logbook = Userfile::where('no_ijin', 'like', '%' . $noIjin . '%')
                ->where('kind', 'logbook')
                ->first();
            
            if (!$logbook) {
                return view('verification.invalid', [
                    'message' => 'Dokumen logbook tidak ditemukan.'
                ]);
            }
            
            // Tampilkan halaman verifikasi berhasil
            return view('verification.success', [
                'commitment' => $commitment,
                'logbook' => $logbook
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error pada verifikasi logbook: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            
            return view('verification.invalid', [
                'message' => 'Terjadi kesalahan saat memverifikasi dokumen.'
            ]);
        }
    }
}
