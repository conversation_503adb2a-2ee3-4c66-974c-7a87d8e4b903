<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class LogMonitoringAccess
{
    /**
     * Daftar user agent dari sistem monitoring yang akan diblokir
     * Tidak digunakan lagi, kita fokus pada IP address
     */
    public $blockedAgents = [];

    /**
     * Daftar alamat IP dari sistem monitoring yang akan diblokir
     */
    public $blockedIPs = [
        '***************', // IP eksternal Uptime-Kuma
		'***********',
        // Range IP yang terkait (jika perlu tambahkan di sini)
    ];

    /**
     * Daftar user agent dari sistem monitoring yang hanya akan dilog
     */
    public $monitoredAgents = [
        'UptimeRobot',
        'Pingdom',
        'StatusCake',
        'Site24x7',
        'Nagios',
        'Zabbix',
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Ambil IP address dari request
        $ipAddress = $request->ip();

        // Jika path adalah /health, izinkan semua akses
        if ($request->path() === 'health') {
            return $next($request);
        }

        // Cek apakah IP address termasuk yang diblokir
        $shouldBlock = false;
        foreach ($this->blockedIPs as $ip) {
            if ($ipAddress === $ip) {
                $shouldBlock = true;
                break;
            }
        }

        // Jika IP diblokir, redirect ke endpoint health
        if ($shouldBlock) {
            // Log::channel('monitoring')->warning('Blocked access from monitoring IP', [
            //     'ip' => $ipAddress,
            //     'user_agent' => $request->userAgent() ?? 'Unknown',
            //     'path' => $request->path(),
            //     'method' => $request->method(),
            //     'timestamp' => now()->toDateTimeString(),
            // ]);

            // Redirect ke endpoint health check
            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Access blocked',
                    'message' => 'Please use the /health endpoint for monitoring',
                    'health_endpoint' => url('/health')
                ], 403);
            }

            return redirect('/health');
        }

        // Proses request normal dan kembalikan response
        return $next($request);
    }
}
