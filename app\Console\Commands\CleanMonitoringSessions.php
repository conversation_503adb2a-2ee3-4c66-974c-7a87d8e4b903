<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CleanMonitoringSessions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sessions:clean-monitoring {--days=1 : Number of days to keep} {--dry-run : Only show what would be deleted}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up sessions from monitoring tools like Uptime-Kuma';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        $dryRun = $this->option('dry-run');
        $cutoff = Carbon::now()->subDays($days)->timestamp;
        
        $this->info("Cleaning monitoring sessions older than {$days} days...");
        
        if ($dryRun) {
            $this->info("DRY RUN MODE - No sessions will be deleted");
        }
        
        // Hitung jumlah session sebelum pembersihan
        $beforeCount = DB::table('sessions')->count();
        $this->info("Total sessions before cleanup: {$beforeCount}");
        
        // Identifikasi monitoring tools berdasarkan user agent
        $monitoringAgents = [
            'Uptime-Kuma',
            'UptimeRobot',
            'Pingdom',
            'StatusCake',
            'Site24x7',
            'Nagios',
            'Zabbix',
        ];
        
        $whereClause = [];
        foreach ($monitoringAgents as $agent) {
            $whereClause[] = "user_agent LIKE '%{$agent}%'";
        }
        
        $whereClauseStr = implode(' OR ', $whereClause);
        
        // Hitung jumlah session monitoring sebelum pembersihan
        $monitoringBeforeCount = DB::table('sessions')
            ->whereRaw("({$whereClauseStr})")
            ->count();
        
        $this->info("Monitoring sessions before cleanup: {$monitoringBeforeCount}");
        
        // Hitung jumlah session monitoring yang akan dihapus
        $toDeleteCount = DB::table('sessions')
            ->whereRaw("({$whereClauseStr})")
            ->where('last_activity', '<', $cutoff)
            ->count();
        
        $this->info("Monitoring sessions to delete: {$toDeleteCount}");
        
        // Tampilkan contoh session yang akan dihapus
        $examples = DB::table('sessions')
            ->whereRaw("({$whereClauseStr})")
            ->where('last_activity', '<', $cutoff)
            ->select('id', 'user_agent', 'ip_address', 'last_activity')
            ->limit(5)
            ->get();
        
        if ($examples->count() > 0) {
            $this->info("Examples of sessions to be deleted:");
            $headers = ['ID', 'User Agent', 'IP Address', 'Last Activity'];
            $rows = [];
            
            foreach ($examples as $example) {
                $rows[] = [
                    $example->id,
                    $example->user_agent,
                    $example->ip_address,
                    date('Y-m-d H:i:s', $example->last_activity),
                ];
            }
            
            $this->table($headers, $rows);
        }
        
        // Hapus session monitoring yang sudah tidak aktif
        if (!$dryRun) {
            $deleted = DB::table('sessions')
                ->whereRaw("({$whereClauseStr})")
                ->where('last_activity', '<', $cutoff)
                ->delete();
            
            $this->info("Deleted {$deleted} old monitoring sessions.");
            
            // Hitung jumlah session setelah pembersihan
            $afterCount = DB::table('sessions')->count();
            $this->info("Total sessions after cleanup: {$afterCount}");
            
            // Log hasil pembersihan
            Log::channel('monitoring')->info("Monitoring sessions cleanup completed", [
                'deleted' => $deleted,
                'before_count' => $beforeCount,
                'after_count' => $afterCount,
                'cutoff_days' => $days,
            ]);
        } else {
            $this->info("DRY RUN COMPLETE - No sessions were deleted");
        }
        
        return Command::SUCCESS;
    }
}
