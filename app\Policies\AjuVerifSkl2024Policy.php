<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\AjuVerifSkl2024;
use App\Models\User;

class AjuVerifSkl2024Policy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any AjuVerifSkl2024');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, AjuVerifSkl2024 $ajuverifskl2024): bool
    {
        return $user->checkPermissionTo('view AjuVerifSkl2024');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create AjuVerifSkl2024');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, AjuVerifSkl2024 $ajuverifskl2024): bool
    {
        return $user->checkPermissionTo('update AjuVerifSkl2024');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, AjuVerifSkl2024 $ajuverifskl2024): bool
    {
        return $user->checkPermissionTo('delete AjuVerifSkl2024');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any AjuVerifSkl2024');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, AjuVerifSkl2024 $ajuverifskl2024): bool
    {
        return $user->checkPermissionTo('restore AjuVerifSkl2024');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any AjuVerifSkl2024');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, AjuVerifSkl2024 $ajuverifskl2024): bool
    {
        return $user->checkPermissionTo('replicate AjuVerifSkl2024');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder AjuVerifSkl2024');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, AjuVerifSkl2024 $ajuverifskl2024): bool
    {
        return $user->checkPermissionTo('force-delete AjuVerifSkl2024');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any AjuVerifSkl2024');
    }
}
