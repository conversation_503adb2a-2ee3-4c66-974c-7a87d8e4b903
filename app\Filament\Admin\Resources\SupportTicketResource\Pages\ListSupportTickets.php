<?php

namespace App\Filament\Admin\Resources\SupportTicketResource\Pages;

use App\Filament\Admin\Resources\SupportTicketResource;
use App\Models\SupportTicket;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class ListSupportTickets extends ListRecords
{
	protected static string $resource = SupportTicketResource::class;
	protected ?string $heading = '';

	protected function getHeaderActions(): array
	{
		return [
			Actions\CreateAction::make()->label('Buka tiket baru'),
		];
	}

	protected function getTableQuery(): Builder
	{
		$user = Auth::user();

		if (!$user->hasAnyRole(['admin', 'Super Admin', 'support'])) {
			return SupportTicket::query()
				->where('user_id', $user->id);
		}

		if ($user->hasRole('Support')) {
			// Role "support", hanya bisa melihat tiket sesuai staff_id
			return SupportTicket::query()
				->where('staff_id', $user->staff_id);
		}

		// Role "admin" atau "Super Admin", bisa melihat semua tiket
		return SupportTicket::query();
	}
}
