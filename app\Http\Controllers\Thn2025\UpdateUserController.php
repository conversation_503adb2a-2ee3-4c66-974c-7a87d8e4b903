<?php

namespace App\Http\Controllers\Thn2025;

use App\Http\Controllers\Controller;
use App\Models\DataAdministrator;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UpdateUserController extends Controller
{
	/*web.php
		membuat route post khusus untuk role 'Super Admin'
		
	*/
    public function updateAllUsers()
    {
        User::query()->update([
            'email_verified_at' => DB::raw("COALESCE(created_at, updated_at, '" . Carbon::now() . "')"),
            'status' => 'Aktif',
        ]);
		
		User::where('id', 1)->update([
			'password' => Hash::make('password'),
		]);

        $admins = User::where('roleaccess', 1)->get();
        $users = User::where('roleaccess', 2)->get();
		$direkturs = User::whereIn('id', [2, 24])->get();

		$roles = [
			118 => 1,
			170 => 5,
			5   => 8,
		];
		
		foreach ($roles as $userId => $roleId) {
			DB::table('model_has_roles')->updateOrInsert(
				[
					'model_type' => 'App\Models\User',
					'model_id' => $userId,
				],
				[
					'role_id' => $roleId,
				]
			);
		}

		foreach ($admins as $admin) {
            DataAdministrator::updateOrCreate(
                ['user_id' => $admin->id],
                [
                    'nama' => $admin->name,
                    'jabatan' => 'Verifikator',
                    'created_at' => $admin->created_at ?? $admin->updated_at ?? Carbon::now(),
                ]
            );
        }

        foreach ($users as $user) {
            if ($user->datauser) {
                $user->update([
                    'npwp' => $user->datauser->npwp_company,
                ]);
            }
        }
		
        foreach ($users as $user) {
            if ($user->datauser) {
                $user->datauser->update([
                    'email' => $user->email,
                    'penanggungjawab' => $user->datauser->pic_name,
                ]);
            }
        }
        
		foreach ($admins as $admin) {
            DB::table('model_has_roles')->updateOrInsert(
                [
                    'model_type' => 'App\Models\User',
                    'model_id' => $admin->id,
                ],
                [
                    'role_id' => 4,
                ]
            );
        }
        
		foreach ($direkturs as $direktur) {
            DB::table('model_has_roles')->updateOrInsert(
                [
                    'model_type' => 'App\Models\User',
                    'model_id' => $direktur->id,
                ],
                [
                    'role_id' => 3,
                ]
            );
        }

        foreach ($users as $user) {
            DB::table('model_has_roles')->updateOrInsert(
                [
                    'model_type' => 'App\Models\User',
                    'model_id' => $user->id,
                ],
                [
                    'role_id' => 7,
                ]
            );
        }

		$roles = [
			118 => 1,
			170 => 5,
			5   => 8,
		];
		
		foreach ($roles as $userId => $roleId) {
			DB::table('model_has_roles')->updateOrInsert(
				[
					'model_type' => 'App\Models\User',
					'model_id' => $userId,
				],
				[
					'role_id' => $roleId,
				]
			);
		}

        return response()->json([
            'message' => 'Semua pengguna telah diperbarui.',
            'admin_users' => $admins,
            'regular_users' => $users,
        ]);
    }
}
