<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\Pks2025;
use App\Models\User;

class Pks2025Policy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any Pks2025');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Pks2025 $pks2025): bool
    {
        return $user->checkPermissionTo('view Pks2025');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create Pks2025');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Pks2025 $pks2025): bool
    {
        return $user->checkPermissionTo('update Pks2025');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Pks2025 $pks2025): bool
    {
        return $user->checkPermissionTo('delete Pks2025');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any Pks2025');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Pks2025 $pks2025): bool
    {
        return $user->checkPermissionTo('restore Pks2025');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any Pks2025');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, Pks2025 $pks2025): bool
    {
        return $user->checkPermissionTo('replicate Pks2025');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder Pks2025');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Pks2025 $pks2025): bool
    {
        return $user->checkPermissionTo('force-delete Pks2025');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any Pks2025');
    }
}
