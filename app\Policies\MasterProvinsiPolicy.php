<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\MasterProvinsi;
use App\Models\User;

class MasterProvinsiPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any MasterProvinsi');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, MasterProvinsi $masterprovinsi): bool
    {
        return $user->checkPermissionTo('view MasterProvinsi');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create MasterProvinsi');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, MasterProvinsi $masterprovinsi): bool
    {
        return $user->checkPermissionTo('update MasterProvinsi');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, MasterProvinsi $masterprovinsi): bool
    {
        return $user->checkPermissionTo('delete MasterProvinsi');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any MasterProvinsi');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, MasterProvinsi $masterprovinsi): bool
    {
        return $user->checkPermissionTo('restore MasterProvinsi');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any MasterProvinsi');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, MasterProvinsi $masterprovinsi): bool
    {
        return $user->checkPermissionTo('replicate MasterProvinsi');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder MasterProvinsi');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, MasterProvinsi $masterprovinsi): bool
    {
        return $user->checkPermissionTo('force-delete MasterProvinsi');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any MasterProvinsi');
    }
}
