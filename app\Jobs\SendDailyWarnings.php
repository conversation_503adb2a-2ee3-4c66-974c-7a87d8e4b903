<?php

namespace App\Jobs;

use App\Models\Pks2025;
use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class SendDailyWarnings implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

	public function handle()
	{
		$now = Carbon::now();
		$sevenDaysFromNow = $now->copy()->addDays(7)->toDateString();
		$today = $now->toDateString();
		$pksToNotify = Pks2025::whereNull('berkas_pks')
			->whereBetween('deadline_at', ["$today 00:00:00", "$sevenDaysFromNow 23:59:59"])
			->get(['npwp', 'nama_poktan', 'deadline_at']);
		// Log::info('Query Result Daily:', ['pksToNotify' => $pksToNotify->toArray()]);
		$groupedPks = $pksToNotify->groupBy('npwp');
		$users = User::whereIn('npwp', $groupedPks->keys())->get()->keyBy('npwp');
	
		// Log::info('Users:', ['users' => $users->toArray()]);
		foreach ($groupedPks as $npwp => $pks) {
			if (!isset($users[$npwp])) {
				continue;
			}
	
			$user = $users[$npwp];
			$listNoPks = "<ul>";
			foreach ($pks as $pksItem) {
				$deadlineAt = Carbon::parse($pksItem->deadline_at)->format('d-m-Y');
				$listNoPks .= "<li><strong>{$pksItem->nama_poktan}</strong> - Tenggat: {$deadlineAt}</li>";
			}
			$listNoPks .= "</ul>";
			Notification::make()
				->title('PEMBERITAHUAN')
				->body("
					<p>Anda memiliki beberapa PKS yang belum lengkap:</p>
					$listNoPks
					<p><strong>Segera lengkapi sebelum tenggat waktu berakhir.</strong></p>
				")
				->sendToDatabase($user);
		}
	}
}
