<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Realisasi2025 extends Model
{
	use LogsActivity;

	public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
			->logFillable('*')
			->logOnlyDirty();
    }

	public $table = 't2025_realisasis';

	protected $fillable = [
		'origin',
		'tcode',
		'npwp',
		'no_ijin',
		'kode_poktan',
		'kode_spatial',
		'ktp_petani',
		'luas_lahan',
		'periode_tanam',
	];

	protected static function booted()
	{
		static::addGlobalScope('npwp', function (Builder $builder) {
			if (Auth::check()) {
				$user = Auth::user();

				if ($user->hasAnyRole(['admin', 'direktur', 'Super Admin', 'verifikator'])) {
				}
				else {
					$builder->where('npwp', $user->npwp);
				}
			}
		});
	}

	public function anggota(): HasOne
	{
		return $this->hasOne(MasterAnggota::class, 'ktp_petani', 'ktp_petani');
	}

	public function poktan(): BelongsTo
	{
		return $this->belongsTo(MasterPoktan::class, 'kode_poktan', 'kode_poktan');
	}

	public function commitment(): BelongsTo
	{
		return $this->belongsTo(Commitment2025::class, 'no_ijin', 'no_ijin');
	}

	public function spatial()
	{
		return $this->hasOne(MasterSpatial::class, 'kode_spatial', 'kode_spatial');
	}

	public function verifikator_tanam()
	{
		return $this->belongsTo(User::class, 'vt_by', 'id');
	}

	public function verifikator_produksi()
	{
		return $this->belongsTo(User::class, 'vp_by', 'id');
	}

	public function detailrealisasi()
	{
		return $this->hasMany(DetailRealisasi2025::class, 'realisasi_id', 'id');
	}

	public function detailrealisasitanam(){
		return $this->hasOne(DetailRealisasi2025::class, 'realisasi_id', 'id')->where('jenis_keg', 'tanam');
	}

	public function detailrealisasiproduksi(){
		return $this->hasOne(DetailRealisasi2025::class, 'realisasi_id', 'id')->where('jenis_keg', 'panen');
	}

	public function detailrealisasidistribusi(){
		return $this->hasOne(DetailRealisasi2025::class, 'realisasi_id', 'id')->where('jenis_keg', 'distribusi');
	}

	public function detailrealisasipupuk(){
		return $this->hasMany(DetailRealisasi2025::class, 'realisasi_id', 'id')->where('jenis_keg', 'pupuk');
	}

	public function detailrealisasimulsa(){
		return $this->hasOne(DetailRealisasi2025::class, 'realisasi_id', 'id')->where('jenis_keg', 'mulsa');
	}

	public function detailrealisasibenih(){
		return $this->hasOne(DetailRealisasi2025::class, 'realisasi_id', 'id')->where('jenis_keg', 'benih');
	}
}
