<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class HealthCheckController extends Controller
{
    /**
     * Endpoint sederhana untuk health check
     * Tidak membuat session dan lebih efisien untuk monitoring
     */
    public function check()
    {
        try {
            // Cek koneksi database
            DB::connection()->getPdo();
            $dbStatus = true;
        } catch (\Exception $e) {
            $dbStatus = false;
            Log::error('Health check database error: ' . $e->getMessage());
        }
        
        // Informasi dasar tentang aplikasi
        $data = [
            'status' => 'ok',
            'timestamp' => now()->toDateTimeString(),
            'environment' => app()->environment(),
            'database' => $dbStatus ? 'connected' : 'error',
        ];
        
        // Log akses ke health check
        Log::channel('monitoring')->info('Health check accessed', [
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
        
        return response()->json($data);
    }
}
