<?php

namespace App\Models;

use App\Models\MasterKabupaten;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class CommitmentRegion extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;
	public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
			->logFillable('*')
			->logOnlyDirty();
    }

	public $table = 't2025_commitment_regions';

	protected $fillable = [
		'npwp',
		'no_ijin',
		'kabupaten_id',
		'quota',
		'status',
        'fullfilled'
	];

	protected static function booted()
	{
		static::addGlobalScope('npwp', function (Builder $builder) {
			if (Auth::check()) {
				$user = Auth::user();

				if ($user->hasAnyRole(['admin', 'direktur', 'Super Admin', 'verifikator'])) {
				}
				else {
					$builder->where('npwp', $user->npwp);
				}
			}
		});
	}

	public function user()
	{
		return $this->belongsTo(User::class, 'npwp', 'npwp');
	}

	public function commitment()
	{
		return $this->belongsTo(Commitment2025::class, 'no_ijin', 'no_ijin');
	}
	public function kabupaten()
	{
		return $this->belongsTo(MasterKabupaten::class, 'kabupaten_id', 'kabupaten_id');
	}

}
