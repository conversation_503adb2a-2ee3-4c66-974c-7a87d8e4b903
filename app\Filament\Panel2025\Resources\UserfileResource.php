<?php

namespace App\Filament\Panel2025\Resources;

use App\Filament\Panel2025\Resources\UserfileResource\Pages;
use App\Filament\Panel2025\Resources\UserfileResource\RelationManagers;
use App\Filament\Panel2025\Resources\UserfileResource\RelationManagers\UserfilesRelationManager;
use App\Models\Commitment2025;
use App\Models\Userfile;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class UserfileResource extends Resource
{
    protected static ?string $model = Commitment2025::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                //
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            UserfilesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            // 'index' => Pages\ListUserfiles::route('/'),
            // 'create' => Pages\CreateUserfile::route('/create'),
            // 'view' => Pages\ViewUserfile::route('/{record}'),
            'edit' => Pages\EditUserfile::route('/{record}/edit'),
            'berkastanam' => Pages\BerkasTanam::route('/{record}/berkastanam'),
        ];
    }

    public static function shouldRegisterNavigation(): bool
    {
            return false;
    }
}
