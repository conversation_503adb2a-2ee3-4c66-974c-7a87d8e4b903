<?php

namespace App\Filament\Panel2025\Widgets;

use App\Models\PengajuanVerifikasi;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\Facades\Auth;

class ActiveVerification extends BaseWidget
{
    protected static ?int $sort = 1;
    protected int | string | array $columnSpan = 'full';
    public function table(Table $table): Table
    {
		$user = Auth::user();
    
		$query = PengajuanVerifikasi::query()
			->where('kind', '!=', 'PVS')
			->whereIn('status', ['1', '2']);

		if ($user->hasRole('importir')) {
			$query->where('npwp', $user->npwp);
		} elseif ($user->hasAnyRole(['verifikator'])) {
			$query->whereHas('authAssignments', fn($q) => $q->where('user_id', $user->id));
		}
        return $table
            ->query($query)
			->heading('Verifikasi Aktif')
            ->defaultSort('created_at', 'desc')
			->emptyStateHeading('Tidak Ada Verifikasi')
			->emptyStateDescription('Anda belum/tidak memiliki pengajuan verifikasi tanam-produksi yang aktif')
            ->columns([
                TextColumn::make('kind')
                    ->label('Jenis')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'PVT' => 'Tanam',
                        'PVP' => 'Produksi',
                        default => $state, // Agar tidak error jika nilai tidak ada dalam daftar
                    })
                    ->color(fn (string $state): string => match ($state){
                        'PVT' => 'success',
                        'PVP' => 'warning',
                    }),
                TextColumn::make('no_pengajuan'),
                TextColumn::make('no_ijin'),
                TextColumn::make('user.datauser.company_name'),
                TextColumn::make('status')
                    ->badge()
                    ->color(function ($record, string $state): string {
						$colorMap = [
							'0' => 'warning',  // Baru
							'1' => 'info',     // Penugasan
							'2' => 'primary',  // Penetapan
							'3' => 'primary',  // Dimulai
							'4' => 'success',  // Selesai
							'5' => 'danger',   // Perbaikan
						];
					
						// Jika kind = PVS, gunakan warna khusus
						if ($record->kind === 'PVS') {
							$colorMap['4'] = 'warning';
							$colorMap += [
								'6' => 'danger',   // Ditolak
								'7' => 'primary',  // Disetujui
								'8' => 'success',  // Diterbitkan/Lunas
							];
						}
						return $colorMap[$state] ?? 'secondary';
					})					
					->formatStateUsing(function ($record, $state) {
						$statusMap = [
							'0' => 'Baru',
							'1' => 'Penugasan',
							'2' => 'Penetapan',
							'3' => 'Dimulai',
							'4' => 'Selesai',
							'5' => 'Perbaikan',
						];
						if ($record->kind === 'PVS') {
							$statusMap['4'] = 'Direkomendasikan';
							$statusMap += [
								'6' => 'Ditolak',
								'7' => 'Disetujui',
								'8' => 'Diterbitkan/Lunas'
							];
						}
						return $statusMap[$state] ?? 'Tidak Diketahui';
					}),
            ]);
    }
}
