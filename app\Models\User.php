<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;
use Althinect\FilamentSpatieRolesPermissions\Concerns\HasSuperAdmin;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Filament\Panel\Concerns\HasAvatars;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Observers\UserObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Models\Activity;
use Spatie\Activitylog\Traits\LogsActivity;

#[ObservedBy([UserObserver::class])]
class User extends Authenticatable implements FilamentUser, MustVerifyEmail
{
	/** @use HasFactory<\Database\Factories\UserFactory> */
	use HasFactory, HasApiTokens,Notifiable, HasRoles, HasSuperAdmin, HasAvatars, SoftDeletes, LogsActivity;

	    /**
     * Get the URL to the user's avatar.
     *
     * @return string|null
     */
    public function getFilamentAvatarUrl(): ?string
    {
        if ($this->avatar_url) {
            // Check if the avatar_url is already a full URL
            if (filter_var($this->avatar_url, FILTER_VALIDATE_URL)) {
                return $this->avatar_url;
            }

            // Check if it's a path to a file in storage
            if (Storage::disk('public')->exists($this->avatar_url)) {
                // Ensure we return an absolute URL with domain
                return url(Storage::url($this->avatar_url));
            }
        }

        // Fallback to UI Avatars
        $name = $this->name;
        $initials = collect(explode(' ', $name))->map(fn (string $segment): string => mb_substr($segment, 0, 1))->join('');
        return 'https://ui-avatars.com/api/?name=' . urlencode($initials) . '&color=FFFFFF&background=111827';
    }

    /**
     * Get the avatar URL attribute.
     * This is needed because Filament may call this directly.
     */
    public function getAvatarUrlAttribute($value)
    {
        if (empty($value)) {
            return null;
        }

        // Check if it's already a full URL
        if (filter_var($value, FILTER_VALIDATE_URL)) {
            return $value;
        }

        // Convert to a full URL if it's a storage path
        return url(Storage::url($value));
    }

	public function getActivitylogOptions(): LogOptions
	{
		return LogOptions::defaults()
			->logFillable('*')
			->logOnlyDirty();
	}

	/**
	 * The attributes that are mass assignable.
	 *
	 * @var list<string>
	 */
	protected $fillable = [
		'npwp',
		'username',
		'name',
		'email',
		'password',
		'avatar_url',
		'email_verified_at',
		'status',
		'department_ids',
	];

	public function canAccessPanel(Panel $panel): bool
	{
		if ($panel->getId() !== 'admin') {
			// Cek apakah user adalah importir tetapi belum memiliki datauser atau npwp kosong
			if ($this->hasRole('importir') && (is_null($this->datauser) || empty($this->datauser->npwp_company))) {
				return false;
			}
		}
		return true;
	}
	

	/**
	 * The attributes that should be hidden for serialization.
	 *
	 * @var list<string>
	 */
	protected $hidden = [
		'password',
		'remember_token',
	];

	/**
	 * Get the attributes that should be cast.
	 *
	 * @return array<string, string>
	 */
	protected function casts(): array
	{
		return [
			'email_verified_at' => 'datetime',
			'password' => 'hashed',
			'department_ids' => 'array',
		];
	}
	

	// public function getFilamentAvatarUrl(): ?string
	// {
	// 	return $this->avatar_url;
	// }

	public function scopeVerifikator($query, array $roles = ['verifikator', 'admin'])
	{
		return $query->whereHas('roles', function ($q) use ($roles) {
			$q->whereIn('name', $roles);
		});
	}

	public function scopeImportir($query)
	{
		return $query->hasRole('importir');
	}

	
	public function datauser():HasOne
	{
		return $this->hasOne(DataUser::class);
	}
	
	public function dataadmin():HasOne
	{
		return $this->hasOne(DataAdministrator::class);
	}
	
	public function commitment():HasMany
	{
		return $this->hasMany(Commitment2025::class);
	}
	
	public function oldcommitment():HasMany
	{
		return $this->hasMany(Commitment2024::class);
	}
	
	public function completed():HasMany
	{
		return $this->hasMany(Completed::class, 'npwp', 'npwp');
	}
	
	public function pks():HasMany
	{
		return $this->hasMany(Pks2025::class, 'npwp', 'npwp');
	}
	
	public function anggota2024():HasMany
	{
		return $this->hasMany(MasterAnggota2024::class, 'npwp', 'npwp');
	}

	public function activities(): HasMany
	{
		return $this->hasMany(Activity::class, 'causer_id', 'id');
	}
	
	public function announcements(): HasMany
	{
		return $this->hasMany(Announcement::class);
	}
}
