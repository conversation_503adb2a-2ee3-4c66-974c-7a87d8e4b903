<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\ForeignApiResource\Pages;
use App\Filament\Admin\Resources\ForeignApiResource\RelationManagers;
use App\Models\ForeignApi;
use Filament\Forms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ForeignApiResource extends Resource
{
    protected static ?string $model = ForeignApi::class;

    protected static ?string $modelLabel = 'API External';
    protected static ?string $pluralModelLabel = 'Daftar API External';

	protected static ?string $navigationGroup = 'Data Induk';
    protected static ?string $navigationLabel = 'API External';
    protected static ?int $navigationSort = 99;
    protected static ?string $navigationIcon = 'heroicon-o-rocket-launch';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('provider')
                    ->required()
                    ->maxLength(255),
                TextInput::make('key')
                    ->required()
                    ->maxLength(255),
				Toggle::make('status')
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('provider')
                    ->searchable(),
                TextColumn::make('key')
                    ->searchable(),
                ToggleColumn::make('status')
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListForeignApis::route('/'),
            'create' => Pages\CreateForeignApi::route('/create'),
            'view' => Pages\ViewForeignApi::route('/{record}'),
            'edit' => Pages\EditForeignApi::route('/{record}/edit'),
        ];
    }
}
