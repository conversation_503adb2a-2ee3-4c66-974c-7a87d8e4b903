<?php

namespace App\Filament\Admin\Pages;

use App\Models\ForeignApi;
use App\Models\MasterKabupaten;
use App\Models\MasterSpatial;
use Filament\Notifications\Notification;
use Filament\Pages\Page;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Attributes\On;

class SpatialMapPage extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-map';

    protected static ?string $navigationLabel = 'Peta Spasial';

    protected static ?string $navigationGroup = 'Data Induk';

    protected static ?string $title = 'Data Spatial Lahan';

    protected static string $view = 'filament.admin.pages.spatial-map-page';

	public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public $mapKey;
    public $mapData = [];
    public $selectedKabupaten = [];
    public $indexKabupaten = [];
    public $searchResults = [];
    public $showResultsTable = false;
    public $searchLimit = 100; // Batas default pencarian

    public function mount()
    {
        try {
            $this->mapKey = ForeignApi::where('status', 1)->select('key')->first();

            $this->indexKabupaten = MasterKabupaten::whereHas('spatials')
                ->select('kabupaten_id', 'nama_kab')
                ->get()
                ->toArray();

            // Tidak memuat data saat halaman pertama kali dibuka
            // Data akan dimuat hanya ketika filter diterapkan
            $this->mapData = [];

            // Tampilkan notifikasi untuk memberi tahu pengguna
            // Notification::make()
            //     ->title('Pilih Kabupaten')
            //     ->body('Silakan pilih kabupaten untuk menampilkan data pada peta.')
            //     ->info()
            //     ->send();
        } catch (\Exception $e) {
            // Tampilkan notifikasi error
            Notification::make()
                ->title('Error')
                ->body('Terjadi kesalahan saat memuat halaman: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public static function getRoutes()
    {
        return Route::get('spatial-map-page', static::class)
            ->name('spatial-map-page');
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('viewFailureReports')
                ->label('Lihat Laporan Kegagalan')
                ->url('/admin/kml-failure-reports')
                ->icon('heroicon-o-exclamation-circle')
                ->color('warning')
                ->visible(function () {
                    $disk = \Illuminate\Support\Facades\Storage::disk('public');

                    // Periksa apakah direktori ada
                    if (!$disk->exists('uploads/kml/not_processed')) {
                        return false;
                    }

                    // Periksa apakah ada file JSON di dalam direktori
                    $files = $disk->files('uploads/kml/not_processed');
                    foreach ($files as $file) {
                        if (pathinfo($file, PATHINFO_EXTENSION) === 'json') {
                            return true;
                        }
                    }

                    return false;
                }),
        ];
    }

    public function loadMapData()
    {
        try {
            // Jika tidak ada filter kabupaten yang dipilih, kembalikan array kosong
            if (empty($this->selectedKabupaten)) {
                $this->mapData = [];

                // Tampilkan notifikasi untuk memberi tahu pengguna
                Notification::make()
                    ->title('Filter Diperlukan')
                    ->body('Silakan pilih filter kabupaten untuk menampilkan data pada peta.')
                    ->info()
                    ->send();

                return;
            }

            $query = MasterSpatial::query()
                ->select('id', 'kode_spatial', 'nama_petani', 'ktp_petani', 'latitude', 'longitude', 'kabupaten_id', 'kecamatan_id', 'kode_poktan', 'luas_lahan')
                ->with(['masterpoktan:kode_poktan,nama_kelompok', 'kecamatan:kecamatan_id,nama_kecamatan', 'kabupaten:kabupaten_id,nama_kab'])
                ->whereIn('kabupaten_id', $this->selectedKabupaten);

            // Batasi jumlah data untuk performa
            $data = $query->limit(500)->get();

            // Pastikan data tidak kosong
            if ($data->isEmpty()) {
                // Jika tidak ada data, tampilkan notifikasi
                Notification::make()
                    ->title('Tidak ada data')
                    ->body('Tidak ada data spasial yang ditemukan untuk kabupaten yang dipilih.')
                    ->warning()
                    ->send();

                $this->mapData = [];
            } else {
                // Konversi ke array
                $dataArray = $data->toArray();
                $this->mapData = $dataArray;

                // Jika tabel hasil sedang ditampilkan, perbarui data
                if ($this->showResultsTable) {
                    $this->prepareSearchResults();
                }

                // Tampilkan notifikasi sukses
                Notification::make()
                    ->title('Data Dimuat')
                    ->body('Berhasil memuat ' . count($this->mapData) . ' data spasial.')
                    ->success()
                    ->send();
            }
        } catch (\Exception $e) {
            // Tampilkan notifikasi error
            Notification::make()
                ->title('Error')
                ->body('Terjadi kesalahan saat memuat data peta: ' . $e->getMessage())
                ->danger()
                ->send();

            // Set data kosong
            $this->mapData = [];
        }
    }

    #[On('search-spatial')]
    public function searchSpatial($data)
    {
        try {
            $searchQuery = $data['searchQuery'] ?? '';
            $searchAllKabupaten = $data['searchAllKabupaten'] ?? false;

            // Simpan parameter pencarian di session untuk penggunaan kembali
            session(['last_search_query' => $searchQuery]);
            session(['last_search_all_kabupaten' => $searchAllKabupaten]);

            // Jika tidak ada filter kabupaten yang dipilih dan tidak mencari di semua kabupaten
            if (empty($this->selectedKabupaten) && !$searchAllKabupaten) {
                Notification::make()
                    ->title('Filter Diperlukan')
                    ->body('Silakan pilih filter kabupaten atau aktifkan pencarian global.')
                    ->info()
                    ->send();
                return;
            }

            if (empty($searchQuery)) {
                $this->loadMapData();
                return;
            }

            $query = MasterSpatial::query()
                ->select('id', 'kode_spatial', 'nama_petani', 'ktp_petani', 'latitude', 'longitude', 'kabupaten_id', 'kecamatan_id', 'kode_poktan', 'luas_lahan')
                ->with(['masterpoktan:kode_poktan,nama_kelompok', 'kecamatan:kecamatan_id,nama_kecamatan', 'kabupaten:kabupaten_id,nama_kab']);

            // Tambahkan kondisi pencarian untuk multiple fields
            $query->where(function ($q) use ($searchQuery) {
                $q->where('kode_spatial', 'like', "%{$searchQuery}%")
                  ->orWhere('nama_petani', 'like', "%{$searchQuery}%")
                  ->orWhere('ktp_petani', 'like', "%{$searchQuery}%")
                  ->orWhereHas('masterpoktan', function ($q) use ($searchQuery) {
                      $q->where('nama_kelompok', 'like', "%{$searchQuery}%");
                  })
                  ->orWhereHas('kecamatan', function ($q) use ($searchQuery) {
                      $q->where('nama_kecamatan', 'like', "%{$searchQuery}%");
                  });
            });

            // Filter berdasarkan kabupaten jika tidak mencari di semua kabupaten
            if (!$searchAllKabupaten && !empty($this->selectedKabupaten)) {
                $query->whereIn('kabupaten_id', $this->selectedKabupaten);
            }

            // Batasi jumlah data untuk performa
            // Catatan: Nilai ini dapat disesuaikan melalui UI
            $result = $query->limit($this->searchLimit)->get();

            if ($result->isEmpty()) {
                Notification::make()
                    ->title('Tidak ditemukan')
                    ->body("Tidak ada data yang cocok dengan pencarian '{$searchQuery}'")
                    ->warning()
                    ->send();

                // Tetap tampilkan data dari filter kabupaten jika ada
                if (!empty($this->selectedKabupaten) && !$searchAllKabupaten) {
                    $this->loadMapData();
                } else {
                    $this->mapData = [];
                }
            } else {
                // Jika hasil mencapai batas, beri tahu pengguna
                $message = "Ditemukan {$result->count()} data yang cocok";
                if ($result->count() >= $this->searchLimit) {
                    $message .= " (dibatasi {$this->searchLimit} data untuk performa)";
                }

                Notification::make()
                    ->title('Pencarian berhasil')
                    ->body($message)
                    ->success()
                    ->send();

                // Konversi ke array
                $resultArray = $result->toArray();
                $this->mapData = $resultArray;

                // Jika tabel hasil sedang ditampilkan, perbarui data
                if ($this->showResultsTable) {
                    $this->prepareSearchResults();
                }
            }
        } catch (\Exception $e) {
            // Tampilkan notifikasi error
            Notification::make()
                ->title('Error')
                ->body('Terjadi kesalahan saat mencari data: ' . $e->getMessage())
                ->danger()
                ->send();

            // Kembalikan ke data awal jika ada filter kabupaten
            if (!empty($this->selectedKabupaten) && !($data['searchAllKabupaten'] ?? false)) {
                $this->loadMapData();
            }
        }
    }

    // Metode untuk mengatur batas pencarian
    #[On('set-search-limit')]
    public function setSearchLimit($data)
    {
        // Validasi input
        $limit = max(10, min(500, (int)($data['limit'] ?? 100))); // Minimal 10, maksimal 500
        $this->searchLimit = $limit;

        // Jika ada hasil pencarian sebelumnya, ulangi pencarian dengan batas baru
        if (!empty($this->mapData) && session()->has('last_search_query')) {
            $this->searchSpatial([
                'searchQuery' => session('last_search_query', ''),
                'searchAllKabupaten' => session('last_search_all_kabupaten', false)
            ]);
        }
    }

    // Metode untuk menampilkan/menyembunyikan tabel hasil
    #[On('toggle-results-table')]
    public function toggleResultsTable()
    {
        $this->showResultsTable = !$this->showResultsTable;

        // Jika tabel ditampilkan, siapkan data hasil pencarian
        if ($this->showResultsTable) {
            $this->prepareSearchResults();
        }
    }

    // Metode untuk menyiapkan data hasil pencarian untuk ditampilkan dalam tabel
    public function prepareSearchResults()
    {
        try {
            if (empty($this->mapData)) {
                $this->searchResults = [];
                return;
            }

            $this->searchResults = collect($this->mapData)->map(function ($item) {
                try {
                    if (!isset($item['id'])) {
                        return null;
                    }

                    $spatial = MasterSpatial::with(['masterpoktan', 'kecamatan', 'kabupaten'])
                        ->find($item['id']);

                    if (!$spatial) {
                        return null;
                    }

                    return [
                        'id' => $spatial->id,
                        'kode_spatial' => $spatial->kode_spatial,
                        'luas_lahan' => $spatial->luas_lahan,
                        'nama_petani' => $spatial->nama_petani,
                        'ktp_petani' => $spatial->ktp_petani,
                        'kecamatan' => $spatial->kecamatan->nama_kecamatan ?? '-',
                        'kabupaten' => $spatial->kabupaten->nama_kab ?? '-',
                        'latitude' => $spatial->latitude,
                        'longitude' => $spatial->longitude,
                        'poktan' => $spatial->masterpoktan->nama_kelompok ?? '-',
                        'luas_tanam' => $spatial->luas_tanam ?? '-',
                        'varietas' => $spatial->varietas ?? '-',
                        'tanggal_tanam' => $spatial->tanggal_tanam ? date('d-m-Y', strtotime($spatial->tanggal_tanam)) : '-',
                        'tanggal_panen' => $spatial->tanggal_panen ? date('d-m-Y', strtotime($spatial->tanggal_panen)) : '-',
                        'provinsi' => $spatial->provinsi->nama_provinsi ?? '-',
                        'kelurahan' => $spatial->kelurahan->nama_desa ?? '-',
                    ];
                } catch (\Exception $e) {
                    return null;
                }
            })->filter()->toArray();

            // Dispatch event untuk memperbarui tabel hasil di frontend
            $this->dispatch('update-results-table', results: $this->searchResults);
        } catch (\Exception $e) {
            // Set hasil kosong jika terjadi error
            $this->searchResults = [];
        }
    }


    #[On('marker-clicked')]
    public function loadMarkerInfo($data)
    {
        try {
            $kodeSpatial = $data['kodeSpatial'] ?? '';

            if (empty($kodeSpatial)) {
                return;
            }

            $spatial = MasterSpatial::where('kode_spatial', $kodeSpatial)
                ->with(['masterpoktan', 'kabupaten', 'kecamatan'])
                ->first();

            if ($spatial) {
                $markerInfo = [
                    'id' => $spatial->id,
                    'kode_spatial' => $spatial->kode_spatial,
                    'nama_petani' => $spatial->nama_petani,
                    'ktp_petani' => $spatial->ktp_petani,
                    'poktan' => $spatial->masterpoktan->nama_kelompok ?? 'Tidak ada data',
                    'latitude' => $spatial->latitude,
                    'longitude' => $spatial->longitude,
                    'luas_lahan' => $spatial->luas_lahan,
                    'kecamatan' => $spatial->kecamatan->nama_kecamatan ?? 'Tidak ada data',
                    'kabupaten' => $spatial->kabupaten->nama_kab ?? 'Tidak ada data',
                    'kml_url' => $spatial->kml_url,
                    'polygon' => $spatial->polygon,
                ];

                $this->dispatch('show-marker-info', markerInfo: $markerInfo);
            } else {
                Notification::make()
                    ->title('Data tidak ditemukan')
                    ->body("Data dengan kode spatial '{$kodeSpatial}' tidak ditemukan")
                    ->warning()
                    ->send();
            }
        } catch (\Exception $e) {
            // Tampilkan notifikasi error
            Notification::make()
                ->title('Error')
                ->body('Terjadi kesalahan saat memuat informasi marker: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
