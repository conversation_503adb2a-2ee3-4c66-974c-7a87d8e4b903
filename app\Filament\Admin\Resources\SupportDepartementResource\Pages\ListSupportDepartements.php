<?php

namespace App\Filament\Admin\Resources\SupportDepartementResource\Pages;

use App\Filament\Admin\Resources\SupportDepartementResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSupportDepartements extends ListRecords
{
    protected static string $resource = SupportDepartementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
				->label('Support Departemen')
				->icon('heroicon-s-plus')
				->modalHeading('Buat Departemen baru'),
        ];
    }
}
