<?php

namespace App\Filament\Panel2024\Resources;

use App\Filament\Panel2024\Resources\Pks2024Resource\Pages;
use App\Filament\Panel2024\Resources\Pks2024Resource\RelationManagers;
use App\Filament\Panel2024\Resources\Pks2024Resource\RelationManagers\DatarealisasiRelationManager;
use App\Filament\Panel2024\Resources\Pks2024Resource\RelationManagers\LokasiRelationManager;
use App\Models\Pks2024;
use App\Models\Varietas;
use Filament\Forms;
use Filament\Forms\Components\{DatePicker, FileUpload, Placeholder, Section, Select, TextInput};
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class Pks2024Resource extends Resource
{
	protected static ?string $model = Pks2024::class;


	protected static ?string $pluralModelLabel = 'PKS';
	protected static ?int $navigationSort = 2;

	public static function getNavigationLabel(): string
	{
		$navLabel = 'Daftar PKS';
		return $navLabel;
	}
	protected static ?string $navigationIcon = 'heroicon-o-bookmark';


	public static function getNavigationBadgeColor(): ?string
	{
		return 'warning';
	}

	public static function form(Form $form): Form
	{
		return $form
		->schema([
			Section::make('Data Mitra')
				->aside()
				->description('Profil Mitra Kelompok Tani')
				->schema([
					Placeholder::make('Nama Kelompok')
						->inlineLabel()
						->content(fn ($record) => $record->masterpoktan->nama_kelompok),
					Placeholder::make('Pimpinan')
						->inlineLabel()
						->content(fn ($record) => $record->masterpoktan->nama_pimpinan),
					Placeholder::make('Jumlah Anggota')
						->inlineLabel()
						->content(fn ($record) => $record->anggota->count() . ' orang'),
					Placeholder::make('Jumlah Lahan')
						->inlineLabel()
						->content(fn ($record) => $record->lokasi->count() . ' petak'),
					Placeholder::make('Total Luas')
						->inlineLabel()
						->content(fn ($record) => number_format($record->lokasi->sum('luas_lahan'),3,',','.') . ' ha'),

					Placeholder::make('Domisili')
						->inlineLabel()
						->content(fn ($record) => new HtmlString('
							<p>'. $record->masterpoktan->desa->nama_desa . ' - '.
							$record->masterpoktan->desa->kecamatan->nama_kecamatan . '</p>'.
							'<p>'. $record->masterpoktan->desa->kecamatan->kabupaten->nama_kab .' - '.
							$record->masterpoktan->desa->kecamatan->kabupaten->provinsi->nama.'</p>')
						),

				]),
			Section::make('Data Perjanjian')
				->aside()
				->description('Lengkapi Data Perjanjian dengan Mitra Poktan.')
				->schema([
					TextInput::make('no_perjanjian')
						->inlineLabel(),
					DatePicker::make('tgl_perjanjian_start')
						->label('Mulai Berlaku')
						->inlineLabel()
						->live()
						->required()
						->closeOnDateSelection(),
					DatePicker::make('tgl_perjanjian_end')
						->label('Sampai dengan')
						->inlineLabel()
						->reactive()
						->required()
						->closeOnDateSelection()
						->minDate(fn ($get) => $get('tgl_perjanjian_start')),

					TextInput::make('periode_tanam')
						->inlineLabel(),

					Select::make('varietas_tanam')
						->inlineLabel()
						->options(
							Varietas::whereNotNull('nama_varietas')
								->where('nama_varietas', '!=', '')
								->pluck('nama_varietas', 'id')
								->filter()
								->toArray()
						),

					FileUpload::make('berkas_pks')
						->openable()
						->required()
						->maxSize(2048)
						->inlineLabel()
						->columnSpan(1)
						->downloadable()
						->deletable()
						->label('Berkas PKS')
						->visibility('public')
						->panelAspectRatio('5:1')
						->imagePreviewHeight('50')
						->fetchFileInformation(true)
						->helperText('Maksimal 2MB, format PDF')
						->disk('public')
						->directory(function ($record) {
							$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
							$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
							return "uploads/{$cleanNpwp}/{$cleanNoIjin}/dokumen/pks";
						})
						->rules([
							'file',
							'mimetypes:application/pdf',
							'mimes:pdf'
						])
						->validationMessages([
							'mimetypes' => 'Hanya file PDF yang diperbolehkan',
							'mimes' => 'Ekstensi file harus .pdf',
						])
						->getUploadedFileNameForStorageUsing(
							function (TemporaryUploadedFile $file, $get, $record): string {
								$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
								$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

								// Format nama file: [ID]_[NPWP]_[NOIJIN].[ext]
								return 'pks_'.$record->id . '_' . $cleanNpwp . '_' . $cleanNoIjin . '_'.uniqid() . '.' . $file->getClientOriginalExtension();
							}
						),
				])
		]);
	}

	public static function table(Table $table): Table
	{
		return $table
			->columns([
				Tables\Columns\TextColumn::make('npwp')
					->searchable(),
				Tables\Columns\TextColumn::make('no_ijin')
					->searchable(),
				Tables\Columns\TextColumn::make('poktan_id')
					->numeric()
					->sortable(),
				Tables\Columns\TextColumn::make('no_perjanjian')
					->searchable(),
				Tables\Columns\TextColumn::make('tgl_perjanjian_start')
					->date()
					->sortable(),
				Tables\Columns\TextColumn::make('tgl_perjanjian_end')
					->date()
					->sortable(),
				Tables\Columns\TextColumn::make('jumlah_anggota')
					->numeric()
					->sortable(),
				Tables\Columns\TextColumn::make('luas_rencana')
					->numeric()
					->sortable(),
				Tables\Columns\TextColumn::make('varietas_tanam')
					->searchable(),
				Tables\Columns\TextColumn::make('periode_tanam')
					->searchable(),
				Tables\Columns\TextColumn::make('provinsi_id')
					->searchable(),
				Tables\Columns\TextColumn::make('kabupaten_id')
					->searchable(),
				Tables\Columns\TextColumn::make('kecamatan_id')
					->searchable(),
				Tables\Columns\TextColumn::make('kelurahan_id')
					->searchable(),
				Tables\Columns\TextColumn::make('berkas_pks')
					->searchable(),
				Tables\Columns\TextColumn::make('status')
					->searchable(),
				Tables\Columns\TextColumn::make('verif_by')
					->numeric()
					->sortable(),
				Tables\Columns\TextColumn::make('verif_at')
					->date()
					->sortable(),
				Tables\Columns\TextColumn::make('created_at')
					->dateTime()
					->sortable()
					->toggleable(isToggledHiddenByDefault: true),
				Tables\Columns\TextColumn::make('updated_at')
					->dateTime()
					->sortable()
					->toggleable(isToggledHiddenByDefault: true),
				Tables\Columns\TextColumn::make('deleted_at')
					->dateTime()
					->sortable()
					->toggleable(isToggledHiddenByDefault: true),
			])
			->filters([
				//
			])
			->actions([
				Tables\Actions\ViewAction::make(),
				Tables\Actions\EditAction::make(),
			])
			->bulkActions([
				Tables\Actions\BulkActionGroup::make([
					Tables\Actions\DeleteBulkAction::make(),
				]),
			]);
	}

	public static function getRelations(): array
	{
		// if (empty(static::$record->berkas_pks)) {
		// 	return [];
		// }

		return [
			DatarealisasiRelationManager::class,
		];
	}


	public static function getPages(): array
	{
		return [
			'index' => Pages\ListPks2024s::route('/'),
			'create' => Pages\CreatePks2024::route('/create'),
			'view' => Pages\ViewPks2024::route('/{record}'),
			'edit' => Pages\EditPks2024::route('/{record}/edit'),
		];
	}

	public static function shouldRegisterNavigation(): bool
	{
		return false;
	}
}
