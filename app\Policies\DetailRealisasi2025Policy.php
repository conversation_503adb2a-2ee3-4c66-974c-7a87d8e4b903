<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\DetailRealisasi2025;
use App\Models\User;

class DetailRealisasi2025Policy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any DetailRealisasi2025');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, DetailRealisasi2025 $detailrealisasi2025): bool
    {
        return $user->checkPermissionTo('view DetailRealisasi2025');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create DetailRealisasi2025');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, DetailRealisasi2025 $detailrealisasi2025): bool
    {
        return $user->checkPermissionTo('update DetailRealisasi2025');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, DetailRealisasi2025 $detailrealisasi2025): bool
    {
        return $user->checkPermissionTo('delete DetailRealisasi2025');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any DetailRealisasi2025');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, DetailRealisasi2025 $detailrealisasi2025): bool
    {
        return $user->checkPermissionTo('restore DetailRealisasi2025');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any DetailRealisasi2025');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, DetailRealisasi2025 $detailrealisasi2025): bool
    {
        return $user->checkPermissionTo('replicate DetailRealisasi2025');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder DetailRealisasi2025');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, DetailRealisasi2025 $detailrealisasi2025): bool
    {
        return $user->checkPermissionTo('force-delete DetailRealisasi2025');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any DetailRealisasi2025');
    }
}
