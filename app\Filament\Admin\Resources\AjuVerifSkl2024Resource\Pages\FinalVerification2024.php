<?php

namespace App\Filament\Admin\Resources\AjuVerifSkl2024Resource\Pages;

use App\Filament\Admin\Resources\AjuVerifSkl2024Resource;
use App\Models\AjuVerifSkl2024;
use App\Models\Pks2024;
use App\Models\SklRekomendasi2024;
use App\Models\User;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Actions\Action as ActionsAction;
use Filament\Forms\Components\{Actions as ComponentsActions, DatePicker, Fieldset, Grid, Group, Hidden, Placeholder, Section, Select, Tabs, Textarea, TextInput, Wizard};
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Wizard\Step;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Auth as FacadesAuth;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;

class FinalVerification2024 extends EditRecord
{
	protected static string $resource = AjuVerifSkl2024Resource::class;

	public static string | Alignment $formActionsAlignment = Alignment::Right;

	public function getTitle(): string | Htmlable
	{
		return 'Verifikasi Akhir Pengajuan SKL';
	}

	public function getSubheading(): ?string
	{
		return 'untuk RIPH Periode sebelum Tahun 2025';
	}

	protected function getHeaderActions(): array
	{
		return [
			// Actions\CreateAction::make(),
			ActionsAction::make('back')
				->label('Kembali')
				->color('success')
				->url(fn () => '/admin/aju-verif-skl2024s'),
		];
	}
	
	protected function getFormActions(): array
	{
		return [
		];
	}

	// protected function mutateFormDataBeforeSave(array $data): array
	// {
	// 		$record = $this->record;

	// 		// Log data yang diterima untuk debugging
	// 		\Illuminate\Support\Facades\Log::info('FinalVerification2024: Data yang diterima', [
	// 			'data' => $data,
	// 			'record_id' => $record->id
	// 		]);

	// 		// Proses update record
	// 		if (isset($data['status']) && $data['status'] === '2') {
	// 			$record->update([
	// 				'status' => $data['status'],
	// 				'note' => $data['note'],
	// 				'check_by' => Auth::user()->id,
	// 				'verif_at' => today(),
	// 			]);

	// 			// Pastikan pengajuan_id diisi dengan benar
	// 			if (isset($data['sklrekomendasi']) && is_array($data['sklrekomendasi'])) {
	// 				// Jika id ada dalam data sklrekomendasi, gunakan itu sebagai pengajuan_id
	// 				if (isset($data['sklrekomendasi']['id'])) {
	// 					$data['sklrekomendasi']['pengajuan_id'] = $record->id;

	// 					\Illuminate\Support\Facades\Log::info('FinalVerification2024: Menggunakan id dari sklrekomendasi', [
	// 						'id' => $data['sklrekomendasi']['id'],
	// 						'record_id' => $record->id
	// 					]);
	// 				} else {
	// 					// Jika tidak ada id, tambahkan pengajuan_id secara manual
	// 					$data['sklrekomendasi']['pengajuan_id'] = $record->id;

	// 					\Illuminate\Support\Facades\Log::info('FinalVerification2024: Menambahkan pengajuan_id secara manual', [
	// 						'pengajuan_id' => $record->id
	// 					]);
	// 				}
	// 			} else {
	// 				// Jika sklrekomendasi tidak ada dalam data, buat secara manual
	// 				\Illuminate\Support\Facades\Log::info('FinalVerification2024: Membuat sklrekomendasi secara manual', [
	// 					'record_id' => $record->id,
	// 					'no_ijin' => $record->no_ijin
	// 				]);

	// 				// Create or update the sklrekomendasi record with pengajuan_id set to the record's id
	// 				$record->sklrekomendasi()->updateOrCreate(
	// 					['no_ijin' => $record->no_ijin],
	// 					[
	// 						'pengajuan_id' => $record->id, // This is the key line that fixes the issue
	// 						'no_skl' => $data['sklrekomendasi']['no_skl'] ?? null,
	// 						'npwp' => $record->npwp ?? null,
	// 						'submit_by' => Auth::user()->id ?? null,
	// 						'published_date' => $data['sklrekomendasi']['published_date'] ?? null,
	// 						'approved_by' => $data['sklrekomendasi']['approved_by'] ?? null,
	// 					]
	// 				);
	// 			}

	// 			Notification::make()
	// 				->title('Penerbitan SKL Direkomendasikan')
	// 				->body('Penerbitan SKL telah direkomendasikan ke Pimpinan.')
	// 				->success()
	// 				->send();
	// 		} else if (isset($data['status']) && $data['status'] === '5') {
	// 			$record->update([
	// 				'status' => $data['status'],
	// 				'note' => $data['note'],
	// 				'check_by' => Auth::user()->id,
	// 				'verif_at' => today(),
	// 			]);

	// 			Notification::make()
	// 				->title('Pengajuan Dikembalikan untuk Perbaikan')
	// 				->body('Pengajuan telah dikembalikan ke Importir untuk perbaikan.')
	// 				->success()
	// 				->send();
	// 		}

	// 		// Log data yang akan dikembalikan
	// 		\Illuminate\Support\Facades\Log::info('FinalVerification2024: Data yang dikembalikan', [
	// 			'data' => $data
	// 		]);

	// 		return $data;
	// }

	public function form(Form $form): Form
	{
		return $form
		->schema([
			Wizard::make([
				Step::make('Pemohon')
					->schema([
						Group::make()
							->relationship('datauser')
							->schema([
								Section::make('Data Pemohon')
									->aside()
									->description('Data RIPH/PPRK')
									->schema([
										Placeholder::make('Company')
											->label('Perusahaan')
											->inlineLabel()
											->content(fn ($record) => new HtmlString('<span class="font-bold">'.$record->company_name.'</span>')),
										Placeholder::make('penanggungjawab')
											->label('Penanggung Jawab')
											->inlineLabel()
											->content(fn ($record) => new HtmlString('<span class="font-bold">'.$record->penanggungjawab.'</span>')),
										Placeholder::make('jabatan')
											->label('Jabatan')
											->inlineLabel()
											->content(fn ($record) => new HtmlString('<span class="font-bold">'.$record->jabatan.'</span>')),
										Placeholder::make('nib')
											->label('NIB')
											->inlineLabel()
											->content(fn ($record) => new HtmlString('<span>'.$record->nib_company.'</span>')),
										Placeholder::make('npwp')
											->label('NPWP')
											->inlineLabel()
											->content(fn ($record) => new HtmlString('<span>'.$record->npwp_company.'</span>')),
										Placeholder::make('address_company')
											->label('Alamat Perusahaan')
											->inlineLabel()
											->content(fn ($record) => new HtmlString(
												'<p>'.$record->address_company.'</p>
												<p>'.$record->mykabupaten->nama_kab.' - '.$record->myprovinsi->nama.', '.$record->kodepos.'</p>'
											)),
									]),
							]),
						Group::make()
							->relationship('commitment')
							->schema([
								Section::make('Data RIPH')
									->aside()
									->description('Data RIPH/PPRK')
									->schema([
										Placeholder::make('no_ijin')
											->label('No. RIPH')
											->inlineLabel()
											->content(fn ($record) => new HtmlString('<span class="font-bold">'.$record->no_ijin.'</span>')),
										Placeholder::make('periodetahun')
											->label('Periode Penerbitan')
											->inlineLabel()
											->content(fn ($record) => new HtmlString('<span class="font-bold">'.$record->periodetahun.'</span>')),
										Placeholder::make('tgl_ijin')
											->label('Mulai Berlaku')
											->inlineLabel()
											->content(fn ($record) => new HtmlString('<span class="font-bold">' . Carbon::parse($record->tgl_ijin)->translatedFormat('d F Y') . '</span>')),
										Placeholder::make('tgl_akhir')
											->label('Berlaku Hingga')
											->inlineLabel()
											->content(fn ($record) => new HtmlString('<span class="font-bold">' . Carbon::parse($record->tgl_akhir)->translatedFormat('d F Y') . '</span>')),
										Placeholder::make('volume_riph')
											->label('Volume RIPH')
											->inlineLabel()
											->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->volume_riph,2,',','.').' ton </span>')),
									]),
							]),
					]),
				Step::make('Realisasi')
					->schema([
						Group::make()
							->relationship('commitment')
							->schema([
								Section::make('Ringkasan')
									->aside()
									->description('Ringkasan Data Komitmen dan Realisasi')
									->afterStateHydrated(function ($set, $record) {
										$set('datarealisasi', $record->datarealisasi->sum('luas_lahan'));
									})
									->schema([
										Placeholder::make('luas_wajib_tanam')
											->label('Komitmen Tanam')
											->inlineLabel()
											->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->luas_wajib_tanam,2,',','.').' ha</span>')),
										Placeholder::make('volume_produksi')
											->label('Komitmen Produksi')
											->inlineLabel()
											->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->volume_produksi,2,',','.').' ton</span>')),

										Placeholder::make('realisasiTanam')
											->label('Realisasi Tanam')
											->inlineLabel()
											->hintIcon(fn ($record) =>
												$record->datarealisasi->sum('luas_lahan') < 0.9 * $record->luas_wajib_tanam
													? 'heroicon-s-exclamation-circle'
													: 'heroicon-s-check-circle'
											)
											->hintColor(fn ($record) =>
												$record->datarealisasi->sum('luas_lahan') < 0.6 * $record->luas_wajib_tanam
													? 'danger'
													: ($record->datarealisasi->sum('luas_lahan') < 0.9 * $record->luas_wajib_tanam
														? 'warning'
														: 'success')
											)
											->hintIconTooltip(fn ($record) =>
												$record->datarealisasi->sum('luas_lahan') < 0.6 * $record->luas_wajib_tanam
													? 'Total realisasi tanam tidak realistis'
													: ($record->datarealisasi->sum('luas_lahan') < 0.9 * $record->luas_wajib_tanam
														? 'Total realisasi tanam kurang realistis'
														: null)
											)
											->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->datarealisasi->sum('luas_lahan'),2,',','.').' ha</span>')),

										Placeholder::make('realisasiProduksi')
											->label('Realisasi Produksi')
											->inlineLabel()
											->hintIcon(fn ($record) =>
												$record->datarealisasi->sum('volume') < $record->volume_produksi
													? 'heroicon-s-x-circle'
													: 'heroicon-s-check-circle'
											)
											->hintColor(fn ($record) =>
												$record->datarealisasi->sum('volume') < $record->volume_produksi
													? 'danger' : 'success'
											)
											->hintIconTooltip(fn ($record) =>
												$record->datarealisasi->sum('luas_lahan') < 0.6 * $record->luas_wajib_tanam
													? 'Total realisasi tanam tidak realistis'
													: ($record->datarealisasi->sum('luas_lahan') < 0.9 * $record->luas_wajib_tanam
														? 'Total realisasi produksi kurang realistis'
														: null)
											)
											->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->datarealisasi->sum('volume'),2,',','.').' ton</span>')),
									]),

								Section::make('Kemitraan dan Lokasi')
									->aside()
									->description('Ringkasan Data Kemitraan dan Lokasi Tanam')
									->schema([
										Placeholder::make('Poktan')
											->label('Komitmen Kerjasama')
											->inlineLabel()
											->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->pks->count(),0,',','.').'  kelompok</span>')),

										Placeholder::make('pks')
											->label('Realisasi Kerjasama')
											->inlineLabel()
											->hintIcon(fn ($record) =>
												$record->pks->count() < $record->pks->count('berkas_pks')
													? 'heroicon-s-x-circle'
													: 'heroicon-s-check-circle'
											)
											->hintColor(fn ($record) =>
												$record->pks->count() < $record->pks->count('berkas_pks')
													? 'warning'
													: 'success'
											)
											->hintIconTooltip(fn ($record) =>
												$record->pks->count() < $record->pks->count('berkas_pks')
													? 'Hanya menjalin kemitraan dengan sejumlah kelompok'
													: null
											)
											->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->pks->count('berkas_pks'),0,',','.').' kelompok</span>')),

										Placeholder::make('lokasi')
											->label('Anggota/Petani')
											->hidden()
											->inlineLabel()
											->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->lokasi->unique('anggota_id')->count(),0,',','.').'  anggota</span>')),

										Placeholder::make('anggotaId')
											->label('Realisasi Petani')
											->inlineLabel()
											->hidden()
											->hintIcon(fn ($record) =>
												$record->lokasi->unique('anggota_id')->count() > $record->datarealisasi->unique('anggota_id')->count()
													? 'heroicon-s-x-circle'
													: 'heroicon-s-check-circle'
											)
											->hintColor(fn ($record) =>
												$record->lokasi->unique('anggota_id')->count() > $record->datarealisasi->unique('anggota_id')->count()
													? 'warning'
													: 'success'
											)
											->hintIconTooltip(fn ($record) =>
												$record->pks->count() < $record->pks->count('berkas_pks')
													? 'Jumlah petani yang sama'
													: null
											)
											->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->datarealisasi->unique('anggota_id')->count(),0,',','.').'  anggota</span>')),

										Placeholder::make('Jumlah Lahan')
											->label('Jumlah Lahan')
											->inlineLabel()
											->content(fn ($record) => new HtmlString('<span class="font-bold">'.number_format($record->datarealisasi->count(),0,',','.').'  titik</span>')),

									])
							]),

						Group::make()
							->schema([
								Section::make('Perjanjian Kemitraan')
									->aside()
									->description('Pemeriksaan Berkas dan Data Perjanjian antara Pelaku Usaha dengan Kelompok Tani Mitra.')
									->schema([
										TableRepeater::make('berkaspks')
										->addable(false)
										->deletable(false)
										->hiddenLabel()
										->relationship('pks')
										->streamlined()
										->headers([
											Header::make('Kelompok Tani'),
											Header::make('No. Perjanjian'),
											Header::make('Tautan'),
											Header::make('Status'),
											Header::make('Rinci'),
										])
										->schema([
											Placeholder::make('poktan')
												->hiddenLabel()
												->content(fn($record) => $record->masterpoktan->nama_kelompok),
											Placeholder::make('no_Perjanjian')
												->hiddenLabel()
												->content(fn($record) => $record->no_perjanjian),
											Placeholder::make('Tautan')
												->hiddenLabel()
												->extraAttributes(['class' => 'text-info-500'])
												->content(function ($record) {
													$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

													$tahun = $record->commitment->periodetahun;

													$fileName = $record->berkas_pks;

													if ($fileName) {
														if (str_starts_with($fileName, 'uploads/')) {
															$fileUrl = Storage::url("{$fileName}");
														} else {
															$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
														}

														return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
													}

													return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
												}),
											Placeholder::make('statuspks')
												->hiddenLabel()
												->content(fn($get) => view('components.status-badge-verifikasi', ['status' => $get('status')])),

											ComponentsActions::make([
												Action::make('detail')
													->hiddenLabel()
													->iconButton()
													->color('warning')
													->fillForm(fn (Pks2024 $record): array => [
														'status' => $record->status,
														'note' => $record->note,
													])
													->form([
														Grid::make([
															'default' => 1,
															'sm' => 1,
															'lg' => 2,
														])->schema([
															Group::make([
																Placeholder::make('poktan')
																	->inlineLabel()
																	->content(fn($record) => $record->masterpoktan->nama_kelompok),
																Placeholder::make('no_Perjanjian')
																	->inlineLabel()
																	->content(fn($record) => $record->no_perjanjian),
																Placeholder::make('masa_berlaku')
																	->inlineLabel()
																	->content(
																		fn($record) => ($record->tgl_perjanjian_start ?
																			Carbon::parse($record->tgl_perjanjian_start)->format('d M Y') : '-') .
																			' s.d ' .
																			($record->tgl_perjanjian_end ?
																				Carbon::parse($record->tgl_perjanjian_end)->format('d M Y') : '-')
																	),
																Placeholder::make('memberCount')
																	->inlineLabel()
																	->label('Anggota')
																	->content(fn($record) => $record->lokasi->count() .' orang'),
																Placeholder::make('luas_rencana')
																	->inlineLabel()
																	->label('Luas Rencana')
																	->content(fn($record) => number_format($record->lokasi->sum('luas_lahan'), 2, ',', '.') . ' ha'),
																Placeholder::make('varietas')
																	->inlineLabel()
																	->label('Rencana Varietas')
																	->content(fn($record) => $record->varietas?->nama_varietas ?? '-')

															]),
															Fieldset::make('Kesimpulan Pemeriksaan')
																->columnSpan(1)
																->columns(1)
																->schema([
																	Textarea::make('note')
																		->label('Catatan Pemeriksaan')
																		->required(),
																	Select::make('status')
																		->inlineLabel()
																		->label('Kesimpulan')
																		->required()
																		->options([
																			'sesuai' => 'Sesuai',
																			'perbaikan' => 'Tidak Sesuai/Perbaikan',
																		]),
																])
														]),
													])
													->icon('icon-layout-text-window-reverse')
													->modal()
													->modalCancelAction(fn($action) => $action->label('Tutup'))
													->modalHeading(fn($record) => 'Detail Perjanjian ' . ($record->no_perjanjian ?? $record->nama_poktan))
													->action(function (array $data, Pks2024 $record): void {
														$record->update([
															'status' => $data['status'] ?? null,
															'note' => $data['note'] ?? null,
														]);
													}),

												Action::make('viewReport')
													->hiddenLabel()
													->tooltip('Lihat Daftar Realisasi')
													->iconButton()
													->icon('heroicon-o-map')
													->url(fn ($record) => route(
														'panel.admin.realisasiReportView',
														['pksId' => Crypt::encryptString($record->id, config('app.qr_secret'))] // Enkripsi dengan key dari config
													)), //encrypt 256 menggunakan key dari config/app 'qr_secret' => env('QR_SECRET', 'B1804bio')
											])->alignCenter(),
											// Placeholder::make('detail')
											//     ->hiddenLabel()
											//     ->extraAttributes(['class'=>'text-center'])
											//     ->content(fn ($record) => $record->no_perjanjian),
										])
									])
								])
					]),
				Step::make('Berkas-berkas')
					->schema([
						Group::make()
							->relationship('commitment')
							->schema([
								Section::make('Tahap Tanam')
									->aside()
									->description('Berkas-berkas yang diunggah oleh importir untuk tahap tanam')
									->schema([
										Placeholder::make('formSptjmTanam')
											->label('Form SPTJM Tanam')
											->inlineLabel()
											->content(function ($record) {
												$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

												$tahun = $record->periodetahun;

												$fileName = $record->userDocs->sptjmtanam;

												if ($fileName) {
													if (str_starts_with($fileName, 'uploads/')) {
														$fileUrl =Storage::url("{$fileName}");
													} else {
														$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
													}

													return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
												}

												return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
											}),

										Placeholder::make('logBookTanam')
											->label('Logbook Tanam')
											->inlineLabel()
											->content(function ($record) {
												$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

												$tahun = $record->periodetahun;

												$fileName = $record->userDocs->logbooktanam;

												if ($fileName) {
													if (str_starts_with($fileName, 'uploads/')) {
														$fileUrl =Storage::url("{$fileName}");
													} else {
														$fileUrl = Storage::url("storage/uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
													}

													return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
												}

												return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
											}),

										Placeholder::make('formRta')
											->label('Form RTA')
											->inlineLabel()
											->content(function ($record) {
												$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

												$tahun = $record->periodetahun;

												$fileName = $record->userDocs->rta;

												if ($fileName) {
													if (str_starts_with($fileName, 'uploads/')) {
														$fileUrl =Storage::url("{$fileName}");
													} else {
														$fileUrl = Storage::url("storage/uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
													}

													return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
												}

												return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
											}),

									]),
								Section::make('Tahap Produksi')
									->aside()
									->description('Berkas-berkas yang diunggah oleh importir untuk tahap produksi')
									->schema([
										Placeholder::make('formSptjmProduksi')
											->label('Form SPTJM Produksi')
											->inlineLabel()
											->content(function ($record) {
												$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

												$tahun = $record->periodetahun;

												$fileName = $record->userDocs->sptjmproduksi;

												if ($fileName) {
													if (str_starts_with($fileName, 'uploads/')) {
														$fileUrl =Storage::url("{$fileName}");
													} else {
														$fileUrl = Storage::url("storage/uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
													}

													return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
												}

												return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
											}),

										Placeholder::make('logBookProduksi')
											->label('Logbook Produksi')
											->inlineLabel()
											->content(function ($record) {
												$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

												$tahun = $record->periodetahun;

												$fileName = $record->userDocs->logbookproduksi;

												if ($fileName) {
													if (str_starts_with($fileName, 'uploads/')) {
														$fileUrl =Storage::url("{$fileName}");
													} else {
														$fileUrl = Storage::url("storage/uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
													}

													return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
												}

												return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
											}),

										Placeholder::make('formRpo')
											->label('Form RPO')
											->inlineLabel()
											->content(function ($record) {
												$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

												$tahun = $record->periodetahun;

												$fileName = $record->userDocs->rpo;

												if ($fileName) {
													if (str_starts_with($fileName, 'uploads/')) {
														$fileUrl =Storage::url("{$fileName}");
													} else {
														$fileUrl = Storage::url("storage/uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
													}

													return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
												}

												return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
											}),

										Placeholder::make('formLa')
											->label('Laporan Akhir')
											->inlineLabel()
											->content(function ($record) {
												$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

												$tahun = $record->periodetahun;

												$fileName = $record->userDocs->formLa;

												if ($fileName) {
													if (str_starts_with($fileName, 'uploads/')) {
														$fileUrl =Storage::url("{$fileName}");
													} else {
														$fileUrl = Storage::url("storage/uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
													}

													return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
												}

												return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
											}),
									]),
							])
					]),
				Step::make('Pemeriksaan Verifikator')
					->schema([
						Group::make()
							->relationship('commitment')
							->schema([
								Section::make('Verifikasi Tanam')
									->aside()
									->description('Catatan hasil verifikasi realisasi tanam')
									->schema([
										Placeholder::make('verifikatorTanam')
											->label('Petugas Verifikasi')
											->inlineLabel()
											->content(function ($record) {
												$ajutanam = $record->ajutanam()
													->withoutGlobalScope('npwp')
													->with('dataadmin')
													->first();

												return new HtmlString(
													'<span class="font-bold">' .
													($ajutanam?->dataadmin?->nama ?? '-') .
													'</span>'
												);
											}),

										Placeholder::make('catatanVerifTanam')
											->label('Catatan Verifikasi')
											->inlineLabel()
											->content(function ($record) {
												$ajutanam = $record->ajutanam()
													->withoutGlobalScope('npwp')
													->with('dataadmin')
													->first();

												return new HtmlString(
													'<p class="font-bold">' .
													($ajutanam?->note ?? '-') .
													'</p>'
												);
											}),
											Placeholder::make('baVerTan')
												->label('Berita Acara Verifikasi')
												->inlineLabel()
												->extraAttributes(['class' => 'text-info-500'])
												->content(function ($record) {
													$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

													$tahun = $record->periodetahun;

													$fileName = $record->ajutanam()->withoutGlobalScope('npwp')->first()->batanam;

													if ($fileName) {
														if (str_starts_with($fileName, 'uploads/')) {
															$fileUrl = Storage::url("{$fileName}");
														} else {
															$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
														}

														return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
													}

													return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
												}),
											Placeholder::make('notaDinasTanam')
												->label('Nota Dinas Verifikasi')
												->inlineLabel()
												->extraAttributes(['class' => 'text-info-500'])
												->content(function ($record) {
													$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

													$tahun = $record->periodetahun;

													$fileName = $record->ajutanam()->withoutGlobalScope('npwp')->first()->ndhprt;

													if ($fileName) {
														if (str_starts_with($fileName, 'uploads/')) {
															$fileUrl = Storage::url("{$fileName}");
														} else {
															$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
														}

														return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
													}

													return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
												}),
										]),
									Section::make('Verifikasi Produksi')
										->aside()
										->description('Catatan hasil verifikasi realisasi produksi')
										->schema([
											Placeholder::make('verifikatorProduksi')
												->label('Petugas Verifikasi')
												->inlineLabel()
												->content(function ($record) {
													$ajuproduksi = $record->ajuproduksi()
														->withoutGlobalScope('npwp')
														->with('dataadmin')
														->first();

													return new HtmlString(
														'<span class="font-bold">' .
														($ajuproduksi?->dataadmin?->nama ?? '-') .
														'</span>'
													);
												}),
											Placeholder::make('catatanVerifProduksi')
												->label('Catatan Verifikasi')
												->inlineLabel()
												->content(function ($record) {
													$ajuproduksi = $record->ajuproduksi()
														->withoutGlobalScope('npwp')
														->with('dataadmin')
														->first();

													return new HtmlString(
														'<p class="font-bold">' .
														($ajuproduksi?->note ?? '-') .
														'</p>'
													);
												}),
											Placeholder::make('baVerProd')
												->label('Berita Acara Verifikasi')
												->inlineLabel()
												->extraAttributes(['class' => 'text-info-500'])
												->content(function ($record) {
													$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

													$tahun = $record->periodetahun;

													$fileName = $record->ajuproduksi()->withoutGlobalScope('npwp')->first()->baproduksi;

													if ($fileName) {
														if (str_starts_with($fileName, 'uploads/')) {
															$fileUrl = Storage::url("{$fileName}");
														} else {
															$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
														}

														return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
													}

													return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
												}),
											Placeholder::make('notaDinasProduksi')
												->label('Nota Dinas Verifikasi')
												->inlineLabel()
												->extraAttributes(['class' => 'text-info-500'])
												->content(function ($record) {
													$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);

													$tahun = $record->periodetahun;

													$fileName = $record->ajuproduksi()->withoutGlobalScope('npwp')->first()->ndhprp;

													if ($fileName) {
														if (str_starts_with($fileName, 'uploads/')) {
															$fileUrl = Storage::url("{$fileName}");
														} else {
															$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
														}

														return new HtmlString('<a class="text-info-500 font-bold" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
													}

													return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
												}),
										]),
							])
					]),
				Step::make('Kesimpulan')->schema([
					Section::make()
						->aside()
						->description('Kesimpulan hasil verifikasi menyeluruh')
						->schema([
							Placeholder::make('disclaimer')
								->hiddenLabel()
								->label('')
								->content(new HtmlString('<p class="text-warning-500 font-bold">Berdasarkan hasil pemeriksaan data dan berkas yang telah dilakukan, dengan ini dapat disimpulkan:</p>')),

							Hidden::make('id'),
							Hidden::make('check_by')->default(Auth::user()->id)->formatStateUsing(fn () => Auth::user()->id),
							
							Select::make('selectstatus')
								->label('Status')
								->options([
									'2' => 'Rekomendasikan ke Pimpinan',
									'5' => 'Kembalikan ke Importir/Verifikator untuk Perbaikan'
								])
								->required()
								->reactive()
								->dehydrated(false)
								->afterStateUpdated(fn ($set, $state) => $set('status', $state)),

							Hidden::make('status'),

							Group::make()
								->visible(fn ($get) => $get('status') === '2')
								->columns(1)
								->relationship('sklrekomendasi')
								->schema([
									Hidden::make('pengajuan_id')
										->default(function ($livewire) {
											$recordId = $livewire->record->id;
											return $recordId;
										}),
									TextInput::make('no_skl')
										->label('No. SKL')
										->inlineLabel()
										->required()
										->live(onBlur: true)
										->helperText(fn (callable $get) =>
											$get('duplicate_warning')
												? new HtmlString('<span class="text-danger-500">' . $get('duplicate_warning') . '</span>')
												: null
										)
										->afterStateUpdated(function (callable $set, $state) {
											$isDuplicate = \App\Models\SklRekomendasi2024::where('no_skl', $state)->first();
											$set('duplicate_warning', $isDuplicate ? 'Nomor SKL sudah digunakan' : '');

											if ($isDuplicate) {
												$set('no_skl', null);
												Notification::make()
													->title('Error')
													->danger()
													->body('Nomor SKL sudah digunakan. Silakan gunakan nomor lain.')
													->send();
											}
										}),
									DatePicker::make('published_date')
										->label('Tanggal Terbit')->inlineLabel()
										->required()
										->helperText('Tanggal akan tercetak di SKL'),
									Select::make('approved_by')
										->label('Pejabat')->inlineLabel()
										->required()
										->helperText('Pejabat penandatangan SKL')
										->options(fn () => User::query()
											->withoutGlobalScope('npwp')
											->role('direktur')
											->where('status', 'aktif')
											->pluck('name', 'id')
											->toArray()
										)
										->preload(),
								]),
							Textarea::make('note')
								->label('Catatan')
								->required()
								->visible(fn ($get) => $get('selectstatus') != null)
								->placeholder(fn (callable $get) => $get('status') === '2'
									? 'Berikan alasan rekomendasi kepada pimpinan'
									: 'Berikan alasan pengembalian dan petunjuk perbaikan untuk Pelaku Usaha/Verifikator')
								->columnSpanFull(),

							TextInput::make('validateUsername')
								->live(onBlur:true)
								->required()
								->afterStateUpdated(function ($set, $state) {
									if($state != Auth::user()->username)
									{
										$set('validateUsername', null);
										$set('showButton', null);
										Notification::make()
											->title('Unauthorized')
											->body('username tidak sesuai.')
											->danger()
											->send();
									}else{
										$set('showButton', 'show');
									}
								})->dehydrated(false),
						])
				]),
				Step::make('Selesai')
					->schema([
						Placeholder::make('finalvalidation')
							->hiddenLabel()
							->reactive()
							->content(function ($get) {
								$status = $get('status');

								$viewData = [
									'status' => $status,
								];

								// Render view
								return new HtmlString(
									view('filament.admin.resources.validationInfo', $viewData)->render()
								);
							}),
					]),
			])
			// ->skippable()
			->persistStepInQueryString()->submitAction(new HtmlString(Blade::render(<<<BLADE
				<x-filament::button
					type="submit"
					size="sm"
					color="warning"
				>
					Submit
				</x-filament::button>
			BLADE))),
		])->columns(1);
	}

}
