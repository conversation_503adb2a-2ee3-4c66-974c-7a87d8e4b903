<?php

namespace App\Filament\Admin\Resources\BotLogResource\Pages;

use App\Filament\Admin\Resources\BotLogResource;
use App\Filament\Admin\Resources\BotLogResource\Widgets\BotStatWidgets;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListBotLogs extends ListRecords
{
    protected static string $resource = BotLogResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }

	protected function getHeaderWidgets(): array
    {
        return [
			BotStatWidgets::class,
        ];
    }
}
