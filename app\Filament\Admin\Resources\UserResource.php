<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\UserResource\Pages;
use App\Filament\Admin\Resources\UserResource\RelationManagers;
use App\Filament\Exports\UserExporter;
use App\Models\DataAdministrator;
use App\Models\User;
use Filament\Actions\Exports\Enums\ExportFormat;
use Filament\Forms;
use Filament\Forms\Components\{Group, Hidden, Radio, Select, TextInput, Toggle};
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ExportAction;
use Filament\Tables\Columns\SelectColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Grouping\Group as GroupingGroup;
use Filament\Tables\Table;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Rmsramos\Activitylog\Actions\ActivityLogTimelineTableAction;
use Rmsramos\Activitylog\RelationManagers\ActivitylogRelationManager;

class UserResource extends Resource
{
	protected static ?string $model = User::class;

	protected static ?string $navigationGroup = 'Manajemen Akses';
	protected static ?string $modelLabel = 'Pengguna';
	protected static ?string $pluralModelLabel = 'Daftar Pengguna Simethris';

	protected static ?string $navigationLabel = 'Pengguna';
	protected static ?int $navigationSort = -1;
	protected static ?string $navigationIcon = 'heroicon-o-user-group';

	public static function getNavigationBadge(): ?string
	{
		return static::getModel()::where('status', 'Baru')->count();
	}

	public static function getNavigationBadgeColor(): ?string
	{
		return static::getModel()::count() > 0 ? 'danger' : 'primary';
	}

	// public static function getGloballySearchableAttributes(): array
	// {
	// 	return ['commitment.no_ijin', 'oldcommitment.no_ijin'];
	// }

	// public static function getGlobalSearchResultDetails($record): array
	// {
	// 	return [
	// 		'Pengguna' => $record->commitment->no_ijin,
	// 	];
	// }

	public static function form(Form $form): Form
	{
		return $form
			->schema([
				Group::make()
					->visible(fn ($record) => $record->status === 'Baru')
					->schema([
						Radio::make('status')
							->inline()
							->inlineLabel(false)
							->options([
								'Baru' => 'Tunda',
								'Aktif' => 'Diterima',
								'Ditolak' => 'Ditolak',
							])
							->reactive()
							->live()
							->afterStateUpdated(function ($state, callable $set, $record) {
								if ($state === 'Baru') {
									$set('email_verified_at', null);
									return;
								}
								$set('email_verified_at', now());
							}),
						])->columnSpanFull(),

				TextInput::make('name')
					->required()
					->maxLength(255),
				TextInput::make('username')
					->required()
					->maxLength(255),
				TextInput::make('email')
					->email()
					->required()
					->maxLength(255),
				Select::make('roles')->multiple()->relationship('roles', 'name')->preload()
			]);
	}

	public static function table(Table $table): Table
	{
		return $table
			->defaultSort('status', 'desc')
			->deferLoading()
			->columns([
				TextColumn::make('index')
                    ->label('No')
					->rowIndex(),
				TextColumn::make('name')
					->sortable()
					->searchable(),

				TextColumn::make('email')
					->sortable()
					->searchable(),
				TextColumn::make('status')
					->badge()
					->color(fn (string $state): string => match ($state){
						'Baru' => 'danger',
						'Aktif' => 'success',
					}),
				TextColumn::make('roles.name')
					->badge()
					->color(fn (string $state): string => match ($state){
						'importir' => 'warning',
						'verifikator' => 'primary',
						'direktur' => 'danger',
						'admin' => 'success',
						default => 'info',
					}),

			])
			->filters([
				//
				// SelectFilter::make('roles')
				// 	->label('Peran')
				// 	->relationship('roles', 'name')
				// 	->searchable()
				// 	->default('7')
				// 	->preload()
			])
			->headerActions([
				// ExportAction::make()
				// 	->exporter(UserExporter::class)
				// 	->formats([
				// 		ExportFormat::Xlsx,
				// 	])
			])
			->actions([
				Tables\Actions\EditAction::make()->hiddenLabel()->tooltip('Edit'),
				ActivityLogTimelineTableAction::make('Activities')->iconButton()->color('warning')->withRelations(['oldcommitment']),
			])
			->bulkActions([
				Tables\Actions\BulkActionGroup::make([
					Tables\Actions\DeleteBulkAction::make(),
				]),
			]);
	}

	public static function getRelations(): array
	{
		return [
			//
			// ActivitylogRelationManager::class,
		];
	}

	public static function getPages(): array
	{
		return [
			'index' => Pages\ListUsers::route('/'),
			'create' => Pages\CreateUser::route('/create'),
			// 'view' => Pages\ViewUser::route('/{record}'),
			'edit' => Pages\EditUser::route('/{record}/edit'),
			'myprofile' => Pages\MyProfile::route('/{record}/myprofile'),
		];
	}

	public static function shouldRegisterNavigation(): bool
    {
        $user = Auth::user();
        if ($user->hasAnyRole(['Super Admin', 'admin'])) {
			return true;
        }else{
        	return false;
        }
    }
}
