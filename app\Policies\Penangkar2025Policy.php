<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\Penangkar2025;
use App\Models\User;

class Penangkar2025Policy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any Penangkar2025');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Penangkar2025 $penangkar2025): bool
    {
        return $user->checkPermissionTo('view Penangkar2025');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create Penangkar2025');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Penangkar2025 $penangkar2025): bool
    {
        return $user->checkPermissionTo('update Penangkar2025');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Penangkar2025 $penangkar2025): bool
    {
        return $user->checkPermissionTo('delete Penangkar2025');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any Penangkar2025');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Penangkar2025 $penangkar2025): bool
    {
        return $user->checkPermissionTo('restore Penangkar2025');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any Penangkar2025');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, Penangkar2025 $penangkar2025): bool
    {
        return $user->checkPermissionTo('replicate Penangkar2025');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder Penangkar2025');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Penangkar2025 $penangkar2025): bool
    {
        return $user->checkPermissionTo('force-delete Penangkar2025');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any Penangkar2025');
    }
}
