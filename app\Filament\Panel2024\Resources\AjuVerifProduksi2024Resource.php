<?php

namespace App\Filament\Panel2024\Resources;

use App\Filament\Panel2024\Resources\AjuVerifProduksi2024Resource\Pages;
use App\Filament\Panel2024\Resources\AjuVerifProduksi2024Resource\Pages\VerificationReport;
use App\Filament\Panel2024\Resources\AjuVerifProduksi2024Resource\RelationManagers;
use App\Models\AjuVerifProduksi2024;
use Filament\Forms;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class AjuVerifProduksi2024Resource extends Resource
{
    protected static ?string $model = AjuVerifProduksi2024::class;
    protected static ?string $navigationIcon = 'icon-garlic-fill';
	protected static ?string $pluralModelLabel = 'Pengajuan Verifikasi Produksi';
	protected static ?int $navigationSort = 2;

	
	public static function getNavigationLabel(): string
	{
		$label = 'Verifikasi Produksi';
		return $label;
    }

	// public static function getNavigationGroup(): ?string
    // {
	// 	$group = null;
	// 	if(Auth::user()->hasAnyRole(['importir'])){
	// 		$group = null;
	// 	}
    //     return $group;
    // }
	
	public static function getNavigationBadge(): ?string
	{
		return static::getModel()::whereIn('status', ['1','2'])->count();
	}

	public static function getNavigationBadgeColor(): ?string
	{
		return static::getModel()::count() > 0 ? 'warning' : 'danger';
	}

	public static function shouldRegisterNavigation(): bool
	{
		if(Auth::user()->hasRole('importir'))
		{
			return static::getModel()::whereIn('status', ['1', '2', '3'])->exists();
		}
		return static::getModel()::whereIn('status', ['1', '2','3','4'])->exists();
	}

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('no_ijin')
					->searchable(),
                TextColumn::make('datauser.company_name')
					->searchable()
					->visible(fn () => Auth::user()->hasAnyRole(['admin', 'Super Admin', 'verifikator'])),
				TextColumn::make('created_at')
					->label('Tanggal Pengajuan')
					->date(),
				TextColumn::make('dataadmin.nama')
					->label('Verifikator')
					->visible(fn () => Auth::user()->hasAnyRole(['admin', 'Super Admin', 'verifikator']))
					->searchable(),
				TextColumn::make('status')
					->badge()
					->color(fn ($record) => match ($record->status) {
						'1' => 'warning',
						'2' => 'primary',
						'3' => 'danger',
						'4' => 'success',
						default => 'danger',
					})->formatStateUsing(fn ($state) => match ($state) {
						'1' => 'Diajukan',
						'2' => 'Diperiksa',
						'3' => 'Perbaikan',
						'4' => 'Selesai',
						default => 'Unknown', // Jika status null atau tidak terdefinisi
					}),
            ])
            ->filters([
                //
            ])
            ->actions([
                // Tables\Actions\ViewAction::make(),
                // Tables\Actions\EditAction::make(),
				Action::make('fileUpload')
					->hiddenLabel()
					->tooltip('Unggah Berkas')
					->icon('icon-cloud-arrow-up-fill')
					->color('success')
					->visible(fn ($record) => $record->status !== '1' && Auth::user()->hasAnyRole(['verifikator', 'admin', 'Super Admin']) && $record->baproduksi !== null && $record->ndhprp !== null)
					->tooltip('Unggah Berkas BA dan Nota Dinas')
					->color('primary')
					->requiresConfirmation()
					->modalHeading('Unggah Berkas')
					->modalDescription('Ubah Berkas Berita Acara Verifikasi dan Nota Dinas')
					->modalSubmitActionLabel('Unggah')
					->form([
						FileUpload::make('baproduksi')
							->openable()
							->required()
							->maxSize(2048)
							->downloadable()
							->deletable()
							->disk('public')
							->visibility('public')
							->panelAspectRatio('4:1')
							->imagePreviewHeight('100')
							->fetchFileInformation(true)
							->label('Unggah Berita Acara')
							// ->acceptedFileTypes(['application/pdf'])
							// ->hidden(fn ($get) => $get('status') !== '4')
							->helperText('Berita Acara Hasil Pemeriksaan. Maksimal 2MB, format PDF')
							->directory(function ($record) {
								$tahun = $record->commitment->periodetahun;
								$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
								return "uploads/{$cleanNpwp}/{$tahun}";
							})
							->rules([
								'file',
								'mimetypes:application/pdf',
								'mimes:pdf'
							])
							->validationMessages([
								'mimetypes' => 'Hanya file PDF yang diperbolehkan',
								'mimes' => 'Ekstensi file harus .pdf',
							])
							->getUploadedFileNameForStorageUsing(
								function (TemporaryUploadedFile $file, $get, $record): string {
									$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
									$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

									// Format nama file: [ID]_[NPWP]_[NOIJIN].[ext]
									return 'bap_' . $record->id . '_' . $cleanNpwp . '_' . $cleanNoIjin . '_'. uniqid() .  '.' . $file->getClientOriginalExtension();
								}
							),

						FileUpload::make('ndhprp')
							->openable()
							->required()
							->maxSize(2048)
							->downloadable()
							->deletable()
							->disk('public')
							->visibility('public')
							->panelAspectRatio('4:1')
							->imagePreviewHeight('100')
							->fetchFileInformation(true)
							->label('Unggah Nota Dinas')
							// ->acceptedFileTypes(['application/pdf'])
							// ->hidden(fn ($get) => $get('status') !== '4')
							->helperText('Nota Dinas Hasil Pemeriksaan. Maksimal 2MB, format PDF')
							->directory(function ($record) {
								$tahun = $record->commitment->periodetahun;
								$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
								return "uploads/{$cleanNpwp}/{$tahun}";
							})
							->rules([
								'file',
								'mimetypes:application/pdf',
								'mimes:pdf'
							])
							->validationMessages([
								'mimetypes' => 'Hanya file PDF yang diperbolehkan',
								'mimes' => 'Ekstensi file harus .pdf',
							])
							->getUploadedFileNameForStorageUsing(
								function (TemporaryUploadedFile $file, $get, $record): string {
									$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
									$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

									// Format nama file: [ID]_[NPWP]_[NOIJIN].[ext]
									return 'ndhp_' . $record->id . '_' . $cleanNpwp . '_' . $cleanNoIjin . '_'. uniqid() .  '.' . $file->getClientOriginalExtension();
								}
							),
						])
					->action(function (array $data, AjuVerifProduksi2024 $record): void {
						$record->update([
							'baproduksi' => $data['baproduksi'] ?? null,
							'ndhprp' => $data['ndhprp'] ?? null,
						]);
					}),

				Action::make('proceedVerif')
					->hiddenLabel()
					->tooltip('Verifikasi')
					->icon('icon-ui-checks-grid')
					->color('danger')
					->visible(fn () => Auth::user()->hasAnyRole(['admin', 'Super Admin', 'verifikator']))
					->url(fn ($record) => route('filament.panel2024.resources.aju-verif-produksi2024s.verifProduksi', ['record' => $record->id])),



				Action::make('viewReport')
					->hiddenLabel()
					->tooltip('Lihat Hasil Verifikasi')
					->icon('icon-binoculars-fill')
					->url(fn ($record) => route('filament.panel2024.resources.aju-verif-produksi2024s.verifReport', ['record' => $record->id])),
			]);
            // ->bulkActions([
            //     Tables\Actions\BulkActionGroup::make([
            //         Tables\Actions\DeleteBulkAction::make(),
            //     ]),
            // ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAjuVerifProduksi2024s::route('/'),
            'create' => Pages\CreateAjuVerifProduksi2024::route('/create'),
            'verifProduksi' => Pages\VerificationProduksi::route('/{record}/verification'),
            'verifReport' => VerificationReport::route('/{record}/report'),
            // 'view' => Pages\ViewAjuVerifProduksi2024::route('/{record}'),
            // 'edit' => Pages\EditAjuVerifProduksi2024::route('/{record}/edit'),
        ];
    }

}
