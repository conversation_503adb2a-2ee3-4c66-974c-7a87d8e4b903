<?php

namespace App\Filament\Admin\Resources\MasterKabupatenResource\Pages;

use App\Filament\Admin\Resources\MasterKabupatenResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;

class ViewMasterKabupaten extends ViewRecord
{
    protected static string $resource = MasterKabupatenResource::class;

	public function getTitle(): string|Htmlable
	{
		$nama = $this->record ? $this->record->nama_kab : '##';
        return 'Data ' . $nama;
	}

    public function getHeading(): string
	{
        $nama = $this->record ? $this->record->nama_kab : '##';
        return 'Data ' . $nama;
	}

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
