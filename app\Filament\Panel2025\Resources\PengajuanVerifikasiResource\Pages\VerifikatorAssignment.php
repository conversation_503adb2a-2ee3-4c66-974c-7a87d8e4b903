<?php

namespace App\Filament\Panel2025\Resources\PengajuanVerifikasiResource\Pages;

use App\Filament\Panel2025\Resources\PengajuanVerifikasiResource;
use App\Models\User;
use App\Models\VerificatorAssignment;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;

class VerifikatorAssignment extends EditRecord
{
    protected static string $resource = PengajuanVerifikasiResource::class;
    public static string | Alignment $formActionsAlignment = Alignment::Right;
    
    protected static ?string $title = 'Penugasan Verifikasi';
    public function getHeading(): string
	{
        return 'Penugasan Verifikasi';
	}

    public function getSubheading(): ?string
    {
        $noIjin = $this->record ? $this->record->no_ijin : '##';
        return 'PPRK No: ' . $noIjin;
    }

    protected function getHeaderActions(): array
    {
        return [
            // Actions\ViewAction::make(),
            // Actions\DeleteAction::make(),
        ];
    }

    public function form(Form $form): Form
	{
		return $form
		->schema([
            Section::make('Ringkasan Pengajuan')
                ->aside()
                ->description('Data Rekomendasi Import Produk Hortikultura')
                ->schema([
                    Placeholder::make('Nomor Pengajuan')
                        ->inlineLabel()
                        ->content(fn ($record) => $record->no_pengajuan),
                    Placeholder::make('Pelaku Usaha')
                        ->inlineLabel()
                        ->content(fn ($record) => $record->user->datauser->company_name),
                    Placeholder::make('NPWP')
                        ->inlineLabel()
                        ->label('NPWP')
                        ->content(fn ($record) => $record->npwp),
                    Placeholder::make('No. PPRK')
                        ->inlineLabel()
                        ->label('No. PPRK')
                        ->content(fn ($record) => $record->no_ijin),
                    Placeholder::make('Tanggal Pengajuan')
                        ->inlineLabel()
                        ->label('Tanggal Pengajuan')
                        ->content(fn ($record): string => $record->created_at->toFormattedDateString()),
                ]),

            Section::make('Berkas Pengajuan')
                ->aside()
                ->description('Kelengkapan Berkas Pengajuan')
                ->schema([
                    TableRepeater::make('berkas')
                        ->hiddenLabel()
                        ->addable(false)
                        ->deletable(false)
                        ->relationship(
                            fn () => Auth::user()->hasAnyRole(['Super Admin', 'admin']) ? 'berkaspengajuan' : 'userfiles'
                        )
                        ->headers([
                            Header::make('Berkas'),
                            Header::make('Tautan'),
                        ])
                        ->schema([
                            Placeholder::make('kind')
                                ->hiddenLabel()
                                ->content(fn ($record) => [
                                    'spvt' => 'Surat Pengajuan Verifikasi (Tanam)',
                                    'spvp' => 'Surat Pengajuan Verifikasi (Produksi)',
                                    'spskl' => 'Surat Pengajuan Penerbitan SKL',
                                ][$record->kind] ?? $record->kind),
                                
                            Placeholder::make('Tautan')
                                ->hiddenLabel()
                                ->content(fn ($record) => new HtmlString(
                                    '<a href="/' . e($record->file_url) . '" target="_blank" rel="noopener noreferrer">Buka File</a>'
                                )),
                        ])
                ]),

            Section::make('Penugasan Verifikator (Assignment)')
                ->aside()
                ->visible(fn () => Auth::user()->hasAnyRole(['Super Admin', 'admin']))
                ->description('Penunjukkan Petugas Pelaksana Verifikasi untuk melaksanakan verifikasi.')
                ->schema([
                    TableRepeater::make('verificatorList')
						->relationship('assignments')
						->addable(fn () => Auth::user()->hasAnyRole(['Super Admin', 'admin']))
						->deletable(fn () => Auth::user()->hasAnyRole(['Super Admin', 'admin']))
						->addActionLabel('Tambah Verifikator')
						->reorderable()
						->headers([
							Header::make('Petugas Verifikasi'),
						])
						->schema([
							Placeholder::make('Verifikator')
								->hiddenLabel()
								->visible(fn () => Auth::user()->hasRole('verifikator'))
								->content(fn ($record) => $record->user->name),
							Select::make('user_id')
								// ->inlineLabel()
								->visible(fn () => Auth::user()->hasAnyRole(['Super Admin', 'admin']))
								->label('Verifikator')
								->preload()
								->required()
								->options([
									User::verifikator()
										->get()
										->pluck('name', 'id')
										->toArray(),
								]),

							Hidden::make('no_ijin')->default(fn ($get)=>$get('../../no_ijin')),
							Hidden::make('kode_pengajuan')->default(fn ($get)=>$get('../../no_pengajuan')),
						])
				]),

        ]);
    }

	protected function handleRecordUpdate(Model $record, array $data): Model
	{
		if($record->status === '1'){
			$record->update(['status' => '2']);
		}
		return $record;
	}
}
