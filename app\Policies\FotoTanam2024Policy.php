<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\FotoTanam2024;
use App\Models\User;

class FotoTanam2024Policy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any FotoTanam2024');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, FotoTanam2024 $fototanam2024): bool
    {
        return $user->checkPermissionTo('view FotoTanam2024');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create FotoTanam2024');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, FotoTanam2024 $fototanam2024): bool
    {
        return $user->checkPermissionTo('update FotoTanam2024');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, FotoTanam2024 $fototanam2024): bool
    {
        return $user->checkPermissionTo('delete FotoTanam2024');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any FotoTanam2024');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, FotoTanam2024 $fototanam2024): bool
    {
        return $user->checkPermissionTo('restore FotoTanam2024');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any FotoTanam2024');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, FotoTanam2024 $fototanam2024): bool
    {
        return $user->checkPermissionTo('replicate FotoTanam2024');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder FotoTanam2024');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, FotoTanam2024 $fototanam2024): bool
    {
        return $user->checkPermissionTo('force-delete FotoTanam2024');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any FotoTanam2024');
    }
}
