<?php

namespace App\Filament\Panel2025\Resources\Realisasi2025Resource\Pages;

use App\Filament\Panel2025\Resources\Realisasi2025Resource;
use App\Models\Pks2025;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Forms\Components\{DatePicker, Fieldset, FileUpload, Grid, Group, Hidden, Placeholder, Radio, Repeater, Section, Select, Textarea, TextInput};
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class LaporanKegiatan extends EditRecord
{
	protected static string $resource = Realisasi2025Resource::class;
	public static string | Alignment $formActionsAlignment = Alignment::Right;

	protected static ?string $title = 'Pengisian Data Realisasi';

	public function getHeading(): string
	{
		return 'Laporan Realisasi Komitmen Tanam dan Produksi';
	}

	public function getSubheading(): ?string
	{
		$petak = $this->record->kode_spatial ? $this->record->kode_spatial : '##';
		$noIjin = $this->record ? $this->record->no_ijin : '##';
		return 'di petak: '. $petak.' / PPRK No: ' . $noIjin;
	}

	protected function getHeaderActions(): array
	{
		return [
			// Actions\ViewAction::make()->label('Lihat Peta')->icon('icon-geo-alt-fill')->color('info'),
			// Actions\DeleteAction::make(),
			Action::make('reporting')
				->hidden()
				->icon('icon-geo-alt-fill')
				->color('info')
				->label('Lihat Peta Lokasi')
				->url(fn ($record) => route('panel.2025.report.singleMap', $record->id) ),
		];
	}

	protected function getFormActions(): array
	{
		$record = $this->getRecord();

		// Pastikan record ada
		if (!$record) {
			return [];
		}

		// Ambil daftar kode_poktan yang memiliki status_dinas = 2
		$kodePoktan = Pks2025::where('no_ijin', $record->no_ijin)
			->where('status_dinas', 2)
			->pluck('kode_poktan')
			->unique()
			->toArray();

		// Cek apakah kode_poktan dari record ada dalam daftar kodePoktan
		if (!in_array($record->kode_poktan, $kodePoktan)) {
			return []; // Jika tidak ada, sembunyikan tombol
		}

		// Jika ada, tampilkan tombol Simpan dan Batal
		return [
			$this->getSaveFormAction()->label('Simpan untuk Pelaporan'),
			$this->getCancelFormAction(),
		];
	}

	// Tambahkan metode untuk menangani event Livewire
	public function refreshMap(): void
	{
		// Metode ini akan dipanggil oleh Livewire setelah repeater diperbarui
		// Emit event untuk me-refresh peta
		$record = $this->getRecord();
		if ($record && $record->spatial) {
			$spatialData = $record->spatial->toArray();
			if (!isset($spatialData['id'])) {
				$spatialData['id'] = $record->id;
			}
			$this->dispatch('refreshMap', $spatialData);
		}
	}

	public function form(Form $form): Form
	{

		return $form
            ->schema(
				function () {
					$record = $this->getRecord();
					if (!$record) {
						return [];
					}
					$kodePoktan = Pks2025::where('no_ijin', $record->no_ijin)
						->where('status_dinas', 2)
						->pluck('kode_poktan')
						->unique()
						->toArray();
					if (!in_array($record->kode_poktan, $kodePoktan)) {
						return [
							Placeholder::make('Restriction')
								->hiddenLabel()
								->columnSpanFull()
								->extraAttributes(['class'=>'text-center uppercase'])
								->content(new HtmlString('Anda belum dapat melakukan pelaporan jika <span class="text-danger-500 font-bold uppercase">Belum Ada PKS yang disetujui</span>'))
						];
					}
				return [
					Section::make('Peta Lokasi')
						->aside()
						->schema([
							Placeholder::make('map')
								->hiddenLabel()
								->columnSpan([
									'sm' => '3',
									'md' => '2',
								])
								->extraAttributes(['class' => 'map-container'])
								->content(function ($record) {
									// Pastikan data spatial memiliki ID untuk identifikasi unik
									$spatialData = $record->spatial->toArray();
									// Tambahkan ID jika belum ada
									if (!isset($spatialData['id'])) {
										$spatialData['id'] = $record->id;
									}
									return view('components.map', ['data' => $spatialData]);
								}),
						]),

					Section::make('Data Peta')
						->aside()
						->schema([
							Group::make()
								->extraAttributes(['class'=>'mb-5'])
								->schema([
									Placeholder::make('kode_lahan')
										->label('Kode Spatial')
										->inlineLabel()
										->content(fn ($record) => new HtmlString('<span class="font-bold">' . $record->kode_spatial . '</span>')),

									Placeholder::make('Luas Lahan')
										->inlineLabel()
										->content(fn ($record)=>number_format($record->luas_lahan,0,',','.').' m2'),

									Placeholder::make('Nama Petani')
										->inlineLabel()
										->content(fn ($record)=>$record->anggota->nama_petani),

									Placeholder::make('NIK Petani')
										->label('NIK')
										->inlineLabel()
										->content(fn ($record)=>$record->anggota->ktp_petani),

									Placeholder::make('Kelompok Tani')
										->inlineLabel()
										->content(fn ($record)=>$record->poktan->nama_kelompok),
								]),
							Group::make()
								->extraAttributes(['class'=>'mb-5'])
								->schema([
									Placeholder::make('Wilayah')
										->inlineLabel()
										->content(fn ($record)=>$record->spatial->kabupaten->nama_kab . ' - ' .$record->spatial->provinsi->nama),

									Placeholder::make('Latitude')
										->inlineLabel()
										->content(fn ($record)=>$record->spatial->latitude . ' LU'),

									Placeholder::make('Longitude')
										->inlineLabel()
										->content(fn ($record)=>$record->spatial->longitude . ' BT'),
								]),

							Group::make()
								->extraAttributes(['class'=>'mb-5'])
								->schema([
									Placeholder::make('Unduh Peta')
										->inlineLabel()
										->content(function ($record) {
											if ($record->spatial && $record->spatial->kml_url) {
												// Gunakan asset() untuk mendapatkan URL yang benar
												$url = asset('storage/' . $record->spatial->kml_url);
												return new HtmlString('<a href="'.$url.'" rel="noreferer nofollow" download><span class="font-bold text-info-500">'.$record->kode_spatial.'</span></a>');
											}
											return new HtmlString('<span class="text-gray-500">File tidak tersedia</span>');
										}),
								]),
							// Placeholder::make('vt_status')
							// 	->hiddenLabel()
							// 	->content(fn ($get) => view('components.status-badge-verifikasi', ['status' => $get('vt_status')])),
						]),

					Section::make('Riwayat Kegiatan')
						->aside()
						->schema([
							Repeater::make('Kegiatan di Lahan')
								->hiddenLabel()
								->relationship('detailrealisasi')
								->reorderable(false)
								->collapsible()
								->collapsed()
								->addable(Auth::user()->hasAnyRole(['importir']))
								->deletable(Auth::user()->hasAnyRole(['importir']))
								// Tambahkan event listener untuk refresh peta saat repeater berubah
								->afterStateUpdated(function () {
									$this->refreshMap();
								})
								->itemLabel(fn (array $state): ?string => match ($state['jenis_keg'] ?? null) {
									'lahan' => 'Persiapan Lahan',
									'benih' => 'Persiapan Benih',
									'mulsa' => 'Pemasangan Mulsa',
									'tanam' => 'Pertanaman',
									'pupuk' => 'Pemupukan',
									'panen' => 'Panen/Produksi',
									'distribusi' => 'Distribusi Hasil',
									'opt' => 'Pengendalian OPT',
									default => '',
								})
								->schema([
									Hidden::make('no_ijin')
										->default(fn ($get) => $get('../../no_ijin')),
									Hidden::make('npwp')
										->default(fn ($get) => $get('../../npwp')),
									Hidden::make('kode_spatial')
										->default(fn ($get) => $get('../../kode_spatial')),
									Hidden::make('desc_keg'),

									Select::make('jenis_keg')
										->label('Jenis Kegiatan')
										->inlineLabel()
										->columnSpanFull()
										->reactive()
										->options([
											'Persiapan' => [
												'lahan' => 'Persiapan Lahan',
												'benih' => 'Persiapan Benih',
												'mulsa' => 'Pemasangan Mulsa',
											],
											'tanam' => 'Pertanaman',
											'pupuk' => 'Pemupukan',
											'Produksi' => [
												'panen' => 'Panen',
												'distribusi' => 'Distribusi Hasil'
											],
											'opt' => 'Pengendalian OPT'
										])
										->afterStateUpdated(function ($set, $state){
											$set('value', null);
											if($state === 'lahan'){
												return $set('desc_keg', 'Persiapan Lahan');
											}
											if($state === 'benih'){
												return $set('desc_keg', 'Persiapan Benih');
											}
											if($state === 'mulsa'){
												return $set('desc_keg', 'Pemasangan Mulsa');
											}
											if($state === 'tanam'){
												return $set('desc_keg', 'Kegiatan Pertanaman');
											}
											if($state === 'pupuk'){
												return $set('desc_keg', 'Kegiatan Pemupukan');
											}
											if($state === 'panen'){
												return $set('desc_keg', 'Kegiatan Panen-Produksi');
											}
											if($state === 'distribusi'){
												return $set('desc_keg', 'Distribusi Hasil');
											}
											if($state === 'opt'){
												return $set('desc_keg', 'Pengendalian OPT');
											}
											return 'Tidak ada';
										}),
										Group::make()
											->columnSpanFull()
											->columns(3)
											->schema([
												FileUpload::make('file_url')
													->openable()
													->hiddenLabel()
													// ->required()
													->maxSize(2048)
													->columnSpan(1)
													->downloadable()
													->deletable()
													->label('Bukti Foto/Berkas Kegiatan')
													->visibility('public')
													->panelAspectRatio('1:1')
													->fetchFileInformation(false)
													->helperText('Maksimal 2MB, format PDF')
													->removeUploadedFileButtonPosition('right')
													->uploadButtonPosition('right')
													->disk('public')
													->directory(function ($get) {
														$npwp = $get('../../npwp');
														$noIjin = $get('../../no_ijin');
														$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $npwp);
														$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $noIjin);
														return "uploads/{$cleanNpwp}/{$cleanNoIjin}/dokumen/pks/foto/";
													})
													->rules([
														'file',
														'mimetypes:image/jpeg,image/png,application/pdf',
														// 'mimes:jpg,jpeg,png,pdf',
														'max:2048',
													])
													->validationMessages([
														'mimetypes' => 'Hanya file gambar (JPG, JPEG, PNG) atau dokumen (PDF) yang diperbolehkan',
														'mimes' => 'Ekstensi file harus .jpg, .jpeg, pdf, atau .png',
														'max' => 'Ukuran file maksimal 2MB',
													])
													->getUploadedFileNameForStorageUsing(
														function (TemporaryUploadedFile $file, $get): string {
															$npwp = $get('../../npwp');
															$noIjin = $get('../../no_ijin');
															$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $npwp);
															$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $noIjin);

															// Format nama file: [prefix]_[id]_[NPWP]_[NOIJIN].[ext]
															$filePrefix = match ($get('jenis_keg')) {
																'lahan' => 'persiapanlahan',
																'benih' => 'persiapanbenih',
																'mulsa' => 'pemasanganmulsa',
																'tanam' => 'pertanaman',
																'pupuk' => 'pemupukan',
																'panen' => 'panen',
																'distribusi' => 'distribusi',
																'opt' => 'opt',
																default => 'none',
															};

															return $filePrefix . '_' . $cleanNpwp . '_' . $cleanNoIjin . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
														}
													),

												Fieldset::make('Data')
													->columnSpan(2)
													->columns(1)
													->schema([
														DatePicker::make('tgl_keg')
															->inlineLabel()
															->label('Tanggal Kegiatan'),

														TextInput::make('value')
															->inlineLabel()
															->live(onBlur:true)
															->afterStateUpdated(function ($state, $get, $set) {
																if ($get('jenis_keg') === 'tanam') {
																	$luasLahan = $get('../../luas_lahan');
																	if ($state > $luasLahan) {
																		$set('value', null);
																		$set('is_exceeding', true);
																	} else {
																		$set('is_exceeding', false);
																	}
																}
															})
															->label(function ($get) {
																switch ($get('jenis_keg')) {
																	case 'benih':
																		return 'Volume Benih';
																	case 'mulsa':
																		return 'Pemasangan Mulsa';
																	case 'tanam':
																		return 'Luas Tanam';
																	case 'panen':
																		return 'Produksi';
																	default:
																		return '';
																}
															})
															->helperText(function ($get) {
																if ($get('jenis_keg') === 'tanam') {
																	$luasLahan = $get('../../luas_lahan');
																	if ($get('is_exceeding')) {
																		return new HtmlString("<span class='text-danger-500'>Luas tanam tidak mungkin melebihi luas lahan (" . number_format($luasLahan,0,',','.') . " m2)</span>");
																	}
																}
																return null;
															})
															->visible(function ($get) {
																switch ($get('jenis_keg')) {
																	case 'benih':
																		return true;
																	case 'mulsa':
																		return true;
																	case 'tanam':
																		return true;
																	case 'panen':
																		return true;
																	default:
																		return false;
																}
															})
															->suffix(function ($get) {
																switch ($get('jenis_keg')) {
																	case 'mulsa':
																		return 'roll';
																	case 'tanam':
																		return 'm2';
																	default:
																		return 'kg';
																}
															})
															->numeric()
															->maxValue(function ($get) {
																if ($get('jenis_keg') === 'tanam') {
																	// Ambil nilai luas_lahan dari record atau state
																	return $get('../../luas_lahan');
																}
																return null; // Tidak ada batasan untuk jenis kegiatan lain
															})
															->minValue(0),

														Group::make()
															->visible(function ($get) {
																switch ($get('jenis_keg')) {
																	case 'distribusi':
																		return true;
																	default:
																		return false;
																}
															})
															->schema([
																TextInput::make('dist_benih')
																	->label('Disimpan')
																	->inlineLabel()
																	->suffix('kg'),
																TextInput::make('dist_jual')
																	->label('Dijual')
																	->inlineLabel()
																	->suffix('kg')
															]),

														Group::make()
															->visible(function ($get) {
																switch ($get('jenis_keg')) {
																	case 'pupuk':
																		return true;
																	default:
																		return false;
																}
															})
															->schema([
																TextInput::make('organik')
																	->inlineLabel()
																	->label('Pupuk Organik')
																	->suffix('kg'),
																TextInput::make('dolomit')
																	->inlineLabel()
																	->label('Kapur Dolomit')
																	->suffix('kg'),
																TextInput::make('npk')
																	->inlineLabel()
																	->label('Pupuk NPK')
																	->suffix('kg'),
																TextInput::make('za')
																	->inlineLabel()
																	->label('Pupuk ZA')
																	->suffix('kg'),
															]),
														Textarea::make('keg_note')
															->label('Catatan Kegiatan')
															->autosize()
													])
											]),
								])->addActionLabel('Tambah Kegiatan Lainnya'),
						]),
				];
			}
		);
	}
}
