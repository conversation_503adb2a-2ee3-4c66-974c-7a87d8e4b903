<?php

namespace App\Filament\Panel2025\Resources\Pks2025Resource\Pages;

use App\Filament\Panel2025\Resources\Pks2025Resource;
use App\Models\Pks2025;
use BladeUI\Icons\Components\Icon;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\ColumnGroup;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class VerifPks2025 extends ListRecords
{
    protected static string $resource = Pks2025Resource::class;
	protected static ?string $title = 'Verifikasi PKS';
    public function getHeading(): string
	{
        return 'Verifikasi PKS';
	}

    public function getSubheading(): ?string
    {
        $routeNoIjin = request()->route('noijin');
		$noIjin = substr($routeNoIjin, 0, 4) . '/' .
			substr($routeNoIjin, 4, 2) . '.' .
			substr($routeNoIjin, 6, 3) . '/' .
			substr($routeNoIjin, 9, 1) . '/' .
			substr($routeNoIjin, 10, 2) . '/' .
			substr($routeNoIjin, 12, 4);

        return 'PPRK No: ' . $noIjin;
    }

	public function getTableQuery(): Builder
    {
        $routeNoIjin = request()->route('noijin');
		$noIjin = substr($routeNoIjin, 0, 4) . '/' .
			substr($routeNoIjin, 4, 2) . '.' .
			substr($routeNoIjin, 6, 3) . '/' .
			substr($routeNoIjin, 9, 1) . '/' .
			substr($routeNoIjin, 10, 2) . '/' .
			substr($routeNoIjin, 12, 4);

		return Pks2025::where('no_ijin', $noIjin);
    }

	public function table(Table $table): Table
	{
		return $table
			->columns([
				TextColumn::make('index')
                    ->label('No')
					->rowIndex(),
				TextColumn::make('nama_poktan')
					->searchable(),
				TextColumn::make('no_perjanjian')
					->label('Nomor')
					->searchable(),
				TextColumn::make('kecamatan.nama_kecamatan')
					->searchable(),
				TextColumn::make('desa.nama_desa')
					->searchable(),
				TextColumn::make('berkas_pks')
					->alignCenter()
					->extraAttributes(['class'=>'text-center'])
					->formatStateUsing(fn ($record) => new HtmlString(
						'<a class="font-bold text-info-500 text-center" href="' . asset($record->berkas_pks) . '" target="_blank" rel="nofollow noreferrer" download>Unduh</a>'
					)),
				TextColumn::make('status')
					->searchable(),
				TextColumn::make('verifikator.name'),
				TextColumn::make('verif_at')
					->date(),
			])
			->filters([
				//
			])
			->actions([
				ViewAction::make()
					->hiddenLabel()
					->tooltip('Verifikasi PKS ini')
					->color(function ($record){
						if ($record->status === 'Sesuai') {
							return 'success';
						}
						return 'danger';
					})
                    ->icon(function ($record){
						if ($record->status === 'Sesuai') {
							return 'icon-bookmark-check-fill';
						}
						if ($record->status === 'Tidak Sesuai') {
							return 'icon-bookmark-check-fill';
						}
						return 'icon-bookmark';
					})
					->url(fn ($record) => route('filament.panel2025.resources.pks2025s.verifpks', $record->id)),
			])
			->bulkActions([
				BulkActionGroup::make([
					// DeleteBulkAction::make(),
				]),
			]);
	}

	public function getPages(): array
	{
		return [
		];
	}
}
