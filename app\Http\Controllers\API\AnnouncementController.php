<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Announcement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AnnouncementController extends Controller
{
    /**
     * Get announcements for the authenticated user
     * 
     * Requirements:
     * 1. Only active announcements
     * 2. Only for the user's role
     * 3. Only unread announcements, unless all are read
     * 4. Maximum 3 latest posts
     * 5. Prioritize by priority (higher first)
     * 6. If all read, show 3 latest posts
     */
    public function getAnnouncements(Request $request)
    {
        $user = $request->user();
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated'
            ], 401);
        }
        
        // Get user's role
        $userRole = $user->roles->first();
        
        if (!$userRole) {
            return response()->json([
                'success' => false,
                'message' => 'User has no assigned role'
            ], 400);
        }
        
        // Get announcements for the user's role that are active
        $query = Announcement::where('is_active', true)
            ->where('role_id', $userRole->id);
        
        // Check if there are unread announcements
        $unreadQuery = clone $query;
        $unreadAnnouncements = $unreadQuery->where(function($q) use ($user) {
                $q->whereJsonDoesntContain('read_by', $user->id)
                  ->orWhereNull('read_by');
            })
            ->orderByRaw("CASE 
                WHEN priority = 'Kritis' THEN 1 
                WHEN priority = 'Sangat Penting' THEN 2 
                WHEN priority = 'Penting' THEN 3 
                WHEN priority = 'Biasa' THEN 4 
                ELSE 5 END")
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get();
        
        // If there are unread announcements, return them
        if ($unreadAnnouncements->count() > 0) {
            return response()->json([
                'success' => true,
                'data' => $unreadAnnouncements,
                'message' => 'Unread announcements retrieved successfully'
            ]);
        }
        
        // If all announcements are read, return the 3 latest
        $latestAnnouncements = $query
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get();
        
        return response()->json([
            'success' => true,
            'data' => $latestAnnouncements,
            'message' => 'Latest announcements retrieved successfully'
        ]);
    }

    public function getAllAnnouncements(Request $request)
    {
        $user = $request->user();
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated'
            ], 401);
        }
        
        // Get user's role
        $userRole = $user->roles->first();
        
        if (!$userRole) {
            return response()->json([
                'success' => false,
                'message' => 'User has no assigned role'
            ], 400);
        }
        
        // Get announcements for the user's role that are active
        $query = Announcement::where('is_active', true)
            ->where('role_id', $userRole->id);
        
        // Check if there are unread announcements
        $unreadQuery = clone $query;
        $unreadAnnouncements = $unreadQuery->where(function($q) use ($user) {
                $q->whereJsonDoesntContain('read_by', $user->id)
                  ->orWhereNull('read_by');
            })
            ->orderByRaw("CASE 
                WHEN priority = 'Kritis' THEN 1 
                WHEN priority = 'Sangat Penting' THEN 2 
                WHEN priority = 'Penting' THEN 3 
                WHEN priority = 'Biasa' THEN 4 
                ELSE 5 END")
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get();
        
        // If there are unread announcements, return them
        if ($unreadAnnouncements->count() > 0) {
            return response()->json([
                'success' => true,
                'data' => $unreadAnnouncements,
                'message' => 'Unread announcements retrieved successfully'
            ]);
        }
        
        // If all announcements are read, return the 3 latest
        $latestAnnouncements = $query
            ->orderBy('created_at', 'desc')
            ->get();
        
        return response()->json([
            'success' => true,
            'data' => $latestAnnouncements,
            'message' => 'Latest announcements retrieved successfully'
        ]);
    }
    
    /**
     * Mark an announcement as read
     */
    public function markAsRead(Request $request, $id)
    {
        $user = $request->user();
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated'
            ], 401);
        }
        
        $announcement = Announcement::find($id);
        
        if (!$announcement) {
            return response()->json([
                'success' => false,
                'message' => 'Announcement not found'
            ], 404);
        }
        
        // Check if the announcement is for the user's role
        $userRole = $user->roles->first();
        
        if ($announcement->role_id != $userRole->id) {
            return response()->json([
                'success' => false,
                'message' => 'This announcement is not for your role'
            ], 403);
        }
        
        // Mark as read
        $readBy = $announcement->read_by ?? [];
        
        if (!in_array($user->id, $readBy)) {
            $readBy[] = $user->id;
            $announcement->update(['read_by' => $readBy]);
        }
        
        return response()->json([
            'success' => true,
            'message' => 'Announcement marked as read'
        ]);
    }
}
