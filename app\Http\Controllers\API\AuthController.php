<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
	/**
	 *
	 * Get Token
	 * @OA\Post(
	 *      path="/getToken",
	 *      operationId="getToken",
	 *      tags={"Get Access Token"},
	 *      summary="Post your username and password to get token. Use the token in the 'Authorization' header like so 'Bearer YOUR_TOKEN'",
	 *      description="get token for Access API",
	 *      @OA\RequestBody(
	 *          @OA\MediaType(
	 *          mediaType="multipart/form-data",
	 *              @OA\Schema(
	 *                  type="object",
	 *                  @OA\Property(property="username", type="string", example="user"),
	 *                  @OA\Property(property="password", type="string", example="mypass")
	 *              )
	 *          )
	 *      ),
	 *      @OA\Response(
	 *          response=200,
	 *          description="OK",
	 *          @OA\JsonContent()
	 *
	 *      ),
	 *      @OA\Response(
	 *          response=422,
	 *          description="The provided credentials are incorrect."
	 *      ),
	 *  )
	 */
	
	public function login(Request $request)
	{
		$request->validate([
			'email' => 'required|email',
			'password' => 'required',
			'device_name' => 'required',
		]);

		$user = User::where('email', $request->email)->first();

		if (! $user || ! Hash::check($request->password, $user->password)) {
			throw ValidationException::withMessages([
				'email' => ['The provided credentials are incorrect.'],
			]);
		}

		// Hapus token lama untuk device yang sama (opsional)
		$user->tokens()->where('name', $request->device_name)->delete();

		// Buat token baru
		$token = $user->createToken($request->device_name)->plainTextToken;

		return response()->json([
			'token' => $token
		]);
	}

	public function logout(Request $request)
	{
		// Hapus token yang digunakan untuk request ini
		$request->user()->tokens()->where('id', $request->user()->currentAccessToken()->id)->delete();

		return response()->json(['message' => 'Logged out successfully']);
	}

	public function getToken(Request $request)
	{
		$request->validate([
			'username' => 'required',
			'password' => 'required'
		]);

		$credentials = [
			'username' => $request->username,
			'password' => $request->password
		];
		try {
			// Log credentials for debugging (remove in production)
			\Illuminate\Support\Facades\Log::info('Login attempt', ['username' => $request->username]);

			// Specify the guard explicitly
			if (!Auth::guard('web')->attempt($credentials)) {
				return response()->json([
					'message' => 'The given data was invalid.',
					'errors' => [
						'password' => [
							'Invalid credentials'
						],
					]
				], 422);
			}
		} catch (\Exception $e) {
			// Log the exception
			\Illuminate\Support\Facades\Log::error('Authentication error', [
				'error' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);

			return response()->json([
				'message' => 'Authentication error',
				'error' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			], 500);
		}

		$user = User::where('username', $request->username)->first();
		// Hapus token lama jika ada
		$user->tokens()->delete();
		// Buat token baru tanpa expiration
		$authToken = $user->createToken('auth-token', ['*'])->plainTextToken;

		return response()->json([
			'access_token' => $authToken,
		]);
	}
}
