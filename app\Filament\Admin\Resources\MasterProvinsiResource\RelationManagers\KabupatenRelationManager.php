<?php

namespace App\Filament\Admin\Resources\MasterProvinsiResource\RelationManagers;

use App\Models\MasterKabupaten;
use Filament\Actions\ViewAction;
use Filament\Forms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class KabupatenRelationManager extends RelationManager
{
    protected static string $relationship = 'kabupaten';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('nama_kab')
                    ->required()
                    ->maxLength(255),
            ]);
    }

	// protected function getTableQuery(): Builder
	// {
	// 	return MasterKabupaten::query()
	// 		->where('status', '!=', 'nonaktif');
	// }

    public function table(Table $table): Table
    {
        return $table
			->recordTitleAttribute('kabupaten_id')
			->modifyQueryUsing(fn (Builder $query) => $query->whereIn('status', [null, 'aktif']))
            ->columns([
                TextColumn::make('kabupaten_id')->searchable(),
                TextColumn::make('nama_kab')->searchable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
				Action::make('syncWilayah')
					->label('Sync Wilayah')
					->hidden()
					->color('success')
					->requiresConfirmation()
					->icon('heroicon-o-arrow-path')
					->iconButton()
					->modalHeading('Konfirmasi Sinkronisasi Data Wilayah')
					->modalDescription('Tindakan ini akan menjalankan proses sinkronisasi data wilayah di latar belakang. Anda dapat meninggalkan halaman ini setelah menekan tombol Lanjutkan, dan proses akan terus berjalan hingga selesai.')
					->modalSubmitActionLabel('Lanjutkan')
					->action(function ($record) {
						ini_set('max_execution_time', 300); // 5 menit
						self::FetchWilayahJob($record);
						self::ProcessWilayahJob($record);
						Notification::make()
							->title('Proses Sinkronisasi Selesai. Refresh/Reload halaman jika diperlukan')
							->success()
							->send();
					}),
				Tables\Actions\ViewAction::make()
					->iconButton()
					->url(fn ($record) => route('filament.admin.resources.master-kabupatens.view', $record->id)),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ]);
    }

	public function FetchWilayahJob($record)
    {
		$provinsiId = $record->provinsi_id;
		$kabupatenUrl = "https://sig.bps.go.id/rest-bridging/getwilayah?level=kabupaten&parent={$provinsiId}&periode_merge=2024_1.2022";
		$kabupaten = Http::get($kabupatenUrl)->json();
		Storage::disk('local')->put("sig-wilayah/kabupaten_{$p['kode']}.json", json_encode($kabupaten));
    }
}
