<?php

namespace App\Filament\Admin\Resources\CompletedResource\Pages;

use App\Filament\Admin\Resources\CompletedResource;
use App\Models\Completed;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class ListCompleteds extends ListRecords
{
    protected static string $resource = CompletedResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }

	public function getTableQuery(): Builder
    {
		if(Auth::user()->hasAnyRole(['importir'])){
			return Completed::where('npwp', Auth::user()->npwp);
		}
		return Completed::query();
    }
}
