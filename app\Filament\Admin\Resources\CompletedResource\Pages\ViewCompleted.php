<?php

namespace App\Filament\Admin\Resources\CompletedResource\Pages;

use App\Filament\Admin\Resources\CompletedResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewCompleted extends ViewRecord
{
    protected static string $resource = CompletedResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\EditAction::make(),
        ];
    }

	protected static ?string $title = 'Surat Keterangan Lunas';


    public function getHeading(): string
	{
        return 'Surat Keterangan Lunas';
	}

    public function getSubheading(): ?string
    {
        $noSkl = $this->record ? $this->record->no_skl : '##';
        return 'Nomor: ' . $noSkl;
    }
}
