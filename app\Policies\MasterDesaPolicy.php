<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\MasterDesa;
use App\Models\User;

class MasterDesaPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any MasterDesa');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, MasterDesa $masterdesa): bool
    {
        return $user->checkPermissionTo('view MasterDesa');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create MasterDesa');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, MasterDesa $masterdesa): bool
    {
        return $user->checkPermissionTo('update MasterDesa');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, MasterDesa $masterdesa): bool
    {
        return $user->checkPermissionTo('delete MasterDesa');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any MasterDesa');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, MasterDesa $masterdesa): bool
    {
        return $user->checkPermissionTo('restore MasterDesa');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any MasterDesa');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, MasterDesa $masterdesa): bool
    {
        return $user->checkPermissionTo('replicate MasterDesa');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder MasterDesa');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, MasterDesa $masterdesa): bool
    {
        return $user->checkPermissionTo('force-delete MasterDesa');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any MasterDesa');
    }
}
