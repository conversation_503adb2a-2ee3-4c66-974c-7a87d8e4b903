<?php

namespace App\Models;

use App\Observers\Commitment2024Observer;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Commitment2024 extends Model
{
	use HasFactory;
	use SoftDeletes, LogsActivity;
	public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
			->logFillable('*')
			->logOnlyDirty();
    }

	/**
     * Mutator untuk memastikan no_ijin selalu dalam format yang benar
     */
    public function setNoIjinAttribute($value)
    {
        // Hapus spasi di awal dan akhir
        $this->attributes['no_ijin'] = trim($value);
    }

	public $table = 'pull_riphs';

	protected $fillable = [
		'user_id',
		'npwp',
		'keterangan',
		'nama',
		'no_ijin',
		'periodetahun',
		'tgl_ijin',
		'tgl_akhir',
		'no_hs',
		'volume_riph',
		'volume_produksi',
		'luas_wajib_tanam',
		'stok_mandiri',
		'pupuk_organik',
		'npk',
		'dolomit',
		'za',
		'mulsa',
		'status',
		'formRiph',
		'formSptjm',
		'logBook',
		'formRt',
		'formRta',
		'formRpo',
		'formLa',
		'no_doc',
		'poktan_share',
		'importir_share',
		'status',
		'skl',
		'datariph'
	];

	protected $dates = [
		'created_at',
		'updated_at',
		'deleted_at',
	];

	protected static function booted()
	{
		static::addGlobalScope('npwp', function (Builder $builder) {
			if (Auth::check()) {
				$user = Auth::user();

				if ($user->hasAnyRole(['admin', 'direktur', 'Super Admin', 'verifikator'])) {
				}
				else {
					$builder->where('npwp', $user->npwp);
				}
			}
		});
	}

	public function user()
	{
		return $this->belongsTo(User::class);
	}

	public function datauser()
	{
		return $this->belongsTo(DataUser::class, 'npwp', 'npwp_company');
	}

	public function userDocs()
	{
		return $this->hasOne(UserDocs2024::class, 'no_ijin', 'no_ijin');
	}

	public function penangkar_riph()
	{
		return $this->hasMany(PenangkarRiph2024::class, 'no_ijin', 'no_ijin');
	}

	public function poktan()
	{
		return $this->hasMany(MasterPoktan2024::class, 'npwp', 'npwp');
	}

	public function anggota()
	{
		return $this->hasMany(MasterAnggota2024::class, 'npwp', 'npwp');
	}

	public function pks()
	{
		return $this->hasMany(Pks2024::class, 'no_ijin', 'no_ijin');
	}

	public function lokasi()
	{
		return $this->hasMany(Lokasi2024::class, 'no_ijin', 'no_ijin');
	}

	public function datarealisasi()
	{
		return $this->hasMany(DataRealisasi2024::class, 'no_ijin', 'no_ijin');
	}

	public function ajutanam()
	{
		return $this->hasOne(AjuVerifTanam2024::class, 'no_ijin', 'no_ijin');
	}

	public function ajuproduksi()
	{
		return $this->hasOne(AjuVerifProduksi2024::class, 'no_ijin', 'no_ijin');
	}

	public function ajuskl()
	{
		return $this->hasOne(AjuVerifSkl2024::class, 'no_ijin', 'no_ijin');
	}

	public function skl()
	{
		// return $this->hasOne(Skl::class, 'no_ijin', 'no_ijin');
	}

	public function completed()
	{
		return $this->hasOne(Completed::class, 'no_ijin', 'no_ijin');
	}
}
