<?php

namespace App\Filament\Panel2024\Resources\DataRealisasi2024Resource\Pages;

use App\Filament\Panel2024\Resources\DataRealisasi2024Resource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;

class EditDataRealisasi2024 extends EditRecord
{
    protected static string $resource = DataRealisasi2024Resource::class;

    protected static ?string $title = 'Foto Tanam';
    public function getHeading(): string
	{
        // $noPks = $this->record ? $this->record->masterpoktan->nama_kelompok : '#';
        return 'Foto Tanam';
	}
    public function getSubheading(): ?string
    {
        $realisasi = $this->record ? $this->record->nama_lokasi. ' / ' .  $this->record->no_ijin : '##';
        return 'Lokasi: ' . $realisasi;
    }

    public static string | Alignment $formActionsAlignment = Alignment::Right;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\ViewAction::make(),
            // Actions\DeleteAction::make(),
        ];
    }
}
