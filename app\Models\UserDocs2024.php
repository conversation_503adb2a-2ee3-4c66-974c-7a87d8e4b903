<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class UserDocs2024 extends Model
{
	use HasFactory, LogsActivity;
	public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
			->logFillable('*')
			->logOnlyDirty();
    }

	public $table = 'user_docs';

	protected $fillable = [
		'npwp',
		'commitment_id',
		'no_ijin',

		//dokumen tanam
		'spvt', //surat pengajuan verifikasi tanam
		'sptjmtanam', //surat pertanggungjawaban mutlak
		'rta', //form realisasi tanam
		'sphtanam', //
		// 'spdst', //surat pengantar dinas telah selesai tanam
		'logbooktanam',

		//hasil periksa dok tanam
		'spvtcheck', //surat pengajuan verifikasi tanam
		'sptjmtanamcheck', //surat pertanggungjawaban mutlak
		'rtacheck', //form realisasi tanam
		'sphtanamcheck', //
		// 'spdstcheck', //surat pengantar dinas telah selesai tanam
		'logbooktanamcheck',
		'tanamcheck_by',
		'tanamverif_at',

		//dokumen produksi
		'spvp', //surat pengajuan verifikasi produksi
		'sptjmproduksi', //surat pertanggungjawaban mutlak
		'rpo', //realisasi produksi
		'sphproduksi', //sph produksi
		// 'spdsp', //surat pengantar dinas telah selesai produksi
		'logbookproduksi',
		'formLa',

		//hasil periksa dokumen produksi
		'spvpcheck', //surat pengajuan verifikasi produksi
		'sptjmproduksicheck',
		'rpocheck', //realisasi produksi
		'sphproduksicheck', //sph produksi
		// 'spdspcheck', //surat pengantar dinas telah selesai produksi
		'logbookproduksicheck',
		'formLacheck',

		'prodcheck_by',
		'prodverif_at',

		//DOKUMEN PENGAJUAN SKL
		// 'spskl', //surat pengajuan penerbitan skl
		// 'spsklcheck',
		// 'spsklcheck_by',
		// 'spsklverif_at',
	];

	protected static function booted()
	{
		static::addGlobalScope('npwp', function (Builder $builder) {
			if (Auth::check()) {
				$user = Auth::user();

				if ($user->hasAnyRole(['admin', 'direktur', 'Super Admin', 'verifikator'])) {
				}
				else {
					$builder->where('npwp', $user->npwp);
				}
			}
		});
	}

	public function commitment()
	{
		return $this->belongsTo(Commitment2024::class, 'no_ijin', 'no_ijin');
	}
}
