<x-filament-widgets::widget>
    <div wire:key="reply-form" class="space-y-4">
    @if(!$this->canReply())
        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                        Tidak dapat membalas
                    </h3>
                    <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                        <p>
                            @if($ticket->status === 'closed')
                                Tiket ini sudah ditutup. Tidak dapat menambahkan balasan baru.
                            @else
                                Anda tidak memiliki izin untuk membalas tiket ini.
                            @endif
                        </p>
                    </div>
                </div>
            </div>
        </div>
    @else
        <!-- Reply To Indicator -->
        @if($replyingTo)
            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                <div class="flex items-start justify-between">
                    <div class="flex items-start space-x-3">
                        <svg class="w-4 h-4 text-blue-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-blue-800 dark:text-blue-200">
                                Membalas pesan dari {{ $replyingTo->user->name }}
                            </p>
                            <p class="text-xs text-blue-600 dark:text-blue-300 mt-1">
                                {{ $replyingTo->created_at->format('d M Y H:i') }}
                            </p>
                        </div>
                    </div>
                    <button 
                        wire:click="clearReply"
                        class="text-blue-400 hover:text-blue-600 dark:hover:text-blue-300"
                    >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        @endif

        <!-- Reply Form -->
        <form wire:submit="sendReply" class="space-y-4">
            {{ $this->form }}
            
            <div class="flex items-center justify-between pt-4">
                <div class="text-xs text-gray-500 dark:text-gray-400">
                    <p>Tip: Gunakan @ untuk mention pengguna lain</p>
                </div>
                
                <div class="flex items-center space-x-3">
                    @if($replyingTo)
                        <button 
                            type="button"
                            wire:click="clearReply"
                            class="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
                        >
                            Batal Reply
                        </button>
                    @endif
                    
                    <button 
                        type="submit"
                        class="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        wire:loading.attr="disabled"
                    >
                        <span wire:loading.remove>
                            {{ $replyingTo ? 'Kirim Balasan' : 'Kirim Pesan' }}
                        </span>
                        <span wire:loading>
                            Mengirim...
                        </span>
                    </button>
                </div>
            </div>
        </form>
    @endif
    </div>
</x-filament-widgets::widget>
