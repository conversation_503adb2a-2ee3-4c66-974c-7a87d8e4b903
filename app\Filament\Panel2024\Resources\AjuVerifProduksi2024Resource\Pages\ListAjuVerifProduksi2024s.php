<?php

namespace App\Filament\Panel2024\Resources\AjuVerifProduksi2024Resource\Pages;

use App\Filament\Panel2024\Resources\AjuVerifProduksi2024Resource;
use App\Models\AjuVerifProduksi2024;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListAjuVerifProduksi2024s extends ListRecords
{
    protected static string $resource = AjuVerifProduksi2024Resource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }

	public function getTabs(): array
	{
		return [
			'Semua' => Tab::make('Semua'),
			'1' => Tab::make('Pengajuan/Baru')->modifyQueryUsing(function (Builder $query){
                $query->where('status', 1);
            })->badge(AjuVerifProduksi2024::query()->where('status', 1)->count()),
			'2' => Tab::make('Diperiksa')->modifyQueryUsing(function (Builder $query){
                $query->where('status', 2);
            })->badge(AjuVerifProduksi2024::query()->where('status', 2)->count()),
			'3' => Tab::make('Perbaikan')->modifyQueryUsing(function (Builder $query){
                $query->where('status', 3);
            })->badge(AjuVerifProduksi2024::query()->where('status', 3)->count()),
			'4' => Tab::make('Selesai')->modifyQueryUsing(function (Builder $query){
                $query->where('status', 4);
            })->badge(AjuVerifProduksi2024::query()->where('status', 4)->count()),
		];
	}
}
