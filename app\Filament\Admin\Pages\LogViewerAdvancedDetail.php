<?php

namespace App\Filament\Admin\Pages;

use Filament\Pages\Page;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Tabs;
use Filament\Infolists\Components\Tabs\Tab;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\HtmlString;

class LogViewerAdvancedDetail extends Page
{
    use InteractsWithInfolists;

    protected static bool $shouldRegisterNavigation = false;

    protected static string $view = 'filament.admin.pages.log-viewer-advanced-detail';
	protected static ?string $title = 'Detail Log';
    public static function getRouteName(?string $panel = null): string
    {
        return 'log-viewer-advanced.detail';
    }

    public array $log = [];
    public string $file;
    public string $hash;
    public string $rawContent = '';

    public function mount(string $hash, string $file): void
    {
        $this->file = $file;
        $this->hash = $hash;
        $this->log = $this->getLogByHash($hash, $file);
    }

    public static function canAccess(): bool
    {
        return Auth::user()?->hasAnyRole(['admin', 'Super Admin']);
    }

    protected function getLogByHash(string $hash, string $file): array
    {
        $path = storage_path('logs/' . $file);

        if (!File::exists($path)) {
            abort(404, 'Log file tidak ditemukan.');
        }

        // Membatasi ukuran file yang dibaca untuk menghindari masalah performa
        $fileSize = File::size($path);
        $maxSize = 1 * 1024 * 1024; // 1MB - Mengurangi ukuran maksimum untuk mencegah memory exhausted

        // Coba cari log dengan pendekatan chunking untuk file besar
        if ($fileSize > $maxSize) {
            // Bagi file menjadi beberapa chunk
            $chunkSize = $maxSize;
            $chunks = ceil($fileSize / $chunkSize);
            $maxChunks = 10; // Batasi jumlah chunk yang diproses

            // Coba cari di beberapa chunk terakhir terlebih dahulu (biasanya log terbaru)
            for ($i = 0; $i < min($chunks, $maxChunks); $i++) {
                $offset = max(0, $fileSize - ($i + 1) * $chunkSize);
                $length = min($chunkSize, $fileSize - $offset);

                // Baca chunk dari file
                $content = file_get_contents($path, false, null, $offset, $length);

                // Pastikan kita mulai dari baris log yang lengkap
                if ($offset > 0) {
                    $content = substr($content, strpos($content, '[') ?: 0);
                }

                // Cari log dengan hash yang sesuai
                $result = $this->findLogInContent($content, $hash);
                if ($result !== null) {
                    return $result;
                }

                // Bersihkan memori
                unset($content);
            }

            // Jika tidak ditemukan, kembalikan error
            abort(404, 'Log entry tidak ditemukan. File terlalu besar untuk diproses seluruhnya.');
        } else {
            // Untuk file kecil, baca seluruh file
            $content = file_get_contents($path);
            $result = $this->findLogInContent($content, $hash);

            if ($result !== null) {
                return $result;
            }

            abort(404, 'Log entry tidak ditemukan.');
        }
    }

    /**
     * Cari log dengan hash tertentu dalam konten
     */
    private function findLogInContent(string $content, string $hash): ?array
    {
        $pattern = '/\[(\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}\.?\d*(?:[\+-]\d{2}:\d{2})?)\]\s+(\w+)\.(\w+):\s+(.*?)(?=\[\d{4}-\d{2}-\d{2}|$)/s';

        preg_match_all($pattern, $content, $matches, PREG_SET_ORDER);

        foreach ($matches as $match) {
            $timestamp = $match[1] ?? '-';
            $env = $match[2] ?? 'local';
            $level = strtoupper($match[3] ?? 'INFO');
            $message = $match[4] ?? '';

            // Untuk hash, gunakan 100 karakter pertama dari pesan untuk mencocokkan dengan LogViewerAdvanced
            $currentHash = md5($timestamp . $env . $level . substr($message, 0, 100));

            if ($currentHash === $hash || md5($timestamp . $env . $level . $message) === $hash) {
                $this->rawContent = trim($message);

                return [
                    'timestamp' => $timestamp,
                    'env' => $env,
                    'level' => $level,
                    'message' => trim($message),
                    'hash' => $currentHash,
                ];
            }
        }

        return null;
    }

    public function getStackTrace(): array
    {
        $stackTrace = [];
        $pattern = '/Stack trace:(.*?)(?:\n\n|$)/s';

        // Batasi panjang rawContent untuk mencegah masalah performa
        $limitedContent = substr($this->rawContent, 0, 50000); // Batasi ke 50KB

        if (preg_match($pattern, $limitedContent, $matches)) {
            $traceContent = $matches[1];
            $lines = explode("\n", $traceContent);

            $maxLines = 100; // Batasi jumlah baris stack trace
            $count = 0;

            foreach ($lines as $line) {
                if ($count >= $maxLines) {
                    $stackTrace[] = '... [truncated, too many lines]';
                    break;
                }

                $line = trim($line);
                if (!empty($line)) {
                    // Batasi panjang setiap baris
                    if (strlen($line) > 500) {
                        $line = substr($line, 0, 500) . '... [truncated]';
                    }
                    $stackTrace[] = $line;
                    $count++;
                }
            }
        }

        return $stackTrace;
    }

    public function getExceptionMessage(): string
    {
        $pattern = '/(.*?)(?:Stack trace:|$)/s';

        // Batasi panjang rawContent untuk mencegah masalah performa
        $limitedContent = substr($this->rawContent, 0, 50000); // Batasi ke 50KB

        if (preg_match($pattern, $limitedContent, $matches)) {
            $message = trim($matches[1]);

            // Batasi panjang pesan
            if (strlen($message) > 5000) {
                $message = substr($message, 0, 5000) . '... [truncated, message too long]';
            }

            return $message;
        }

        // Jika tidak ada pola yang cocok, batasi panjang rawContent
        if (strlen($this->rawContent) > 5000) {
            return substr($this->rawContent, 0, 5000) . '... [truncated, message too long]';
        }

        return $this->rawContent;
    }

    public function getRequestData(): array
    {
        $requestData = [];
        $pattern = '/\{.*\}/s';

        // Batasi panjang rawContent untuk mencegah masalah performa
        $limitedContent = substr($this->rawContent, 0, 50000); // Batasi ke 50KB

        if (preg_match($pattern, $limitedContent, $matches)) {
            try {
                // Batasi ukuran JSON untuk mencegah masalah performa
                $jsonString = $matches[0];
                if (strlen($jsonString) > 10000) {
                    $jsonString = substr($jsonString, 0, 10000) . '...}';
                }

                $jsonData = json_decode($jsonString, true);
                if (is_array($jsonData)) {
                    // Batasi jumlah kunci untuk mencegah terlalu banyak data
                    $maxKeys = 50;
                    $count = 0;

                    foreach ($jsonData as $key => $value) {
                        if ($count >= $maxKeys) {
                            $requestData['...'] = '[truncated, too many items]';
                            break;
                        }

                        // Batasi ukuran nilai array/objek
                        if (is_array($value) && count($value) > 10) {
                            $truncated = array_slice($value, 0, 10);
                            $truncated['...'] = '[truncated, too many items]';
                            $requestData[$key] = $truncated;
                        } else {
                            $requestData[$key] = $value;
                        }

                        $count++;
                    }
                }
            } catch (\Exception) {
                // Ignore JSON parsing errors
                $requestData['error'] = 'Failed to parse JSON data (possibly too large)';
            }
        }

        return $requestData;
    }

    protected function getViewData(): array
    {
        return [
            'log' => $this->log,
            'file' => $this->file,
            'stackTrace' => $this->getStackTrace(),
            'exceptionMessage' => $this->getExceptionMessage(),
            'requestData' => $this->getRequestData(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->state($this->log)
            ->schema([
                Section::make('Informasi Log')
					// ->aside()
					->columnSpan(1)
                    ->schema([
                        Grid::make(1)
                            ->schema([
                                TextEntry::make('timestamp')
                                    ->label('Waktu')
									->inlineLabel(),
                                TextEntry::make('file')
                                    ->label('File')
									->columnStart(1)
									->inlineLabel()
                                    ->state($this->file),

                                TextEntry::make('level')
                                    ->label('Level')
									->inlineLabel()
									->columnStart(1)
                                    ->badge()
									->color(fn (string $state): string => match ($state) {
                                        'ERROR', 'CRITICAL', 'ALERT', 'EMERGENCY' => 'danger',
                                        'WARNING' => 'warning',
                                        'INFO', 'DEBUG' => 'info',
                                        'NOTICE' => 'success',
                                        default => 'gray',
                                    }),

                                TextEntry::make('env')
									->inlineLabel()
									->columnStart(1)
                                    ->badge()
                                    ->color('gray'),

                            ]),
                    ]),
				Tabs::make('Log Details')
					->columnSpan(3)
					->contained(true)
					->tabs([
						Tab::make('Pesan')
							->schema([
								TextEntry::make('exceptionMessage')
									->label(false)
									->words(50)
									->state($this->getExceptionMessage())
									->formatStateUsing(fn (string $state): HtmlString => new HtmlString('<pre class="text-xs overflow-auto whitespace-normal">' . e($state) . '</pre>'))
							]),

						Tab::make('Stack Trace')
							->schema([
								TextEntry::make('stackTrace')
									->label(false)
									->visible(fn () => count($this->getStackTrace()) > 0)
									->formatStateUsing(function (): HtmlString {
										$stackTrace = $this->getStackTrace();
										$html = '<ol class="list-decimal list-inside space-y-1">';

										foreach ($stackTrace as $trace) {
											$html .= '<li class="text-xs font-mono">' . e($trace) . '</li>';
										}

										$html .= '</ol>';

										return new HtmlString($html);
									})
							]),

						Tab::make('Raw Message')
							->schema([
								TextEntry::make('message')
									->label(false)
									->words(50)
									->formatStateUsing(fn (string $state): HtmlString => new HtmlString('<pre class="text-xs overflow-auto whitespace-normal">' . e($state) . '</pre>'))
							]),

						Tab::make('Request Data')
							->schema([
								TextEntry::make('requestData')
									->label(false)
									->visible(fn () => count($this->getRequestData()) > 0)
									->formatStateUsing(function (): HtmlString {
										$requestData = $this->getRequestData();

										if (empty($requestData)) {
											return new HtmlString('<div class="text-sm text-gray-500">Tidak ada data request yang tersedia.</div>');
										}

										$html = '<table class="min-w-full divide-y divide-gray-200">';
										$html .= '<thead class="bg-gray-100"><tr>';
										$html .= '<th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Key</th>';
										$html .= '<th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>';
										$html .= '</tr></thead><tbody class="bg-white divide-y divide-gray-200">';

										foreach ($requestData as $key => $value) {
											$html .= '<tr>';
											$html .= '<td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">' . e($key) . '</td>';
											$html .= '<td class="px-3 py-2 text-sm text-gray-500">';

											if (is_array($value)) {
												$html .= '<pre class="text-xs">' . e(json_encode($value, JSON_PRETTY_PRINT)) . '</pre>';
											} else {
												$html .= e($value);
											}

											$html .= '</td></tr>';
										}

										$html .= '</tbody></table>';

										return new HtmlString($html);
									})
							]),
						]),
            ])->columns(4);
    }
}
