<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Commitment2025;
use App\Models\DetailRealisasi2025;
use App\Models\MasterSpatial;
use App\Models\Pks2025;
use App\Models\Realisasi2025;
use App\Models\Varietas;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class MobileRealisasiController extends Controller
{
    /**
     * Get description for status_dinas value
     */
    private function getStatusDinasDescription($statusDinas)
    {
        $descriptions = [
            null => 'Draft - Dokumen masih dalam keadaan draft, belum dikirim ke Dinas',
            '0' => 'Di<PERSON>rim ke Dinas - Dokumen telah dikirim ke Dinas Kabupaten untuk diperiksa',
            '1' => 'Perbaikan - Dokumen perlu diperbaiki berdasarkan catatan dari Dinas',
            '2' => 'Disetujui - Dokumen telah diperiksa dan disetujui oleh Dinas'
        ];

        return $descriptions[$statusDinas] ?? 'Status tidak diketahui';
    }
    /**
     * Get list of commitments for the authenticated user
     */
    public function getCommitments()
    {
        $user = Auth::user();

        // Get commitments for the authenticated user
        $commitments = Commitment2025::where('npwp', $user->npwp)
            ->select('id', 'nama','no_ijin', 'periodetahun', 'volume_riph', 'volume_produksi','luas_wajib_tanam', 'tgl_ijin', 'tgl_akhir', 'status')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $commitments
        ]);
    }

    /**
     * Get commitment details by ID
     */
    public function getCommitmentDetail($id)
    {
        $user = Auth::user();

        $commitment = Commitment2025::where('id', $id)
            ->where('npwp', $user->npwp)
            ->first();

        if (!$commitment) {
            return response()->json([
                'success' => false,
                'message' => 'Commitment not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $commitment
        ]);
    }

    /**
     * Get PKS list for a commitment
     */
    public function getPksList($commitmentId)
    {
        $user = Auth::user();

        $commitment = Commitment2025::where('id', $commitmentId)
            ->where('npwp', $user->npwp)
            ->first();

        if (!$commitment) {
            return response()->json([
                'success' => false,
                'message' => 'Commitment not found'
            ], 404);
        }

        $pksList = Pks2025::where('no_ijin', $commitment->no_ijin)
            ->with(['provinsi:provinsi_id,nama', 'kabupaten:kabupaten_id,nama_kab', 'varietas'])
            ->get()
            ->map(function ($pks) {
                return [
                    'id' => $pks->id,
                    'no_perjanjian' => $pks->no_perjanjian,
                    'tgl_perjanjian_start' => $pks->tgl_perjanjian_start,
                    'tgl_perjanjian_end' => $pks->tgl_perjanjian_end,
                    'luas_rencana' => $pks->luas_rencana,
                    'varietas' => $pks->varietas,
                    'status' => $pks->status,
                    'status_dinas' => $pks->status_dinas,
                    'status_description' => $this->getStatusDinasDescription($pks->status_dinas),
                    'provinsi' => $pks->provinsi ? $pks->provinsi->nama : null,
                    'kabupaten' => $pks->kabupaten ? $pks->kabupaten->nama_kab : null,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $pksList
        ]);
    }

    /**
     * Get locations list for a PKS
     */
    public function getLocationsList($pksId)
    {
        $user = Auth::user();

        // Get PKS with related data
        $pks = Pks2025::with(['provinsi:provinsi_id,nama', 'kabupaten:kabupaten_id,nama_kab', 'varietas'])
            ->find($pksId);

        if (!$pks) {
            return response()->json([
                'success' => false,
                'message' => 'PKS not found'
            ], 404);
        }

        // Verify that the PKS belongs to the authenticated user
        $commitment = Commitment2025::where('no_ijin', $pks->no_ijin)
            ->where('npwp', $user->npwp)
            ->first();

        if (!$commitment) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        // Check if PKS has been approved by Dinas
        if ($pks->status_dinas !== '2') {
            return response()->json([
                'success' => false,
                'message' => 'PKS belum disetujui oleh Dinas',
                'status_dinas' => $pks->status_dinas,
                'status_description' => $this->getStatusDinasDescription($pks->status_dinas)
            ], 403);
        }

        // Get locations (MasterSpatial) associated with this commitment and poktan
        $locations = MasterSpatial::where('reserved_by', $commitment->no_ijin)
            ->where('kode_poktan', $pks->kode_poktan)
            ->where('status', 2) // Only confirmed locations
            ->with(['masterpoktan:kode_poktan,nama_kelompok', 'anggota:ktp_petani,nama_petani'])
            ->get();

        $formattedLocations = $locations->map(function ($location) use ($commitment) {
            // Check if there's a realization for this location
            $realization = Realisasi2025::where('kode_spatial', $location->kode_spatial)
                ->where('no_ijin', $commitment->no_ijin)
                ->first();

            $status = 'belum_diisi';
            $progress = 0;
            $realisasiId = null;

            if ($realization) {
                // Count how many detail realizations exist
                $detailCount = DetailRealisasi2025::where('realisasi_id', $realization->id)->count();
                $totalSteps = 7; // Total number of steps (lahan, benih, mulsa, tanam, pupuk, panen, distribusi)

                if ($detailCount == 0) {
                    $status = 'belum_diisi';
                    $progress = 0;
                } elseif ($detailCount < $totalSteps) {
                    $status = 'sebagian_diisi';
                    $progress = round(($detailCount / $totalSteps) * 100);
                } else {
                    $status = 'lengkap';
                    $progress = 100;
                }

                $realisasiId = $realization->id;
            }

            // Format the location data
            return [
                'id' => $location->id,
                'kode_spatial' => $location->kode_spatial,
                'nama_petani' => $location->anggota ? $location->anggota->nama_petani : $location->nama_petani,
                'kode_poktan' => $location->kode_poktan,
                'nama_poktan' => $location->masterpoktan ? $location->masterpoktan->nama_kelompok : null,
                'luas_lahan' => $location->luas_lahan,
                'latitude' => $location->latitude,
                'longitude' => $location->longitude,
                'polygon' => $location->polygon,
                'status' => $status,
                'progress' => $progress,
                'realisasi_id' => $realisasiId,
            ];
        });

        // Add PKS information to the response
        $pksInfo = [
            'id' => $pks->id,
            'no_perjanjian' => $pks->no_perjanjian,
            'tgl_perjanjian_start' => $pks->tgl_perjanjian_start,
            'tgl_perjanjian_end' => $pks->tgl_perjanjian_end,
            'luas_rencana' => $pks->luas_rencana,
            'varietas' => $pks->varietas,
            'provinsi' => $pks->provinsi ? $pks->provinsi->nama : null,
            'kabupaten' => $pks->kabupaten ? $pks->kabupaten->nama_kab : null,
        ];

        return response()->json([
            'success' => true,
            'pks' => $pksInfo,
            'data' => $formattedLocations
        ]);
    }

    /**
     * Get realization details for a location
     */
    public function getRealizationDetail($realisasiId)
    {
        $user = Auth::user();

        $realization = Realisasi2025::find($realisasiId);

        if (!$realization) {
            return response()->json([
                'success' => false,
                'message' => 'Realization not found'
            ], 404);
        }

        // Check if the realization belongs to the user
        $commitment = Commitment2025::where('no_ijin', $realization->no_ijin)
            ->where('npwp', $user->npwp)
            ->first();

        if (!$commitment) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        // Get the spatial data with location relations and poktan
        $spatial = MasterSpatial::with(['provinsi:provinsi_id,nama',
                                        'kabupaten:kabupaten_id,nama_kab',
                                        'kecamatan:kecamatan_id,nama_kecamatan',
                                        'desa:kelurahan_id,nama_desa',
                                        'masterpoktan:kode_poktan,nama_kelompok'])
                                ->where('kode_spatial', $realization->kode_spatial)
                                ->first();

        // Format polygon data if exists
        if ($spatial && $spatial->polygon) {
            try {
                // Check if polygon is already a JSON string
                $polygonData = json_decode($spatial->polygon, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    // Already valid JSON, keep as is
                    $spatial->polygon = $polygonData;
                } else {
                    // Try to parse as a different format or set to null if invalid
                    $spatial->polygon = null;
                }
            } catch (\Exception $e) {
                // If any error occurs, set polygon to null
                $spatial->polygon = null;
            }
        }

        // Add location names and poktan name to spatial data
        if ($spatial) {
            $spatial->nama_provinsi = $spatial->provinsi ? $spatial->provinsi->nama : null;
            $spatial->nama_kab = $spatial->kabupaten ? $spatial->kabupaten->nama_kab : null;
            $spatial->nama_kecamatan = $spatial->kecamatan ? $spatial->kecamatan->nama_kecamatan : null;
            $spatial->nama_desa = $spatial->desa ? $spatial->desa->nama_desa : null;
            $spatial->nama_poktan = $spatial->masterpoktan ? $spatial->masterpoktan->nama_kelompok : null;
        }

        // Get all detail realizations
        $details = DetailRealisasi2025::where('realisasi_id', $realization->id)->get();

        // Organize details by type
        $organizedDetails = [
            'lahan' => $details->where('jenis_keg', 'lahan')->first(),
            'benih' => $details->where('jenis_keg', 'benih')->first(),
            'mulsa' => $details->where('jenis_keg', 'mulsa')->first(),
            'tanam' => $details->where('jenis_keg', 'tanam')->first(),
            'pupuk' => $details->where('jenis_keg', 'pupuk')->first(),
            'panen' => $details->where('jenis_keg', 'panen')->first(),
            'distribusi' => $details->where('jenis_keg', 'distribusi')->first(),
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'realization' => $realization,
                'spatial' => $spatial,
                'details' => $organizedDetails
            ]
        ]);
    }

    /**
     * Create a new realization for a location
     */
    public function createRealization(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'kode_spatial' => 'required|string',
            'no_ijin' => 'required|string',
            'luas_lahan' => 'required|numeric',
            'periode_tanam' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if the commitment belongs to the user
        $commitment = Commitment2025::where('no_ijin', $request->no_ijin)
            ->where('npwp', $user->npwp)
            ->first();

        if (!$commitment) {
            return response()->json([
                'success' => false,
                'message' => 'Commitment not found or unauthorized'
            ], 404);
        }

        // Check if the spatial exists and is reserved by this commitment
        $spatial = MasterSpatial::where('kode_spatial', $request->kode_spatial)
            ->where('reserved_by', $request->no_ijin)
            ->first();

        if (!$spatial) {
            return response()->json([
                'success' => false,
                'message' => 'Location not found or not associated with this commitment'
            ], 404);
        }

        // Check if the PKS for this location has been approved by Dinas
        $pks = Pks2025::where('no_ijin', $request->no_ijin)
            ->where('kode_poktan', $spatial->kode_poktan)
            ->first();

        if (!$pks) {
            return response()->json([
                'success' => false,
                'message' => 'PKS not found for this location'
            ], 404);
        }

        if ($pks->status_dinas !== '2') {
            return response()->json([
                'success' => false,
                'message' => 'PKS belum disetujui oleh Dinas',
                'status_dinas' => $pks->status_dinas,
                'status_description' => $this->getStatusDinasDescription($pks->status_dinas)
            ], 403);
        }

        // Check if realization already exists
        $existingRealization = Realisasi2025::where('kode_spatial', $request->kode_spatial)
            ->where('no_ijin', $request->no_ijin)
            ->first();

        if ($existingRealization) {
            return response()->json([
                'success' => false,
                'message' => 'Realization already exists for this location'
            ], 409);
        }

        // Create new realization
        $realization = new Realisasi2025();
        $realization->origin = 'mobile';
        $realization->tcode = 'MOBILE-' . Str::random(8);
        $realization->npwp = $user->npwp;
        $realization->no_ijin = $request->no_ijin;
        $realization->kode_poktan = $spatial->kode_poktan;
        $realization->kode_spatial = $request->kode_spatial;
        $realization->ktp_petani = $spatial->ktp_petani;
        $realization->luas_lahan = $request->luas_lahan;
        $realization->periode_tanam = $request->periode_tanam;
        $realization->save();

        return response()->json([
            'success' => true,
            'message' => 'Realization created successfully',
            'data' => $realization
        ], 201);
    }

    /**
     * Create or update a detail realization
     */
    public function saveDetailRealization(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'realisasi_id' => 'required|integer',
            'activity_type' => 'required|string|in:lahan,benih,mulsa,tanam,pupuk,panen,distribusi,opt',
            'date' => 'required|date',
            'value' => 'required_unless:activity_type,lahan,opt|numeric',
            'note' => 'nullable|string',
            'photo' => 'nullable|file|mimes:jpeg,png,jpg|max:5120', // 5MB max

            // Parameter khusus untuk pupuk
            'organik' => 'required_if:activity_type,pupuk|numeric',
            'npk' => 'required_if:activity_type,pupuk|numeric',
            'dolomit' => 'required_if:activity_type,pupuk|numeric',
            'za' => 'required_if:activity_type,pupuk|numeric',

            // Parameter khusus untuk distribusi
            'dist_benih' => 'required_if:activity_type,distribusi|numeric',
            'dist_jual' => 'required_if:activity_type,distribusi|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Get the realization
        $realization = Realisasi2025::find($request->realisasi_id);

        if (!$realization) {
            return response()->json([
                'success' => false,
                'message' => 'Realization not found'
            ], 404);
        }

        // Check if the realization belongs to the user
        $commitment = Commitment2025::where('no_ijin', $realization->no_ijin)
            ->where('npwp', $user->npwp)
            ->first();

        if (!$commitment) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        // Check if the PKS for this realization has been approved by Dinas
        $spatial = MasterSpatial::where('kode_spatial', $realization->kode_spatial)->first();
        if (!$spatial) {
            return response()->json([
                'success' => false,
                'message' => 'Location not found'
            ], 404);
        }

        $pks = Pks2025::where('no_ijin', $realization->no_ijin)
            ->where('kode_poktan', $spatial->kode_poktan)
            ->first();

        if (!$pks) {
            return response()->json([
                'success' => false,
                'message' => 'PKS not found for this location'
            ], 404);
        }

        if ($pks->status_dinas !== '2') {
            return response()->json([
                'success' => false,
                'message' => 'PKS belum disetujui oleh Dinas',
                'status_dinas' => $pks->status_dinas,
                'status_description' => $this->getStatusDinasDescription($pks->status_dinas)
            ], 403);
        }

        // Check if detail already exists
        $detail = DetailRealisasi2025::where('realisasi_id', $request->realisasi_id)
            ->where('jenis_keg', $request->activity_type)
            ->first();

        if (!$detail) {
            $detail = new DetailRealisasi2025();
            $detail->realisasi_id = $request->realisasi_id;
            $detail->npwp = $user->npwp;
            $detail->no_ijin = $realization->no_ijin;
            $detail->kode_spatial = $realization->kode_spatial;
            $detail->jenis_keg = $request->activity_type;
        }

        $detail->tgl_keg = $request->date;
        $detail->value = $request->value ?? 0;
        $detail->keg_note = $request->note;

        // Handle additional fields based on activity type
        if ($request->activity_type === 'pupuk') {
            $detail->organik = $request->organik;
            $detail->npk = $request->npk;
            $detail->dolomit = $request->dolomit;
            $detail->za = $request->za;
        } elseif ($request->activity_type === 'distribusi') {
            $detail->dist_benih = $request->dist_benih;
            $detail->dist_jual = $request->dist_jual;
        }



        // Handle file upload
        if ($request->hasFile('photo')) {
            $file = $request->file('photo');
            $fileName = time() . '_' . $file->getClientOriginalName();

            // Format direktori sesuai dengan yang digunakan di LaporanKegiatan.php
            $cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $user->npwp);
            $cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $realization->no_ijin);
            $filePath = "uploads/{$cleanNpwp}/{$cleanNoIjin}/dokumen/pks/foto/";

            // Store the file
            $path = $file->storeAs($filePath, $fileName, 'public');
            $detail->file_url = Storage::url($path);
        }

        $detail->status = 'submitted';
        $detail->save();

        return response()->json([
            'success' => true,
            'message' => 'Detail realization saved successfully',
            'data' => $detail
        ]);
    }

    /**
     * Get detail activity by ID
     */
    public function getDetailActivity($detailId)
    {
        $user = Auth::user();

        // Find the detail
        $detail = DetailRealisasi2025::find($detailId);

        if (!$detail) {
            return response()->json([
                'success' => false,
                'message' => 'Detail activity not found'
            ], 404);
        }

        // Check if the detail belongs to the user
        $commitment = Commitment2025::where('no_ijin', $detail->no_ijin)
            ->where('npwp', $user->npwp)
            ->first();

        if (!$commitment) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $detail
        ]);
    }

    /**
     * Upload a photo for a detail realization
     */
    public function uploadPhoto(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'detail_id' => 'required|integer',
            'photo' => 'required|file|mimes:jpeg,png,jpg|max:5120', // 5MB max
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Get the detail
        $detail = DetailRealisasi2025::find($request->detail_id);

        if (!$detail) {
            return response()->json([
                'success' => false,
                'message' => 'Detail not found'
            ], 404);
        }

        // Check if the detail belongs to the user
        $commitment = Commitment2025::where('no_ijin', $detail->no_ijin)
            ->where('npwp', $user->npwp)
            ->first();

        if (!$commitment) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        // Handle file upload
        if ($request->hasFile('photo')) {
            $file = $request->file('photo');
            $fileName = time() . '_' . $file->getClientOriginalName();

            // Format direktori sesuai dengan yang digunakan di LaporanKegiatan.php
            $cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $user->npwp);
            $cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $detail->no_ijin);
            $filePath = "uploads/{$cleanNpwp}/{$cleanNoIjin}/dokumen/pks/foto/";

            // Store the file
            $path = $file->storeAs($filePath, $fileName, 'public');
            $detail->file_url = Storage::url($path);
            $detail->save();

            return response()->json([
                'success' => true,
                'message' => 'Photo uploaded successfully',
                'data' => [
                    'file_url' => $detail->file_url
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'No file uploaded'
        ], 400);
    }

    /**
     * Get list of varieties
     */
    public function getVarieties()
    {
        // This is a simplified list. In a real implementation, you might fetch this from a database
        $varieties = Varietas::select('id', 'nama_varietas as name')->get()->toArray();

        return response()->json([
            'success' => true,
            'data' => $varieties
        ]);
    }

    /**
     * Get list of fertilizer types
     */
    public function getFertilizerTypes()
    {
        // This is a simplified list. In a real implementation, you might fetch this from a database
        $fertilizerTypes = [
            ['id' => 1, 'name' => 'Pupuk Organik'],
            ['id' => 2, 'name' => 'NPK'],
            ['id' => 3, 'name' => 'Dolomit'],
            ['id' => 4, 'name' => 'ZA'],
        ];

        return response()->json([
            'success' => true,
            'data' => $fertilizerTypes
        ]);
    }

    /**
     * Get activity history for a realization
     */
    public function getActivityHistory($realisasiId)
    {
        $user = Auth::user();

        $realization = Realisasi2025::find($realisasiId);

        if (!$realization) {
            return response()->json([
                'success' => false,
                'message' => 'Realization not found'
            ], 404);
        }

        // Check if the realization belongs to the user
        $commitment = Commitment2025::where('no_ijin', $realization->no_ijin)
            ->where('npwp', $user->npwp)
            ->first();

        if (!$commitment) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        // Get all detail realizations ordered by date
        $activities = DetailRealisasi2025::where('realisasi_id', $realization->id)
            ->orderBy('tgl_keg', 'asc')
            ->get()
            ->map(function ($detail) {
                $activityName = '';
                switch ($detail->jenis_keg) {
                    case 'lahan':
                        $activityName = 'Persiapan Lahan';
                        break;
                    case 'benih':
                        $activityName = 'Persiapan Benih';
                        break;
                    case 'mulsa':
                        $activityName = 'Pemasangan Mulsa';
                        break;
                    case 'tanam':
                        $activityName = 'Penanaman';
                        break;
                    case 'pupuk':
                        $activityName = 'Pemupukan';
                        break;
                    case 'panen':
                        $activityName = 'Panen';
                        break;
                    case 'distribusi':
                        $activityName = 'Distribusi';
                        break;
                }

                return [
                    'id' => $detail->id,
                    'activity_name' => $activityName,
                    'activity_type' => $detail->jenis_keg,
                    'date' => $detail->tgl_keg,
                    'value' => $detail->value,
                    'note' => $detail->keg_note,
                    'file_url' => $detail->file_url,
                    'status' => $detail->status,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $activities
        ]);
    }

	    /**
     * Update an existing detail realization
     */
    public function updateDetailRealization(Request $request, $detailId)
    {
        $user = Auth::user();
		Log::info($user);
        $validator = Validator::make($request->all(), [
            'activity_type' => 'required|string|in:lahan,benih,mulsa,tanam,pupuk,panen,distribusi,opt',
            'date' => 'required|date',
            'value' => 'required_unless:activity_type,lahan,opt|numeric',
            'note' => 'nullable|string',
            'photo' => 'nullable|file|mimes:jpeg,png,jpg|max:5120', // 5MB max

            // Parameter khusus untuk pupuk
            'organik' => 'required_if:activity_type,pupuk|numeric',
            'npk' => 'required_if:activity_type,pupuk|numeric',
            'dolomit' => 'required_if:activity_type,pupuk|numeric',
            'za' => 'required_if:activity_type,pupuk|numeric',

            // Parameter khusus untuk distribusi
            'dist_benih' => 'required_if:activity_type,distribusi|numeric',
            'dist_jual' => 'required_if:activity_type,distribusi|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Get the detail
        $detail = DetailRealisasi2025::find($detailId);

        if (!$detail) {
            return response()->json([
                'success' => false,
                'message' => 'Detail not found'
            ], 404);
        }

        // Check if the detail belongs to the user
        $commitment = Commitment2025::where('no_ijin', $detail->no_ijin)
            ->where('npwp', $user->npwp)
            ->first();

        if (!$commitment) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        // Get the realization
        $realization = Realisasi2025::find($detail->realisasi_id);

        if (!$realization) {
            return response()->json([
                'success' => false,
                'message' => 'Realization not found'
            ], 404);
        }

        // Get the PKS
        $pks = Pks2025::where('no_ijin', $realization->no_ijin)
            ->where('kode_poktan', $realization->spatial->kode_poktan)
            ->first();

        if (!$pks) {
            return response()->json([
                'success' => false,
                'message' => 'PKS not found'
            ], 404);
        }

        // Check if PKS is approved by Dinas
        if ($pks->status_dinas !== '2') {
            return response()->json([
                'success' => false,
                'message' => 'PKS belum disetujui oleh Dinas',
                'status_dinas' => $pks->status_dinas,
                'status_description' => $this->getStatusDinasDescription($pks->status_dinas)
            ], 403);
        }

        // Update detail
        $detail->jenis_keg = $request->activity_type;
        $detail->tgl_keg = $request->date;
        $detail->value = $request->value;
        $detail->keg_note = $request->note;

        // Update specific fields based on activity type
        if ($request->activity_type === 'pupuk') {
            $detail->organik = $request->organik;
            $detail->npk = $request->npk;
            $detail->dolomit = $request->dolomit;
            $detail->za = $request->za;
        } elseif ($request->activity_type === 'distribusi') {
            $detail->dist_benih = $request->dist_benih;
            $detail->dist_jual = $request->dist_jual;
        }

        // Handle photo upload if provided
        if ($request->hasFile('photo')) {
            $photo = $request->file('photo');
            $filename = time() . '_' . $photo->getClientOriginalName();
            $path = $photo->storeAs('uploads/' . $user->npwp . '/' . date('Y') . '/realisasi', $filename, 'public');
            $detail->file_url = '/storage/' . $path;
        }

        $detail->save();

        return response()->json([
            'success' => true,
            'message' => 'Detail realization updated successfully',
            'data' => $detail
        ]);
    }

    /**
     * Delete a detail realization
     */
    public function deleteDetailRealization($detailId)
    {
        $user = Auth::user();

        // Get the detail
        $detail = DetailRealisasi2025::find($detailId);

        if (!$detail) {
            return response()->json([
                'success' => false,
                'message' => 'Detail not found'
            ], 404);
        }

        // Check if the detail belongs to the user
        $commitment = Commitment2025::where('no_ijin', $detail->no_ijin)
            ->where('npwp', $user->npwp)
            ->first();

        if (!$commitment) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        // Get the realization
        $realization = Realisasi2025::find($detail->realisasi_id);

        if (!$realization) {
            return response()->json([
                'success' => false,
                'message' => 'Realization not found'
            ], 404);
        }

        // Get the PKS
        $pks = Pks2025::where('no_ijin', $realization->no_ijin)
            ->where('kode_poktan', $realization->spatial->kode_poktan)
            ->first();

        if (!$pks) {
            return response()->json([
                'success' => false,
                'message' => 'PKS not found'
            ], 404);
        }

        // Check if PKS is approved by Dinas
        if ($pks->status_dinas !== '2') {
            return response()->json([
                'success' => false,
                'message' => 'PKS belum disetujui oleh Dinas',
                'status_dinas' => $pks->status_dinas,
                'status_description' => $this->getStatusDinasDescription($pks->status_dinas)
            ], 403);
        }

        // Delete the detail
        $detail->delete();

        return response()->json([
            'success' => true,
            'message' => 'Detail realization deleted successfully'
        ]);
    }

}
