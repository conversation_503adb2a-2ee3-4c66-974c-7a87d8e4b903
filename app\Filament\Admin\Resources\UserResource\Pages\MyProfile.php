<?php

namespace App\Filament\Admin\Resources\UserResource\Pages;

use App\Filament\Admin\Resources\UserResource;
use App\Models\MasterKabupaten;
use App\Models\MasterProvinsi;
use Filament\Actions;
use Filament\Forms\Components\{FileUpload, Group, Hidden, Radio, Section, Select, TextInput, Toggle};
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class MyProfile extends EditRecord
{
    protected static string $resource = UserResource::class;
    public static string | Alignment $formActionsAlignment = Alignment::Right;

	public function getHeading(): string
	{
        return 'Data Profil';
	}
    protected static ?string $title = 'Data Profil';

    public function getSubheading(): ?string
    {
		$me = Auth::user();
		$nama = $me->name;
        return $nama;
    }

	public function form(Form $form): Form
	{
		return $form
			->schema([
				Section::make('Akun Pengguna')
					->aside()
					->description('Data akun pengguna anda. Data ini digunakan untuk mengakses aplikasi')
					->columns(4)
					->schema([
						FileUpload::make('avatar_url')
							->rules([
								'image',
								'mimes:jpg,jpeg,png,webp',
								'max:1024',
							])
							->label('Foto Profil')
							->openable()
							->maxSize(2048)
							->downloadable()
							->disk('public')
							->previewable(true)
							->visibility('public')
							->imagePreviewHeight('250')
							->panelAspectRatio('1:1')
							->fetchFileInformation(true)
							->columnSpan(1),
						Group::make()
							->columnSpan(3)
							->schema([
								TextInput::make('name')
									->required()
									->inlineLabel()
									->maxLength(255),
								TextInput::make('username')
									->required()
									->readOnly()
									->inlineLabel()
									->maxLength(255),
								TextInput::make('email')
									->email()
									->inlineLabel()
									->readOnly()
									->required()
									->maxLength(255),

							]),
					]),

				Section::make('Katakunci')
					->aside()
					->description('Ubah/perbarui katakunci pengguna ini.')
					->schema([
						Toggle::make('visiblePass')
							->label('Ubah katakunci?')
							->onIcon('heroicon-m-bolt')
							->offIcon('heroicon-m-user')
							->columnSpanFull()
							->reactive(),
		
						TextInput::make('password')
							->password()
							->required()
							->reactive()
							->revealable()
							->visible(fn ($get)=>$get('visiblePass') === true)
							->maxLength(255),
					]),

				Group::make()
					->relationship('dataadmin')
					->columnSpanFull()
					->schema([
						Section::make('Data Profil')
							->aside()
							->description('Lengkapi data profil Anda. Untuk digunakan pada dokumen-dokumen cetak.')
							->schema([
								TextInput::make('nama')
									->inlineLabel()
									->required()
									->maxLength(50),
								TextInput::make('jabatan')
									->inlineLabel()
									->maxLength(50),
								TextInput::make('nip')
									->inlineLabel()
									->required()
									->maxLength(18),
							]),
						Section::make('Data Institusi')
							->aside()
							->description('Institusi tempat Anda bernaung.')
							->schema([
								TextInput::make('mobile_number')
									->label('Nomor HP')
									->inlineLabel()
									->helperText('Nomor kontak HP yang dapat dihubungi melalui aplikasi perpesanan.')
									->readOnly(fn () => Auth::id() !== $this->record->id),
								TextInput::make('nama_dinas')
									->inlineLabel()
									->required(fn () => Auth::user()->hasRole('dinas'))
									->maxLength(50),
								Select::make('provinsi_id')
									->inlineLabel()
									->label('Provinsi')
									->required(fn () => Auth::user()->hasRole('dinas'))
									->reactive()
									->searchable()
									->options(MasterProvinsi::all()->pluck('nama', 'provinsi_id'))
									->afterStateUpdated(fn ($set) => $set('kabupaten_id', null)),
								
								Select::make('kabupaten_id')
									->inlineLabel()
									->label('Kabupaten/Kota')
									->required(fn () => Auth::user()->hasRole('dinas'))
									->reactive()
									->searchable()
									->options(fn (callable $get) => 
										MasterKabupaten::where('provinsi_id', $get('provinsi_id'))
											->pluck('nama_kab', 'kabupaten_id')
									),
							])
					])
			]);
	}
}
