<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\UserDocs2024;
use App\Models\User;

class UserDocs2024Policy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any UserDocs2024');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, UserDocs2024 $userdocs2024): bool
    {
        return $user->checkPermissionTo('view UserDocs2024');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create UserDocs2024');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, UserDocs2024 $userdocs2024): bool
    {
        return $user->checkPermissionTo('update UserDocs2024');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, UserDocs2024 $userdocs2024): bool
    {
        return $user->checkPermissionTo('delete UserDocs2024');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any UserDocs2024');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, UserDocs2024 $userdocs2024): bool
    {
        return $user->checkPermissionTo('restore UserDocs2024');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any UserDocs2024');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, UserDocs2024 $userdocs2024): bool
    {
        return $user->checkPermissionTo('replicate UserDocs2024');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder UserDocs2024');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, UserDocs2024 $userdocs2024): bool
    {
        return $user->checkPermissionTo('force-delete UserDocs2024');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any UserDocs2024');
    }
}
