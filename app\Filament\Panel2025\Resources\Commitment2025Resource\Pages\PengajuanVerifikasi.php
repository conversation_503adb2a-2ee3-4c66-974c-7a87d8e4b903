<?php

namespace App\Filament\Panel2025\Resources\Commitment2025Resource\Pages;

use App\Filament\Panel2025\Resources\Commitment2025Resource;
use App\Models\PengajuanVerifikasi as ModelsPengajuanVerifikasi;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
use Filament\Forms\Components\{Actions, Hidden, Placeholder, Repeater, Section, Select, Textarea, TextInput};
use Filament\Forms\Components\Actions\Action as ActionsAction;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Support\HtmlString;

class PengajuanVerifikasi extends EditRecord
{
    protected static string $resource = Commitment2025Resource::class;
    public static string | Alignment $formActionsAlignment = Alignment::Right;
    protected static ?string $title = 'Pengajuan Verifikasi';

    protected function getHeaderActions(): array
    {
        return [];
    }


    public function getHeading(): string
	{
        return 'Pengajuan Verifikasi';
	}

    public function getSubheading(): ?string
    {
        $noIjin = $this->record ? $this->record->no_ijin : '##';
        return 'untuk PPRK No: ' . $noIjin;
    }

    public function form(Form $form): Form
	{
		return $form
		->schema([
            Section::make('Ringkasan Umum')
                ->aside()
                ->description('Data Rekomendasi Import Produk Hortikultura')
                ->schema([
                    Placeholder::make('Pelaku Usaha')
                        ->inlineLabel()
                        ->content(fn ($record) => $record->user->datauser->company_name),
                    Placeholder::make('Nomor Ijin')
                        ->inlineLabel()
                        ->content(fn ($record) => $record->no_ijin),
                    Placeholder::make('Periode')
                        ->inlineLabel()
                        ->content(fn ($record) => $record->periodetahun),
                    Placeholder::make('Masa berlaku')
                        ->inlineLabel()
                        ->content(fn ($record) => Carbon::parse($record->tgl_ijin)->translatedFormat('d M Y') . 
                            ' s.d ' . 
                            Carbon::parse($record->tgl_akhir)->translatedFormat('d M Y')),
                    Placeholder::make('Komoditas')
                        ->inlineLabel()
                        ->content(fn ($record) => $record->no_hs),
                    Placeholder::make('volume')
                        ->label('Volume PPRK')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->volume_riph,2,',','.') . ' ton'),
                ]),

            Section::make('Ringkasan Kewajiban dan Realisasi')
                ->aside()
                ->description('Data Realisasi wajib tanam-produksi yang telah anda isi/laporan pada aplikasi ini.')
                ->schema([
                    Placeholder::make('Jumlah Lokasi')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->realisasi->count(),0,',','.') . ' titik'),
                    Placeholder::make('Luas Komitmen Tanam')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->luas_wajib_tanam,0,',','.') . ' ha. (pembulatan 2 desimal)'),
                    Placeholder::make('Luas Lahan')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->realisasi->sum('luas_lahan'),0,',','.') . ' m2. (setara '. number_format($record->realisasi->sum('luas_lahan')/1000,2,',','.').' ha)'),
                    Placeholder::make('Realisasi Tanam')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->realisasi->sum('luas_tanam'),0,',','.') . ' m2. (setara '. number_format($record->realisasi->sum('luas_tanam')/1000,2,',','.').' ha)'),
                    Placeholder::make('Volume Komitmen Produksi')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->volume_produksi,0,',','.') . ' ton'),
                    Placeholder::make('Realisasi Produksi')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->realisasi->sum('volume'),0,',','.') . ' ton'),
                ]),

            Section::make('Ringkasan Kemitraan')
                ->aside()
                ->description('Data Komitmen sesuai dengan data di aplikasi/sistem RIPH')
                ->schema([
                    Placeholder::make('Kelompok Tani')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->realisasi
                            ->unique('kode_poktan')->count(), 0, ',', '.') . ' kelompok'),

                    Placeholder::make('Jumlah PKS')
                        ->inlineLabel()
                        ->label('Jumlah Berkas PKS')
                        ->helperText('Jumlah berkas perjanjian yang diunggah.')
                        ->content(fn ($record) => number_format($record->pks->whereNotNull('berkas_pks')->count(), 0, ',', '.') . ' berkas'),
                            
                    Placeholder::make('Anggota')
                        ->inlineLabel()
                        ->helperText('Jumlah anggota pelaksana realisasi.')
                        ->content(fn ($record) => number_format($record->realisasi
                            ->unique('ktp_petani')->count(), 0, ',', '.') . ' anggota'),
                            
                    Placeholder::make('Lokasi Tanam')
                        ->inlineLabel()
                        ->helperText('Jumlah lokasi yang dilaksanakan penanaman.')
                        ->content(fn ($record) => number_format($record->realisasi->whereNotNull('luas_tanam')->count(), 0, ',', '.') . ' titik'),
                ]),

            Section::make('Kelengkapan Berkas')
                ->aside()
                ->description('Berkas/dokumen unggahan')
                ->schema([
                    TableRepeater::make('berkas')
                        ->hiddenLabel()
                        ->addable(false)
                        ->deletable(false)
                        ->relationship('userfiles')
                        ->streamlined()
                        ->headers([
                            Header::make('Berkas'),
                            Header::make('Tautan'),
                            Header::make('status'),
                        ])
                        ->schema([
                            Placeholder::make('kind')
                                ->hiddenLabel()
                                ->content(fn ($record) => [
                                    'spvt' => 'Surat Pengajuan Verifikasi (Tanam)',
                                    'spvp' => 'Surat Pengajuan Verifikasi (Produksi)',
                                    'spskl' => 'Surat Pengajuan Penerbitan SKL',
                                    'sptjmt' => 'Surat Pernyataan Tangggung Jawab Mutlak (Periode Tanam)',
                                    'sptjmp' => 'Surat Pernyataan Tangggung Jawab Mutlak (Periode Produksi)',
                                    'rta' => 'Form Realisasi Tanam',
                                    'rpo' => 'Form Realisasi Produksi',
                                    'spht' => 'Statistik Pertanian Hortikultura (Periode Tanam)',
                                    'sphb' => 'Statistik Pertanian Hortikultura (Periode Produksi)',
                                    'spdst' => 'Surat Pengantar Dinas Telah Selesai Tanam',
                                    'spdsp' => 'Surat Pengantar Dinas Telah Selesai Produksi',
                                    'logbook' => 'Logbook (Tanam/Produksi)',
                                    'la' => 'Laporan Akhir',
                                    'skl' => 'Surat Keterangan Lunas',
                                    'ft' => 'Foto Tanam',
                                    'fp' => 'Foto Produksi',
                                    'pks' => 'Berkas PKS',
                                ][$record->kind] ?? $record->kind),
                                
                            Placeholder::make('Tautan')
                                ->hiddenLabel()
                                ->content(fn ($record) => new HtmlString(
                                    '<a href="/' . e($record->file_url) . '" target="_blank" rel="noopener noreferrer">Buka File</a>'
                                )),

                            Placeholder::make('Status')
                                ->hiddenLabel()
                                ->content(fn ($record) => new HtmlString(
                                    '<x-filament::icon 
                                        icon="' . match ($record->status) {
                                            'Sesuai' => 'heroicon-o-bookmark',
                                            'Tidak Sesuai' => 'heroicon-o-bookmark',
                                            default => 'heroicon-o-bookmark',
                                        } . '" 
                                        class="w-5 h-5 ' . match ($record->status) {
                                            'Sesuai' => 'text-success-700',
                                            'Tidak Sesuai' => 'text-danger-500',
                                            default => 'text-gray-500',
                                        } . '" 
                                    /> ' . match ($record->status) {
                                        'Sesuai' => 'Sesuai',
                                        'Tidak Sesuai' => 'Tidak Sesuai',
                                        default => '?',
                                    }
                                )),
                        ])
                ]),
            Section::make('Ringkasan (Pengajuan) Verifikasi')
                ->aside()
                ->description('Ringkasan pengajuan dan hasil verifikasi')
                ->schema([
                    Placeholder::make('Tanggal Pengajuan')
                        ->inlineLabel()
                        ->content(fn ($record): string => $record->created_at->toFormattedDateString()),
                    Textarea::make('verif_note')
                        ->inlineLabel()
                        ->label('Catatan Verifikasi')
                        ->autosize()
                        ->readOnly(),
                    Placeholder::make('verif_status')
                        ->inlineLabel()
                        ->label('Status Verifikasi')
                        ->content(fn ($record) => [
                            null => 'Belum/Tidak ada pengajuan verifikasi',
                            0 => 'Verifikasi sudah diajukan',
                            1 => 'Penetapan dan Penugasan Verifikator',
                            2 => 'Dalam proses pemeriksaan/verifikasi oleh Petugas.',
                            3 => 'Laporan harus diperbaiki.',
                            4 => 'Selesai.',
                        ][$record->verif_status] ?? $record->verif_status),
                ]),

			Section::make('Jenis Pengajuan')
                ->aside()
                ->description('Pilih jenis verifikasi yang akan dimohonkan. Pastikan seluruh persyaratan telah dipenuhi sebelum melakukan pengajuan ini.')
                ->schema([
                    Hidden::make('no_ijin'),
                    Hidden::make('npwp'),
                    Select::make('kind')
                        ->label('Jenis Pengajuan')
						->required()
                        ->options([
                            'PVT' => 'Verifikasi Tanam',
                            'PVP' => 'Verifikasi Produksi',
                            'PVS' => 'Penerbitan SKL',
                        ])
						->disableOptionWhen(function (string $value, $record): bool {
							// Pastikan record ada
							if (!$record) {
								return false;
							}
					
							// Ambil pengajuan verifikasi terbaru berdasarkan kind
							$pengajuan = ModelsPengajuanVerifikasi::where('kind', $value)
								->where('no_ijin', $record->no_ijin)
								->latest('created_at')
								->first();
					
							// Jika tidak ditemukan pengajuan, opsi tetap aktif
							if (!$pengajuan) {
								return false;
							}
					
							// Jika status null, kosong, atau bernilai 2, opsi tetap aktif
							return !in_array($pengajuan->status, [null, '', 3]);
						})
						->in(fn (Select $component) => array_keys($component->getEnabledOptions())),
                ]),
        ]);
    }
    
    protected function getFormActions(): array
	{
		// $visible = $this->data['visible'];
		// if($visible === 'visible'){
			return [
                $this->getSaveFormAction()->label('Ajukan')->color('warning')->icon('icon-cloud-upload'),
                $this->getCancelFormAction()->icon('icon-x-circle')
			];
		// }else{
		// 	return [];
		// }
	}

}
