<?php

namespace App\Observers;

use App\Models\User;
use App\Models\VerificatorAssignment;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class AssignmentObserver
{
    /**
     * Handle the VerificatorAssignment "created" event.
     */
	public function created(VerificatorAssignment $verificatorAssignment): void
	{
		$user = $verificatorAssignment->user;
		$pengajuan = $verificatorAssignment->pengajuan;

		$jenisMapping = [
			'PVT' => 'Verifikasi Tanam',
			'PVP' => 'Verifikasi Produksi',
			'PVS' => 'Penerbitan SKL',
		];
		$jenis = $pengajuan->kind ?? null;
		$jenisLabel = $jenisMapping[$jenis] ?? 'Jenis Tidak Dikenal';

		if ($user) {
			Notification::make()
				->title("Penugasan {$jenisLabel}")
				->body("Anda telah ditugaskan untuk melaksanakan {$jenisLabel} pada pengajuan No: {$pengajuan?->no_pengajuan}. Segera hubungi Administrator untuk mendapatkan arahan dan informasi lebih lanjut.")
				->sendToDatabase($user);
		} else {
			Log::warning("Gagal mengirim notifikasi: User tidak ditemukan untuk VerificatorAssignment ID {$verificatorAssignment->id}");
		}
	}

    /**
     * Handle the VerificatorAssignment "updated" event.
     */
    public function updated(VerificatorAssignment $verificatorAssignment): void
    {
        //
    }

    /**
     * Handle the VerificatorAssignment "deleted" event.
     */
    public function deleted(VerificatorAssignment $verificatorAssignment): void
    {
        //
    }

    /**
     * Handle the VerificatorAssignment "restored" event.
     */
    public function restored(VerificatorAssignment $verificatorAssignment): void
    {
        //
    }

    /**
     * Handle the VerificatorAssignment "force deleted" event.
     */
    public function forceDeleted(VerificatorAssignment $verificatorAssignment): void
    {
        //
    }
}
