<?php

namespace App\Filament\Admin\Widgets;

use App\Models\SupportTicketMessage;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class MessageReplyWidget extends Component implements HasForms
{
    use InteractsWithForms;
    
    public $ticketId;
    public $ticket;
    public $data = [];
    public $parentId = null;
    public $replyingTo = null;

    protected $listeners = [
        'set-reply-parent' => 'setReplyParent'
    ];

    public function mount($ticketId = null): void
    {
        if ($ticketId) {
            $this->ticketId = $ticketId;
            $this->ticket = \App\Models\SupportTicket::find($ticketId);
        }
        $this->form->fill();
    }

    public function render()
    {
        return view('filament.admin.widgets.message-reply-widget');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Hidden::make('ticket_id')
                    ->default(fn() => $this->ticket ? $this->ticket->id : null),
                    
                Hidden::make('user_id')
                    ->default(Auth::id()),
                    
                Hidden::make('parent_id')
                    ->default($this->parentId),
                    
                RichEditor::make('message')
                    ->label('Pesan')
                    ->placeholder('Tulis balasan Anda...')
                    ->required()
                    ->columnSpanFull()
                    ->toolbarButtons([
                        'bold',
                        'italic',
                        'underline',
                        'bulletList',
                        'orderedList',
                        'link',
                    ]),
                    
                FileUpload::make('attachment')
                    ->label('Lampiran (opsional)')
                    ->directory('support-attachments')
                    ->acceptedFileTypes(['image/*', 'application/pdf', '.doc', '.docx', '.txt'])
                    ->maxSize(5120) // 5MB
                    ->columnSpanFull(),
            ])
            ->statePath('data');
    }

    public function setReplyParent($data): void
    {
        // Handle both array and direct value
        if (is_array($data)) {
            $this->parentId = $data['parentId'] ?? null;
        } else {
            $this->parentId = $data;
        }
        
        if ($this->parentId) {
            $this->replyingTo = SupportTicketMessage::find($this->parentId);
        } else {
            $this->replyingTo = null;
        }
        
        // Update form dengan parent_id baru
        $this->form->fill(['parent_id' => $this->parentId]);
    }

    public function clearReply(): void
    {
        $this->parentId = null;
        $this->replyingTo = null;
        $this->form->fill(['parent_id' => null]);
    }

    public function sendReply(): void
    {
        $data = $this->form->getState();
        
        try {
            // Validasi bahwa user bisa reply
            if (!$this->canReply()) {
                Notification::make()
                    ->title('Tidak diizinkan')
                    ->body('Anda tidak memiliki izin untuk membalas tiket ini.')
                    ->danger()
                    ->send();
                return;
            }

            // Buat pesan baru
            SupportTicketMessage::create($data);

            // Update ticket
            $this->ticket->update([
                'last_replied_by' => Auth::user()->hasAnyRole(['admin', 'Super Admin', 'support']) ? 'staff' : 'user',
                'last_activity_at' => now(),
                'status' => $this->ticket->status === 'closed' ? 'open' : $this->ticket->status,
            ]);

            // Reset form
            $this->form->fill([
                'ticket_id' => $this->ticket ? $this->ticket->id : null,
                'user_id' => Auth::id(),
                'parent_id' => null,
                'message' => '',
                'attachment' => null,
            ]);
            
            $this->clearReply();

            // Refresh messages
            $this->dispatch('messageAdded');

            Notification::make()
                ->title('Pesan terkirim')
                ->body('Balasan Anda telah berhasil dikirim.')
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Error')
                ->body('Terjadi kesalahan: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function canReply(): bool
    {
        if (!$this->ticket) {
            return false;
        }

        $user = Auth::user();

        // Cek apakah tiket masih bisa di-reply
        if (in_array($this->ticket->status, ['closed'])) {
            return false;
        }

        // User yang membuat tiket bisa reply
        if ($this->ticket->user_id === $user->id) {
            return true;
        }

        // Staff yang di-assign bisa reply
        if ($this->ticket->staff_id === $user->id) {
            return true;
        }

        // Admin dan Super Admin bisa reply
        if ($user->hasAnyRole(['admin', 'Super Admin'])) {
            return true;
        }

        return false;
    }
}
