<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\MasterSpatialResource\Pages;
use App\Filament\Admin\Resources\MasterSpatialResource\RelationManagers;
use App\Models\MasterKabupaten;
use App\Models\MasterKecamatan;
use App\Models\MasterPoktan;
use App\Models\MasterSpatial;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\{EditAction, ViewAction};
use Filament\Tables\Columns\{TextColumn, ToggleColumn};
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

class MasterSpatialResource extends Resource
{
protected static ?string $model = MasterSpatial::class;

	// protected static ?string $recordTitleAttribute = 'kode_spatial';
	public static function getGloballySearchableAttributes(): array
	{
		return ['kode_spatial', 'nama_petani'];
	}

	public static function getGlobalSearchResultDetails(Model $record): array
	{
		return [
			'Kode Spatial' => $record->kode_spatial,
			'Nama Petani' => $record->nama_petani,
			'KTP' => $record->ktp_petani,
		];
	}

    protected static ?string $modelLabel = 'Master Spatial';
    protected static ?string $pluralModelLabel = 'Daftar Spatial Lahan';

	protected static ?string $navigationGroup = 'Data Induk';
    protected static ?string $navigationLabel = 'Spatial';
    protected static ?int $navigationSort = 5;
    protected static ?string $navigationIcon = 'heroicon-o-map';

	public static function shouldRegisterNavigation(): bool
    {
        $user = Auth::user();
        if ($user && $user->hasAnyRole(['Super Admin', 'admin', 'spatial'])) {
			return true;
        }
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // TextInput::make('komoditas')
                //     ->maxLength(255),
				Section::make('Data Pengelola')
					->aside()
					->description('Petani pemilik/pengelola/penggarap lahan ini.'),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
			->deferLoading() //async load
            ->columns([
				TextColumn::make('index')
                    ->label('No')
					->rowIndex(),
                TextColumn::make('kode_spatial')
					->sortable()
                    ->searchable(),
                TextColumn::make('nama_petani')
                    ->searchable()
					->sortable(),
                TextColumn::make('luas_lahan')
                    ->numeric()
					->suffix(' m2')
                    ->sortable(),
                TextColumn::make('kabupaten.nama_kab')
                    ->searchable()
					->sortable()->hidden(fn () => Auth::user()->hasRole('dinas')),
                TextColumn::make('kecamatan.nama_kecamatan')
                    ->searchable()
					->sortable(),
                ToggleColumn::make('is_active')
                    ->sortable()->hidden(fn () => Auth::user()->hasRole('dinas')),
				TextColumn::make('status')
					->badge()
					->sortable()
					->color(fn ($state) => match ((int) $state) {
						0 => 'gray',
						1 => 'warning',
						2 => 'success',
						default => 'secondary',
					})->formatStateUsing(fn ($state) => match ($state) {
						0 => 'Terbuka',
						1 => 'Terpilih',
						2 => 'Ber-PKS',
						default => 'Tidak diketahui',
					}),

                TextColumn::make('reserved_by')
					->label('Mitra Aktif')
                    ->searchable(),

            ])
            ->filters([
                //
				SelectFilter::make('kabupaten_id')
					->label('Kabupaten')
					->searchable()
					->preload()
					->options(function () {
						return MasterKabupaten::whereHas('spatials')->pluck('nama_kab', 'kabupaten_id')
								->toArray();
					})->hidden(fn () => Auth::user()->hasRole('dinas')),
				SelectFilter::make('kecamatan_id')
					->label('Kecamatan')
					->options(function () {
							return MasterKecamatan::where('kabupaten_id', optional(Auth::user()->dataadmin)->kabupaten_id)->whereHas('spatials')
								->pluck('nama_kecamatan', 'kecamatan_id')
								->toArray();
					})
					->searchable()
					->preload()
					->visible(fn () => Auth::user()->hasRole('dinas')),
				SelectFilter::make('kode_poktan')
					->label('Kelompok Tani')
					->options(function () {
						return MasterPoktan::where('kabupaten_id', optional(Auth::user()->dataadmin)->kabupaten_id)
							->pluck('nama_kelompok', 'kode_poktan')
							->toArray();
					})
					->searchable()
					->preload()
					->visible(fn () => Auth::user()->hasRole('dinas')),
            ])
            ->actions([
                ViewAction::make()->hiddenLabel(),
                EditAction::make()->hiddenLabel(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMasterSpatials::route('/'),
            'create' => Pages\CreateMasterSpatial::route('/create'),
            'view' => Pages\ViewMasterSpatial::route('/{record}'),
            'edit' => Pages\EditMasterSpatial::route('/{record}/edit'),
        ];
    }

    public static function canAccess(): bool
    {
        $user = Auth::user();
        return $user && $user->hasAnyRole(['Super Admin', 'admin', 'spatial']);
    }
}
