<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Node Path
    |--------------------------------------------------------------------------
    |
    | Path to the node executable. If not provided, Browsershot will try to find
    | it automatically.
    |
    */
    'node_path' => env('NODE_PATH', '/usr/bin/node'),

    /*
    |--------------------------------------------------------------------------
    | Node Module Path
    |--------------------------------------------------------------------------
    |
    | Path to the node_modules directory. If not provided, Browsershot will try
    | to find it automatically.
    |
    */
    'node_module_path' => env('NODE_MODULE_PATH', '/usr/local/lib/node_modules'),

    /*
    |--------------------------------------------------------------------------
    | Include Path
    |--------------------------------------------------------------------------
    |
    | Additional paths to include in the PATH environment variable when running
    | Browsershot commands.
    |
    */
    'include_path' => env('BROWSERSHOT_INCLUDE_PATH', '/usr/local/bin:/opt/homebrew/bin'),

    /*
    |--------------------------------------------------------------------------
    | Timeout
    |--------------------------------------------------------------------------
    |
    | The maximum time in seconds that Browsershot should wait for the page to
    | render before timing out.
    |
    */
    'timeout' => env('BROWSERSHOT_TIMEOUT', 120),

    /*
    |--------------------------------------------------------------------------
    | Chrome Path
    |--------------------------------------------------------------------------
    |
    | Path to the Chrome executable. If not provided, Browsershot will try to find
    | it automatically.
    |
    */
    'chrome_path' => env('CHROME_PATH', '/usr/bin/google-chrome-stable'),

    /*
    |--------------------------------------------------------------------------
    | Chrome Arguments
    |--------------------------------------------------------------------------
    |
    | Additional arguments to pass to Chrome when launching.
    |
    */
    'chrome_args' => [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--headless',
    ],

    /*
    |--------------------------------------------------------------------------
    | Puppeteer Cache Directory
    |--------------------------------------------------------------------------
    |
    | Directory where Puppeteer will store its cache. If not provided, Puppeteer
    | will use its default location.
    |
    */
    'puppeteer_cache_dir' => env('PUPPETEER_CACHE_DIR', null),
];
