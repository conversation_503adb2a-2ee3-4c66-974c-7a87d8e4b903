<?php

namespace App\Http\Controllers\Thn2025;

use App\Http\Controllers\Controller;
use App\Models\Commitment2025;
use App\Models\CommitmentRegion;
use App\Models\DataUser;
use App\Models\MasterKabupaten;
use App\Models\MasterKecamatan;
use App\Models\MasterPoktan;
use App\Models\MasterSpatial;
use App\Models\Realisasi2025;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

use function Pest\Laravel\json;

class DataFeederController extends Controller
{
	public function pickSpatials(Request $request, $noRiph)
	{
		$npwp = Auth::user()->datauser->npwp_company;
		$noIjin = preg_replace('/(\d{4})(\D{2})(\d+)(\D)(\d{2})(\d{4})/', '$1/$2.$3/$4/$5/$6', $noRiph);
		$commitment = Commitment2025::where('no_ijin', $noIjin)->select('id')->first();
		$myCommitment = CommitmentRegion::where('no_ijin', $noIjin)
			->with(['kabupaten:kabupaten_id,nama_kab'])
			->select('kabupaten_id','quota')
			->get();

		$quotas = (float) $myCommitment->sum('quota');
		$commitmentId = $commitment->id;
		$kabupatenIds = $myCommitment->pluck('kabupaten_id');

		$query = MasterSpatial::where('is_active', 1)->whereIn('kabupaten_id', $kabupatenIds);

		$queryCount = $query->where('status', 0)->count();
		$querySum = $query->where('status', 0)->sum('luas_lahan');

		$regionData = CommitmentRegion::where('no_ijin', $noIjin)->select('kabupaten_id', 'quota', 'status')->get();
		$regionList = [];
		foreach ($regionData as $region) {
			$sumLuasLahan = MasterSpatial::where('kabupaten_id', $region->kabupaten_id)->sum('luas_lahan');
			$regionList[] = [
				'kabupaten_id' => $region->kabupaten_id,
				'kabupaten_nama' => $region->kabupaten->nama_kab,
				'quota' => $region->quota,
				'available' => $sumLuasLahan,
			];
		}

		$mySpatials = MasterSpatial::where('is_active', 1)->whereIn('kabupaten_id', $kabupatenIds);
		$spatials = $mySpatials->get()->map(function ($item) {
			return [
				'id' => $item->id,
				'kode_spatial' => e($item->kode_spatial),
				'nama_petani'  => $item->anggota?->nama_petani ?? $item->nama_petani,
				'ktp_petani'  => $item->anggota?->ktp_petani ?? $item->ktp_petani,
				'latitude' => $item->latitude,
				'longitude' => $item->longitude,
				'luas_lahan' => $item->luas_lahan,
				'kabupaten_id' => $item->kabupaten_id,
				'kecamatan_id' => $item->kecamatan_id,
				'status' => $item->status,
			];
		});

		return response()->json([
			'commitmentId' => $commitmentId,
			'myCommitment' => $quotas,
			'availableMarker' => $queryCount,
			'availableLuas' => $querySum,
			'regionList' => $regionList,
			'data' => $spatials,
		]);
	}

	public function pickMarker($kodeSpatial)
	{
		$lokasi = MasterSpatial::where('kode_spatial', $kodeSpatial)->first();

		$data = [
			'id'			=> $lokasi->id,
			'kode_spatial'	=> e($lokasi->kode_spatial),
			'ktp_petani'	=> e($lokasi->ktp_petani),
			'nama_petani'	=> e($lokasi->nama_petani),
			'poktan'		=> e($lokasi->masterpoktan->nama_kelompok),
			'latitude'		=> $lokasi->latitude,
			'longitude'		=> $lokasi->longitude,
			'polygon'		=> $lokasi->polygon,
			'altitude'		=> $lokasi->altitude,
			'luas_lahan'	=> $lokasi->luas_lahan,
			'kabupaten'		=> $lokasi->kabupaten->nama_kab,
			'kecamatan'		=> $lokasi->kecamatan->nama_kecamatan,
			'kml_url'		=> e($lokasi->kml_url),
			'is_active'		=> $lokasi->is_active,
			'status'		=> $lokasi->status,
			'reserved_by'	=> $lokasi->reserved_by,
			'reserved_at'	=> $lokasi->reserved_at,
		];

		return response()->json([
			'success' => true,
			'data' => $data,
		]);
	}

    public function getPostedMarker($noIjin)
	{
		$no_Ijin = preg_replace('/(\d{4})(\D{2})(\d+)(\D)(\d{2})(\d{4})/', '$1/$2.$3/$4/$5/$6', $noIjin);
		$myMarkers = MasterSpatial::where('reserved_by', $no_Ijin)->get();
		$data = [];
		foreach ($myMarkers as $marker) {
			$data[] = [
				'kdSpatial'  => $marker->kode_spatial,
				'petani' => $marker->anggota->nama_petani,
				'ktp' => $marker->anggota->ktp_petani,
				'poktan' => $marker->masterpoktan->nama_kelompok,
				'kabupaten_id' => $marker->kabupaten_id,
				'kabupaten_nama' => $marker->kabupaten->nama_kab,
				'kecamatan_id' => $marker->kecamatan_id,
				'nama_kecamatan' => $marker->kecamatan->nama_kecamatan,
				'luas' => $marker->luas_lahan,
			];
		}
		return response()->json($data);
	}

	public function postMarker(Request $request, $kodeSpatial)
	{
		$validatedData = $request->validate([
			'kode_spatial' => 'required|string',
			'status' => 'required|integer',
			'reserved_by' => 'nullable|string',
			'reserved_at' => 'nullable|date',
		]);
		$maxRetries = 3;
		$attempts = 0;

		while ($attempts < $maxRetries) {
			try {
				DB::beginTransaction();
				$marker = MasterSpatial::where('kode_spatial', $kodeSpatial)->lockForUpdate()->first();

				if ($marker) {
					$marker->update($validatedData);
					$message = 'Data berhasil diperbarui';
				} else {
					MasterSpatial::create($validatedData);
					$message = 'Data berhasil disimpan';
				}

				DB::commit();

				return response()->json([
					'success' => true,
					'message' => $message,
					'received_data' => $validatedData,
				]);
			} catch (\Exception $e) {
				DB::rollBack();
				usleep(500000);

				$attempts++;

				if ($attempts >= $maxRetries) {
					return response()->json([
						'success' => false,
						'message' => 'Gagal menyimpan data setelah beberapa percobaan',
						'error' => $e->getMessage(),
					], 500);
				}
			}
		}
	}

	public function getSingleMarker(Request $requet, $id)
	{
		$realisasi = Realisasi2025::findOrFail($id);
		$importir = DataUser::where('npwp_company', $realisasi->npwp)->select('company_name')->first();
		$realisasi->importir = $importir->company_name;
		// $realisasi->importir = $realisasi->commitment->user->datauser->company_name;
		// inject ke $realisasi:
		// 'importir' => $realisasi->user->datauser->company_name

		$lokasi = MasterSpatial::where('kode_spatial', $realisasi->kode_spatial)->first();
		$datalokasi = [
			'kode_spatial' => $lokasi->kode_spatial,
			'nama_petani' => $lokasi->nama_petani,
			'poktan' => $lokasi->masterpoktan->nama_kelompok,
			'latitude' => $lokasi->latitude,
			'longitude' => $lokasi->longitude,
			'polygon' => $lokasi->polygon,
			'luas_lahan' => $lokasi->luas_lahan,
			'kode_spatial' => $lokasi->kode_spatial,
			'kml_url' => $lokasi->kml_url,
			'status' => $lokasi->status,
			'kecamatan' => $lokasi->kecamatan->nama_kecamatan,
			'kabupaten' => $lokasi->kabupaten->nama_kab,
		];
		return response()->json([
			'realisasi' => $realisasi,
			'lokasi' => $datalokasi
		]);
	}


	//data peta untuk ditampilkan di uploadSpatials
	public function getSelectedKabs(Request $request)
	{
		try {
			$kabupatenIds = $request->input('kabupaten_ids', []);

			// Selalu kembalikan array kosong jika tidak ada kabupaten yang dipilih
			if (empty($kabupatenIds)) {
				return response()->json([]);
			}

			$spatials = MasterSpatial::whereIn('kabupaten_id', $kabupatenIds)
				->select('id', 'kode_spatial', 'latitude', 'longitude', 'kabupaten_id')
				->get();

			Log::info('Selected kabupaten IDs: ' . json_encode($kabupatenIds));
			Log::info('Found ' . $spatials->count() . ' spatial records');

			return response()->json($spatials);
		} catch (\Exception $e) {
			Log::error('Error in getSelectedKabs: ' . $e->getMessage());
			return response()->json([
				'error' => 'An error occurred while fetching data',
				'message' => $e->getMessage()
			], 500);
		}
	}

	public function getMarkerInfo($kodeSpatial)
	{
		try {
			$spatial = MasterSpatial::where('kode_spatial', $kodeSpatial)->firstOrFail();
			$poktan = MasterPoktan::where('kode_poktan', $spatial->kode_poktan)->select('nama_kelompok')->first();
			$kecamatan = MasterKecamatan::where('kecamatan_id', $spatial->kecamatan_id)->select('nama_kecamatan')->first();
			$kabupaten = MasterKabupaten::where('kabupaten_id', $spatial->kabupaten_id)->select('nama_kab')->first();

			$markerInfo = $spatial->toArray();
			$markerInfo['poktan'] = $poktan ? $poktan->nama_kelompok : 'Tidak ada data';
			$markerInfo['kecamatan'] = $kecamatan ? $kecamatan->nama_kecamatan : 'Tidak ada data';
			$markerInfo['kabupaten'] = $kabupaten ? $kabupaten->nama_kab : 'Tidak ada data';

			Log::info('Marker info retrieved for: ' . $kodeSpatial);
			return response()->json($markerInfo);
		} catch (\Exception $e) {
			Log::error('Error in getMarkerInfo: ' . $e->getMessage());
			return response()->json([
				'error' => 'An error occurred while fetching marker info',
				'message' => $e->getMessage()
			], 500);
		}
	}

	public function searchSpatial(Request $request)
	{
		try {
			$searchQuery = $request->input('searchQuery');
			// Konversi string 'true'/'false' menjadi boolean
			$searchAllKabupaten = filter_var($request->input('searchAllKabupaten', 'false'), FILTER_VALIDATE_BOOLEAN);
			$limit = intval($request->input('limit', 100));
			$kabupatenIds = $request->input('kabupaten_ids', []);

			// Log parameter untuk debugging
			Log::info('Search parameters:', [
				'searchQuery' => $searchQuery,
				'searchAllKabupaten' => $searchAllKabupaten,
				'limit' => $limit,
				'kabupatenIds' => $kabupatenIds
			]);

			$query = MasterSpatial::query()
				->select('id', 'kode_spatial', 'nama_petani', 'ktp_petani', 'latitude', 'longitude', 'kabupaten_id', 'kecamatan_id', 'kode_poktan', 'luas_lahan')
				->with(['masterpoktan:kode_poktan,nama_kelompok', 'kecamatan:kecamatan_id,nama_kecamatan', 'kabupaten:kabupaten_id,nama_kab']);

			// Filter berdasarkan kabupaten jika tidak mencari di semua kabupaten
			if (!$searchAllKabupaten && !empty($kabupatenIds)) {
				$query->whereIn('kabupaten_id', $kabupatenIds);
			}

			// Pencarian berdasarkan kata kunci
			if (!empty($searchQuery)) {
				$query->where(function ($q) use ($searchQuery) {
					$q->where('kode_spatial', 'like', "%{$searchQuery}%")
						->orWhere('nama_petani', 'like', "%{$searchQuery}%")
						->orWhere('ktp_petani', 'like', "%{$searchQuery}%")
						->orWhereHas('masterpoktan', function ($q) use ($searchQuery) {
							$q->where('nama_kelompok', 'like', "%{$searchQuery}%");
						})
						->orWhereHas('kecamatan', function ($q) use ($searchQuery) {
							$q->where('nama_kecamatan', 'like', "%{$searchQuery}%");
						});
				});
			}

			// Batasi hasil pencarian
			$result = $query->limit($limit)->get();

			Log::info('Search spatial data: ' . count($result) . ' results found for query: ' . $searchQuery);
			return response()->json($result);
		} catch (\Exception $e) {
			Log::error('Error in searchSpatial: ' . $e->getMessage());
			return response()->json([
				'error' => 'An error occurred while searching spatial data',
				'message' => $e->getMessage()
			], 500);
		}
	}
}
