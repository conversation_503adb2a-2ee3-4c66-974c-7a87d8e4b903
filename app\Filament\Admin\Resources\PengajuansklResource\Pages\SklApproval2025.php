<?php

namespace App\Filament\Admin\Resources\PengajuansklResource\Pages;

use App\Filament\Admin\Resources\PengajuansklResource;
use App\Models\PengajuanVerifikasi;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class SklApproval2025 extends EditRecord
{
    protected static string $resource = PengajuansklResource::class;
    public static string | Alignment $formActionsAlignment = Alignment::Right;
    protected static ?string $title = 'Approval SKL';

    protected function getHeaderActions(): array
    {
        return [];
    }

    public function getHeading(): string
	{
        return 'Form Persetujuan Penerbitan SKL';
	}

    public function getSubheading(): ?string
    {
        $noIjin = $this->record ? $this->record->no_ijin : '##';
        return 'untuk PPRK No: ' . $noIjin;
    }


	public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Data Pemohon')
					->aside()
					->description('Data Pemohon status dan penerbitan Surat Keterangan Lunas')
					->schema([
						Group::make()
							->relationship('datauser')
							->schema([
								Placeholder::make('companyname')
									->label('Perusahaan')
									->inlineLabel()
									->content(fn ($record)=>$record->company_name),

								Placeholder::make('nib')
									->label('Nomor Induk Berusaha')
									->inlineLabel()
									->content(fn ($record)=>$record->nib_company),

								Placeholder::make('npwp')
									->label('Nomor Pokok Wajib Pajak')
									->inlineLabel()
									->content(fn ($record)=>$record->npwp_company),

								Placeholder::make('penanggungjawab')
									->label('Penangggungjawab')
									->inlineLabel()
									->content(fn ($record)=>$record->penanggungjawab),

								Placeholder::make('alamat')
									->label('Alamat')
									->inlineLabel()
									->content(fn ($record)=>new HtmlString('
										<p class="">'.$record->address_company.'</p>
										<span class="">'.$record->myKabupaten->nama_kab .' - '.$record->myProvinsi->nama.'</span>
									')),

							])
					]),

				Section::make('Data PPRK')
					->aside()
					->description('Data PPRK (Pertimbangan Penetapan Rencana Kebutuhan)')
					->schema([
						Group::make()
							->relationship('commitment')
							->schema([
								Placeholder::make('noijin')
									->label('Nomor Ijin')
									->inlineLabel()
									->content(fn ($record)=>$record->no_ijin),

								Placeholder::make('Periode')
									->label('Periode')
									->inlineLabel()
									->content(fn ($record)=>$record->periodetahun),

								Placeholder::make('Masa Berlaku')
									->label('Masa Berlaku')
									->inlineLabel()
									->content(fn ($record) => Carbon::parse($record->tgl_ijin)->translatedFormat('d F Y') . ' -s.d- ' . Carbon::parse($record->tgl_akhir)->translatedFormat('d F Y')),

								Placeholder::make('Volume Ijin Import')
									->label('Volume Ijin Import')
									->inlineLabel()
									->content(fn ($record)=> number_format($record->volume_riph,0,',','.') . '  ton'),

								Placeholder::make('Komitmen Luas Tanam')
									->label('Komitmen Luas Tanam')
									->inlineLabel()
									->content(fn ($record)=> number_format($record->luas_wajib_tanam,0,',','.') . '  m2'),

								Placeholder::make('Komitmen Produksi')
									->label('Komitmen Produksi')
									->inlineLabel()
									->content(fn ($record)=> number_format($record->volume_produksi,0,',','.') . '  kg'),

							])
					]),

				Section::make('Data Realisasi')
					->aside()
					->description('Data ringkasan realisasi komitmen tanam dan produksi')
					->schema([
						Group::make()
						->schema([
							Placeholder::make('luastanam')
								->label('Total Luas Tanam')
								->inlineLabel()
								->content(fn ($record) => number_format($record->detailrealisasitanam->sum('value'),0,',','.').' m2'),

							Placeholder::make('volume_panen')
								->label('Total Produksi')
								->inlineLabel()
								->content(fn ($record) => number_format($record->detailrealisasiproduksi->sum('value'),0,',','.').' kg'),

							Placeholder::make('distribusibenih')
								->label('Untuk Benih')
								->inlineLabel()
								->content(fn ($record) => number_format($record->detailrealisasidistribusi->sum('dist_benih'),0,',','.').' kg'),

							Placeholder::make('distribusijual')
								->label('Untuk Dijual')
								->inlineLabel()
								->content(fn ($record) => number_format($record->detailrealisasidistribusi->sum('dist_jual'),0,',','.').' kg'),

							Placeholder::make('poktan')
								->label('Kelompok Tani')
								->inlineLabel()
								->content(fn ($record) => number_format($record->commitment->pks->count(),0,',','.').' kelompok'),

							Placeholder::make('anggota')
								->label('Anggota')
								->inlineLabel()
								->content(fn ($record) =>
									number_format($record->realisasi->pluck('ktp_petani')->unique()->count(), 0, ',', '.') . ' anggota'
								),

							Placeholder::make('lokasi')
								->label('Jumlah Lahan')
								->inlineLabel()
								->content(fn ($record) =>
									number_format($record->realisasi->count(), 0, ',', '.') . ' titik'
								),

							Fieldset::make('Realisasi Pupuk dan Mulsa')
								->schema([
									Placeholder::make('mulsa')
										->label('Mulsa')
										->inlineLabel()
										->content(fn ($record) => number_format($record->detailrealisasimulsa->sum('value'),0,',','.').' roll'),

									Placeholder::make('organik')
										->label('organik')
										->inlineLabel()
										->content(fn ($record) => number_format($record->detailrealisasipupuk->sum('organik'),0,',','.').' kg'),

									Placeholder::make('dolomit')
										->label('dolomit')
										->inlineLabel()
										->content(fn ($record) => number_format($record->detailrealisasipupuk->sum('dolomit'),0,',','.').' kg'),

									Placeholder::make('npk')
										->label('NPK')
										->inlineLabel()
										->content(fn ($record) => number_format($record->detailrealisasipupuk->sum('npk'),0,',','.').' kg'),

									Placeholder::make('za')
										->label('ZA')
										->inlineLabel()
										->content(fn ($record) => number_format($record->detailrealisasipupuk->sum('za'),0,',','.').' kg'),
								])
						])

					]),

				Section::make('Data Verifikasi Tanam')
					->aside()
					->description(function ($record) {
						if (!$record->dataverifikasitanam) {
							return 'Data pelaksanaan dan kesimpulan verifikasi pada tahap tanam: Tidak tersedia';
						}
						return new HtmlString(
							'Data pelaksanaan dan kesimpulan verifikasi pada tahap tanam:'.
							view('components.badge-verifikasi', ['status' => $record->dataverifikasitanam->status])
						);
					})
					->schema([
						Placeholder::make('statusverifikasitanam')
							->label('Status Verifikasi')
							->inlineLabel()
							->content(function ($record) {
								if (!$record->dataverifikasitanam) {
									return new HtmlString('<span class="text-danger-500">Data verifikasi tidak tersedia</span>');
								}
								return new HtmlString(
									view('components.badge-verifikasi', ['status' => $record->dataverifikasitanam->status])
								);
							}),

						Placeholder::make('noajutanam')
							->label('No. Pengajuan')
							->inlineLabel()
							->content(function ($record) {
								if (!$record->dataverifikasitanam) {
									return 'Tidak tersedia';
								}
								return $record->dataverifikasitanam->no_pengajuan;
							}),

						Placeholder::make('tgl_mulai')
							->label('Tanggal Pelaksanaan')
							->inlineLabel()
							->content(function ($record) {
								if ($record->realisasi->isEmpty()) {
									return 'Tidak tersedia';
								}

								$maxDate = $record->realisasi->max('vt_at');
								$minDate = $record->realisasi->min('vt_at');

								if (!$maxDate || !$minDate) {
									return 'Tidak tersedia';
								}

								return Carbon::parse($maxDate)->translatedFormat('d F Y') . ' -s.d- ' . Carbon::parse($minDate)->translatedFormat('d F Y');
							}),

						Placeholder::make('fileBa')
							->label('Berita Acara Verifikasi')
							->inlineLabel()
							->extraAttributes(['class' => 'text-info-500'])
							->content(function ($record) {
								if (!$record->dataverifikasitanam || !$record->dataverifikasitanam->fileBa) {
									return 'Tidak tersedia';
								}
								return new HtmlString('
									<a class="font-bold" href="/'.$record->dataverifikasitanam->fileBa.'" rel="nofollow noreferer" target="_blank">Lihat</a>
								');
							}),

						Placeholder::make('fileNdhp')
							->label('Nota Dinas')
							->inlineLabel()
							->extraAttributes(['class' => 'text-info-500'])
							->content(function ($record) {
								if (!$record->dataverifikasitanam || !$record->dataverifikasitanam->fileNdhp) {
									return 'Tidak tersedia';
								}
								return new HtmlString('
									<a class="font-bold" href="/'.$record->dataverifikasitanam->fileNdhp.'" rel="nofollow noreferer" target="_blank">Lihat</a>
								');
							}),

						Placeholder::make('catatantanam')
							->label('Catatan Verifikasi')
							->inlineLabel()
							->content(function ($record) {
								if (!$record->dataverifikasitanam) {
									return 'Tidak tersedia';
								}
								return new HtmlString('
									<p class="">'.$record->dataverifikasitanam->note.'</p>
								');
							}),

						Repeater::make('Verifikator Tanam')
							->addable(false)
							->deletable(false)
							->label('Petugas Verifikasi')
							->inlineLabel()
							->relationship('dataverifikatortanam')
							->schema([
								Placeholder::make('Verifikator')
									->hiddenLabel()
									->inlineLabel()
									->content(fn ($record) => $record->user->name),
								Placeholder::make('Catatan/Pendapat Verifikator')
									->extraAttributes(['style'=>'background-color: #f3f4f6; padding: 10px; border-radius: 5px'])
									->content(fn ($record) => new HtmlString(
										'<p class="bg-gray-500">'.$record->myNote.'</p>'
									)),
							])
					]),

				Section::make('Data Verifikasi Produksi')
					->aside()
					->description(function ($record) {
						if (!$record->dataverifikasiproduksi) {
							return 'Data kesimpulan hasil verifikasi pada tahap produksi: Tidak tersedia';
						}
						return 'Data kesimpulan hasil verifikasi pada tahap produksi';
					})
					->schema([
						Placeholder::make('statusverifikasitanam')
							->label('Status Verifikasi')
							->inlineLabel()
							->content(function ($record) {
								if (!$record->dataverifikasiproduksi) {
									return new HtmlString('<span class="text-danger-500">Data verifikasi tidak tersedia</span>');
								}
								return new HtmlString(
									view('components.badge-verifikasi', ['status' => $record->dataverifikasiproduksi->status])
								);
							}),

						Placeholder::make('noajutanam')
							->label('No. Pengajuan')
							->inlineLabel()
							->content(function ($record) {
								if (!$record->dataverifikasiproduksi) {
									return 'Tidak tersedia';
								}
								return $record->dataverifikasiproduksi->no_pengajuan;
							}),

						Placeholder::make('tgl_mulai')
							->label('Tanggal Pelaksanaan')
							->inlineLabel()
							->content(function ($record) {
								if ($record->realisasi->isEmpty()) {
									return 'Tidak tersedia';
								}

								$maxDate = $record->realisasi->max('vp_at');
								$minDate = $record->realisasi->min('vp_at');

								if (!$maxDate || !$minDate) {
									return 'Tidak tersedia';
								}

								return Carbon::parse($maxDate)->translatedFormat('d F Y') . ' -s.d- ' . Carbon::parse($minDate)->translatedFormat('d F Y');
							}),

						Placeholder::make('fileBa')
							->label('Berita Acara Verifikasi')
							->inlineLabel()
							->extraAttributes(['class' => 'text-info-500'])
							->content(function ($record) {
								if (!$record->dataverifikasiproduksi || !$record->dataverifikasiproduksi->fileBa) {
									return 'Tidak tersedia';
								}
								return new HtmlString('
									<a class="font-bold" href="/'.$record->dataverifikasiproduksi->fileBa.'" rel="nofollow noreferer" target="_blank">Lihat</a>
								');
							}),

						Placeholder::make('fileNdhp')
							->label('Nota Dinas')
							->inlineLabel()
							->extraAttributes(['class' => 'text-info-500'])
							->content(function ($record) {
								if (!$record->dataverifikasiproduksi || !$record->dataverifikasiproduksi->fileNdhp) {
									return 'Tidak tersedia';
								}
								return new HtmlString('
									<a class="font-bold" href="/'.$record->dataverifikasiproduksi->fileNdhp.'" rel="nofollow noreferer" target="_blank">Lihat</a>
								');
							}),

						Placeholder::make('catatantanam')
							->label('Catatan Verifikasi')
							->inlineLabel()
							->content(function ($record) {
								if (!$record->dataverifikasiproduksi) {
									return 'Tidak tersedia';
								}
								return new HtmlString('
									<p class="">'.$record->dataverifikasiproduksi->note.'</p>
								');
							}),

						Repeater::make('Verifikator Produksi')
							->addable(false)
							->deletable(false)
							->label('Petugas Verifikasi')
							->inlineLabel()
							->relationship('dataverifikatorproduksi')
							->schema([
								Placeholder::make('Verifikator')
									->hiddenLabel()
									->inlineLabel()
									->content(fn ($record) => $record->user->name),
								Placeholder::make('Catatan/Pendapat Verifikator')
									->extraAttributes(['style'=>'background-color: #f3f4f6; padding: 10px; border-radius: 5px'])
									->content(fn ($record) => new HtmlString(
										'<p class="bg-gray-500">'.$record->myNote.'</p>'
									)),
							])
					]),

				Section::make('Persetujuan')
					->aside()
					->description('Form persetujuan penerbitan Surat Keterangan Lunas dan perubahan Status Komitmen Tanam-Produksi')
					->schema([
						Placeholder::make('disclaimer')
							->hiddenLabel()
							->content('Setelah mempertimbangkan kesimpulan verifikasi di tahap Tanam dan Produksi, dengan ini Saya:'),

						Radio::make('status')
							->label('Persetujuan')
							->required()
							->inline()
							->options([
								'1' => 'Menyetujui',
								'2' => 'Tidak Menyetujui',
							]),
						Placeholder::make('lastdisclaimer')
							->hiddenLabel()
							->content('untuk diterbitkannya Surat Keterangan Lunas serta penetapan status LUNAS atas Komitmen ini'),
						]),

						Hidden::make('approved_by')->formatStateUsing(fn ()=>Auth::user()->id),
						Hidden::make('no_pengajuan'),
						Hidden::make('no_ijin'),
						Hidden::make('approved_at')->formatStateUsing(fn ()=>today()),

            ]);
    }

	protected function mutateFormDataBeforeSave(array $data): array
	{
		$noPengajuan = $data['no_pengajuan'] ?? null;
		$noIjin = $data['no_ijin'] ?? null;

		$status = isset($data['status']) ? ($data['status'] == '1' ? 7 : 6) : null;

		// Only update pengajuan if noPengajuan and noIjin are available
		if ($noPengajuan && $noIjin) {
			$pengajuan = PengajuanVerifikasi::where('no_pengajuan', $noPengajuan)
				->where('no_ijin', $noIjin)
				->first();

			if ($pengajuan) {
				$pengajuan->status = $status;
				$pengajuan->save();
			}
		}

		return $data;
	}
}
