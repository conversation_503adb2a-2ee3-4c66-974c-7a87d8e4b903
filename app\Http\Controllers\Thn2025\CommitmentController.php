<?php

namespace App\Http\Controllers\Thn2025;

use App\Http\Controllers\Controller;
use App\Models\Commitment2025;
use App\Models\MasterSpatial;
use App\Models\Pks2025;
use App\Models\Realisasi2025;
use Filament\Notifications\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CommitmentController extends Controller
{
	public function index(Request $request, $noRiph)
	{
		if (!Auth::check()) {
			return redirect()->route('logout');
		}
		if(!Auth::user()->hasRole('importir')){
			Notification::make()
					->danger()
					->title('Aks<PERSON>lak!')
					->body('Anda tidak memiliki izin untuk mengakses data di halaman tujuan.')
					->send();
			
				return redirect()->back();
		}

		$noIjin = vsprintf('%s/%s.%s/%s/%s/%s', [
			substr($noRiph, 0, 4),
			substr($noRiph, 4, 2),
			substr($noRiph, 6, 3),
			substr($noRiph, 9, 1),
			substr($noRiph, 10, 2),
			substr($noRiph, 12, 4),
		]);

		if(Auth::user()->hasRole('importir')){
			$commitment = Commitment2025::where('no_ijin', $noIjin)->select('user_id')->first();
			if (!$commitment || $commitment->user_id !== Auth::user()->id) {
				Notification::make()
					->danger()
					->title('Akses Ditolak!')
					->body('Anda tidak memiliki izin untuk mengakses data di halaman tujuan.')
					->send();
			
				return redirect()->back();
			}
			
			$pksList = Pks2025::where('no_ijin', $noIjin)->get();
    
			// if ($pksList->isNotEmpty() && $pksList->every(fn($pks) => !is_null($pks->berkas_pks))) {
			// 	Notification::make()
			// 		->danger()
			// 		->title('Akses Ditolak!')
			// 		->body('Semua berkas PKS terkait sudah diunggah. Perubahan data lokasi tidak diijinkan.')
			// 		->send();
			
			// 	return redirect()->back();
			// }
		}
		
		$pageTitle = 'Panel Pemetaan';
		$modul = 'Pemilihan Lokasi Komitmen Tanam';
		$pageFooter = 'Footer';
		$printTime = now();

		return view('velzon.efilling.index', compact(
			'modul',
			'pageTitle',
			'pageFooter',
			'printTime',
			'noIjin',
			'noRiph',
			// 'id',
		));
	}

	public function singleMap(Request $request, $id)
	{
		if (!Auth::check()) {
			return redirect()->route('logout');
		}

		$pageTitle = 'Panel Pemetaan';
		$modul = 'Lokasi Tanam';
		$pageFooter = 'Footer';
		$printTime = now();

		$data = Realisasi2025::select('id', 'npwp')->findOrFail($id);
		$dataNpwp = $data->npwp;
		$user = Auth::user();
		if ($user->hasRole('importir')) {
			$userNpwp = optional($user->datauser)->npwp_company;

			if ($userNpwp !== $dataNpwp) {
				abort(403, 'Unauthorized access');
			}
			return view('velzon.realisasi.single', compact(
				'modul',
				'pageTitle',
				'pageFooter',
				'printTime',
				'data'
			));
		}
		$allowedRoles = ['Super Admin', 'admin', 'direktur', 'verifikator'];

		if ($user->hasAnyRole($allowedRoles)) {
			return view('velzon.realisasi.single', compact(
				'modul',
				'pageTitle',
				'pageFooter',
				'printTime',
				'data'
			));
		}
		abort(403, 'Unauthorized access');
	}

	public function applyVerif(Request $request, $noRiph)
	{
		return true;
	}
}
