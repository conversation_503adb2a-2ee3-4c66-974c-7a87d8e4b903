<?php

namespace App\Filament\Admin\Resources\CmsCategoryResource\Pages;

use App\Filament\Admin\Resources\CmsCategoryResource;
use App\Models\CmsCategory;
use Illuminate\Support\Str;
use Filament\Actions;
use Filament\Forms\Components\{ColorPicker, FileUpload, Grid, Section, Select, Textarea, TextInput, Toggle};
use Filament\Forms\Form;
use Filament\Resources\Pages\CreateRecord;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use TomatoPHP\FilamentIcons\Components\IconPicker;

class CreateCmsCategory extends CreateRecord
{
    protected static string $resource = CmsCategoryResource::class;
    protected static ?string $title = 'Kategori Baru';

    public function getHeading(): string
	{
		// $sales_no = $this->record ? $this->record->sales_no : 'N/A';
        return 'Buat Kategori Baru ';
	}

}
