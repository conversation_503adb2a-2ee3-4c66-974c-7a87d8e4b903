<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\CommitmentRegion;
use App\Models\User;

class CommitmentRegionPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any CommitmentRegion');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, CommitmentRegion $commitmentregion): bool
    {
        return $user->checkPermissionTo('view CommitmentRegion');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create CommitmentRegion');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, CommitmentRegion $commitmentregion): bool
    {
        return $user->checkPermissionTo('update CommitmentRegion');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, CommitmentRegion $commitmentregion): bool
    {
        return $user->checkPermissionTo('delete CommitmentRegion');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any CommitmentRegion');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, CommitmentRegion $commitmentregion): bool
    {
        return $user->checkPermissionTo('restore CommitmentRegion');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any CommitmentRegion');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, CommitmentRegion $commitmentregion): bool
    {
        return $user->checkPermissionTo('replicate CommitmentRegion');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder CommitmentRegion');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, CommitmentRegion $commitmentregion): bool
    {
        return $user->checkPermissionTo('force-delete CommitmentRegion');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any CommitmentRegion');
    }
}
