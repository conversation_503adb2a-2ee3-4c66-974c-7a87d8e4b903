name: Simet<PERSON><PERSON>

on:
  push:
    branches:
      - main
  
jobs:
  kementrian:
    name: Run on kementrian
    runs-on: [self-hosted, kementrian]  # Specify the kementrian runner

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3
        with:
          token: ${{ secrets.PAT }}  # Use PAT stored in GitHub secrets

      - name: Navigate to Work Directory and Pull Changes
        run: |
          cd /var/www/simethris.hortikultura.pertanian.go.id/simcore/
          php artisan optimize:clear
          git reset --hard
          git pull https://${{ secrets.PAT }}@github.com/BhreAstrajingga/simethrisfilament/ main
          php artisan optimize:clear
