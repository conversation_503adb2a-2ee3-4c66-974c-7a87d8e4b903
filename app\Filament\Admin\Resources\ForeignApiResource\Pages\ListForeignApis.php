<?php

namespace App\Filament\Admin\Resources\ForeignApiResource\Pages;

use App\Filament\Admin\Resources\ForeignApiResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListForeignApis extends ListRecords
{
    protected static string $resource = ForeignApiResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
