<?php

namespace App\Filament\Panel2024\Resources;

use App\Filament\Panel2024\Resources\DataRealisasi2024Resource\Pages;
use App\Filament\Panel2024\Resources\DataRealisasi2024Resource\RelationManagers;
use App\Models\DataRealisasi2024;
use Filament\Forms;
use Filament\Forms\Components\{Actions, DatePicker, Fieldset, Group, Placeholder, TextInput};
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class DataRealisasi2024Resource extends Resource
{
    protected static ?string $model = DataRealisasi2024::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                //
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDataRealisasi2024s::route('/'),
            'create' => Pages\CreateDataRealisasi2024::route('/create'),
            'view' => Pages\ViewDataRealisasi2024::route('/{record}'),
            'edit' => Pages\EditDataRealisasi2024::route('/{record}/edit'),
            'fototanam' => Pages\FotoTanam::route('/{record}/fototanam'),
            'fotoproduksi' => Pages\FotoProduksi::route('/{record}/fotoproduksi'),
        ];
    }

	public static function shouldRegisterNavigation(): bool
	{
		return false;
	}
}
