<?php

namespace App\Observers;

use App\Models\AjuVerifTanam2024;
use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AjuVerifTanamObserver
{
    /**
     * Handle the AjuVerifTanam2024 "created" event.
     */
    public function created(AjuVerifTanam2024 $ajuVerifTanam2024): void
    {
        $pusat = User::whereHas('roles', function ($query) {
			$query->whereIn('name', ['Super Admin', 'admin', 'verifikator']);
		})->get();

		$noIjin = $ajuVerifTanam2024->no_ijin;
		$periode = $ajuVerifTanam2024->commitment->periodetahun;
		$company = $ajuVerifTanam2024->commitment->user->datauser->company_name;
		$cleanNoIjin = str_replace(['/', '-','.'], '', $noIjin);

		Notification::make()
			->title('Verifikasi Tanam')

			->body("<p class='mb-3'><span class='text-info-500 font-bold'>{$company}</span> telah mengajukan verifikasi tanam Komitmen No.: <span class='text-info-500 font-bold'>{$noIjin}</span>, untuk periode {$periode}.</p>
			<p class='mb-3 mt-5'>Segera tindaklanjut.</p>")
			->sendToDatabase($pusat);
    }

    /**
     * Handle the AjuVerifTanam2024 "updated" event.
     */
    public function updated(AjuVerifTanam2024 $ajuVerifTanam2024): void
    {

		$noIjin = $ajuVerifTanam2024->no_ijin;
		$periode = $ajuVerifTanam2024->commitment->periodetahun;
        if ($ajuVerifTanam2024->wasChanged('status') && $ajuVerifTanam2024->status === '1') {
			$company = $ajuVerifTanam2024->commitment->user->datauser->company_name;
			$cleanNoIjin = str_replace(['/', '-','.'], '', $noIjin);
			
			$pusat = User::whereHas('roles', function ($query) {
				$query->whereIn('name', ['Super Admin', 'admin', 'verifikator']);
			})->get();

			Notification::make()
				->title('Verifikasi Tanam')

				->body("<p class='mb-3'><span class='text-info-500 font-bold'>{$company}</span> telah mengajukan verifikasi tanam Komitmen No.: <span class='text-info-500 font-bold'>{$noIjin}</span>, untuk periode {$periode}.</p>
				<p class='mb-3 mt-5'>Segera tindaklanjut.</p>")
				->sendToDatabase($pusat);
		}

        if ($ajuVerifTanam2024->wasChanged('status') && $ajuVerifTanam2024->status === '2') {
			$registrar = $ajuVerifTanam2024->commitment->user;
			$pusat = User::whereHas('roles', function ($query) {
				$query->whereIn('name', ['Super Admin', 'admin']);
			})->get();

			Notification::make()
				->title('Verifikasi Tanam')
				->body("<p class='mb-3'>Verifikasi tanam Komitmen No.: <span class='text-info-500 font-bold'>{$noIjin}</span> periode {$periode} telah dimulai.</p>")
				->sendToDatabase($pusat);
			Notification::make()
				->title('Verifikasi Tanam')
				->body("<p class='mb-3'>Verifikasi tanam Komitmen No.: <span class='text-info-500 font-bold'>{$noIjin}</span> periode {$periode} telah dimulai.</p>")
				->sendToDatabase($registrar);
		}

        if ($ajuVerifTanam2024->wasChanged('status') && $ajuVerifTanam2024->status === '3') {
			$registrar = $ajuVerifTanam2024->commitment->user;
			$pusat = User::whereHas('roles', function ($query) {
				$query->whereIn('name', ['Super Admin', 'admin']);
			})->get();

			Notification::make()
				->title('Verifikasi Tanam')
				->body("<p class='mb-3'>Verifikasi tanam Komitmen No.: <span class='text-danger-500 font-bold'>{$noIjin}</span> periode {$periode} telah berakhir.</p>
				<p class='mb-3 mt-5'>Segera perbaiki untuk kemudian ajukan verifikasi kembali.</p>")
				->sendToDatabase($registrar);

			Notification::make()
				->title('Verifikasi Tanam')
				->body("<p class='mb-3'>Verifikasi tanam Komitmen No.: <span class='text-info-500 font-bold'>{$noIjin}</span> periode {$periode} telah berakhir.</p>")
				->sendToDatabase($pusat);
		}

        if ($ajuVerifTanam2024->wasChanged('status') && $ajuVerifTanam2024->status === '4') {
			$registrar = $ajuVerifTanam2024->commitment->user;
			$pusat = User::whereHas('roles', function ($query) {
				$query->whereIn('name', ['Super Admin', 'admin']);
			})->get();

			Notification::make()
				->title('Verifikasi Tanam')
				->body("<p class='mb-3'>Verifikasi tanam Komitmen No.: <span class='text-success-500 font-bold'>{$noIjin}</span> periode {$periode} telah berakhir dengan status <span class='text-success-500 font-bold'>SELESAI</span>.</p>
				<p class='mb-3 mt-5'>Silahkan melanjutkan pelaporan realisasi produksi.</p>")
				->sendToDatabase($registrar);

			Notification::make()
				->title('Verifikasi Tanam')
				->body("<p class='mb-3'>Verifikasi tanam Komitmen No.: <span class='text-success-500 font-bold'>{$noIjin}</span> periode {$periode} telah berakhir.</p>")
				->sendToDatabase($pusat);
		}
    }

    /**
     * Handle the AjuVerifTanam2024 "deleted" event.
     */
    public function deleted(AjuVerifTanam2024 $ajuVerifTanam2024): void
    {
        //
    }

    /**
     * Handle the AjuVerifTanam2024 "restored" event.
     */
    public function restored(AjuVerifTanam2024 $ajuVerifTanam2024): void
    {
        //
    }

    /**
     * Handle the AjuVerifTanam2024 "force deleted" event.
     */
    public function forceDeleted(AjuVerifTanam2024 $ajuVerifTanam2024): void
    {
        //
    }
}
