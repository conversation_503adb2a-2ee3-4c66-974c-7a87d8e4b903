<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\MasterPoktan2024;
use App\Models\User;

class MasterPoktan2024Policy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any MasterPoktan2024');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, MasterPoktan2024 $masterpoktan2024): bool
    {
        return $user->checkPermissionTo('view MasterPoktan2024');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create MasterPoktan2024');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, MasterPoktan2024 $masterpoktan2024): bool
    {
        return $user->checkPermissionTo('update MasterPoktan2024');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, MasterPoktan2024 $masterpoktan2024): bool
    {
        return $user->checkPermissionTo('delete MasterPoktan2024');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any MasterPoktan2024');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, MasterPoktan2024 $masterpoktan2024): bool
    {
        return $user->checkPermissionTo('restore MasterPoktan2024');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any MasterPoktan2024');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, MasterPoktan2024 $masterpoktan2024): bool
    {
        return $user->checkPermissionTo('replicate MasterPoktan2024');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder MasterPoktan2024');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, MasterPoktan2024 $masterpoktan2024): bool
    {
        return $user->checkPermissionTo('force-delete MasterPoktan2024');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any MasterPoktan2024');
    }
}
