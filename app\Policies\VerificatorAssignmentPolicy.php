<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\VerificatorAssignment;
use App\Models\User;

class VerificatorAssignmentPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any VerificatorAssignment');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, VerificatorAssignment $verificatorassignment): bool
    {
        return $user->checkPermissionTo('view VerificatorAssignment');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create VerificatorAssignment');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, VerificatorAssignment $verificatorassignment): bool
    {
        return $user->checkPermissionTo('update VerificatorAssignment');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, VerificatorAssignment $verificatorassignment): bool
    {
        return $user->checkPermissionTo('delete VerificatorAssignment');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any VerificatorAssignment');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, VerificatorAssignment $verificatorassignment): bool
    {
        return $user->checkPermissionTo('restore VerificatorAssignment');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any VerificatorAssignment');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, VerificatorAssignment $verificatorassignment): bool
    {
        return $user->checkPermissionTo('replicate VerificatorAssignment');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder VerificatorAssignment');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, VerificatorAssignment $verificatorassignment): bool
    {
        return $user->checkPermissionTo('force-delete VerificatorAssignment');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any VerificatorAssignment');
    }
}
