<?php

namespace App\Filament\Admin\Pages;

use Filament\Pages\Page;
use League\CommonMark\Environment\Environment;
use League\CommonMark\Extension\GithubFlavoredMarkdownExtension;
use League\CommonMark\Extension\CommonMark\CommonMarkCoreExtension;
use League\CommonMark\MarkdownConverter;

class About extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-information-circle';
    protected static ?string $navigationLabel = 'Tentang Simethris';
    protected static ?string $title = 'Tentang Simethris (DRAFT)';
    protected static ?string $navigationGroup = 'Documentations';
    protected static ?int $navigationSort = 4;

    public static function shouldRegisterNavigation(): bool
    {
        return true; // Visible to all users
    }

    protected static string $view = 'filament.admin.pages.about';

    public function getViewData(): array
    {
        $markdownPath = base_path('ABOUT.md');

        if (!file_exists($markdownPath)) {
            return [
                'aboutHtml' => '<div class="text-red-500">About file not found. Please make sure the file exists at: ' . $markdownPath . '</div>',
            ];
        }

        $markdownContent = file_get_contents($markdownPath);

        if (empty($markdownContent)) {
            return [
                'aboutHtml' => '<div class="text-red-500">About file is empty.</div>',
            ];
        }

        // Create a new environment with GFM extension
        $environment = new Environment([
            'html_input' => 'strip',
            'allow_unsafe_links' => false,
        ]);

        // Add the CommonMark core extension and GFM extension
        $environment->addExtension(new CommonMarkCoreExtension());
        $environment->addExtension(new GithubFlavoredMarkdownExtension());

        // Create a new converter using the configured environment
        $converter = new MarkdownConverter($environment);

        // Convert the markdown to HTML
        $result = $converter->convert($markdownContent);
        $html = $result->getContent();

        return [
            'aboutHtml' => $html,
        ];
    }
}
