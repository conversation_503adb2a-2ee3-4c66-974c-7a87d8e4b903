<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\PostResource\Pages;
use App\Filament\Admin\Resources\PostResource\RelationManagers;
use App\Models\CmsCategory;
use App\Models\Post;
use App\Models\User;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Illuminate\Support\Str;
use Filament\Forms;
use Filament\Forms\Components\{DatePicker, DateTimePicker, Fieldset, FileUpload, Grid, Group, MarkdownEditor, Repeater, RichEditor, Section, Select, Textarea, TextInput, TimePicker, Toggle, ToggleButtons};
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Filament\Tables\Actions\{BulkActionGroup, DeleteAction, DeleteBulkAction, EditAction, ForceDeleteAction, ReplicateAction, RestoreAction, RestoreBulkAction, ViewAction};
use Filament\Tables\Columns\{ImageColumn,TextColumn,ToggleColumn};
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\{Builder,SoftDeletingScope};
use Illuminate\Support\Facades\{Auth, Storage};

class PostResource extends Resource
{
	protected static ?string $model = Post::class;

	// protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
	public static function getNavigationGroup(): ?string
	{
		return 'CMS';
	}

	public static function getPluralLabel(): ?string
	{
		return 'Daftar Artikel';
	}

	public static function getLabel(): ?string
	{
		return 'Artikel';
	}

	public static function getNavigationLabel(): string
	{
		return 'Artikel';
	}

	public static function form(Form $form): Form
	{
		return $form
			->schema([
				Grid::make([
					'default' => 1,
					'sm' => 1,
					'md' => 6,
					'lg' => 12,
				])
					->schema([
						Grid::make()
							->schema([
								Section::make('Detail Artikel')
									->description()
									->schema([
										TextInput::make('title')
											->label('Judul')
											->afterStateUpdated( function(Forms\Get $get, Forms\Set $set){
												if($get('type') !== 'open-source'){
													$set('slug', Str::of($get('title'))->replace(' ', '-')->lower()->toString());
												}
											})
											->lazy()
											->required(),
										TextInput::make('slug')
											->required()
											->readOnly()
											->maxLength(255),
										Textarea::make('short_description')
											->label('Deskripsi Singkat')
											->maxLength(255)
											->autosize(),
										RichEditor::make('body')
											->toolbarButtons([
												'blockquote',
												'bold',
												'bulletList',
												'codeBlock',
												'italic',
												'link',
												'orderedList',
												'redo',
												'strike',
												'table',
												'undo',
												'h2',
												'h3',
											])
											->columnSpanFull(),
									]),
							])
							->columnSpan([
								'sm' => 1,
								'md' => 4,
								'lg' => 8,
							]),
						Grid::make()
							->schema([
								Section::make('Pengaturan')
									->description('')
									->schema([
										Select::make('type')
											->label('Jenis Artikel')
											->live()
											->required()
											->options([
												'Artikel' => 'Artikel',
												'Berita' => 'Berita',
												'Event' => 'Event',
												'Halaman' => 'Halaman',
											]),
										Select::make('category_id')
											->label('Kategori')
											->hidden(fn($get)=> in_array($get('type'), ['Halaman']))
											->searchable()
											->required()
											->preload()
											->options(fn(Forms\Get $get)=> CmsCategory::where('for', $get('type'))->where('type', 'category')->pluck('name', 'id')->toArray()),
										Toggle::make('is_published')
											->label('Publish')
											->default(true),
										Toggle::make('is_trend')
											->hidden(fn(Forms\Get $get)=> in_array($get('type'), ['page', 'builder']))
											->label('Trending'),
										DateTimePicker::make('published_at')
											->hidden(fn(Forms\Get $get)=> in_array($get('type'), ['page', 'builder']))
											->label('Tanggal Publikasi')
											->default(now()->format('Y-m-d H:i:s')),

										Select::make('author_id')
											->searchable()
											->preload()
											->default(Auth::id())
											->required()
											->selectablePlaceholder(false)
											->options(
												User::whereHas('roles', function ($query) {
													$query->whereIn('name', ['admin', 'direktur', 'Super Admin']);
												})->pluck('name', 'id') // Ambil ID sebagai key dan nama sebagai value
											)
											->afterStateHydrated(function (Select $component, ?string $state) {
												if ($state === null) {
													$component->state(Auth::id());
												}
											}),
										
									]),
							Section::make('Post Image')
								->description('')
								->columns(2)
								->schema([
									FileUpload::make('feature_image')
										->openable()
										->maxSize(2048)
										->downloadable()
										->disk('public')
										->deletable()
										->imageEditor()
										->previewable(true)
										->columnSpan(1)
										->visibility('public')
										->imagePreviewHeight('250')
										->panelAspectRatio('1:1')
										->fetchFileInformation(true)
										->helperText('Maksimal 2MB, format gambar')
										->rules([
											'mimetypes:image/jpeg,image/png',
											'mimes:jpg,jpeg,png',
										])
										->validationMessages([
											'mimetypes' => 'Hanya file gambar (JPG, JPEG, PNG) yang diperbolehkan',
											'mimes' => 'Ekstensi file harus .jpg, .jpeg atau .png',
										])
										->directory('uploads/cmsimage/posts/features'),
										
									FileUpload::make('cover_image')
										->openable()
										->maxSize(2048)
										->downloadable()
										->deletable()
										->imageEditor()
										->disk('public')
										->previewable(true)
										->columnSpan(1)
										->visibility('public')
										->imagePreviewHeight('250')
										->panelAspectRatio('1:1')
										->fetchFileInformation(true)
										->helperText('Maksimal 2MB, format gambar')
										->rules([
											'mimetypes:image/jpeg,image/png',
											'mimes:jpg,jpeg,png',
										])
										->validationMessages([
											'mimetypes' => 'Hanya file gambar (JPG, JPEG, PNG) yang diperbolehkan',
											'mimes' => 'Ekstensi file harus .jpg, .jpeg atau .png',
										])
										->directory('uploads/posts/cover'),
								]),
							])
							->columnSpan([
								'sm' => 1,
								'md' => 2,
								'lg' => 4,
							]),
						
						Section::make('Detail Kegiatan')
							->description()
							->reactive()
							->visible(fn ($get) => $get('type') === 'Event')
							->schema([
								Textarea::make('venue')->autosize(),
								Group::make()
									->columns(2)
									->schema([
										DatePicker::make('date_start')
										->reactive(),
										DatePicker::make('date_end')->minDate(fn ($get) => $get('date_start'))
										->reactive(),
									]),
								Repeater::make('pembicara')
									->columns(8)
									->relationship('eventSpeaker')
									->schema([
										FileUpload::make('avatar_url')
											->openable()
											->maxSize(2048)
											->downloadable()
											->deletable()
											->imageEditor()
											->disk('public')
											->previewable(true)
											->columnSpan(2)
											->visibility('public')
											->imagePreviewHeight('250')
											->panelAspectRatio('1:1')
											->fetchFileInformation(true)
											->helperText('Maksimal 2MB, format gambar')
											->rules([
												'mimetypes:image/jpeg,image/png',
												'mimes:jpg,jpeg,png',
											])
											->validationMessages([
												'mimetypes' => 'Hanya file gambar (JPG, JPEG, PNG) yang diperbolehkan',
												'mimes' => 'Ekstensi file harus .jpg, .jpeg atau .png',
											])
											->directory('uploads/cmsimage/posts/events/speaker'),
										Group::make()
											->columnSpan(6)
											->schema([
												TextInput::make('speaker')
													->label('Pembicara')
													->live()
													->lazy(),
												TextInput::make('profesi'),
											])
									]),

								TableRepeater::make('Jadwal Kegiatan')
									->addActionAlignment(Alignment::End)
									->relationship('eventSchedule')
									->headers([
										Header::make('Tanggal')->markAsRequired(),
										Header::make('Jam')->markAsRequired(),
										Header::make('Judul/Materi')->markAsRequired(),
										Header::make('Deskripsi'),
										Header::make('Pembicara'),
									])
									->schema([
										DatePicker::make('scheduled_date')
											->minDate(fn ($get) => $get('../../date_start')) //get parent date)start
											->maxDate(fn ($get) => $get('../../date_end'))
											->closeOnDateSelection()
											->reactive(),
										TimePicker::make('scheduled_time'),
										TextInput::make('title'),
										TextInput::make('description')->maxLength(255),
										Select::make('keynote')
											->options(fn ($get) => 
												collect($get('../../pembicara') ?: [])
													->mapWithKeys(fn ($item) => [
														$item['speaker'] ?? 'unknown' => $item['speaker'] ?: 'Tidak Ada Pembicara'
													])
													->toArray()
											)									
									])
							])
					])

			]);
	}

	public static function table(Table $table): Table
	{
		return $table
			->columns([
				ImageColumn::make('cover_image')
					->circular()
					->defaultImageUrl(Storage::url('resources/svg/person-circle.svg')),
				TextColumn::make('title')
					->label('Judul')
					->lineClamp(2)
					->wrap()
					->limit(30)
					// ->width('25%')
					->searchable(),
				TextColumn::make('type')
					->badge()
					->sortable()
					->label('Jenis')
					->searchable(),
				TextColumn::make('category.name')
					->badge()
					->color(fn ($record) => $record->category->color)
					->sortable()
					->label('Category')
					->searchable(),
				ToggleColumn::make('is_published')
					->visible(fn () => Auth::user()->hasAnyRole(['admin', 'Super Admin']))
					->sortable(),
				ToggleColumn::make('is_trend')
					->visible(fn () => Auth::user()->hasAnyRole(['admin', 'Super Admin']))
					->sortable(),
				TextColumn::make('published_at')
					->date()
					->sortable(),
				TextColumn::make('created_at')
					->dateTime()
					->sortable()
					->toggleable(isToggledHiddenByDefault: true),
				TextColumn::make('updated_at')
					->dateTime()
					->sortable()
					->toggleable(isToggledHiddenByDefault: true),
			])
			->filters([
				SelectFilter::make('type')
					->options([
						'Artikel' => 'Artikel',
						'Berita' => 'Berita',
						'Event' => 'Event',
						'Halaman' => 'Halaman',
					])
			])
			->actions([
				ViewAction::make()
					->iconButton(),
				EditAction::make()
					->iconButton(),
				DeleteAction::make()
					->iconButton(),
				ForceDeleteAction::make()
					->iconButton(),
				RestoreAction::make()
					->iconButton(),
			])
			->bulkActions([
				BulkActionGroup::make([
					DeleteBulkAction::make(),
					// ForceDeleteAction::make(),
					RestoreBulkAction::make(),
				]),
			]);
	}

	protected function paginateTableQuery(Builder $query): Paginator
	{
		return $query->simplePaginate(($this->getTableRecordsPerPage() === 'all') ? $query->count() : $this->getTableRecordsPerPage());
	}

	public static function getRelations(): array
	{
		return [
			//
		];
	}

	public static function getPages(): array
	{
		return [
			'index' => Pages\ListPosts::route('/'),
			'create' => Pages\CreatePost::route('/create'),
			'view' => Pages\ViewPost::route('/{record}'),
			'edit' => Pages\EditPost::route('/{record}/edit'),
		];
	}
}
