<?php

namespace App\Filament\Panel2025\Resources;

use App\Filament\Panel2025\Resources\Realisasi2025Resource\Pages;
use App\Filament\Panel2025\Resources\Realisasi2025Resource\RelationManagers;
use App\Models\Realisasi2025;
use App\Models\Varietas;
use Filament\Forms;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\{Action as TableAction, EditAction, ViewAction};
use Filament\Tables\Columns\ColumnGroup;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Grouping\Group as GroupingGroup;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

use Filament\Infolists;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Infolist;

class Realisasi2025Resource extends Resource
{
	protected static ?string $model = Realisasi2025::class;

	protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

	public static function form(Form $form): Form
	{
		return $form
			->schema([
				Grid::make(3)
					->schema([
						Section::make()
							->columnSpan([
								'sm' => 3,
								'md' => 1,
							])
							->schema([
								Placeholder::make('Status Verifikasi')
									->inlineLabel()
									->extraAttributes(fn($record) => [
										'class' => match ($record->status) {
											0 => 'text-danger-500',
											1 => 'text-success-500',
											default => 'text-gray-500',
										},
									])
									->content(fn($record) => new HtmlString(match ($record->status) {
										null => 'Belum di-verifikasi',
										0 => 'Tidak sesuai',
										1 => 'Sesuai',
									})),
								Radio::make('status')
									->label('Status Verifikasi')
									->options([
										1 => 'Sesuai',
										0 => 'Tidak Sesuai'
									])
									->descriptions([
										0 => 'Jika data "Tidak Sesuai" dengan hasil pemeriksaan administrasi dan lapangan',
										1 => 'Jika data "Sesuai" dengan hasil pemeriksaan administrasi dan lapangan'
									]),
								DatePicker::make('verif_t_At')
									->label('Verifikasi Tanam pada')
									->closeOnDateSelection(),
								DatePicker::make('verif_p_At')
									->label('Verifikasi Produksi pada')
									->closeOnDateSelection(),
								TextInput::make('is_selected')
									->required()
									->numeric()
									->default(0),
							]),
						Group::make()
							->columnSpan([
								'sm' => 3,
								'md' => 2,
							])
							->schema([
								Group::make()
									->columns(2)
									->columnSpanFull()
									->schema([
										TextInput::make('periode_tanam')
											->maxLength(255)
											->columnSpan(1)
											->helperText('Jadwal/Periode tanam, contoh: Januari - Agustus'),
										Select::make('varietas_tanam')
											->label('Varietas')
											->columnSpan(1)
											->required()
											->options(fn() => Varietas::orderBy('nama_varietas')->pluck('nama_varietas', 'id'))
											->preload(),
									]),
								Section::make('Persiapan Lahan')
									->description('Lengkapi data untuk melanjutkan ke bagian Pelaporan Kegiatan Persiapan Benih')
									->collapsed()
									->schema([
										Group::make()
											->columnSpan(1)
											->schema([
												FileUpload::make('lahanfoto')
													->openable()
													->required()
													->moveFiles()
													->maxSize(2048)
													->downloadable()
													->deletable()
													->disk('public')
													->previewable(true)
													->visibility('public')
													->label('Foto Kegiatan')
													->imagePreviewHeight('250')
													->panelAspectRatio('1:1')
													->fetchFileInformation(true)
													->helperText('Maksimal 2MB, format gambar')
													->directory(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
														return "uploads/{$cleanNpwp}/{$cleanNoIjin}/pks/foto/";
													})
													->rules([
														'mimetypes:image/jpeg,image/png,image/gif', // Format gambar yang diizinkan
														'mimes:jpg,jpeg,png,gif', // Ekstensi file yang diizinkan
													])
													->validationMessages([
														'mimetypes' => 'Hanya file gambar (JPEG, PNG, GIF) yang diperbolehkan',
														'mimes' => 'Ekstensi file harus .jpg, .jpeg, .png, atau .gif',
													])
													->getUploadedFileNameForStorageUsing(
														function (TemporaryUploadedFile $file, $get, $record): string {
															$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
															$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

															// Format nama file: [ID]_[NPWP]_[NOIJIN]_[UNIQID].[ext]
															return 'persiapanlahan_' . $record->id . '_' . $cleanNpwp . '_' . $cleanNoIjin . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
														}
													),
											]),
										Group::make()
											->columnSpan(2)
											->columnStart(2)
											->schema([
												Placeholder::make('Status Verifikasi')
													->inlineLabel()
													->extraAttributes(fn($record) => [
														'class' => match ($record->lahanStatus) {
															0 => 'text-danger-500',
															1 => 'text-success-500',
															default => 'text-gray-500',
														},
													])
													->content(fn($record) => new HtmlString(match ($record->lahanStatus) {
														null => 'Belum di-verifikasi',
														0 => 'Tidak sesuai',
														1 => 'Sesuai',
													})),
												DatePicker::make('lahandate')
													->label('Tanggal Persiapan')
													->required()
													->closeOnDateSelection(),
												Textarea::make('lahancomment')
													->label('Catatan Kegiatan'),
												Radio::make('lahanStatus')
													->label('Status Verifikasi')
													->inline()
													->columns(2)
													->options([
														1 => 'Sesuai',
														0 => 'Tidak Sesuai'
													]),
											]),
									])->columns(3),
								Section::make('Persiapan Benih')
									->description('Lengkapi data untuk melanjutkan ke bagian Pelaporan Kegiatan Penanaman')
									->hidden(fn($record) => is_null($record->lahanfoto))
									->collapsed()
									->columns(3)
									->schema([
										Group::make()
											->columnSpan(1)
											->schema([
												FileUpload::make('benihFoto')
													->openable()
													->required()
													->maxSize(2048)
													->downloadable()
													->deletable()
													->disk('public')
													->previewable(true)
													->visibility('public')
													->label('Foto Kegiatan')
													->imagePreviewHeight('250')
													->moveFiles()
													->panelAspectRatio('1:1')
													->fetchFileInformation(true)
													->helperText('Maksimal 2MB, format gambar')
													->directory(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
														return "uploads/{$cleanNpwp}/{$cleanNoIjin}/pks/foto/";
													})
													->rules([
														'mimetypes:image/jpeg,image/png,image/gif', // Format gambar yang diizinkan
														'mimes:jpg,jpeg,png,gif', // Ekstensi file yang diizinkan
													])
													->validationMessages([
														'mimetypes' => 'Hanya file gambar (JPEG, PNG, GIF) yang diperbolehkan',
														'mimes' => 'Ekstensi file harus .jpg, .jpeg, .png, atau .gif',
													])
													->getUploadedFileNameForStorageUsing(
														function (TemporaryUploadedFile $file, $get, $record): string {
															$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
															$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

															// Format nama file: [ID]_[NPWP]_[NOIJIN]_[UNIQID].[ext]
															return 'persiapanbenih_' . $record->id . '_' . $cleanNpwp . '_' . $cleanNoIjin . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
														}
													),
											]),
										Group::make()
											->columnSpan(2)
											->schema([
												Placeholder::make('Status Verifikasi')
													->inlineLabel()
													->extraAttributes(fn($record) => [
														'class' => match ($record->benihStatus) {
															0 => 'text-danger-500',
															1 => 'text-success-500',
															default => 'text-gray-500',
														},
													])
													->content(fn($record) => new HtmlString(match ($record->benihStatus) {
														null => 'Belum di-verifikasi',
														0 => 'Tidak sesuai',
														1 => 'Sesuai',
													})),
												DatePicker::make('benihDate')
													->label('Tanggal Pelaksanaan')
													->required()
													->closeOnDateSelection(),
												TextInput::make('benihsize')
													->label('Volume Benih')
													->suffix('Kg')
													->minValue(0)
													->numeric(),
												Textarea::make('benihComment')
													->label('Catatan Kegiatan'),
												Radio::make('benihStatus')
													->label('Status Verifikasi')
													->inline()
													->columns(2)
													->options([
														1 => 'Sesuai',
														0 => 'Tidak Sesuai'
													]),
											]),
									]),
								Section::make('Penanaman')
									->description('Lengkapi data untuk melanjutkan ke bagian Pelaporan Pemasangan Mulsa')
									->hidden(fn($record) => is_null($record->benihFoto))
									->collapsed()
									->columns(3)
									->schema([
										Group::make()
											->columnSpan(1)
											->schema([
												FileUpload::make('tanamFoto')
													->openable()
													->required()
													->moveFiles()
													->maxSize(2048)
													->downloadable()
													->deletable()
													->disk('public')
													->previewable(true)
													->visibility('public')
													->label('Foto Kegiatan')
													->imagePreviewHeight('250')
													->panelAspectRatio('1:1')
													->fetchFileInformation(true)
													->helperText('Maksimal 2MB, format gambar')
													->directory(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
														return "uploads/{$cleanNpwp}/{$cleanNoIjin}/pks/foto/";
													})
													->rules([
														'mimetypes:image/jpeg,image/png,image/gif', // Format gambar yang diizinkan
														'mimes:jpg,jpeg,png,gif', // Ekstensi file yang diizinkan
													])
													->validationMessages([
														'mimetypes' => 'Hanya file gambar (JPEG, PNG, GIF) yang diperbolehkan',
														'mimes' => 'Ekstensi file harus .jpg, .jpeg, .png, atau .gif',
													])
													->getUploadedFileNameForStorageUsing(
														function (TemporaryUploadedFile $file, $get, $record): string {
															$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
															$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

															// Format nama file: [ID]_[NPWP]_[NOIJIN]_[UNIQID].[ext]
															return 'pertanaman_' . $record->id . '_' . $cleanNpwp . '_' . $cleanNoIjin . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
														}
													),
											]),
										Group::make()
											->columnSpan(2)
											->schema([
												Placeholder::make('Status Verifikasi')
													->inlineLabel()
													->extraAttributes(fn($record) => [
														'class' => match ($record->tanamStatus) {
															0 => 'text-danger-500',
															1 => 'text-success-500',
															default => 'text-gray-500',
														},
													])
													->content(fn($record) => new HtmlString(match ($record->tanamStatus) {
														null => 'Belum di-verifikasi',
														0 => 'Tidak sesuai',
														1 => 'Sesuai',
													})),
												DatePicker::make('tgl_tanam')
													->label('Tanggal Pelaksanaan')
													->closeOnDateSelection(),
												TextInput::make('luas_tanam')
													->label('Luas ditanam')
													->minValue(0)
													->suffix('m2')
													->numeric()
													->helperText('luas dalam satuan meterpersegi'),
												Textarea::make('tanamComment')
													->label('Catatan Kegiatan'),
												Radio::make('tanamStatus')
													->label('Status Verifikasi')
													->inline()
													->columns(2)
													->options([
														1 => 'Sesuai',
														0 => 'Tidak Sesuai'
													]),
											]),
									]),
								Section::make('Pemasangan Mulsa')
									->description('Lengkapi data untuk melanjutkan ke bagian Pelaporan Kegiatan Pemupukan Pertama')
									->hidden(fn($record) => is_null($record->tanamFoto))
									->collapsed()
									->columns(3)
									->schema([
										Group::make()
											->columnSpan(1)
											->schema([
												FileUpload::make('mulsaFoto')
													->openable()
													->required()
													->moveFiles()
													->maxSize(2048)
													->downloadable()
													->deletable()
													->disk('public')
													->previewable(true)
													->visibility('public')
													->label('Foto Kegiatan')
													->imagePreviewHeight('250')
													->panelAspectRatio('1:1')
													->fetchFileInformation(true)
													->helperText('Maksimal 2MB, format gambar')
													->directory(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
														return "uploads/{$cleanNpwp}/{$cleanNoIjin}/pks/foto/";
													})
													->rules([
														'mimetypes:image/jpeg,image/png,image/gif', // Format gambar yang diizinkan
														'mimes:jpg,jpeg,png,gif', // Ekstensi file yang diizinkan
													])
													->validationMessages([
														'mimetypes' => 'Hanya file gambar (JPEG, PNG, GIF) yang diperbolehkan',
														'mimes' => 'Ekstensi file harus .jpg, .jpeg, .png, atau .gif',
													])
													->getUploadedFileNameForStorageUsing(
														function (TemporaryUploadedFile $file, $get, $record): string {
															$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
															$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

															// Format nama file: [ID]_[NPWP]_[NOIJIN]_[UNIQID].[ext]
															return 'mulsa_' . $record->id . '_' . $cleanNpwp . '_' . $cleanNoIjin . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
														}
													),
											]),
										Group::make()
											->columnSpan(2)
											->schema([
												Placeholder::make('Status Verifikasi')
													->inlineLabel()
													->extraAttributes(fn($record) => [
														'class' => match ($record->mulsaStatus) {
															0 => 'text-danger-500',
															1 => 'text-success-500',
															default => 'text-gray-500',
														},
													])
													->content(fn($record) => new HtmlString(match ($record->mulsaStatus) {
														null => 'Belum di-verifikasi',
														0 => 'Tidak sesuai',
														1 => 'Sesuai',
													})),
												DatePicker::make('mulsaDate')
													->label('Tanggal Kegiatan')
													->closeOnDateSelection(),
												TextInput::make('mulsaSize')
													->label('Volume Mulsa')
													->suffix('roll')
													->helperText('dalam satuan roll')
													->minValue(0)
													->numeric(),
												Textarea::make('mulsaComment')
													->label('Catatan Kegiatan'),
												Radio::make('mulsaStatus')
													->label('Status Verifikasi')
													->inline()
													->columns(2)
													->options([
														1 => 'Sesuai',
														0 => 'Tidak Sesuai'
													]),
											]),
									]),
								Section::make('Pemupukan Pertama')
									->hidden(fn($record) => is_null($record->mulsaFoto))
									->collapsed()
									->columns(3)
									->schema([
										Group::make()
											->columnSpan(1)
											->schema([
												FileUpload::make('pupuk1Foto')
													->openable()
													->required()
													->moveFiles()
													->maxSize(2048)
													->downloadable()
													->deletable()
													->disk('public')
													->previewable(true)
													->visibility('public')
													->label('Foto Kegiatan')
													->imagePreviewHeight('250')
													->panelAspectRatio('1:1')
													->fetchFileInformation(true)
													->helperText('Maksimal 2MB, format gambar')
													->directory(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
														return "uploads/{$cleanNpwp}/{$cleanNoIjin}/pks/foto/";
													})
													->rules([
														'mimetypes:image/jpeg,image/png,image/gif',
														'mimes:jpg,jpeg,png,gif',
													])
													->validationMessages([
														'mimetypes' => 'Hanya file gambar (JPEG, PNG, GIF) yang diperbolehkan',
														'mimes' => 'Ekstensi file harus .jpg, .jpeg, .png, atau .gif',
													])
													->getUploadedFileNameForStorageUsing(
														function (TemporaryUploadedFile $file, $get, $record): string {
															$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
															$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

															// Format nama file: [ID]_[NPWP]_[NOIJIN]_[UNIQID].[ext]
															return 'pemupukanpertama_' . $record->id . '_' . $cleanNpwp . '_' . $cleanNoIjin . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
														}
													),
											]),
										Group::make()
											->columnSpan(2)
											->schema([
												Placeholder::make('Status Verifikasi')
													->inlineLabel()
													->extraAttributes(fn($record) => [
														'class' => match ($record->pupuk1Status) {
															0 => 'text-danger-500',
															1 => 'text-success-500',
															default => 'text-gray-500',
														},
													])
													->content(fn($record) => new HtmlString(match ($record->pupuk1Status) {
														null => 'Belum di-verifikasi',
														0 => 'Tidak sesuai',
														1 => 'Sesuai',
													})),
												DatePicker::make('pupuk1Date')
													->label('Tanggal Kegiatan')
													->required()
													->closeOnDateSelection(),
												TextInput::make('organik1')
													->suffix('ton')
													->minValue(0)
													->label('Pupuk Organik')
													->helperText('Pupuk organik yang digunakan pada kegiatan pertama. Dalam satuan ton')
													->numeric(),
												TextInput::make('npk1')
													->suffix('ton')
													->minValue(0)
													->label('NPK')
													->helperText('NPK (Nitrogen, Phosphorus, Kalium) yang digunakan pada kegiatan pertama. Dalam satuan ton')
													->numeric(),
												TextInput::make('dolomit1')
													->suffix('ton')
													->minValue(0)
													->label('Dolomit')
													->helperText('Kapur Dolomit yang digunakan pada kegiatan pertama. Dalam satuan ton')
													->numeric(),
												TextInput::make('za1')
													->suffix('ton')
													->minValue(0)
													->label('ZA')
													->helperText('Zwavelzuur Ammonium yang digunakan pada kegiatan pertama. Dalam satuan ton')
													->numeric(),
												Textarea::make('pupuk1Comment')
													->label('Catatan Kegiatan'),
												Radio::make('pupuk1Status')
													->label('Status Verifikasi')
													->inline()
													->columns(2)
													->options([
														1 => 'Sesuai',
														0 => 'Tidak Sesuai'
													]),
											]),
									]),
								Section::make('Pemupukan Kedua')
									->description('Lengkapi data untuk melanjutkan ke bagian Pelaporan Kegiatan Pemupukan Ketiga')
									->hidden(fn($record) => is_null($record->pupuk1Foto))
									->collapsed()
									->columns(3)
									->schema([
										Group::make()
											->columnSpan(1)
											->schema([
												FileUpload::make('pupuk2Foto')
													->openable()
													->required()
													->moveFiles()
													->maxSize(2048)
													->downloadable()
													->deletable()
													->disk('public')
													->previewable(true)
													->visibility('public')
													->label('Foto Kegiatan')
													->imagePreviewHeight('250')
													->panelAspectRatio('1:1')
													->fetchFileInformation(true)
													->helperText('Maksimal 2MB, format gambar')
													->directory(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
														return "uploads/{$cleanNpwp}/{$cleanNoIjin}/pks/foto/";
													})
													->rules([
														'mimetypes:image/jpeg,image/png,image/gif',
														'mimes:jpg,jpeg,png,gif',
													])
													->validationMessages([
														'mimetypes' => 'Hanya file gambar (JPEG, PNG, GIF) yang diperbolehkan',
														'mimes' => 'Ekstensi file harus .jpg, .jpeg, .png, atau .gif',
													])
													->getUploadedFileNameForStorageUsing(
														function (TemporaryUploadedFile $file, $get, $record): string {
															$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
															$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

															// Format nama file: [ID]_[NPWP]_[NOIJIN]_[UNIQID].[ext]
															return 'pemupukankedua_' . $record->id . '_' . $cleanNpwp . '_' . $cleanNoIjin . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
														}
													),
											]),
										Group::make()
											->columnSpan(2)
											->schema([
												Placeholder::make('Status Verifikasi')
													->inlineLabel()
													->extraAttributes(fn($record) => [
														'class' => match ($record->pupuk2Status) {
															0 => 'text-danger-500',
															1 => 'text-success-500',
															default => 'text-gray-500',
														},
													])
													->content(fn($record) => new HtmlString(match ($record->pupuk2Status) {
														null => 'Belum di-verifikasi',
														0 => 'Tidak sesuai',
														1 => 'Sesuai',
													})),
												DatePicker::make('pupuk2Date')
													->label('Tanggal Kegiatan')
													->required()
													->closeOnDateSelection(),
												TextInput::make('organik2')
													->suffix('ton')
													->minValue(0)
													->label('Pupuk Organik')
													->helperText('Pupuk organik yang digunakan pada kegiatan kedua. Dalam satuan ton')
													->numeric(),
												TextInput::make('npk2')
													->suffix('ton')
													->minValue(0)
													->label('NPK')
													->helperText('NPK (Nitrogen, Phosphorus, Kalium) yang digunakan pada kegiatan kedua. Dalam satuan ton')
													->numeric(),
												TextInput::make('dolomit2')
													->suffix('ton')
													->minValue(0)
													->label('Dolomit')
													->helperText('Kapur Dolomit yang digunakan pada kegiatan kedua. Dalam satuan ton')
													->numeric(),
												TextInput::make('za2')
													->suffix('ton')
													->minValue(0)
													->label('ZA')
													->helperText('Zwavelzuur Ammonium yang digunakan pada kegiatan kedua. Dalam satuan ton')
													->numeric(),
												Textarea::make('pupuk2Comment')
													->label('Catatan Kegiatan'),
												Radio::make('pupuk2Status')
													->label('Status Verifikasi')
													->inline()
													->columns(2)
													->options([
														1 => 'Sesuai',
														0 => 'Tidak Sesuai'
													]),
											]),
									]),
								Section::make('Pemupukan Ketiga')
									->description('Lengkapi data untuk melanjutkan ke bagian Pelaporan Kegiatan Panen/Produksi')
									->hidden(fn($record) => is_null($record->pupuk2Foto))
									->collapsed()
									->columns(3)
									->schema([
										Group::make()
											->columnSpan(1)
											->schema([
												FileUpload::make('pupuk3Foto')
													->openable()
													->required()
													->moveFiles()
													->maxSize(2048)
													->downloadable()
													->deletable()
													->disk('public')
													->previewable(true)
													->visibility('public')
													->label('Foto Kegiatan')
													->imagePreviewHeight('250')
													->panelAspectRatio('1:1')
													->fetchFileInformation(true)
													->helperText('Maksimal 2MB, format gambar')
													->directory(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
														return "uploads/{$cleanNpwp}/{$cleanNoIjin}/pks/foto/";
													})
													->rules([
														'mimetypes:image/jpeg,image/png,image/gif',
														'mimes:jpg,jpeg,png,gif',
													])
													->validationMessages([
														'mimetypes' => 'Hanya file gambar (JPEG, PNG, GIF) yang diperbolehkan',
														'mimes' => 'Ekstensi file harus .jpg, .jpeg, .png, atau .gif',
													])
													->getUploadedFileNameForStorageUsing(
														function (TemporaryUploadedFile $file, $get, $record): string {
															$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
															$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

															// Format nama file: [ID]_[NPWP]_[NOIJIN]_[UNIQID].[ext]
															return 'pemupukanketiga_' . $record->id . '_' . $cleanNpwp . '_' . $cleanNoIjin . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
														}
													),
											]),
										Group::make()
											->columnSpan(2)
											->schema([
												Placeholder::make('Status Verifikasi')
													->inlineLabel()
													->extraAttributes(fn($record) => [
														'class' => match ($record->pupuk3Status) {
															0 => 'text-danger-500',
															1 => 'text-success-500',
															default => 'text-gray-500',
														},
													])
													->content(fn($record) => new HtmlString(match ($record->pupuk3Status) {
														null => 'Belum di-verifikasi',
														0 => 'Tidak sesuai',
														1 => 'Sesuai',
													})),
												DatePicker::make('pupuk3Date')
													->label('Tanggal Kegiatan')
													->required()
													->closeOnDateSelection(),
												TextInput::make('organik3')
													->suffix('ton')
													->minValue(0)
													->label('Pupuk Organik')
													->helperText('Pupuk organik yang digunakan pada kegiatan ketiga. Dalam satuan ton')
													->numeric(),
												TextInput::make('npk3')
													->suffix('ton')
													->minValue(0)
													->label('NPK')
													->helperText('NPK (Nitrogen, Phosphorus, Kalium) yang digunakan pada kegiatan ketiga. Dalam satuan ton')
													->numeric(),
												TextInput::make('dolomit3')
													->suffix('ton')
													->minValue(0)
													->label('Dolomit')
													->helperText('Kapur Dolomit yang digunakan pada kegiatan ketiga. Dalam satuan ton')
													->numeric(),
												TextInput::make('za3')
													->suffix('ton')
													->minValue(0)
													->label('ZA')
													->helperText('Zwavelzuur Ammonium yang digunakan pada kegiatan ketiga. Dalam satuan ton')
													->numeric(),
												Textarea::make('pupuk3Comment')
													->label('Catatan Kegiatan'),
												Radio::make('pupuk3Status')
													->label('Status Verifikasi')
													->inline()
													->columns(2)
													->options([
														1 => 'Sesuai',
														0 => 'Tidak Sesuai'
													]),
											]),
									]),
								Section::make('Temuan OPT')
									->description('Lengkapi data ini jika ditemukan serangan OPT')
									->collapsed()
									->columns(3)
									->schema([
										Group::make()
											->columnSpan(1)
											->schema([
												FileUpload::make('optFoto')
													->openable()
													->moveFiles()
													->maxSize(2048)
													->downloadable()
													->deletable()
													->disk('public')
													->previewable(true)
													->visibility('public')
													->label('Foto Kegiatan')
													->imagePreviewHeight('250')
													->panelAspectRatio('1:1')
													->fetchFileInformation(true)
													->helperText('Maksimal 2MB, format gambar')
													->directory(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
														return "uploads/{$cleanNpwp}/{$cleanNoIjin}/pks/foto/";
													})
													->rules([
														'mimetypes:image/jpeg,image/png,image/gif',
														'mimes:jpg,jpeg,png,gif',
													])
													->validationMessages([
														'mimetypes' => 'Hanya file gambar (JPEG, PNG, GIF) yang diperbolehkan',
														'mimes' => 'Ekstensi file harus .jpg, .jpeg, .png, atau .gif',
													])
													->getUploadedFileNameForStorageUsing(
														function (TemporaryUploadedFile $file, $get, $record): string {
															$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
															$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

															// Format nama file: [ID]_[NPWP]_[NOIJIN].[ext]
															return 'temuanopt_' . $record->id . '_' . $cleanNpwp . '_' . $cleanNoIjin . '.' . $file->getClientOriginalExtension();
														}
													),
											]),
										Group::make()
											->columnSpan(2)
											->schema([
												Placeholder::make('Status Verifikasi')
													->inlineLabel()
													->extraAttributes(fn($record) => [
														'class' => match ($record->optStatus) {
															0 => 'text-danger-500',
															1 => 'text-success-500',
															default => 'text-gray-500',
														},
													])
													->content(fn($record) => new HtmlString(match ($record->optStatus) {
														null => 'Belum di-verifikasi',
														0 => 'Tidak sesuai',
														1 => 'Sesuai',
													})),
												DatePicker::make('optDate')
													->label('Tanggal Temuan')
													->closeOnDateSelection(),
												Textarea::make('optComment')
													->label('Catatan Temuan OPT')
													->helperText('Serangan, Ciri-ciri, dan lain-lain'),
												Radio::make('optStatus')
													->label('Status Verifikasi')
													->inline()
													->columns(2)
													->options([
														1 => 'Sesuai',
														0 => 'Tidak Sesuai'
													]),
											]),
									]),
								Section::make('Panen/Produksi')
									->description('Lengkapi data untuk melanjutkan ke bagian Pelaporan Kegiatan Pendistribusian Hasil Panen')
									->hidden(fn($record) => is_null($record->pupuk3Foto))
									->collapsed()
									->columns(3)
									->schema([
										Group::make()
											->columnSpan(1)
											->schema([
												FileUpload::make('prodFoto')
													->openable()
													->required()
													->moveFiles()
													->maxSize(2048)
													->downloadable()
													->deletable()
													->disk('public')
													->previewable(true)
													->visibility('public')
													->label('Foto Kegiatan')
													->imagePreviewHeight('250')
													->panelAspectRatio('1:1')
													->fetchFileInformation(true)
													->helperText('Maksimal 2MB, format gambar')
													->directory(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
														return "uploads/{$cleanNpwp}/{$cleanNoIjin}/pks/foto/";
													})
													->rules([
														'mimetypes:image/jpeg,image/png,image/gif',
														'mimes:jpg,jpeg,png,gif',
													])
													->validationMessages([
														'mimetypes' => 'Hanya file gambar (JPEG, PNG, GIF) yang diperbolehkan',
														'mimes' => 'Ekstensi file harus .jpg, .jpeg, .png, atau .gif',
													])
													->getUploadedFileNameForStorageUsing(
														function (TemporaryUploadedFile $file, $get, $record): string {
															$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
															$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

															// Format nama file: [ID]_[NPWP]_[NOIJIN]_[UNIQID].[ext]
															return 'panen_' . $record->id . '_' . $cleanNpwp . '_' . $cleanNoIjin . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
														}
													),
											]),
										Group::make()
											->columnSpan(2)
											->schema([
												Placeholder::make('Status Verifikasi')
													->inlineLabel()
													->extraAttributes(fn($record) => [
														'class' => match ($record->prodStatus) {
															0 => 'text-danger-500',
															1 => 'text-success-500',
															default => 'text-gray-500',
														},
													])
													->content(fn($record) => new HtmlString(match ($record->prodStatus) {
														null => 'Belum di-verifikasi',
														0 => 'Tidak sesuai',
														1 => 'Sesuai',
													})),
												DatePicker::make('tgl_panen')
													->label('Tanggal Kegiatan')
													->closeOnDateSelection(),
												TextInput::make('volume')
													->suffix('ton')
													->minValue(0)
													->label('Volume Panen')
													->helperText('Jumlah Panen pada lokasi/petak ini. Dalam satuan ton')
													->numeric(),
												Textarea::make('prodComment')
													->label('Catatan Kegiatan'),
												Radio::make('prodStatus')
													->label('Status Verifikasi')
													->inline()
													->columns(2)
													->options([
														1 => 'Sesuai',
														0 => 'Tidak Sesuai'
													]),
											])
									]),
								Section::make('Distribusi Hasil')
									->description('Akhir Pelaporan Kegiatan Realisasi Tanam-Produksi di Lokasi')
									->hidden(fn($record) => is_null($record->prodFoto))
									->collapsed()
									->columns(3)
									->schema([
										Group::make()
											->columnSpan(1)
											->schema([
												FileUpload::make('distFoto')
													->openable()
													->required()
													->moveFiles()
													->maxSize(2048)
													->downloadable()
													->deletable()
													->disk('public')
													->previewable(true)
													->visibility('public')
													->label('Foto Kegiatan')
													->imagePreviewHeight('250')
													->panelAspectRatio('1:1')
													->fetchFileInformation(true)
													->helperText('Maksimal 2MB, format gambar')
													->directory(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
														return "uploads/{$cleanNpwp}/{$cleanNoIjin}/pks/foto/";
													})
													->rules([
														'mimetypes:image/jpeg,image/png,image/gif',
														'mimes:jpg,jpeg,png,gif',
													])
													->validationMessages([
														'mimetypes' => 'Hanya file gambar (JPEG, PNG, GIF) yang diperbolehkan',
														'mimes' => 'Ekstensi file harus .jpg, .jpeg, .png, atau .gif',
													])
													->getUploadedFileNameForStorageUsing(
														function (TemporaryUploadedFile $file, $get, $record): string {
															$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
															$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

															// Format nama file: [ID]_[NPWP]_[NOIJIN]_[UNIQID].[ext]
															return 'distribusihasil_' . $record->id . '_' . $cleanNpwp . '_' . $cleanNoIjin . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
														}
													),
											]),
										Group::make()
											->columnSpan(2)
											->schema([
												Placeholder::make('Status Verifikasi')
													->inlineLabel()
													->extraAttributes(fn($record) => [
														'class' => match ($record->distStatus) {
															0 => 'text-danger-500',
															1 => 'text-success-500',
															default => 'text-gray-500',
														},
													])
													->content(fn($record) => new HtmlString(match ($record->distStatus) {
														null => 'Belum di-verifikasi',
														0 => 'Tidak sesuai',
														1 => 'Sesuai',
													})),
												TextInput::make('vol_benih')
													->label('Disimpan sebagai Benih')
													->suffix('Kg')
													->helperText('Hasil panen yang digunakan kembali sebagai Benih. Isi dalam satuan kilogram (kg)')
													->numeric()
													->live(onBlur: true)
													->afterStateUpdated(function ($get, $set, $state) {
														$panen = $get('volume');
														$set('vol_jual', $panen - $state);
														$set('display_jual', number_format($panen - $state, 0, ',', '.'));
													}),

												TextInput::make('display_jual')
													->label('Dijual kembali')
													->suffix('Kg')
													->disabled()
													->helperText('Hasil panen yang didistribusikan untuk dijual kembali. Isi dalam satuan kilogram (kg)')
													->readOnly()
													->reactive()
													->formatStateUsing(fn($state) => number_format($state, 0, ',', '.')),

												Hidden::make('vol_jual')
													->label('Dijual kembali')
													->reactive(),
												Textarea::make('distComment')
													->label('Catatan Kegiatan'),
												Radio::make('distStatus')
													->label('Status Verifikasi')
													->inline()
													->columns(2)
													->options([
														1 => 'Sesuai',
														0 => 'Tidak Sesuai'
													]),
											]),

									]),
							]),


					]),
			])->columns(3);
	}

	private static function formatNoIjin($noIjin)
	{
		$formattedNoIjin = substr($noIjin, 0, 4) . '/' .     // "0022/"
			substr($noIjin, 4, 2) . '.' .     // "PP."
			substr($noIjin, 6, 3) . '/' .     // "240/"
			substr($noIjin, 9, 1) . '/' .     // "D/"
			substr($noIjin, 10, 2) . '/' .    // "01/"
			substr($noIjin, 12, 4);          // "2025";
		return $formattedNoIjin;
	}

	public static function table(Table $table): Table
	{

		return $table
			->columns([])
			->filters([
				//
			])
			->actions([
				TableAction::make('reporting')
					->label('Input Realisasi Komitmen')
					->hiddenLabel()
					->tooltip('Input Realisasi Tanam-Produksi')
					->color('success')
					->url(function ($record) {
						route('filament.panel2025.resources.realisasi2025s.lapgiat', ['record' => $record->id]);
					})
					->icon('icon-ui-checks'),

				TableAction::make('goToMap')
					->label('Pelaporan Realisasi Komitmen')
					->hiddenLabel()
					->tooltip('Lihat lokasi')
					->color('info')
					->url(fn($record) => route('panel.2025.report.singleMap', ['id' => $record->id]))
					->icon('icon-geo-alt-fill'),

			]);
	}

	public static function getRelations(): array
	{
		return [
			//
		];
	}

	public static function getPages(): array
	{
		return [
			'index' => Pages\ListRealisasi2025s::route('/'),
			'daftarrealisasi' => Pages\DaftarRealisasi2025s::route('/{noijin}'),
			'daftarlokasi' => Pages\DaftarRealisasi2025s::route('/{record}/{pengajuan}/{noijin}'),
			'lapgiat' => Pages\LaporanKegiatan::route('/{record}/lapgiat'),
			'viewlapgiat' => Pages\ViewLapgiat::route('/{record}/viewlapgiat'),
		];
	}

	public static function shouldRegisterNavigation(): bool
	{
		return false;
	}
}
