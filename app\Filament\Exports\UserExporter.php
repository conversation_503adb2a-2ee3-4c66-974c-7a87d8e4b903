<?php

namespace App\Filament\Exports;

use App\Models\User;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;
use OpenSpout\Common\Entity\Style\CellAlignment;
use OpenSpout\Common\Entity\Style\CellVerticalAlignment;
use OpenSpout\Common\Entity\Style\Color;
use OpenSpout\Common\Entity\Style\Style;

class UserExporter extends Exporter
{
    protected static ?string $model = User::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('id')
                ->label('ID'),
            ExportColumn::make('name'),
            ExportColumn::make('username'),
            ExportColumn::make('email'),
            ExportColumn::make('email_verified_at'),
            ExportColumn::make('roleaccess'),
            ExportColumn::make('jabatan'),
            ExportColumn::make('nip'),
            ExportColumn::make('ttd'),
            ExportColumn::make('digisign'),
            ExportColumn::make('created_at'),
            ExportColumn::make('updated_at'),
            ExportColumn::make('deleted_at'),
            ExportColumn::make('avatar_url'),
            ExportColumn::make('status'),
            ExportColumn::make('custom_fields'),
            ExportColumn::make('npwp'),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Your user export has completed and ' . number_format($export->successful_rows) . ' ' . str('row')->plural($export->successful_rows) . ' exported.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to export.';
        }

        return $body;
    }

	

	public function getXlsxHeaderCellStyle(): ?Style
	{
		return (new Style())
			->setFontBold()
			->setFontItalic()
			->setFontSize(14)
			->setFontName('Consolas')
			->setFontColor(Color::rgb(255, 255, 77))
			->setBackgroundColor(Color::rgb(0, 0, 0))
			->setCellAlignment(CellAlignment::CENTER)
			->setCellVerticalAlignment(CellVerticalAlignment::CENTER);
	}
}
