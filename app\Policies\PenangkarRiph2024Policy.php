<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\PenangkarRiph2024;
use App\Models\User;

class PenangkarRiph2024Policy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any PenangkarRiph2024');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, PenangkarRiph2024 $penangkarriph2024): bool
    {
        return $user->checkPermissionTo('view PenangkarRiph2024');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create PenangkarRiph2024');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, PenangkarRiph2024 $penangkarriph2024): bool
    {
        return $user->checkPermissionTo('update PenangkarRiph2024');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, PenangkarRiph2024 $penangkarriph2024): bool
    {
        return $user->checkPermissionTo('delete PenangkarRiph2024');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any PenangkarRiph2024');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, PenangkarRiph2024 $penangkarriph2024): bool
    {
        return $user->checkPermissionTo('restore PenangkarRiph2024');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any PenangkarRiph2024');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, PenangkarRiph2024 $penangkarriph2024): bool
    {
        return $user->checkPermissionTo('replicate PenangkarRiph2024');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder PenangkarRiph2024');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, PenangkarRiph2024 $penangkarriph2024): bool
    {
        return $user->checkPermissionTo('force-delete PenangkarRiph2024');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any PenangkarRiph2024');
    }
}
