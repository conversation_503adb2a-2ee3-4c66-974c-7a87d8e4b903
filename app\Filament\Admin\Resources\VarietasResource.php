<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\VarietasResource\Pages;
use App\Filament\Admin\Resources\VarietasResource\RelationManagers;
use App\Models\Varietas;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

class VarietasResource extends Resource
{
    protected static ?string $model = Varietas::class;
    protected static ?string $modelLabel = 'Master Varietas';
    protected static ?string $pluralModelLabel = 'Daftar Varietas Bawang Putih';

	protected static ?string $navigationGroup = 'Data Induk';
    protected static ?string $navigationLabel = 'Varietas';
    protected static ?int $navigationSort = 6;

    protected static ?string $navigationIcon = 'icon-growing-plant';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('kode_komoditas')
                    ->maxLength(255),
                Forms\Components\TextInput::make('nama_komoditas')
                    ->maxLength(255),
                Forms\Components\TextInput::make('kode_varietas')
                    ->maxLength(255),
                Forms\Components\TextInput::make('nama_varietas')
                    ->maxLength(255),
                Forms\Components\Textarea::make('keterangan')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('datalain')
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('kode_komoditas')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('nama_komoditas')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('kode_varietas')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('nama_varietas')
                    ->searchable(),
                Tables\Columns\TextColumn::make('datalain')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVarietas::route('/'),
            'create' => Pages\CreateVarietas::route('/create'),
            'edit' => Pages\EditVarietas::route('/{record}/edit'),
        ];
    }
}
