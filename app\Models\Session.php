<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class Session extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'sessions';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The "type" of the primary key ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        // Tidak perlu cast last_activity sebagai datetime karena disimpan sebagai timestamp
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'device',
        'browser',
        'platform',
        'last_activity_formatted',
        'status',
        'payload_formatted',
    ];

    /**
     * Get the user that owns the session.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Determine if the session is active.
     *
     * @return bool
     */
    public function isActive()
    {
        // Pastikan last_activity adalah timestamp (integer)
        if (!is_numeric($this->last_activity)) {
            return false;
        }

        // Bandingkan timestamp last_activity dengan timestamp saat ini - 5 menit
        // Pastikan timezone yang sama digunakan
        $now = Carbon::now()->setTimezone('Asia/Jakarta');
        $lastActivity = Carbon::createFromTimestamp((int) $this->last_activity)->setTimezone('Asia/Jakarta');

        return $lastActivity->greaterThan($now->copy()->subMinutes(5));
    }

    /**
     * Get the device information from user agent.
     *
     * @return array
     */
    public function getDeviceInfo()
    {
        $userAgent = $this->user_agent;
        $device = 'Unknown';
        $browser = 'Unknown';
        $platform = 'Unknown';

        // Cek apakah ini adalah session dari aplikasi mobile Simethris
        if (strpos($userAgent, 'Simethris Mobile App') !== false ||
            strpos($userAgent, 'Dart') !== false) {

            // Coba ambil informasi dari payload
            try {
                $payload = $this->getPayloadData();

                // Jika payload berisi informasi device_name = mobile_app, ini adalah session mobile app
                if (isset($payload['device_name']) && $payload['device_name'] === 'mobile_app') {
                    $device = 'Mobile App';
                    $browser = isset($payload['browser']) ? $payload['browser'] : 'Simethris Mobile App';
                    $platform = isset($payload['platform']) ? $payload['platform'] : 'Mobile App';

                    return [
                        'device' => $device,
                        'browser' => $browser,
                        'platform' => $platform,
                    ];
                }
            } catch (\Exception) {
                // Jika gagal mengambil dari payload, gunakan default untuk mobile app
                return [
                    'device' => 'Mobile App',
                    'browser' => 'Simethris Mobile App',
                    'platform' => 'Mobile App',
                ];
            }
        }

        // Deteksi platform/OS
        if (strpos($userAgent, 'Windows') !== false) {
            $platform = 'Windows';
        } elseif (strpos($userAgent, 'Mac') !== false) {
            $platform = 'MacOS';
        } elseif (strpos($userAgent, 'Linux') !== false) {
            $platform = 'Linux';
        } elseif (strpos($userAgent, 'Android') !== false) {
            $platform = 'Android';
        } elseif (strpos($userAgent, 'iPhone') !== false || strpos($userAgent, 'iPad') !== false) {
            $platform = 'iOS';
        }

        // Deteksi browser
        if (strpos($userAgent, 'Chrome') !== false && strpos($userAgent, 'Edg') === false) {
            $browser = 'Chrome';
        } elseif (strpos($userAgent, 'Firefox') !== false) {
            $browser = 'Firefox';
        } elseif (strpos($userAgent, 'Safari') !== false && strpos($userAgent, 'Chrome') === false) {
            $browser = 'Safari';
        } elseif (strpos($userAgent, 'Edg') !== false) {
            $browser = 'Edge';
        } elseif (strpos($userAgent, 'MSIE') !== false || strpos($userAgent, 'Trident') !== false) {
            $browser = 'Internet Explorer';
        } elseif (strpos($userAgent, 'Simethris Mobile App') !== false) {
            $browser = 'Simethris Mobile App';
        }

        // Deteksi perangkat
        if (strpos($userAgent, 'Mobile') !== false) {
            $device = 'Mobile';
        } elseif (strpos($userAgent, 'Tablet') !== false) {
            $device = 'Tablet';
        } else {
            $device = 'Desktop';
        }

        return [
            'device' => $device,
            'browser' => $browser,
            'platform' => $platform,
        ];
    }

    /**
     * Get the payload data.
     *
     * @return array|null
     */
    public function getPayloadData()
    {
        try {
            // Jika payload kosong, kembalikan array kosong
            if (empty($this->payload)) {
                return [
                    'error' => 'Payload kosong',
                    'payload_length' => 0
                ];
            }

            // Deteksi format payload
            $isPhpSerialized = strpos($this->payload, 'a:') === 0 || strpos($this->payload, 'O:') === 0;
            $isJsonEncoded = strpos($this->payload, '{') === 0 || strpos($this->payload, '[') === 0;

            // Coba unserialize payload langsung (cara standar Laravel)
            if ($isPhpSerialized) {
                $payload = @unserialize($this->payload);
                if ($payload !== false) {
                    return $payload;
                }

                // Jika gagal, coba dengan stripslashes
                $payload = @unserialize(stripslashes($this->payload));
                if ($payload !== false) {
                    return $payload;
                }
            }

            // Coba dengan json_decode jika terlihat seperti JSON
            if ($isJsonEncoded) {
                $payload = json_decode($this->payload, true);
                if ($payload !== null) {
                    return $payload;
                }
            }

            // Coba cara lain jika belum berhasil
            // 1. Coba unserialize langsung
            $payload = @unserialize($this->payload);
            if ($payload !== false) {
                return $payload;
            }

            // 2. Coba dengan stripslashes
            $payload = @unserialize(stripslashes($this->payload));
            if ($payload !== false) {
                return $payload;
            }

            // 3. Coba dengan base64_decode
            $payload = @unserialize(base64_decode($this->payload));
            if ($payload !== false) {
                return $payload;
            }

            // 4. Coba dengan json_decode
            $payload = json_decode($this->payload, true);
            if ($payload !== null) {
                return $payload;
            }

            // Jika semua cara gagal, kembalikan informasi tentang payload raw
            return [
                'error' => 'Payload tidak dapat di-unserialize',
                'payload_raw' => $this->getReadablePayload(),
                'payload_hex' => $this->getHexPayload(),
                'payload_length' => strlen($this->payload),
                'payload_sample' => substr($this->payload, 0, 100),
                'payload_format' => $isPhpSerialized ? 'PHP Serialized' : ($isJsonEncoded ? 'JSON' : 'Unknown')
            ];
        } catch (\Exception $e) {
            // Jika terjadi error, kembalikan informasi error
            return [
                'error' => 'Tidak dapat memproses payload: ' . $e->getMessage(),
                'payload_raw' => $this->getReadablePayload(),
                'payload_hex' => $this->getHexPayload(),
                'payload_length' => strlen($this->payload),
                'payload_sample' => substr($this->payload, 0, 100)
            ];
        }
    }

    /**
     * Get a readable version of the payload.
     *
     * @return string
     */
    protected function getReadablePayload()
    {
        $payload = $this->payload;

        // Ganti karakter non-printable dengan titik
        $readable = '';
        for ($i = 0; $i < strlen($payload); $i++) {
            $char = $payload[$i];
            $ord = ord($char);
            if ($ord < 32 || $ord > 126) {
                $readable .= '.';
            } else {
                $readable .= $char;
            }
        }

        // Batasi panjang dan tambahkan elipsis jika terlalu panjang
        if (strlen($readable) > 200) {
            $readable = substr($readable, 0, 200) . '...';
        }

        return $readable;
    }

    /**
     * Get a hexadecimal representation of the payload.
     *
     * @return string
     */
    protected function getHexPayload()
    {
        $payload = $this->payload;

        // Konversi ke format hexadecimal
        $hex = '';
        for ($i = 0; $i < min(100, strlen($payload)); $i++) {
            $hex .= sprintf('%02x', ord($payload[$i]));
            if ($i % 2 == 1) {
                $hex .= ' ';
            }
        }

        if (strlen($payload) > 100) {
            $hex .= '...';
        }

        return $hex;
    }

    /**
     * Scope a query to only include active sessions.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        // Gunakan timezone Asia/Jakarta untuk konsistensi
        $timestamp = Carbon::now()->setTimezone('Asia/Jakarta')->subMinutes(5)->getTimestamp();
        return $query->whereRaw('CAST(last_activity AS UNSIGNED) > ?', [$timestamp]);
    }

    /**
     * Scope a query to only include authenticated sessions.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAuthenticated($query)
    {
        return $query->whereNotNull('user_id');
    }

    /**
     * Scope a query to only include guest sessions.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeGuests($query)
    {
        return $query->whereNull('user_id');
    }

    /**
     * Get the device attribute.
     *
     * @return string
     */
    public function getDeviceAttribute()
    {
        $deviceInfo = $this->getDeviceInfo();
        return $deviceInfo['device'];
    }

    /**
     * Get the browser attribute.
     *
     * @return string
     */
    public function getBrowserAttribute()
    {
        $deviceInfo = $this->getDeviceInfo();
        return $deviceInfo['browser'];
    }

    /**
     * Get the platform attribute.
     *
     * @return string
     */
    public function getPlatformAttribute()
    {
        $deviceInfo = $this->getDeviceInfo();
        return $deviceInfo['platform'];
    }

    /**
     * Get the last activity formatted attribute.
     *
     * @return string
     */
    public function getLastActivityFormattedAttribute()
    {
        try {
            if (!is_numeric($this->last_activity)) {
                return 'Format tidak valid: ' . $this->last_activity;
            }

            $timestamp = (int) $this->last_activity;

            // Debug info
            $debug = '';
            $debug .= 'Timestamp: ' . $timestamp . "\n";
            $debug .= 'Current Time: ' . time() . "\n";
            $debug .= 'Difference: ' . (time() - $timestamp) . ' seconds' . "\n";
            $debug .= 'Server Timezone: ' . date_default_timezone_get() . "\n";
            $debug .= 'App Timezone: ' . config('app.timezone') . "\n";

            // Coba konversi timestamp ke Carbon dengan timezone yang benar
            $lastActivity = Carbon::createFromTimestamp($timestamp)->setTimezone('Asia/Jakarta');
            $now = Carbon::now()->setTimezone('Asia/Jakarta');

            $debug .= 'Formatted Time: ' . $lastActivity->format('d M Y H:i:s') . "\n";
            $debug .= 'Current Time (Carbon): ' . $now->format('d M Y H:i:s') . "\n";

            // Format tanggal dan waktu
            $formatted = $lastActivity->format('d M Y H:i:s');

            // Tambahkan relative time
            $formatted .= ' (' . $lastActivity->diffForHumans() . ')';

            // Tambahkan debug info jika timestamp terlihat aneh
            if (abs(time() - $timestamp) > 31536000) { // Lebih dari 1 tahun
                $formatted .= "\n\nPossible invalid timestamp: " . $debug;
            }

            // Tambahkan debug info di lingkungan local
            if (app()->environment('local')) {
                $formatted .= "\n\nDebug Info:\n" . $debug;
            }

            return $formatted;
        } catch (\Exception $e) {
            return 'Error: ' . $e->getMessage() . "\nTimestamp: " . $this->last_activity;
        }
    }

    /**
     * Get the status attribute.
     *
     * @return string
     */
    public function getStatusAttribute()
    {
        return $this->isActive() ? 'Online' : 'Offline';
    }

    /**
     * Get the payload formatted attribute.
     *
     * @return string
     */
    public function getPayloadFormattedAttribute()
    {
        try {
            $payload = $this->getPayloadData();
            $output = '';

            // Jika ada error, tampilkan informasi error dan data tambahan
            if (isset($payload['error'])) {
                $output .= "ERROR MEMPROSES PAYLOAD\n";
                $output .= "=======================\n\n";
                $output .= "Error: " . $payload['error'] . "\n\n";

                if (isset($payload['payload_format'])) {
                    $output .= "Format Terdeteksi: " . $payload['payload_format'] . "\n\n";
                }

                if (isset($payload['payload_length'])) {
                    $output .= "Payload Length: " . $payload['payload_length'] . " bytes\n\n";
                }

                if (isset($payload['payload_sample'])) {
                    $output .= "Payload Sample (first 100 chars):\n" . $payload['payload_sample'] . "\n\n";
                }

                if (isset($payload['payload_raw'])) {
                    $output .= "Readable Payload:\n" . $payload['payload_raw'] . "\n\n";
                }

                if (isset($payload['payload_hex'])) {
                    $output .= "Hex Payload:\n" . $payload['payload_hex'] . "\n\n";
                }

                $output .= "TIPS DEBUGGING:\n";
                $output .= "1. Pastikan session driver di config/session.php sesuai ('database')\n";
                $output .= "2. Cek apakah session encryption diaktifkan di config/session.php\n";
                $output .= "3. Jika menggunakan Laravel 5.8+, format serialisasi mungkin berbeda\n";

                return $output;
            }

            // Jika payload valid, ekstrak dan tampilkan informasi penting
            if (is_array($payload) && !empty($payload)) {
                // Filter payload untuk menghapus data sensitif
                $filteredPayload = $this->filterSensitiveData($payload);

                // Deteksi tipe session (web atau mobile)
                $isMobileSession = isset($payload['device_name']) && $payload['device_name'] === 'mobile_app';
                $isWebSession = isset($payload['_token']) && !$isMobileSession;

                $output .= "RINGKASAN SESSION\n";
                $output .= "================\n\n";

                // Tampilkan tipe session
                $output .= "Tipe Session: " . ($isMobileSession ? "Mobile App" : ($isWebSession ? "Web Browser" : "Unknown")) . "\n\n";

                // Ekstrak informasi penting dari payload
                $importantInfo = $this->extractImportantInfo($filteredPayload);

                if (!empty($importantInfo)) {
                    $output .= "Informasi Penting:\n";
                    foreach ($importantInfo as $key => $value) {
                        $output .= "- $key: " . (is_array($value) ? json_encode($value) : $value) . "\n";
                    }
                    $output .= "\n";
                }

                // Tampilkan informasi khusus berdasarkan tipe session
                if ($isMobileSession) {
                    $output .= "INFORMASI MOBILE APP\n";
                    $output .= "===================\n\n";

                    if (isset($filteredPayload['user']) && is_array($filteredPayload['user'])) {
                        $user = $filteredPayload['user'];
                        $output .= "User ID: " . ($user['id'] ?? 'Unknown') . "\n";
                        $output .= "Nama: " . ($user['name'] ?? 'Unknown') . "\n";
                        $output .= "Email: " . ($user['email'] ?? 'Unknown') . "\n\n";
                    }

                    if (isset($filteredPayload['device_name'])) {
                        $output .= "Device: " . $filteredPayload['device_name'] . "\n";
                    }

                    if (isset($filteredPayload['platform'])) {
                        $output .= "Platform: " . $filteredPayload['platform'] . "\n";
                    }

                    if (isset($filteredPayload['browser'])) {
                        $output .= "App: " . $filteredPayload['browser'] . "\n";
                    }

                    if (isset($filteredPayload['login_at']) && is_numeric($filteredPayload['login_at'])) {
                        $loginTime = Carbon::createFromTimestamp($filteredPayload['login_at']);
                        $output .= "Login: " . $loginTime->format('d M Y H:i:s') . " (" . $loginTime->diffForHumans() . ")\n";
                    }

                    $output .= "\n";
                } elseif ($isWebSession) {
                    $output .= "INFORMASI WEB SESSION\n";
                    $output .= "====================\n\n";

                    // Tampilkan informasi tentang user yang login
                    if (isset($filteredPayload['login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d'])) {
                        $userId = $filteredPayload['login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d'];
                        $output .= "User Login ID: $userId\n";
                    }

                    // Tampilkan informasi tentang CSRF token
                    if (isset($filteredPayload['_token'])) {
                        $output .= "CSRF Token: [REDACTED]\n";
                    }

                    // Tampilkan informasi tentang flash messages
                    if (isset($filteredPayload['_flash'])) {
                        $output .= "Flash Messages:\n";
                        if (isset($filteredPayload['_flash']['old']) && is_array($filteredPayload['_flash']['old'])) {
                            $output .= "- Old: " . implode(', ', $filteredPayload['_flash']['old']) . "\n";
                        }
                        if (isset($filteredPayload['_flash']['new']) && is_array($filteredPayload['_flash']['new'])) {
                            $output .= "- New: " . implode(', ', $filteredPayload['_flash']['new']) . "\n";
                        }
                    }

                    // Tampilkan informasi tentang previous URL
                    if (isset($filteredPayload['_previous'])) {
                        if (isset($filteredPayload['_previous']['url'])) {
                            $output .= "Previous URL: " . $filteredPayload['_previous']['url'] . "\n";
                        }
                    }

                    $output .= "\n";
                }

                // Tampilkan payload lengkap
                $output .= "PAYLOAD LENGKAP\n";
                $output .= "==============\n\n";
                $output .= json_encode($filteredPayload, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

                return $output;
            }

            return 'Tidak ada data payload yang valid';
        } catch (\Exception $e) {
            return 'Error: ' . $e->getMessage() . "\n\nStack Trace:\n" . $e->getTraceAsString();
        }
    }

    /**
     * Extract important information from payload.
     *
     * @param array $payload
     * @return array
     */
    protected function extractImportantInfo(array $payload)
    {
        $important = [];

        // Cek apakah ini session Laravel
        if (isset($payload['_token'])) {
            $important['CSRF Token'] = '[REDACTED]';
        }

        // Cek apakah ada informasi user (format mobile app)
        if (isset($payload['user'])) {
            if (is_array($payload['user'])) {
                $user = $payload['user'];
                $important['User ID'] = $user['id'] ?? 'Unknown';
                $important['User Name'] = $user['name'] ?? 'Unknown';
                $important['User Email'] = $user['email'] ?? 'Unknown';
            } else {
                $important['User'] = $payload['user'];
            }
        }

        // Cek apakah ada informasi device (format mobile app)
        if (isset($payload['device_name'])) {
            $important['Device Name'] = $payload['device_name'];
        }

        if (isset($payload['platform'])) {
            $important['Platform'] = $payload['platform'];
        }

        if (isset($payload['browser'])) {
            $important['Browser'] = $payload['browser'];
        }

        if (isset($payload['login_at'])) {
            $loginTime = is_numeric($payload['login_at'])
                ? date('Y-m-d H:i:s', $payload['login_at']) . ' (' . Carbon::createFromTimestamp($payload['login_at'])->diffForHumans() . ')'
                : $payload['login_at'];
            $important['Login Time'] = $loginTime;
        }

        // Cek apakah ada informasi login (format web)
        if (isset($payload['login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d'])) {
            $important['User Login ID'] = $payload['login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d'];
        }

        // Cek apakah ada flash messages
        if (isset($payload['_flash'])) {
            if (isset($payload['_flash']['old']) && is_array($payload['_flash']['old'])) {
                $important['Flash Messages (Old)'] = implode(', ', $payload['_flash']['old']);
            }
            if (isset($payload['_flash']['new']) && is_array($payload['_flash']['new'])) {
                $important['Flash Messages (New)'] = implode(', ', $payload['_flash']['new']);
            }
        }

        // Cek apakah ada previous URL
        if (isset($payload['_previous'])) {
            if (isset($payload['_previous']['url'])) {
                $important['Previous URL'] = $payload['_previous']['url'];
            }
        }

        // Cek apakah ada informasi locale
        if (isset($payload['locale'])) {
            $important['Locale'] = $payload['locale'];
        }

        // Cek apakah ada informasi Filament
        if (isset($payload['filament'])) {
            $important['Filament Data'] = 'Ya';
        }

        // Cek apakah ada informasi tambahan
        $additionalKeys = array_diff(array_keys($payload), [
            '_token', 'user', 'device_name', 'platform', 'browser', 'login_at',
            'login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d', '_flash', '_previous',
            'locale', 'filament'
        ]);

        if (!empty($additionalKeys)) {
            $important['Additional Keys'] = implode(', ', $additionalKeys);
        }

        return $important;
    }

    /**
     * Filter data sensitif dari payload.
     *
     * @param array $payload
     * @return array
     */
    protected function filterSensitiveData(array $payload)
    {
        $sensitiveKeys = [
            'password', 'password_confirmation', 'token', 'remember_token', 'auth',
            'key', 'secret', 'private', 'csrf', '_token'
        ];

        $filtered = [];

        foreach ($payload as $key => $value) {
            // Jika key mengandung kata sensitif, ganti nilainya
            $isSensitive = false;
            foreach ($sensitiveKeys as $sensitiveKey) {
                if (stripos($key, $sensitiveKey) !== false) {
                    $isSensitive = true;
                    break;
                }
            }

            if ($isSensitive) {
                $filtered[$key] = '[REDACTED]';
            } else if (is_array($value)) {
                $filtered[$key] = $this->filterSensitiveData($value);
            } else {
                $filtered[$key] = $value;
            }
        }

        return $filtered;
    }
}
