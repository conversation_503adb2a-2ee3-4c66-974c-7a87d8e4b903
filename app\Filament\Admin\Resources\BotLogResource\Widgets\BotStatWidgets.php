<?php

namespace App\Filament\Admin\Resources\BotLogResource\Widgets;

use App\Models\Commitment2025;
use App\Models\BotLog;
use Filament\Support\Enums\IconPosition;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class BotStatWidgets extends BaseWidget
{
	protected function getStats(): array
	{
		$totalBots = BotLog::count();
	
		// Ambil data user_agent dan hitung jumlah per browser
		$browserStats = BotLog::all()
			->map(fn ($log) => $this->detectBrowser($log->user_agent))
			->countBy();
	
		return [
			Stat::make('Total', number_format($totalBots, 0, ',', '.') . ' Bot')
				->description('Jumlah s.d Tahun ' . date('Y'))
				->descriptionIcon('heroicon-o-shield-exclamation', IconPosition::Before)
				->color('danger'),
	
			Stat::make('Bot', $browserStats->get('Google Chrome', 0))
				->description('Chrome')
				->descriptionIcon('icon-browser-chrome', IconPosition::Before)
				->color('success'),
	
			Stat::make('Bot', $browserStats->get('Mozilla Firefox', 0))
				->description('Firefox')
				->descriptionIcon('icon-browser-firefox', IconPosition::Before)
				->color('warning'),
	
			Stat::make('Bot', $browserStats->get('Microsoft Edge', 0))
				->description('Edge')
				->descriptionIcon('icon-browser-edge', IconPosition::Before)
				->color('info'),
	
			Stat::make('Bot', $browserStats->get('Safari', 0))
				->description('Safari')
				->descriptionIcon('icon-browser-safari', IconPosition::Before)
				->color('gray'),
	
			Stat::make('Bot', $browserStats->get('Lainnya', 0))
				->description('Lainnya')
				->descriptionIcon('heroicon-o-globe-alt', IconPosition::Before)
				->color('gray'),
		];
	}

	protected function detectBrowser(string $userAgent): string
	{
		return match (true) {
			str_contains($userAgent, 'Edg/') => 'Microsoft Edge',
			str_contains($userAgent, 'OPR/') => 'Opera',
			str_contains($userAgent, 'Firefox/') => 'Mozilla Firefox',
			str_contains($userAgent, 'Chrome/') && !str_contains($userAgent, 'Edg/') && !str_contains($userAgent, 'OPR/') => 'Google Chrome',
			str_contains($userAgent, 'Safari/') && !str_contains($userAgent, 'Chrome/') => 'Safari',
			default => 'Lainnya',
		};
		
	}

	
}