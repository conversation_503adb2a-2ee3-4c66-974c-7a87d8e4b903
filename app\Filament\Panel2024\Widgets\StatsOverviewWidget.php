<?php

namespace App\Filament\Panel2024\Widgets;

use App\Models\Commitment2024;
use App\Models\Summary2024;
use Carbon\Carbon;
use Filament\Support\Enums\IconPosition;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Number;

class StatsOverviewWidget extends BaseWidget
{
    use InteractsWithPageFilters;

    protected static ?int $sort = 0;

	// protected function getHeading(): ?string
	// {
	// 	return 'Analytics';
	// }

	// protected function getDescription(): ?string
	// {
	// 	return 'Ringkasan realisasi dan komitmen.';
	// }
	public static function canView(): bool
	{
		$user = Auth::user();
	
		return $user->hasAnyRole(['admin', 'Super Admin']);
	}
    protected function getStats(): array
    {
		$periode = $this->filters['periodeFilter'] ?? null;

		$commitments = Summary2024::when($periode && $periode !== 'all', function ($query) use ($periode) {
			return $query->where('periode', $periode);
		})
		->get();
		
		$countCommitment = number_format($commitments->sum('riph'), 0, ',', '.');
		$countSKL = number_format($commitments->sum('completed'), 0, ',', '.');
		$lokasiCount = number_format($commitments->sum('petani'), 0, ',', '.');

		$commitmentTanam = number_format($commitments->sum('wajibtanam'), 2, ',', '.');
		$commitmentPanen = number_format($commitments->sum('wajibpanen'), 2, ',', '.');
		$totalLuasLahan = number_format($commitments->sum('realisasitanam'), 2, ',', '.');
		$totalVolume = number_format($commitments->sum('realisasipanen'), 2, ',', '.');
		// Log::info($commitments);

        return [
			Stat::make('', $countSKL . ' SKL')
				->label('SKL Terbit')
				->description("dari " . $countCommitment . ' komitmen')
				->chart([7, 2, 10, 3, 15, 4, 17])
				->descriptionIcon('icon-journal-bookmark-fill', IconPosition::Before)
				->color('danger'),
			Stat::make('', $lokasiCount)
				->label('Partisipan')
				->description("Petani")
				->chart([7, 2, 10, 3, 15, 4, 17])
				->descriptionIcon('icon-people-fill', IconPosition::Before)
				->color('info'),
			Stat::make('', $totalLuasLahan . ' ha')
				->label('Realisasi Tanam')
				->description("dari " .$commitmentTanam." ha komitmen")
				->chart([7, 2, 10, 3, 15, 4, 17])
				->descriptionIcon('icon-growing-plant', IconPosition::Before)
				->color('success'),
			Stat::make('', $totalVolume . ' ton')
				->label('Realisasi Produksi')
				->description("dari " .$commitmentPanen." ton komitmen")
				->chart([7, 2, 10, 3, 15, 4, 17])
				->descriptionIcon('icon-garlic-line', IconPosition::Before)
				->color('warning'),
		];
    }
}
