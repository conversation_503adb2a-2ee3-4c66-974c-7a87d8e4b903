<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DataAdministrator extends Model
{
	use HasFactory, SoftDeletes;
	public $table = 'data_administrators';

	protected $dates = [
		'created_at',
		'updated_at',
		'deleted_at',
	];

	protected $fillable = [
		'user_id',
		'nama',
		'jabatan',
		'nip',
		'sign_img',
		'digital_sign',
		'nama_dinas',
		'provinsi_id',
		'kabupaten_id',
		'status',
		'mobile_number',
	];

	public function user()
	{
		return $this->belongsTo(User::class);
	}

	public function provinsi()
	{
		return $this->belongsTo(MasterProvinsi::class, 'provinsi_id', 'provinsi_id');
	}

	public function kabupaten()
	{
		return $this->belongsTo(MasterKabupaten::class, 'kabupaten_id', 'kabupaten_id');
	}
}
