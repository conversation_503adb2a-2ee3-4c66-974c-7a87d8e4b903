<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
		Schema::create('bot_logs', function (Blueprint $table) {
			$table->id();
			$table->string('ip')->nullable();
			$table->text('user_agent')->nullable();
			$table->string('method', 10)->nullable();
			$table->text('referer')->nullable();
			$table->text('url')->nullable();
			$table->string('accept_language', 100)->nullable();
			$table->timestamps();
		});
    }
};
