<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class MobileAuthController extends Controller
{
    /**
     * Handle login for mobile app with session creation
     */
    public function login(Request $request)
    {
        $request->validate([
            'username' => 'required|string',
            'password' => 'required',
            'device_name' => 'required',
        ]);

        // Determine if the input is an email or username
        $loginField = filter_var($request->username, FILTER_VALIDATE_EMAIL) ? 'email' : 'username';

        // Find the user
        $user = User::where($loginField, $request->username)->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'username' => ['The provided credentials are incorrect.'],
            ]);
        }

        // Create a session record manually instead of using Auth::login
        // This avoids CSRF token issues
        $sessionId = Str::random(40);

        // Create a session record in the database with more detailed payload
        $payload = [
            '_token' => Str::random(40),
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
            ],
            'device_name' => $request->device_name,
            'platform' => 'Mobile App',
            'browser' => 'Simethris Mobile App',
            'login_at' => time(),
        ];

        DB::table('sessions')->insert([
            'id' => $sessionId,
            'user_id' => $user->id,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent() . ' | Simethris Mobile App',
            'payload' => serialize($payload),
            'last_activity' => time(),
        ]);

        // Log session information for debugging
        Log::info('Mobile login session created', [
            'user_id' => $user->id,
            'session_id' => $sessionId,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        // Delete old tokens for this device
        $user->tokens()->where('name', $request->device_name)->delete();

        // Create a new token
        $token = $user->createToken($request->device_name)->plainTextToken;

        // Load relasi roles terlebih dahulu untuk menentukan jenis pengguna
        $user->load(['roles']);

        $profileType = $user->hasRole('importir') ? 'importir' : 'admin';
        $profileData = null;

        // Load data sesuai tipe profil
        if ($profileType === 'importir') {
            $user->load(['datauser']);
            $profileData = $user->datauser;
        } else {
            $user->load(['dataadmin']);
            $profileData = $user->dataadmin;
        }

        // Load permissions
        $user->load(['permissions']);

        return \response()->json([
            'success' => true,
            'token' => $token,
            'session_id' => $sessionId,
            'user' => [
                'id' => $user->id ?? null,
                'name' => $user->name ?? null,
                'email' => $user->email ?? null,
                'username' => $user->username ?? null,
                'npwp' => $user->npwp ?? null,
                'status' => $user->status ?? null,
                'avatar_url' => $user->avatar_url ?? null,
                'email_verified_at' => $user->email_verified_at ? $user->email_verified_at->toDateTimeString() : null,
            ],
            'profile_type' => $profileType ?? 'unknown',
            'profile_data' => $profileData ?? null,
            'roles' => $user->roles ? $user->roles->pluck('name') : [],
            'permissions' => $user->permissions ? $user->permissions->pluck('name') : [],
        ]);
    }

    /**
     * Handle logout for mobile app with session destruction
     */
    public function logout(Request $request)
    {
        // Get the user from the token
        $user = $request->user();

        if ($user) {
            // Log session information for debugging
            Log::info('Mobile logout session', [
                'user_id' => $user->id,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            // Delete the current token
            $user->currentAccessToken()->delete();

            // If a session_id was provided, delete that session
            if ($request->has('session_id')) {
                DB::table('sessions')
                    ->where('id', $request->session_id)
                    ->where('user_id', $user->id)
                    ->delete();

                Log::info('Deleted session', ['session_id' => $request->session_id]);
            }
        }

        return \response()->json(['message' => 'Logged out successfully']);
    }

	public function getProfile(Request $request)
	{
		$user = $request->user();

		if (!$user) {
			return \response()->json([
				'success' => false,
				'message' => 'User not authenticated'
			], 401);
		}

		// Load relasi roles terlebih dahulu untuk menentukan jenis pengguna
		$user->load(['roles']);

		$profileType = $user->hasRole('importir') ? 'importir' : 'admin';
		$profileData = null;

		// Load data sesuai tipe profil
		if ($profileType === 'importir') {
			$user->load(['datauser']);
			$profileData = $user->datauser;
		} else {
			$user->load(['dataadmin']);
			$profileData = $user->dataadmin;
		}

		// Load permissions
		$user->load(['permissions']);

		// Struktur respons yang lebih terorganisir dengan null safety
		return \response()->json([
			'success' => true,
			'user' => [
				'id' => $user->id ?? null,
				'name' => $user->name ?? null,
				'email' => $user->email ?? null,
				'username' => $user->username ?? null,
				'npwp' => $user->npwp ?? null,
				'status' => $user->status ?? null,
				'avatar_url' => $user->avatar_url ?? null,
				'email_verified_at' => $user->email_verified_at ? $user->email_verified_at->toDateTimeString() : null,
			],
			'profile_type' => $profileType ?? 'unknown',
			'profile_data' => $profileData ?? null,
			'roles' => $user->roles ? $user->roles->pluck('name') : [],
			'permissions' => $user->permissions ? $user->permissions->pluck('name') : [],
		]);
	}
}
