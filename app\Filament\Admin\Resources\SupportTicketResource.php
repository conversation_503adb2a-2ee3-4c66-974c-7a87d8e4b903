<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\SupportTicketResource\Pages;
use App\Filament\Admin\Resources\SupportTicketResource\RelationManagers;
use App\Models\SupportDepartement;
use App\Models\SupportTicket;
use Filament\Forms;
use Filament\Forms\Components\{DatePicker, FileUpload, Hidden, RichEditor, Select, TextInput};
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

class SupportTicketResource extends Resource
{
    protected static ?string $model = SupportTicket::class;
	protected static ?int $navigationSort = 99;

    protected static ?string $navigationIcon = 'heroicon-o-ticket';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Hidden::make('user_id')
                    ->default(Auth::user()->id),
                TextInput::make('subject')
                    ->required()
                    ->maxLength(255),
                Select::make('related_service')
					->label('Layanan terkait')
					->searchable()
					->options(function () {
						$user = Auth::user();

						$commitmentIjin = $user->commitment()
							->pluck('no_ijin')
							->filter()
							->mapWithKeys(fn ($ijin) => [$ijin => $ijin]);

						$oldCommitmentIjin = $user->oldCommitment()
							->pluck('no_ijin')
							->filter()
							->mapWithKeys(fn ($ijin) => [$ijin => $ijin]);

						return $commitmentIjin->merge($oldCommitmentIjin)->unique();
					}),
                RichEditor::make('message')
                    ->required()->label('Isi Pesan')
                    ->columnSpanFull(),

                Select::make('department_id')
                    ->required()
					->label('Departement')
					->options(fn () => SupportDepartement::pluck('name', 'id')),
                Select::make('priority')
                    ->required()
					->options(['low', 'medium', 'high', 'urgent']),
                FileUpload::make('attachment')->columnSpanFull(),

                Hidden::make('last_replied_by'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
				TextColumn::make('department.name')
					->numeric()
					->label('Layanan')
					->sortable(),
                TextColumn::make('id')
                    ->searchable()
					->label('Subject')
					->formatStateUsing(fn ($state) => '#'.$state)
					->extraCellAttributes(['class' => 'font-bold'])
					->color('primary')
					->description(fn ($record) => $record->subject)
					->url(fn ($record) => SupportTicketResource::getUrl('view', ['record' => $record])),
                TextColumn::make('status')
                    ->badge()
					->color(fn (string $state): string => match ($state) {
						'open' => 'danger',
						'in_progress' => 'warning',
						'resolved' => 'success',
						'closed' => 'gray',
						default => 'gray', // fallback jika ada status lain
					}),
                TextColumn::make('updated_at')
                    ->dateTime()
					->label('Last Updated')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('Lihat')
                    ->icon('heroicon-o-eye'),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\SupportTicket::route('/'),
            'list' => Pages\ListSupportTickets::route('/list'),
            'create' => Pages\CreateSupportTicket::route('/create'),
            'view' => Pages\ViewSupportTicket::route('/{record}'),
            'edit' => Pages\EditSupportTicket::route('/{record}/edit'),
        ];
    }
}
