<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class MobileApp extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'version',
        'version_code',
        'description',
        'file_name',
        'min_android_version',
        'min_ram',
        'min_storage',
        'required_features',
        'recommended_specs',
        'release_notes',
        'is_active',
        'allowed_roles',
    ];

    protected $casts = [
        'allowed_roles' => 'array',
        'required_features' => 'array',
        'is_active' => 'boolean',
        'version_code' => 'integer',
        'min_ram' => 'integer',
        'min_storage' => 'integer',
    ];

    /**
     * Mendapatkan path lengkap ke file APK
     */
    public function getFilePathAttribute()
    {
        return 'mobile_apps/' . $this->file_name;
    }

    /**
     * Mendapatkan ukuran file
     */
    public function getFileSizeAttribute()
    {
        if (Storage::disk('public')->exists($this->file_path)) {
            return Storage::disk('public')->size($this->file_path);
        }

        return 0;
    }

    /**
     * Memeriksa apakah file ada
     */
    public function getFileExistsAttribute()
    {
        return Storage::disk('public')->exists($this->file_path);
    }

    /**
     * Mendapatkan URL untuk mengunduh file
     */
    public function getDownloadUrlAttribute()
    {
        return route('mobile-app.download', $this);
    }

    /**
     * Mendapatkan metadata dari nama file
     * Format: role_name_version.apk
     */
    public static function getMetadataFromFileName($fileName)
    {
        $parts = explode('_', pathinfo($fileName, PATHINFO_FILENAME));

        if (count($parts) >= 3) {
            $role = $parts[0];
            $name = $parts[1];
            $versionPart = $parts[2];

            // Hapus 'v' dari awal versi jika ada
            $version = preg_replace('/^v/', '', $versionPart);

            // Konversi versi ke version_code (misalnya 1.0.0 -> 100)
            $versionParts = explode('.', $version);
            $versionCode = 0;
            if (count($versionParts) >= 3) {
                $versionCode = (int)$versionParts[0] * 10000 + (int)$versionParts[1] * 100 + (int)$versionParts[2];
            } elseif (count($versionParts) == 2) {
                $versionCode = (int)$versionParts[0] * 10000 + (int)$versionParts[1] * 100;
            } elseif (count($versionParts) == 1) {
                $versionCode = (int)$versionParts[0] * 10000;
            }

            return [
                'name' => ucfirst($name),
                'version' => $version,
                'version_code' => $versionCode,
                'file_name' => $fileName,
                'allowed_roles' => [$role],
            ];
        }

        return [
            'name' => pathinfo($fileName, PATHINFO_FILENAME),
            'version' => '1.0.0',
            'version_code' => 10000,
            'file_name' => $fileName,
            'allowed_roles' => [],
        ];
    }
}
