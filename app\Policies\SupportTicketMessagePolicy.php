<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\SupportTicketMessage;
use App\Models\User;

class SupportTicketMessagePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any SupportTicketMessage');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, SupportTicketMessage $supportticketmessage): bool
    {
        return $user->checkPermissionTo('view SupportTicketMessage');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create SupportTicketMessage');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, SupportTicketMessage $supportticketmessage): bool
    {
        return $user->checkPermissionTo('update SupportTicketMessage');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, SupportTicketMessage $supportticketmessage): bool
    {
        return $user->checkPermissionTo('delete SupportTicketMessage');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any SupportTicketMessage');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, SupportTicketMessage $supportticketmessage): bool
    {
        return $user->checkPermissionTo('restore SupportTicketMessage');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any SupportTicketMessage');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, SupportTicketMessage $supportticketmessage): bool
    {
        return $user->checkPermissionTo('replicate SupportTicketMessage');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder SupportTicketMessage');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, SupportTicketMessage $supportticketmessage): bool
    {
        return $user->checkPermissionTo('force-delete SupportTicketMessage');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any SupportTicketMessage');
    }
}
