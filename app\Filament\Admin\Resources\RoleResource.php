<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\RoleResource\Pages\CreateRole;
use App\Filament\Admin\Resources\RoleResource\Pages\EditRole;
use App\Filament\Admin\Resources\RoleResource\Pages\ListRoles;
use App\Filament\Admin\Resources\RoleResource\Pages\ViewRole;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Spatie\Permission\Models\Role;

class RoleResource extends Resource
{
    protected static ?string $model = Role::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';

    protected static ?string $navigationGroup = 'Manajemen Akses';

    protected static ?int $navigationSort = 0;

    public static function getLabel(): string
    {
        return 'Role';
    }

    public static function getPluralLabel(): string
    {
        return 'Roles';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('name')
                                    ->label('Nama Role')
                                    ->required()
                                    ->unique(ignoreRecord: true),

                                Select::make('guard_name')
                                    ->label('Guard Name')
                                    ->options([
                                        'web' => 'web',
                                        'api' => 'api',
                                    ])
                                    ->default('web')
                                    ->live()
                                    ->required(),
                            ]),
                    ]),

                Fieldset::make(fn (\Filament\Forms\Get $get) => 'Permissions (' . ($get('guard_name') ?? 'web') . ')')
                    ->columns(2)
                    ->schema(function (\Filament\Forms\Get $get) {
                        $guardName = $get('guard_name') ?? 'web';

                        return \Spatie\Permission\Models\Permission::query()
                            ->where('guard_name', $guardName)
                            ->orderBy('name')
                            ->get()
                            ->groupBy(fn($permission) => explode(' ', $permission->name)[1]) // Kelompok berdasarkan prefix
                            ->map(fn($permissions, $group) =>
                                Section::make("Model {$group}")
                                    ->collapsed()
                                    ->columnSpan(1)
                                    ->schema([
                                        CheckboxList::make("Model {$group}")
                                            ->label(ucfirst($group))
                                            ->relationship(
                                                name: 'permissions',
                                                modifyQueryUsing: fn(Builder $query) => $query->whereIn('id', $permissions->pluck('id'))
                                            )
                                            ->bulkToggleable()
                                            ->searchable()
                                            ->columns(2)
                                            ->getOptionLabelFromRecordUsing(fn(Model $record) => "{$record->name}")
                                    ])
                                )->values()->toArray();
                    }),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->query(Role::query()->where('name', '!=', 'Super Admin'))
            ->columns([
                TextColumn::make('index')
                    ->label('No')
                    ->rowIndex(),
                TextColumn::make('name')
                    ->label('Peran')
                    ->searchable(),
                TextColumn::make('permissions_count')
                    ->counts('permissions')
                    ->label('Jumlah Permissions'),
                TextColumn::make('guard_name')
                    ->label('Guard')
                    ->badge()
                    ->color(fn (string $state): string => $state === 'web' ? 'primary' : 'danger')
                    ->searchable(),
            ])
            ->filters([])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListRoles::route('/'),
            'create' => CreateRole::route('/create'),
            'edit' => EditRole::route('/{record}/edit'),
            'view' => ViewRole::route('/{record}'),
        ];
    }
}
