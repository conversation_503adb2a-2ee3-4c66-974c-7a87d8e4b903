<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\ActionLog;
use App\Models\User;

class ActionLogPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(App\Models\User $user): bool
    {
        return $user->checkPermissionTo('view-any ActionLog');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(App\Models\User $user, ActionLog $actionlog): bool
    {
        return $user->checkPermissionTo('view ActionLog');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(App\Models\User $user): bool
    {
        return $user->checkPermissionTo('create ActionLog');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(App\Models\User $user, ActionLog $actionlog): bool
    {
        return $user->checkPermissionTo('update ActionLog');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(App\Models\User $user, ActionLog $actionlog): bool
    {
        return $user->checkPermissionTo('delete ActionLog');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(App\Models\User $user): bool
    {
        return $user->checkPermissionTo('delete-any ActionLog');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(App\Models\User $user, ActionLog $actionlog): bool
    {
        return $user->checkPermissionTo('restore ActionLog');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(App\Models\User $user): bool
    {
        return $user->checkPermissionTo('restore-any ActionLog');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(App\Models\User $user, ActionLog $actionlog): bool
    {
        return $user->checkPermissionTo('replicate ActionLog');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(App\Models\User $user): bool
    {
        return $user->checkPermissionTo('reorder ActionLog');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(App\Models\User $user, ActionLog $actionlog): bool
    {
        return $user->checkPermissionTo('force-delete ActionLog');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(App\Models\User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any ActionLog');
    }
}
