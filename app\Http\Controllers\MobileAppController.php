<?php

namespace App\Http\Controllers;

use App\Models\MobileApp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class MobileAppController extends Controller
{
    /**
     * Menampilkan daftar aplikasi yang tersedia untuk pengguna
     */
    public function index()
    {
        $user = Auth::user();
        $userRole = $user->roles->pluck('name')->first();

        // Jika Super Admin, tampilkan semua aplikasi
        if ($user->hasRole('Super Admin')) {
            $apps = MobileApp::where('is_active', true)->get();
        } else {
            // Tampilkan hanya aplikasi yang sesuai dengan role pengguna
            $apps = MobileApp::where('is_active', true)
                ->where(function ($query) use ($userRole) {
                    $query->whereJsonContains('allowed_roles', $userRole);
                })
                ->get();
        }

        // Filter hanya aplikasi yang file-nya ada
        $apps = $apps->filter(function ($app) {
            return $app->file_exists;
        });

        return view('mobile-apps.index', compact('apps'));
    }

    /**
     * Mengunduh aplikasi
     */
    public function download(MobileApp $mobileApp)
    {
        $user = Auth::user();
        $userRole = $user->roles->pluck('name')->first();

        // Verifikasi akses
        if (!$user->hasRole('Super Admin') &&
            !in_array($userRole, $mobileApp->allowed_roles)) {
            abort(403, 'Anda tidak memiliki akses untuk mengunduh aplikasi ini.');
        }

        // Verifikasi status aktif
        if (!$mobileApp->is_active) {
            abort(404, 'Aplikasi tidak tersedia untuk diunduh.');
        }

        // Verifikasi keberadaan file
        if (!$mobileApp->file_exists) {
            abort(404, 'File aplikasi tidak ditemukan.');
        }

        // Log unduhan
        Log::info('User mengunduh aplikasi', [
            'user_id' => $user->id,
            'user_name' => $user->name,
            'user_role' => $userRole,
            'app_id' => $mobileApp->id,
            'app_name' => $mobileApp->name,
            'app_version' => $mobileApp->version,
            'timestamp' => now(),
        ]);

        // Return file untuk diunduh
        $filePath = Storage::disk('public')->path($mobileApp->file_path);
        return response()->download($filePath, $mobileApp->name . '-' . $mobileApp->version . '.apk');
    }
}
