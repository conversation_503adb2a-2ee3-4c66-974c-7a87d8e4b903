<?php

namespace App\Filament\Panel2025\Resources\Commitment2025Resource\Pages;

use App\Filament\Panel2025\Resources\Commitment2025Resource;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\HtmlString;

class ViewlunasCommitment2025 extends ViewRecord
{
    protected static string $resource = Commitment2025Resource::class;
	public function getHeading(): string
	{
        return 'Realisasi Komitmen';
	}
    protected static ?string $title = 'Lihat Data PPRK';
    // protected static string $view = 'realisasi.view-map';

    public function getSubheading(): ?string
    {
        $noIjin = $this->record ? $this->record->no_ijin : '##';
        return 'PPRK No: ' . $noIjin;
    }

	public function form(Form $form): Form
    {
        return $form
            ->schema([
				Section::make('Data PPRK')
					->aside()
					->description('Data PPRK (Pertimbangan Penetapan Rencana Kebutuhan)')
					->schema([
						Placeholder::make('noijin')
							->label('Nomor Ijin')
							->inlineLabel()
							->content(fn ($record)=>$record->no_ijin),
						
						Placeholder::make('Periode')
							->label('Periode')
							->inlineLabel()
							->content(fn ($record)=>$record->periodetahun),
						
						Placeholder::make('Masa Berlaku')
							->label('Masa Berlaku')
							->inlineLabel()
							->content(fn ($record) => Carbon::parse($record->tgl_ijin)->translatedFormat('d F Y') . ' -s.d- ' . Carbon::parse($record->tgl_akhir)->translatedFormat('d F Y')),
						
						Placeholder::make('Volume Ijin Import')
							->label('Volume Ijin Import')
							->inlineLabel()
							->content(fn ($record)=> number_format($record->volume_riph,0,',','.') . '  ton'),
						
						Placeholder::make('Komitmen Luas Tanam')
							->label('Komitmen Luas Tanam')
							->inlineLabel()
							->content(fn ($record)=> number_format($record->luas_wajib_tanam,0,',','.') . '  m2'),
						
						Placeholder::make('Komitmen Produksi')
							->label('Komitmen Produksi')
							->inlineLabel()
							->content(fn ($record)=> number_format($record->volume_produksi,0,',','.') . '  kg'),
					]),

				Section::make('Data Realisasi')
					->aside()
					->description('Data ringkasan realisasi komitmen tanam dan produksi')
					->schema([
						Placeholder::make('luastanam')
							->label('Total Luas Tanam')
							->inlineLabel()
							->content(fn ($record) => number_format($record->detailrealisasitanam->sum('value'),0,',','.').' m2'),

						Placeholder::make('volume_panen')
							->label('Total Produksi')
							->inlineLabel()
							->content(fn ($record) => number_format($record->detailrealisasiproduksi->sum('value'),0,',','.').' kg'),

						Placeholder::make('distribusibenih')
							->label('Untuk Benih')
							->inlineLabel()
							->content(fn ($record) => number_format($record->detailrealisasidistribusi->sum('dist_benih'),0,',','.').' kg'),

						Placeholder::make('distribusijual')
							->label('Untuk Dijual')
							->inlineLabel()
							->content(fn ($record) => number_format($record->detailrealisasidistribusi->sum('dist_jual'),0,',','.').' kg'),

						Placeholder::make('poktan')
							->label('Kelompok Tani')
							->inlineLabel()
							->content(fn ($record) => number_format($record->pks->count(),0,',','.').' kelompok'),

						Placeholder::make('anggota')
							->label('Anggota')
							->inlineLabel()
							->content(fn ($record) => 
								number_format($record->realisasi->pluck('ktp_petani')->unique()->count(), 0, ',', '.') . ' anggota'
							),

						Placeholder::make('lokasi')
							->label('Jumlah Lahan')
							->inlineLabel()
							->content(fn ($record) => 
								number_format($record->realisasi->count(), 0, ',', '.') . ' titik'
							),

						Fieldset::make('Realisasi Pupuk dan Mulsa')
							->schema([
								Placeholder::make('mulsa')
									->label('Mulsa')
									->inlineLabel()
									->content(fn ($record) => number_format($record->detailrealisasimulsa->sum('value'),0,',','.').' roll'),

								Placeholder::make('organik')
									->label('organik')
									->inlineLabel()
									->content(fn ($record) => number_format($record->detailrealisasipupuk->sum('organik'),0,',','.').' kg'),

								Placeholder::make('dolomit')
									->label('dolomit')
									->inlineLabel()
									->content(fn ($record) => number_format($record->detailrealisasipupuk->sum('dolomit'),0,',','.').' kg'),

								Placeholder::make('npk')
									->label('NPK')
									->inlineLabel()
									->content(fn ($record) => number_format($record->detailrealisasipupuk->sum('npk'),0,',','.').' kg'),

								Placeholder::make('za')
									->label('ZA')
									->inlineLabel()
									->content(fn ($record) => number_format($record->detailrealisasipupuk->sum('za'),0,',','.').' kg'),
							])
					]),

				Section::make('Data Berkas Kelengkapan')
					->aside()
					->description('Berkas-berkas kelengkapan yang diunggah')
					->schema([
						TableRepeater::make('berkaspks')
							->addable(false)
							->deletable(false)
							->extraAttributes(['class' => 'mb-5'])
							->relationship('pks')
							->label(new HtmlString('<span class="font-bold">Berkas PKS</span>'))
							->headers([
								Header::make('Kelompoktani'),
								Header::make('No PKS'),
								Header::make('Tautan')
							])
							->schema([
								Placeholder::make('poktan')
									->hiddenLabel()
									->content(fn ($record) => $record->poktan->nama_kelompok),
								Placeholder::make('noperjanjian')
									->hiddenLabel()
									->content(fn ($record) => $record->no_perjanjian),
								Placeholder::make('link')
									->hiddenLabel()
									->extraAttributes(['class' => 'text-primary-500'])
									->content(fn ($record) => new HtmlString(
										'<a class="font-bold" href="/' . e($record->berkas_pks) . '" target="_blank" rel="noopener noreferrer">Lihat File</a>'
									)),
							]),
						TableRepeater::make('berkasberkas')
							->addable(false)
							->deletable(false)
							->relationship('userfiles')
							->label(new HtmlString('<span class="font-bold">Berkas Kelengkapan</span>'))
							->headers([
								Header::make('Berkas'),
								Header::make('Tautan')
							])
							->schema([
								Placeholder::make('namaberkas')
									->hiddenLabel()
									->content(fn ($record) => [
										'spvt' => 'Surat Pengajuan Verifikasi (Tanam)',
										'spvp' => 'Surat Pengajuan Verifikasi (Produksi)',
										'spskl' => 'Surat Pengajuan Penerbitan SKL',
										'sptjmt' => 'Surat Pernyataan Tangggung Jawab Mutlak (Periode Tanam)',
										'sptjmp' => 'Surat Pernyataan Tangggung Jawab Mutlak (Periode Produksi)',
										'rta' => 'Form Realisasi Tanam',
										'rpo' => 'Form Realisasi Produksi',
										'spht' => 'Statistik Pertanian Hortikultura (Periode Tanam)',
										'sphb' => 'Statistik Pertanian Hortikultura (Periode Produksi)',
										'spdst' => 'Surat Pengantar Dinas Telah Selesai Tanam',
										'spdsp' => 'Surat Pengantar Dinas Telah Selesai Produksi',
										'logbook' => 'Logbook (Tanam/Produksi)',
										'la' => 'Laporan Akhir',
										'skl' => 'Surat Keterangan Lunas',
										'ft' => 'Foto Tanam',
										'fp' => 'Foto Produksi',
										'pks' => 'Berkas PKS',
									][$record->kind] ?? $record->kind),

								Placeholder::make('link')
										->hiddenLabel()
										->extraAttributes(['class' => 'text-primary-500'])
										->content(fn ($record) => new HtmlString(
											'<a class="font-bold" href="/' . e($record->file_url) . '" target="_blank" rel="noopener noreferrer">Lihat File</a>'
										)),
							]),

					]),

				Section::make('Data Verifikasi Tanam')
					->aside()
					->description(fn ($record) => new HtmlString(
						'Data pelaksanaan dan kesimpulan verifikasi pada tahap tanam:'.
						view('components.badge-verifikasi', ['status' => $record->dataverifikasitanam->status])
					))
					->schema([
						Placeholder::make('statusverifikasitanam')
							->label('Status Verifikasi')
							->inlineLabel()
							->content(fn ($record) => new HtmlString(
								view('components.badge-verifikasi', ['status' => $record->dataverifikasitanam->status])
							)),

						Placeholder::make('noajutanam')
							->label('No. Pengajuan')
							->inlineLabel()
							->content(fn ($record) => $record->dataverifikasitanam->no_pengajuan),

						Placeholder::make('tgl_mulai')
							->label('Tanggal Pelaksanaan')
							->inlineLabel()
							->content(fn ($record) => Carbon::parse($record->realisasi->max('vt_at'))->translatedFormat('d F Y') . ' -s.d- ' . Carbon::parse($record->realisasi->min('vt_at'))->translatedFormat('d F Y')),

						Placeholder::make('fileBa')
							->label('Berita Acara Verifikasi')
							->inlineLabel()
							->extraAttributes(['class' => 'text-info-500'])
							->content(fn ($record)=>new HtmlString('
								<a class="font-bold" href="/'.$record->dataverifikasitanam->fileBa.'" rel="nofollow noreferer" target="_blank">Lihat</a>
							')),

						Placeholder::make('fileNdhp')
							->label('Nota Dinas')
							->inlineLabel()
							->extraAttributes(['class' => 'text-info-500'])
							->content(fn ($record)=>new HtmlString('
								<a class="font-bold" href="/'.$record->dataverifikasitanam->fileNdhp.'" rel="nofollow noreferer" target="_blank">Lihat</a>
							')),

						Placeholder::make('catatantanam')
							->label('Catatan Verifikasi')
							->inlineLabel()
							->content(fn ($record)=>new HtmlString('
								<p class="">'.$record->dataverifikasitanam->note.'</p>
							')),

						TableRepeater::make('Verifikator Tanam')
							->addable(false)
							->deletable(false)
							->hiddenLabel()
							->relationship('dataverifikatortanam')
							->headers([
								Header::make('Catatan/Pendapat Verifikator')
							])
							->schema([
								Placeholder::make('Catatan/Pendapat Verifikator')
									->hiddenLabel()
									->content(fn ($record) => new HtmlString(
										'<p class="bg-gray-500">'.$record->myNote.'</p>'
									)),
							])
					]),

				Section::make('Data Verifikasi Produksi')
					->aside()
					->description('Data kesimpulan hasil verifikasi pada tahap produksi')
					->schema([
						Placeholder::make('statusverifikasitanam')
							->label('Status Verifikasi')
							->inlineLabel()
							->content(fn ($record) => new HtmlString(
							view('components.badge-verifikasi', ['status' => $record->dataverifikasiproduksi->status])
						)),
						
						Placeholder::make('noajutanam')
							->label('No. Pengajuan')
							->inlineLabel()
							->content(fn ($record) => $record->dataverifikasiproduksi->no_pengajuan),

						Placeholder::make('tgl_mulai')
							->label('Tanggal Pelaksanaan')
							->inlineLabel()
							->content(fn ($record) => Carbon::parse($record->realisasi->max('vp_at'))->translatedFormat('d F Y') . ' -s.d- ' . Carbon::parse($record->realisasi->min('vp_at'))->translatedFormat('d F Y')),

						Placeholder::make('fileBa')
							->label('Berita Acara Verifikasi')
							->inlineLabel()
							->extraAttributes(['class' => 'text-info-500'])
							->content(fn ($record)=>new HtmlString('
								<a class="font-bold" href="/'.$record->dataverifikasiproduksi->fileBa.'" rel="nofollow noreferer" target="_blank">Lihat</a>
							')),

						Placeholder::make('fileNdhp')
							->label('Nota Dinas')
							->inlineLabel()
							->extraAttributes(['class' => 'text-info-500'])
							->content(fn ($record)=>new HtmlString('
								<a class="font-bold" href="/'.$record->dataverifikasiproduksi->fileNdhp.'" rel="nofollow noreferer" target="_blank">Lihat</a>
							')),

						Placeholder::make('catatantanam')
							->label('Catatan Verifikasi')
							->inlineLabel()
							->content(fn ($record)=>new HtmlString('
								<p class="">'.$record->dataverifikasiproduksi->note.'</p>
							')),

						TableRepeater::make('Verifikator Produksi')
							->addable(false)
							->deletable(false)
							->hiddenLabel()
							->relationship('dataverifikatorproduksi')
							->headers([
								Header::make('Catatan/Pendapat Verifikator')
							])
							->schema([
								Placeholder::make('Catatan/Pendapat Verifikator')
									->hiddenLabel()
									->content(fn ($record) => new HtmlString(
										'<p class="bg-gray-500">'.$record->myNote.'</p>'
									)),
							])
					]),
			]);
		}
}
