<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class DetailRealisasi2025 extends Model
{
    //
	public $table = 't2025_detailrealisasis';
	use LogsActivity;
	public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
			->logFillable('*')
			->logOnlyDirty();
    }
	protected $fillable = [
		'realisasi_id',
		'npwp',
		'no_ijin',
		'kode_spatial',
		'jenis_keg',
		'desc_keg',
		'tgl_keg',
		'value',
		'organik',
		'npk',
		'dolomit',
		'za',
		'dist_benih',
		'dist_jual',
		'keg_note',
		'file_url',
		'status',
	];

	protected static function booted()
	{
		static::addGlobalScope('npwp', function (Builder $builder) {
			if (Auth::check()) {
				$user = Auth::user();

				if ($user->hasAnyRole(['admin', 'direktur', 'Super Admin', 'verifikator'])) {
				}
				else {
					$builder->where('npwp', $user->npwp);
				}
			}
		});
	}

	public function user(): BelongsTo
	{
		return $this->belongsTo(User::class, 'npwp', 'npwp');
	}

	protected static function boot()
    {
        parent::boot();
        
        static::addGlobalScope('orderByDate', function ($query) {
            $query->orderBy('tgl_keg', 'asc'); // Bisa diubah ke 'desc' jika ingin terbaru di atas
        });
    }

	public function commitment(): BelongsTo
	{
		return $this->belongsTo(Commitment2025::class, 'no_ijin', 'no_ijin');
	}

	public function anggota(): HasOne
	{
		return $this->hasOne(MasterAnggota::class, 'ktp_petani', 'ktp_petani');
	}

	public function spatial(): HasOne
	{
		return $this->hasOne(MasterSpatial::class, 'kode_spatial', 'kode_spatial');
	}
}
