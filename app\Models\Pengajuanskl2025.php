<?php

namespace App\Models;

use App\Observers\PengajuanSkl2025Observer;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

#[ObservedBy([PengajuanSkl2025Observer::class])]
class Pengajuanskl2025 extends Model
{
    //t2025_pengajuanskl
	use SoftDeletes, LogsActivity;
	public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
			->logFillable('*')
			->logOnlyDirty();
    }

	public $table = 't2025_pengajuanskl';

	protected $fillable = [
		'pengajuan_id',
		'no_pengajuan',
		'no_skl',
		'npwp',
		'no_ijin',
		'submit_by',
		'published_date',
		'qrcode',
		'nota_attch',
		'publisher',
		'approved_by',
		'approved_at',
		'skl_upload',
		'status',
	];

	protected static function booted()
	{
		static::addGlobalScope('npwp', function (Builder $builder) {
			if (Auth::check()) {
				$user = Auth::user();

				if ($user->hasAnyRole(['admin', 'direktur', 'Super Admin', 'verifikator'])) {
				}
				else {
					$builder->where('npwp', $user->npwp);
				}
			}
		});
	}

	public function user(): BelongsTo{
		return $this->belongsTo(User::class,'npwp', 'npwp');
	}

	public function approvedBy(): BelongsTo{
		return $this->belongsTo(User::class,'approved_by', 'id');
	}

	public function datauser(): BelongsTo{
		return $this->belongsTo(DataUser::class,'npwp', 'npwp_company');
	}

	public function commitment(): BelongsTo{
		return $this->belongsTo(Commitment2025::class,'no_ijin', 'no_ijin');
	}

	public function realisasi(): HasMany{
		return $this->hasMany(Realisasi2025::class, 'no_ijin', 'no_ijin');
	}

	public function detailrealisasitanam(): HasMany{
		return $this->hasMany(DetailRealisasi2025::class, 'no_ijin', 'no_ijin')->where('jenis_keg', 'tanam');
	}

	public function detailrealisasiproduksi(): HasMany{
		return $this->hasMany(DetailRealisasi2025::class, 'no_ijin', 'no_ijin')->where('jenis_keg', 'panen');
	}

	public function detailrealisasidistribusi(): HasMany{
		return $this->hasMany(DetailRealisasi2025::class, 'no_ijin', 'no_ijin')->where('jenis_keg', 'distribusi');
	}

	public function detailrealisasipupuk(): HasMany{
		return $this->hasMany(DetailRealisasi2025::class, 'no_ijin', 'no_ijin')->where('jenis_keg', 'pupuk');
	}

	public function detailrealisasimulsa(): HasMany{
		return $this->hasMany(DetailRealisasi2025::class, 'no_ijin', 'no_ijin')->where('jenis_keg', 'mulsa');
	}

	public function dataverifikasitanam(): HasOne{
		return $this->hasOne(PengajuanVerifikasi::class, 'no_ijin', 'no_ijin')
        ->where('kind', 'PVT')
        ->latest();
	}

	public function dataverifikasiproduksi(): HasOne{
		return $this->hasOne(PengajuanVerifikasi::class, 'no_ijin', 'no_ijin')
        ->where('kind', 'PVP')
        ->latest();
	}

	public function dataverifikatortanam(): HasMany {
		$latestPengajuan = PengajuanVerifikasi::where('no_ijin', $this->no_ijin)
			->where('kind', 'PVT')
			->latest()
			->first();

		// Handle when latestPengajuan is null
		$pengajuanId = $latestPengajuan ? $latestPengajuan->no_pengajuan : null;

		return $this->hasMany(VerificatorAssignment::class, 'no_ijin', 'no_ijin')
			->when($pengajuanId, function ($query, $pengajuanId) {
				return $query->where('kode_pengajuan', $pengajuanId);
			});
	}

	public function dataverifikatorproduksi(): HasMany {
		$latestPengajuan = PengajuanVerifikasi::where('no_ijin', $this->no_ijin)
			->where('kind', 'PVP')
			->latest()
			->first();

		// Handle when latestPengajuan is null
		$pengajuanId = $latestPengajuan ? $latestPengajuan->no_pengajuan : null;

		return $this->hasMany(VerificatorAssignment::class, 'no_ijin', 'no_ijin')
			->when($pengajuanId, function ($query, $pengajuanId) {
				return $query->where('kode_pengajuan', $pengajuanId);
			});
	}

	public function completed(): HasOne
	{
		return $this->hasOne(Completed::class, 'no_ijin', 'no_ijin');
	}
}