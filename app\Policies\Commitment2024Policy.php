<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\Commitment2024;
use App\Models\User;

class Commitment2024Policy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any Commitment2024');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Commitment2024 $commitment2024): bool
    {
        return $user->checkPermissionTo('view Commitment2024');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create Commitment2024');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Commitment2024 $commitment2024): bool
    {
        return $user->checkPermissionTo('update Commitment2024');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Commitment2024 $commitment2024): bool
    {
        return $user->checkPermissionTo('delete Commitment2024');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any Commitment2024');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Commitment2024 $commitment2024): bool
    {
        return $user->checkPermissionTo('restore Commitment2024');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any Commitment2024');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, Commitment2024 $commitment2024): bool
    {
        return $user->checkPermissionTo('replicate Commitment2024');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder Commitment2024');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Commitment2024 $commitment2024): bool
    {
        return $user->checkPermissionTo('force-delete Commitment2024');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any Commitment2024');
    }
}
