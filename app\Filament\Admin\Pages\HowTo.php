<?php

namespace App\Filament\Admin\Pages;

use Filament\Pages\Page;
use League\CommonMark\Environment\Environment;
use League\CommonMark\Extension\GithubFlavoredMarkdownExtension;
use League\CommonMark\Extension\CommonMark\CommonMarkCoreExtension;
use League\CommonMark\MarkdownConverter;

class HowTo extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';
    protected static ?string $navigationLabel = 'Panduan Penggunaan';
    protected static ?string $title = 'Panduan Penggunaan (DRAFT)';
    protected static ?string $navigationGroup = 'Documentations';
    protected static ?int $navigationSort = 3;

    public static function shouldRegisterNavigation(): bool
    {
        return true; // Visible to all users
    }

    protected static string $view = 'filament.admin.pages.how-to';

    public function getViewData(): array
    {
        $markdownPath = base_path('documentations/how-to-guide.md');

        if (!file_exists($markdownPath)) {
            return [
                'howToHtml' => '<div class="text-red-500">How-To Guide file not found. Please make sure the file exists at: ' . $markdownPath . '</div>',
            ];
        }

        $markdownContent = file_get_contents($markdownPath);

        if (empty($markdownContent)) {
            return [
                'howToHtml' => '<div class="text-red-500">How-To Guide file is empty.</div>',
            ];
        }

        // Create a new environment with GFM extension
        $environment = new Environment([
            'html_input' => 'strip',
            'allow_unsafe_links' => false,
        ]);

        // Add the CommonMark core extension and GFM extension
        $environment->addExtension(new CommonMarkCoreExtension());
        $environment->addExtension(new GithubFlavoredMarkdownExtension());

        // Create a new converter using the configured environment
        $converter = new MarkdownConverter($environment);

        // Convert the markdown to HTML
        $result = $converter->convert($markdownContent);
        $html = $result->getContent();

        return [
            'howToHtml' => $html,
        ];
    }
}
