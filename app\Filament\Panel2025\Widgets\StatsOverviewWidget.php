<?php

namespace App\Filament\Panel2025\Widgets;

use App\Models\Commitment2025;
use Carbon\Carbon;
use Filament\Support\Enums\IconPosition;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Number;

class StatsOverviewWidget extends BaseWidget
{
    use InteractsWithPageFilters;

    protected static ?int $sort = 0;

    protected function getStats(): array
    {
		if(Auth::user()->hasRole('importir')){
			
		};

		$periode = $this->filters['periodeFilter'] ?? null;
		$commitments = Commitment2025::when($periode && $periode !== 'all', function ($query) use ($periode) {
			return $query->where('periodetahun', $periode);
		})
		->withCount(['realisasi'])
		->get();

		$countCommitment = number_format($commitments->count(), 0, ',', '.');
		$petaniCount = number_format($commitments->flatMap->realisasi->pluck('ktp_petani')->unique()->count(), 0, ',', '.');
		$realisasiCount = number_format($commitments->sum('realisasi_count'), 0, ',', '.');
		$totalLuasTanam = number_format($commitments->sum(function ($commitment) {
			return $commitment->realisasi->sum('luas_tanam');
		}), 2, ',', '.');
		$totalVolume = number_format($commitments->sum(function ($commitment) {
			return $commitment->realisasi->sum('volume');
		}), 2, ',', '.');	

        return [
			Stat::make('', $countCommitment)
				->description("PPRK")
				->chart([7, 2, 10, 3, 15, 4, 17])
				->descriptionIcon('icon-journal-bookmark-fill', IconPosition::Before)
				->color('danger'),
			Stat::make('', $petaniCount)
				->description("Petani")
				->chart([7, 2, 10, 3, 15, 4, 17])
				->descriptionIcon('icon-people-fill', IconPosition::Before)
				->color('info'),
			Stat::make('', $totalLuasTanam . ' m²')
				->description("Luas Tanam")
				->chart([7, 2, 10, 3, 15, 4, 17])
				->descriptionIcon('icon-growing-plant', IconPosition::Before)
				->color('success'),
			Stat::make('', $totalVolume . ' ton')
				->description("Volume produksi")
				->chart([7, 2, 10, 3, 15, 4, 17])
				->descriptionIcon('icon-garlic-line', IconPosition::Before)
				->color('warning'),
		];
    }
}
