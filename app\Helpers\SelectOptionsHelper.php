<?php

namespace App\Helpers;

use Illuminate\Support\Collection;

class SelectOptionsHelper
{
    /**
     * Filter out null values from select options array
     * 
     * @param array $options
     * @return array
     */
    public static function filterNullOptions(array $options): array
    {
        return array_filter($options, function($value, $key) {
            return !is_null($value) && !is_null($key) && $value !== '' && $key !== '';
        }, ARRAY_FILTER_USE_BOTH);
    }

    /**
     * Filter out null values from collection before converting to array
     * 
     * @param Collection $collection
     * @return array
     */
    public static function filterNullCollection(Collection $collection): array
    {
        return $collection->filter(function($value, $key) {
            return !is_null($value) && !is_null($key) && $value !== '' && $key !== '';
        })->toArray();
    }

    /**
     * Safe pluck method that filters out null values
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $value
     * @param string $key
     * @return array
     */
    public static function safePluck($query, string $value, string $key): array
    {
        return $query
            ->whereNotNull($value)
            ->where($value, '!=', '')
            ->whereNotNull($key)
            ->where($key, '!=', '')
            ->pluck($value, $key)
            ->filter()
            ->toArray();
    }
}
