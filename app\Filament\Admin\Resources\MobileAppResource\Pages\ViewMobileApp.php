<?php

namespace App\Filament\Admin\Resources\MobileAppResource\Pages;

use App\Filament\Admin\Resources\MobileAppResource;
use Filament\Actions;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\HtmlString;
use League\CommonMark\Environment\Environment;
use League\CommonMark\Extension\CommonMark\CommonMarkCoreExtension;
use League\CommonMark\Extension\GithubFlavoredMarkdownExtension;
use League\CommonMark\MarkdownConverter;

class ViewMobileApp extends ViewRecord
{
    protected static string $resource = MobileAppResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\Action::make('download')
                ->label('Unduh Aplikasi')
                ->icon('heroicon-o-arrow-down-tray')
                ->url(fn () => route('mobile-app.download', ['mobileApp' => $this->record]))
                ->openUrlInNewTab()
                ->visible(fn () => $this->record->file_exists),
        ];
    }

    /**
     * Helper method untuk mengonversi markdown ke HTML
     */
    protected function markdownToHtml(?string $markdown, string $emptyMessage = 'Tidak ada data'): HtmlString
    {
        if (empty($markdown)) {
            return new HtmlString('<em>' . $emptyMessage . '</em>');
        }

        // Konfigurasi Markdown parser
        $environment = new Environment([
            'html_input' => 'strip',
            'allow_unsafe_links' => false,
        ]);

        // Add the CommonMark core extension and GFM extension
        $environment->addExtension(new CommonMarkCoreExtension());
        $environment->addExtension(new GithubFlavoredMarkdownExtension());

        // Create a new converter using the configured environment
        $converter = new MarkdownConverter($environment);

        // Convert the markdown to HTML
        $result = $converter->convert($markdown);
        $html = $result->getContent();

        return new HtmlString('<div class="prose dark:prose-invert max-w-none">' . $html . '</div>');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Aplikasi')
					->aside()
                    ->schema([
                        Hidden::make('recommended_specs'),
						TextInput::make('name')
                            ->label('Nama Aplikasi')
							->inlineLabel(),
                        TextInput::make('version')
                            ->label('Versi')
							->inlineLabel(),
                        TextInput::make('version_code')
                            ->label('Kode Versi')
							->inlineLabel(),
                        Textarea::make('description')
							->inlineLabel()
                            ->label('Deskripsi'),
						TextInput::make('file_name')
                            ->label('Nama File APK')
                            ->inlineLabel()
                            ->dehydrated(),
						TagsInput::make('required_features')
							->label('Fitur yang Diperlukan')
							->placeholder('Tambahkan fitur dan tekan Enter'),
                    ]),

				Section::make('Minimum Requirements')
					->aside()
                    ->schema([
                        Placeholder::make('minreq')
							->hiddenLabel()
							->content(fn ($get) => $this->markdownToHtml($get('recommended_specs'), 'Tidak ada spesifikasi yang direkomendasikan')),
                    ]),
            ]);
    }
}