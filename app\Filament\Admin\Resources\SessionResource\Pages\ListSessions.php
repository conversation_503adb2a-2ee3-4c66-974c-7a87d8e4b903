<?php

namespace App\Filament\Admin\Resources\SessionResource\Pages;

use App\Filament\Admin\Resources\SessionResource;
use App\Filament\Admin\Resources\SessionResource\Widgets\ActiveUsersOverview;
use App\Models\Session;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;

class ListSessions extends ListRecords
{
    protected static string $resource = SessionResource::class;

    protected function getHeaderWidgets(): array
    {
        return [
            ActiveUsersOverview::class,
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('refresh')
                ->label('Refresh Data')
                ->color('success')
                ->icon('heroicon-o-arrow-path')
                ->action(function () {
                    $this->refreshData();
                }),
            Actions\ActionGroup::make([
                Actions\Action::make('clear_all_inactive')
                    ->label('Hapus Semua Session Tidak Aktif')
                    ->color('warning')
                    ->icon('heroicon-o-clock')
                    ->requiresConfirmation()
                    ->modalHeading('Hapus Semua Session Tidak Aktif')
                    ->modalDescription('Apakah Anda yakin ingin menghapus semua session yang tidak aktif (tidak aktif dalam 5 menit terakhir)?')
                    ->modalSubmitActionLabel('Ya, Hapus')
                    ->action(function () {
                        // Hapus semua session yang tidak aktif (tidak aktif dalam 5 menit terakhir)
                        $inactiveTime = Carbon::now()->subMinutes(5)->getTimestamp();
                        $deleted = Session::query()
                            ->where('last_activity', '<', $inactiveTime)
                            ->delete();

                        $this->refreshData();
                        Notification::make()
                            ->success()
                            ->title($deleted . ' session tidak aktif berhasil dihapus.')
                            ->send();
                    }),
                Actions\Action::make('clear_inactive_24h')
                    ->label('Hapus Session Tidak Aktif > 24 Jam')
                    ->color('warning')
                    ->icon('heroicon-o-clock')
                    ->requiresConfirmation()
                    ->modalHeading('Hapus Session Tidak Aktif > 24 Jam')
                    ->modalDescription('Apakah Anda yakin ingin menghapus semua session yang tidak aktif selama lebih dari 24 jam?')
                    ->modalSubmitActionLabel('Ya, Hapus')
                    ->action(function () {
                        // Hapus semua session yang tidak aktif selama lebih dari 24 jam
                        $inactiveTime = Carbon::now()->subHours(24)->getTimestamp();
                        $deleted = Session::query()
                            ->where('last_activity', '<', $inactiveTime)
                            ->delete();

                        $this->refreshData();
                        Notification::make()
                            ->success()
                            ->title($deleted . ' session tidak aktif berhasil dihapus.')
                            ->send();
                    }),
                Actions\Action::make('clear_inactive_week')
                    ->label('Hapus Session Tidak Aktif > 1 Minggu')
                    ->color('warning')
                    ->icon('heroicon-o-clock')
                    ->requiresConfirmation()
                    ->modalHeading('Hapus Session Tidak Aktif > 1 Minggu')
                    ->modalDescription('Apakah Anda yakin ingin menghapus semua session yang tidak aktif selama lebih dari 1 minggu?')
                    ->modalSubmitActionLabel('Ya, Hapus')
                    ->action(function () {
                        // Hapus semua session yang tidak aktif selama lebih dari 1 minggu
                        $inactiveTime = Carbon::now()->subWeek()->getTimestamp();
                        $deleted = Session::query()
                            ->where('last_activity', '<', $inactiveTime)
                            ->delete();

                        $this->refreshData();
                        Notification::make()
                            ->success()
                            ->title($deleted . ' session tidak aktif berhasil dihapus.')
                            ->send();
                    }),
                Actions\Action::make('clear_inactive_month')
                    ->label('Hapus Session Tidak Aktif > 1 Bulan')
                    ->color('warning')
                    ->icon('heroicon-o-clock')
                    ->requiresConfirmation()
                    ->modalHeading('Hapus Session Tidak Aktif > 1 Bulan')
                    ->modalDescription('Apakah Anda yakin ingin menghapus semua session yang tidak aktif selama lebih dari 1 bulan?')
                    ->modalSubmitActionLabel('Ya, Hapus')
                    ->action(function () {
                        // Hapus semua session yang tidak aktif selama lebih dari 1 bulan
                        $inactiveTime = Carbon::now()->subMonth()->getTimestamp();
                        $deleted = Session::query()
                            ->where('last_activity', '<', $inactiveTime)
                            ->delete();

                        $this->refreshData();
                        Notification::make()
                            ->success()
                            ->title($deleted . ' session tidak aktif berhasil dihapus.')
                            ->send();
                    }),
            ])->label('Hapus Berdasarkan Waktu')
                ->icon('heroicon-o-clock'),

            Actions\ActionGroup::make([
                Actions\Action::make('clear_guests')
                    ->label('Hapus Session Tamu')
                    ->color('warning')
                    ->icon('heroicon-o-user-minus')
                    ->requiresConfirmation()
                    ->modalHeading('Hapus Session Tamu')
                    ->modalDescription('Apakah Anda yakin ingin menghapus semua session tamu?')
                    ->modalSubmitActionLabel('Ya, Hapus')
                    ->action(function () {
                        // Hapus semua session tamu (user_id is null)
                        $deleted = Session::guests()->delete();

                        $this->refreshData();
                        Notification::make()
                            ->success()
                            ->title($deleted . ' session tamu berhasil dihapus.')
                            ->send();
                    }),
                Actions\Action::make('clear_offline_users')
                    ->label('Hapus Session Pengguna Offline')
                    ->color('warning')
                    ->icon('heroicon-o-user-minus')
                    ->requiresConfirmation()
                    ->modalHeading('Hapus Session Pengguna Offline')
                    ->modalDescription('Apakah Anda yakin ingin menghapus semua session pengguna yang offline (tidak aktif dalam 5 menit terakhir)?')
                    ->modalSubmitActionLabel('Ya, Hapus')
                    ->action(function () {
                        // Hapus semua session pengguna yang offline
                        $currentSession = session()->getId();
                        $offlineTime = Carbon::now()->subMinutes(5)->getTimestamp();
                        $deleted = Session::query()
                            ->whereNotNull('user_id')
                            ->where('id', '!=', $currentSession)
                            ->where('last_activity', '<', $offlineTime)
                            ->delete();

                        $this->refreshData();
                        Notification::make()
                            ->success()
                            ->title($deleted . ' session pengguna offline berhasil dihapus.')
                            ->send();
                    }),
                Actions\Action::make('clear_all')
                    ->label('Hapus Semua Session')
                    ->color('danger')
                    ->icon('heroicon-o-trash')
                    ->requiresConfirmation()
                    ->modalHeading('Hapus Semua Session')
                    ->modalDescription('Apakah Anda yakin ingin menghapus semua session? Tindakan ini akan mengeluarkan semua pengguna dari aplikasi.')
                    ->modalSubmitActionLabel('Ya, Hapus Semua')
                    ->action(function () {
                        // Hapus semua session kecuali session pengguna yang sedang login
                        $currentSession = session()->getId();
                        $deleted = Session::query()
                            ->where('id', '!=', $currentSession)
                            ->delete();

                        $this->refreshData();
                        Notification::make()
                            ->success()
                            ->title($deleted . ' session berhasil dihapus.')
                            ->send();
                    }),
            ])->label('Hapus Berdasarkan Tipe')
                ->icon('heroicon-o-user-group'),
        ];
    }

    private function refreshData(): void
    {
        $this->dispatch('refresh');
    }

    public function getPollingInterval(): ?string
    {
        // Auto refresh setiap 30 detik
        return '30s';
    }
}
