<?php

namespace App\Filament\Admin\Resources\AnnouncementResource\Pages;

use App\Filament\Admin\Resources\AnnouncementResource;
use App\Models\Announcement;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class ListAnnouncements extends ListRecords
{
    protected static string $resource = AnnouncementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Pengumuman baru')->icon('heroicon-s-megaphone'),
        ];
    }

	public function getSubheading(): string|Htmlable|null
	{
		$publisher = Auth::user()->hasAnyRole(['admin', 'Super Admin']);
		if($publisher)
		{
			return 'Daftar Pengumuman untuk pengguna aplikasi';
		}
		return 'Pengumuman untuk Anda';
	}

	protected function getTableQuery(): Builder
	{
		$user = Auth::user();
		return $user->hasRole(['Admin', 'Super Admin']) 
			? Announcement::query() 
			: Announcement::where('is_active', true)->where('role_id', $user->roles->first()->id);
	}

	protected function paginateTableQuery(Builder $query): Paginator
	{
		return $query->simplePaginate(($this->getTableRecordsPerPage() === 'all') ? $query->count() : $this->getTableRecordsPerPage());
	}
}
