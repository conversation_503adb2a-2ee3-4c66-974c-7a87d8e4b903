<?php

namespace App\Filament\Admin\Resources\CmsCategoryResource\Pages;

use App\Filament\Admin\Resources\CmsCategoryResource;
use App\Models\CmsCategory;
use Illuminate\Support\Str;
use Filament\Actions;
use Filament\Forms\Components\Select;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteAction;
use Filament\Tables\Actions\RestoreAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\ColorColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Storage;

class ListCmsCategories extends ListRecords
{
    protected static string $resource = CmsCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Kategori Baru'),
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('cover_image')->circular()->defaultImageUrl(Storage::url('resources/svg/person-circle.svg')),
                TextColumn::make('name')
                    ->label('Nama')
                    ->description(fn(CmsCategory $category)=> Str::of($category->description)->limit(50))
                    ->label('Category')
                    ->searchable(),
                TextColumn::make('for')
                    ->badge()
                    ->label('Untuk')
                    ->sortable()
                    ->label('Jenis')
                    ->searchable(),
                TextColumn::make('type')
                    ->badge()
                    ->sortable()
                    ->label('Type')
                    ->searchable(),
                IconColumn::make('icon')
                    ->label('Icon')
                    ->color(fn ($record) => $record->color)
                    ->icon(fn ($record) => $record->icon)
                    ->sortable()
                    ->searchable(),
                ColorColumn::make('color')
                    ->sortable()
                    ->searchable(),
                ToggleColumn::make('is_active')
                    ->sortable(),
                ToggleColumn::make('show_in_menu')
                    ->label('Menu')
                    ->sortable(),
                TextColumn::make('parent.name')
                    ->sortable()
                    ->label('Induk')
                    ->sortable(),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
				SelectFilter::make('for')
					->options([
						'Artikel' => 'Artikel',
						'Berita' => 'Berita',
						'Event' => 'Event',
					])
            ])
            ->actions([
                ViewAction::make()
                    ->iconButton(),
                EditAction::make()
                    ->iconButton(),
                DeleteAction::make()
                    ->iconButton(),
                ForceDeleteAction::make()
                    ->iconButton(),
                RestoreAction::make()
                    ->iconButton(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    // ForceDeleteAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ]);
    }
}
