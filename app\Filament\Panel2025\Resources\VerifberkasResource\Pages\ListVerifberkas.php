<?php

namespace App\Filament\Panel2025\Resources\VerifberkasResource\Pages;

use App\Filament\Panel2025\Resources\VerifberkasResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListVerifberkas extends ListRecords
{
    protected static string $resource = VerifberkasResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
