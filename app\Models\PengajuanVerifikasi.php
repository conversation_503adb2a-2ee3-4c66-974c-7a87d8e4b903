<?php

namespace App\Models;

use App\Observers\PengajuanVerifikasiObserver;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

#[ObservedBy([PengajuanVerifikasiObserver::class])]
class PengajuanVerifikasi extends Model
{
    use SoftDeletes, LogsActivity;
	public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
			->logFillable('*')
			->logOnlyDirty();
    }

	public $table = 't2025_aju_verifikasis';

	protected $fillable = [
		'kind',
		'tcode',
		'npwp',
		'no_ijin',
		'status',
		'note',

		'fileBa', //berita acara hasil pemeriksaan realisasi tanam
		'fileNdhp', //nota dinas hasil pemeriksaan realisasi tanam

		'verif_by',
		'verif_at',
		'report_url', //laporan hasil verifikasi
		'metode', //metode verifikasi
	];

    protected static function booted()
    {
        static::creating(function ($data) {
            $data->no_pengajuan = strtoupper($data->kind) . '-' . Carbon::now()->format('dmy') . '-' . time();
        });
    }

    public function commitment(): BelongsTo
    {
        return $this->belongsTo(Commitment2025::class, 'no_ijin', 'no_ijin');
    }

	public function hasInCommon()
	{
		return $this->hasMany(PengajuanVerifikasi::class, 'no_ijin', 'no_ijin')->where('kind', '!=', 'PVS');
	}
	

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'npwp', 'npwp');
    }

    public function verifikator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verif_by', 'id');
    }

    public function pks(): HasMany
    {
        return $this->hasMany(Pks2025::class, 'no_ijin', 'no_ijin');
    }

    public function realisasi(): HasMany
    {
        return $this->hasMany(Realisasi2025::class, 'no_ijin', 'no_ijin');
    }

    public function userfiles(): HasMany
    {
        return $this->hasMany(Userfile::class, 'no_ijin', 'no_ijin');
    }

    public function berkaspengajuan(): HasMany
    {
        return $this->hasMany(Userfile::class, 'no_ijin', 'no_ijin')
            ->whereIn('kind', ['spvt', 'spvp', 'spskl']); //hanya berkas-berkas sesuai pengajuan. bukan semua berkas
    }

    public function assignments(): HasMany
    {
        return $this->hasMany(VerificatorAssignment::class, 'pengajuan_id', 'id');
    }
	

    public function authAssignments(): HasMany
    {
        return $this->hasMany(VerificatorAssignment::class, 'pengajuan_id', 'id')->where('user_id',Auth::user()->id);
    }

    public function assignmentsInCommitment(): HasMany
    {
        return $this->hasMany(VerificatorAssignment::class, 'no_ijin', 'no_ijin');
    }

	public function getGroupedRealisasiAttribute()
	{
		return $this->realisasi()
			->selectRaw('kode_poktan, COUNT(*) as total')
			->groupBy('kode_poktan')
			->get();
	}
}
