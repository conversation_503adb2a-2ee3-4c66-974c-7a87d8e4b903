<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Commitment2025;
use App\Models\Completed;
use Filament\Support\Enums\IconPosition;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AdminOverview extends BaseWidget
{
	protected static ?int $sort = 0;
    protected function getStats(): array
    {
		if(Auth::user()->hasRole('importir')){
			$newCommitmentCount = Commitment2025::query()->where('npwp', Auth::user()->npwp)->count();
			$oldCommitmentCount = DB::table('pull_riphs')->where('npwp', Auth::user()->npwp)->count();
			$completeds = Completed::query()->where('npwp', Auth::user()->npwp)->count();
			
			//tanam
			$rawCommitmentTanam = Commitment2025::query()->where('npwp', Auth::user()->npwp)->sum('luas_wajib_tanam');
			$newCommitmentTanam = $rawCommitmentTanam/10000;
			$oldCommitmentTanam = DB::table('pull_riphs')->where('npwp', Auth::user()->npwp)->sum('luas_wajib_tanam');

			$rawRealisasiTanam = Completed::query()->where('npwp', Auth::user()->npwp)->where('periodetahun', '>=',2025)->sum('luas_tanam');
			$newRealisasiTanam = $rawRealisasiTanam/10000;
			$oldRealisasiTanam = Completed::query()->where('npwp', Auth::user()->npwp)->where('periodetahun', '<',2025)->sum('luas_tanam');
			
			//produksi
			$rawCommitmentPanen = Commitment2025::query()->where('npwp', Auth::user()->npwp)->sum('volume_produksi');
			$newCommitmentPanen = $rawCommitmentPanen/1000;
			$oldCommitmentPanen = DB::table('pull_riphs')->where('npwp', Auth::user()->npwp)->sum('volume_produksi');

			$rawRealisasiPanen = Completed::query()->where('npwp', Auth::user()->npwp)->where('periodetahun', '>=',2025)->sum('volume');
			$newRealisasiPanen = $rawRealisasiPanen/1000;
			$oldRealisasiPanen = Completed::query()->where('npwp', Auth::user()->npwp)->where('periodetahun', '<',2025)->sum('volume');
			
		}else{
			$newCommitmentCount = Commitment2025::query()->count();
			$oldCommitmentCount = DB::table('pull_riphs')->count();
			$completeds = Completed::query()->count();
			
			$rawCommitmentTanam = Commitment2025::query()->sum('luas_wajib_tanam');
			$newCommitmentTanam = $rawCommitmentTanam/10000;
			$oldCommitmentTanam = DB::table('pull_riphs')->sum('luas_wajib_tanam');
			
			$rawRealisasiTanam = Completed::query()->where('periodetahun', '>=',2025)->sum('luas_tanam');
			$newRealisasiTanam = $rawRealisasiTanam/10000;
			$oldRealisasiTanam = Completed::query()->where('periodetahun', '<',2025)->sum('luas_tanam');

			$rawCommitmentPanen = Commitment2025::query()->sum('volume_produksi');
			$newCommitmentPanen = $rawCommitmentPanen/1000;
			$oldCommitmentPanen = DB::table('pull_riphs')->sum('volume_produksi');

			$rawRealisasiPanen = Completed::query()->where('periodetahun', '>=',2025)->sum('volume');
			$newRealisasiPanen = $rawRealisasiPanen/1000;
			$oldRealisasiPanen = Completed::query()->where('periodetahun', '<',2025)->sum('volume');
		}

		$commitmentCount = number_format($newCommitmentCount + $oldCommitmentCount,0,',','.');
		$commitmentTanam = number_format($newCommitmentTanam + $oldCommitmentTanam,0,',','.');
		$realisasiTanam = number_format($newRealisasiTanam + $oldRealisasiTanam,0,',','.');
		
		$commitmentPanen = number_format($newCommitmentPanen + $oldCommitmentPanen,0,',','.');
		$realisasiPanen = number_format($newRealisasiPanen + $oldRealisasiPanen,0,',','.');

		return [
            Stat::make('SKL', $completeds)
				->description("dari {$commitmentCount} Komitmen")
				->descriptionIcon('icon-journal-bookmark-fill', IconPosition::Before)
				->chart([7, 2, 10, 3, 15, 4, 17])
				->color('danger'),
			Stat::make('Realisasi Tanam', $realisasiTanam . ' ha')
				->description("dari {$commitmentTanam} ha Komitmen")
				->chart([7, 2, 10, 3, 15, 4, 17])
				->descriptionIcon('icon-rulers', IconPosition::Before)
				->color('success'),
			Stat::make('Realisasi Produksi', $realisasiPanen . ' ton')
				->description("dari {$commitmentPanen} ton Komitmen")
				->chart([7, 2, 10, 3, 15, 4, 17])
				->descriptionIcon('icon-truck-flatbed', IconPosition::Before)
				->color('warning'),
        ];
    }
}
