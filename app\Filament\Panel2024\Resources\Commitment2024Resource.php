<?php

namespace App\Filament\Panel2024\Resources;

use App\Filament\Panel2024\Resources\Commitment2024Resource\Pages;
use App\Filament\Panel2024\Resources\Commitment2024Resource\RelationManagers;
use App\Models\Commitment2024;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms;
use Filament\Forms\Components\{Actions, Fieldset, FileUpload, Hidden, Placeholder, Section, TextInput};
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\HtmlString;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class Commitment2024Resource extends Resource
{
    protected static ?string $model = Commitment2024::class;

	protected static ?string $pluralModelLabel = 'Daftar Komitmen';
	protected static ?int $navigationSort = -1;
	protected static ?string $navigationLabel = 'Daftar Komitmen';
	protected static ?string $navigationIcon = 'heroicon-o-bookmark';
	
	public static function getGloballySearchableAttributes(): array
	{
		return ['no_ijin', 'npwp','datauser.company_name'];
	}

	public static function getGlobalSearchResultDetails($record): array
	{
		return [
			'Nomor Ijin' => $record->no_ijin,
			'NPWP' => $record->npwp,
			'Nama Perusahaan' => $record->datauser->company_name,
		];
	}

	protected static ?string $currentUser = null;

    public static function form(Form $form): Form
    {
        return $form
		->schema([
            Section::make('Data Komitmen')
                ->aside()
                ->description('Data Komitmen sesuai PPRK')
                ->schema([
                    Placeholder::make('komoditas')
                        ->label('Komoditas')
                        ->inlineLabel()
                        ->content(fn ($record) => $record->no_hs),
                    Placeholder::make('kuota')
                        ->label('Volume Import')
                        ->inlineLabel()
                        ->content(fn ($record) => $record->volume_riph),
                    Placeholder::make('wajibproduksi')
                        ->label('Komitmen Produksi')
                        ->inlineLabel()
                        ->content(fn ($record) => $record->volume_produksi),
                    Placeholder::make('wajibtanam')
                        ->label('Komitmen Tanam')
                        ->inlineLabel()
                        ->content(fn ($record) => $record->luas_wajib_tanam),
                    Placeholder::make('mulai')
                            ->label('Tgl Ijin')
                            ->inlineLabel()
                            ->content(fn ($record) => $record->tgl_ijin),
                    Placeholder::make('akhir')
                        ->label('Tgl Berakhir')
                        ->inlineLabel()
                        ->content(fn ($record) => $record->tgl_akhir),
                ]),

			Section::make('Berkas Tanam')
				->aside()
				->description('Berkas-berkas kelengkapan tahap tanam yang wajib diunggah.')
				->schema([
					Placeholder::make('sptjmTanam')
						->inlineLabel()
						->label('SPTJM Tanam')
						->content(function ($record) {
							$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
							$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
							$tahun = $record->periodetahun;
							$fileName = $record->userDocs?->sptjmtanam;

							if ($fileName) {
								if (str_starts_with($fileName, 'uploads/')) {
									$fileUrl ="/{$fileName}";
								} else {
									$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
								}
					
								return new HtmlString('<a class="font-bold" href="' . $fileUrl . '" target="_blank">Terlampir</a>');
							}

							return 'Tidak ada';
						}),

					Placeholder::make('rta')
						->inlineLabel()
						->label('Rencana/Realisasi Tanam')
						->content(function ($record) {
							$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
							$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
							$tahun = $record->periodetahun;
							$fileName = $record->userDocs?->rta;

							if ($fileName) {
								if (str_starts_with($fileName, 'uploads/')) {
									$fileUrl ="/{$fileName}";
								} else {
									$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
								}
					
								return new HtmlString('<a class="font-bold" href="' . $fileUrl . '" target="_blank">Terlampir</a>');
							}

							return 'Tidak ada';
						}),

					Placeholder::make('sphtanam')
						->inlineLabel()
						->label('SPH Tanam')
						->content(function ($record) {
							$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
							$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
							$tahun = $record->periodetahun;
							$fileName = $record->userDocs?->sphtanam;

							if ($fileName) {
								if (str_starts_with($fileName, 'uploads/')) {
									$fileUrl ="/{$fileName}";
								} else {
									$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
								}
					
								return new HtmlString('<a class="font-bold" href="' . $fileUrl . '" target="_blank">Terlampir</a>');
							}

							return 'Tidak ada';
						}),

					Placeholder::make('logbooktanam')
						->inlineLabel()
						->label('Logbook Tanam')
						->content(function ($record) {
							$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
							$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
							$tahun = $record->periodetahun;
							$fileName = $record->userDocs?->logbooktanam;

							if ($fileName) {
								if (str_starts_with($fileName, 'uploads/')) {
									$fileUrl ="/{$fileName}";
								} else {
									$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
								}
					
								return new HtmlString('<a class="font-bold" href="' . $fileUrl . '" target="_blank">Terlampir</a>');
							}

							return 'Tidak ada';
						}),
					Actions::make([
						Action::make('UnggahBerkasTanam')
							->modalHeading('Unggah Berkas Tanam')
							// ->visible(function (Commitment2024 $record){
							// 	if(Auth::user()->hasRole('importir')) {
							// 		if(in_array($record->ajutanam()->withoutGlobalScopes()->first()?->status, ['1', '2', '4'])){
							// 			return false;
							// 		}
							// 		return true;
							// 	}
							// 	return false;
							// })
							->visible(fn () => Auth::user()->hasRole('importir'))
							->icon('icon-cloud-upload')
							->iconButton()
							->button()
							->color('info')
							->fillForm(fn (Commitment2024 $record): array => [
								'sptjmtanam' => $record->userDocs?->sptjmtanam,
								'rta' => $record->userDocs?->rta,
								'sphtanam' => $record->userDocs?->sphtanam,
								'logbooktanam' => $record->userDocs?->logbooktanam,
							])
							->form([
								FileUpload::make('sptjmtanam')
									->openable()
									->required()
									->maxSize(2048)
									->inlineLabel()
									->columnSpan(1)
									->downloadable()
									->deletable()
									->label('SPTJM Tanam')
									->visibility('public')
									->panelAspectRatio('5:1')
									->imagePreviewHeight('50')
									->fetchFileInformation(true)
									->helperText('Maksimal 2MB, format PDF')
									->disk('public')
									->directory(function ($record) {
										$tahun = $record->periodetahun;
										$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
										$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
										return "uploads/{$cleanNpwp}/{$tahun}";
									})
									->rules([
										'file',
										'mimetypes:application/pdf',
										'mimes:pdf'
									])
									->validationMessages([
										'mimetypes' => 'Hanya file PDF yang diperbolehkan',
										'mimes' => 'Ekstensi file harus .pdf',
									])
									->getUploadedFileNameForStorageUsing(
										function (TemporaryUploadedFile $file, $get, $record): string {
											$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

											return 'sptjmtanam_'. $cleanNoIjin .'_'.uniqid(). '.' . $file->getClientOriginalExtension();
										}
									),
								FileUpload::make('rta')
									->openable()
									->required()
									->maxSize(2048)
									->inlineLabel()
									->columnSpan(1)
									->downloadable()
									->deletable()
									->label('Rencana/Realisasi Tanam')
									->visibility('public')
									->panelAspectRatio('5:1')
									->imagePreviewHeight('50')
									->fetchFileInformation(true)
									->helperText('Maksimal 2MB, format PDF')
									->disk('public')
									->directory(function ($record) {
										$tahun = $record->periodetahun;
										$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
										$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
										return "uploads/{$cleanNpwp}/{$tahun}";
									})
									->rules([
										'file',
										'mimetypes:application/pdf',
										'mimes:pdf'
									])
									->validationMessages([
										'mimetypes' => 'Hanya file PDF yang diperbolehkan',
										'mimes' => 'Ekstensi file harus .pdf',
									])
									->getUploadedFileNameForStorageUsing(
										function (TemporaryUploadedFile $file, $get, $record): string {
											$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

											return 'rta_'. $cleanNoIjin .'_'.uniqid(). '.' . $file->getClientOriginalExtension();
										}
									),
								FileUpload::make('sphtanam')
									->openable()
									->required()
									->maxSize(2048)
									->inlineLabel()
									->columnSpan(1)
									->downloadable()
									->deletable()
									->label('SPH Tanam')
									->visibility('public')
									->panelAspectRatio('5:1')
									->imagePreviewHeight('50')
									->fetchFileInformation(true)
									->helperText('Maksimal 2MB, format PDF')
									->disk('public')
									->directory(function ($record) {
										$tahun = $record->periodetahun;
										$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
										$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
										return "uploads/{$cleanNpwp}/{$tahun}";
									})
									->rules([
										'file',
										'mimetypes:application/pdf',
										'mimes:pdf'
									])
									->validationMessages([
										'mimetypes' => 'Hanya file PDF yang diperbolehkan',
										'mimes' => 'Ekstensi file harus .pdf',
									])
									->getUploadedFileNameForStorageUsing(
										function (TemporaryUploadedFile $file, $get, $record): string {
											$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

											return 'sphtanam_'. $cleanNoIjin .'_'.uniqid(). '.' . $file->getClientOriginalExtension();
										}
									),
								FileUpload::make('logbooktanam')
									->openable()
									->required()
									->maxSize(2048)
									->inlineLabel()
									->columnSpan(1)
									->downloadable()
									->deletable()
									->label('Logbook Tanam')
									->visibility('public')
									->panelAspectRatio('5:1')
									->imagePreviewHeight('50')
									->fetchFileInformation(true)
									->helperText('Maksimal 2MB, format PDF')
									->disk('public')
									->directory(function ($record) {
										$tahun = $record->periodetahun;
										$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
										$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
										return "uploads/{$cleanNpwp}/{$tahun}";
									})
									->rules([
										'file',
										'mimetypes:application/pdf',
										'mimes:pdf'
									])
									->validationMessages([
										'mimetypes' => 'Hanya file PDF yang diperbolehkan',
										'mimes' => 'Ekstensi file harus .pdf',
									])
									->getUploadedFileNameForStorageUsing(
										function (TemporaryUploadedFile $file, $get, $record): string {
											$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

											return 'logbooktanam_'. $cleanNoIjin .'_'.uniqid(). '.' . $file->getClientOriginalExtension();
										}
									),
							])
							->action(function (array $data, Commitment2024 $record): void {
								$record->userDocs()->updateOrCreate(
									['commitment_id' => $record->id], // Kondisi pencarian
									[
										'sptjmtanam' => $data['sptjmtanam'] ?? null,
										'rta' => $data['rta'] ?? null,
										'sphtanam' => $data['sphtanam'] ?? null,
										'logbooktanam' => $data['logbooktanam'] ?? null,
										'npwp' => $record->npwp,
									]
								);
							}),
					])->alignEnd()
				]),

			Section::make('Berkas Produksi')
				->aside()
				->description('Berkas-berkas kelengkapan tahap produksi yang wajib diunggah.')
				->schema([
					Placeholder::make('sptjmProduksi')
						->inlineLabel()
						->label('SPTJM Produksi')
						->content(function ($record) {
							$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
							$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
							$tahun = $record->periodetahun;
							$fileName = $record->userDocs?->sptjmproduksi;

							if ($fileName) {
								if (str_starts_with($fileName, 'uploads/')) {
									$fileUrl ="/{$fileName}";
								} else {
									$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
								}
					
								return new HtmlString('<a class="font-bold" href="' . $fileUrl . '" target="_blank">Terlampir</a>');
							}

							return 'Tidak ada';
						}),

					Placeholder::make('rpo')
						->inlineLabel()
						->label('Rencana/Realisasi Produksi')
						->content(function ($record) {
							$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
							$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
							$tahun = $record->periodetahun;
							$fileName = $record->userDocs?->rpo;

							if ($fileName) {
								if (str_starts_with($fileName, 'uploads/')) {
									$fileUrl ="/{$fileName}";
								} else {
									$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
								}
					
								return new HtmlString('<a class="font-bold" href="' . $fileUrl . '" target="_blank">Terlampir</a>');
							}

							return 'Tidak ada';
						}),

					Placeholder::make('sphproduksi')
						->inlineLabel()
						->label('SPH Produksi')
						->content(function ($record) {
							$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
							$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
							$tahun = $record->periodetahun;
							$fileName = $record->userDocs?->sphproduksi;

							if ($fileName) {
								if (str_starts_with($fileName, 'uploads/')) {
									$fileUrl ="/{$fileName}";
								} else {
									$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
								}
					
								return new HtmlString('<a class="font-bold" href="' . $fileUrl . '" target="_blank">Terlampir</a>');
							}

							return 'Tidak ada';
						}),

					Placeholder::make('logbookproduksi')
						->inlineLabel()
						->label('Logbook Tanam')
						->content(function ($record) {
							$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
							$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
							$tahun = $record->periodetahun;
							$fileName = $record->userDocs?->logbookproduksi;

							if ($fileName) {
								if (str_starts_with($fileName, 'uploads/')) {
									$fileUrl ="/{$fileName}";
								} else {
									$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
								}
					
								return new HtmlString('<a class="font-bold" href="' . $fileUrl . '" target="_blank">Terlampir</a>');
							}

							return 'Tidak ada';
						}),

					Placeholder::make('formLa')
						->inlineLabel()
						->label('Laporan Akhir')
						->content(function ($record) {
							$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
							$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
							$tahun = $record->periodetahun;
							$fileName = $record->userDocs?->formLa;

							if ($fileName) {
								if (str_starts_with($fileName, 'uploads/')) {
									$fileUrl ="/{$fileName}";
								} else {
									$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
								}
					
								return new HtmlString('<a class="font-bold" href="' . $fileUrl . '" target="_blank">Terlampir</a>');
							}

							return 'Tidak ada';
						}),
					Actions::make([
						Action::make('UnggahBerkasProduksi')
							->modalHeading('Unggah Berkas Produksi')
							->visible(fn () => Auth::user()->hasRole('importir'))
							->icon('icon-cloud-upload')
							->iconButton()
							->button()
							->color('info')
							->fillForm(fn (Commitment2024 $record): array => [
								'sptjmproduksi' => $record->userDocs?->sptjmproduksi,
								'rpo' => $record->userDocs?->rpo,
								'sphproduksi' => $record->userDocs?->sphproduksi,
								'logbookproduksi' => $record->userDocs?->logbookproduksi,
								'formLa' => $record->userDocs?->formLa,
							])
							->form([
								FileUpload::make('sptjmproduksi')
									->openable()
									->required()
									->maxSize(2048)
									->inlineLabel()
									->columnSpan(1)
									->downloadable()
									->deletable()
									->label('SPTJM Produksi')
									->visibility('public')
									->panelAspectRatio('5:1')
									->imagePreviewHeight('50')
									->fetchFileInformation(true)
									->helperText('Maksimal 2MB, format PDF')
									->disk('public')
									->directory(function ($record) {
										$tahun = $record->periodetahun;
										$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
										$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
										return "uploads/{$cleanNpwp}/{$tahun}";
									})
									->rules([
										'file',
										'mimetypes:application/pdf',
										'mimes:pdf'
									])
									->validationMessages([
										'mimetypes' => 'Hanya file PDF yang diperbolehkan',
										'mimes' => 'Ekstensi file harus .pdf',
									])
									->getUploadedFileNameForStorageUsing(
										function (TemporaryUploadedFile $file, $get, $record): string {
											$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

											return 'sptjmproduksi_'. $cleanNoIjin .'_'.uniqid(). '.' . $file->getClientOriginalExtension();
										}
									),
								FileUpload::make('rpo')
									->openable()
									->required()
									->maxSize(2048)
									->inlineLabel()
									->columnSpan(1)
									->downloadable()
									->deletable()
									->label('Rencana/Realisasi Produksi')
									->visibility('public')
									->panelAspectRatio('5:1')
									->imagePreviewHeight('50')
									->fetchFileInformation(true)
									->helperText('Maksimal 2MB, format PDF')
									->disk('public')
									->directory(function ($record) {
										$tahun = $record->periodetahun;
										$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
										$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
										return "uploads/{$cleanNpwp}/{$tahun}";
									})
									->rules([
										'file',
										'mimetypes:application/pdf',
										'mimes:pdf'
									])
									->validationMessages([
										'mimetypes' => 'Hanya file PDF yang diperbolehkan',
										'mimes' => 'Ekstensi file harus .pdf',
									])
									->getUploadedFileNameForStorageUsing(
										function (TemporaryUploadedFile $file, $get, $record): string {
											$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

											return 'rpo_'. $cleanNoIjin .'_'.uniqid(). '.' . $file->getClientOriginalExtension();
										}
									),
								FileUpload::make('sphproduksi')
									->openable()
									->required()
									->maxSize(2048)
									->inlineLabel()
									->columnSpan(1)
									->downloadable()
									->deletable()
									->label('SPH Produksi')
									->visibility('public')
									->panelAspectRatio('5:1')
									->imagePreviewHeight('50')
									->fetchFileInformation(true)
									->helperText('Maksimal 2MB, format PDF')
									->disk('public')
									->directory(function ($record) {
										$tahun = $record->periodetahun;
										$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
										$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
										return "uploads/{$cleanNpwp}/{$tahun}";
									})
									->rules([
										'file',
										'mimetypes:application/pdf',
										'mimes:pdf'
									])
									->validationMessages([
										'mimetypes' => 'Hanya file PDF yang diperbolehkan',
										'mimes' => 'Ekstensi file harus .pdf',
									])
									->getUploadedFileNameForStorageUsing(
										function (TemporaryUploadedFile $file, $get, $record): string {
											$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

											return 'sphproduksi_'. $cleanNoIjin .'_'.uniqid(). '.' . $file->getClientOriginalExtension();
										}
									),
								FileUpload::make('logbookproduksi')
									->openable()
									->required()
									->maxSize(2048)
									->inlineLabel()
									->columnSpan(1)
									->downloadable()
									->label('Logbook Produksi')
									->visibility('public')
									->panelAspectRatio('5:1')
									->imagePreviewHeight('50')
									->fetchFileInformation(true)
									->helperText('Maksimal 2MB, format PDF')
									->disk('public')
									->directory(function ($record) {
										$tahun = $record->periodetahun;
										$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
										$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
										return "uploads/{$cleanNpwp}/{$tahun}";
									})
									->rules([
										'file',
										'mimetypes:application/pdf',
										'mimes:pdf'
									])
									->validationMessages([
										'mimetypes' => 'Hanya file PDF yang diperbolehkan',
										'mimes' => 'Ekstensi file harus .pdf',
									])
									->getUploadedFileNameForStorageUsing(
										function (TemporaryUploadedFile $file, $get, $record): string {
											$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

											return 'logbookproduksi_'. $cleanNoIjin .'_'.uniqid(). '.' . $file->getClientOriginalExtension();
										}
									),
								FileUpload::make('formLa')
									->openable()
									->required()
									->maxSize(2048)
									->inlineLabel()
									->columnSpan(1)
									->downloadable()
									->deletable()
									->label('Laporan akhir')
									->visibility('public')
									->panelAspectRatio('5:1')
									->imagePreviewHeight('50')
									->fetchFileInformation(true)
									->helperText('Maksimal 2MB, format PDF')
									->disk('public')
									->directory(function ($record) {
										$tahun = $record->periodetahun;
										$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
										$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
										return "uploads/{$cleanNpwp}/{$tahun}";
									})
									->rules([
										'file',
										'mimetypes:application/pdf',
										'mimes:pdf'
									])
									->validationMessages([
										'mimetypes' => 'Hanya file PDF yang diperbolehkan',
										'mimes' => 'Ekstensi file harus .pdf',
									])
									->getUploadedFileNameForStorageUsing(
										function (TemporaryUploadedFile $file, $get, $record): string {
											$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

											return 'formLa_'. $cleanNoIjin .'_'.uniqid(). '.' . $file->getClientOriginalExtension();
										}
									),
							])
							->action(function (array $data, Commitment2024 $record): void {
								$record->userDocs()->updateOrCreate(
									['commitment_id' => $record->id], // Kondisi pencarian
									[
										'sptjmproduksi' => $data['sptjmproduksi'] ?? null,
										'rpo' => $data['rpo'] ?? null,
										'sphproduksi' => $data['sphproduksi'] ?? null,
										'logbookproduksi' => $data['logbookproduksi'] ?? null,
										'formLa' => $data['formLa'] ?? null,
										'npwp' => $record->npwp,
									]
								);
							}),
					])->alignEnd()
				]),

			Section::make('Berkas Pengajuan Verifikasi')
				->aside()
				->description('Berkas-berkas kelengkapan yang wajib diunggah.')
				->schema([
					Placeholder::make('spvt')
						->inlineLabel()
						->label('Tanam')
						->content(function ($record) {
							$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
							$tahun = $record->periodetahun;
							$fileName = $record->userDocs?->spvt;
					
							if ($fileName) {
								// Jika fileName sudah mengandung 'uploads/', gunakan langsung sebagai URL
								if (str_starts_with($fileName, 'uploads/')) {
									$fileUrl ="/{$fileName}";
								} else {
									// Jika tidak, anggap file ada di dalam folder uploads/{npwp}/{tahun}/
									$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
								}
					
								return new HtmlString('<a class="font-bold" href="' . $fileUrl . '" target="_blank">Terlampir</a>');
							}
					
							return 'Tidak ada';
						}),
					Placeholder::make('spvp')
						->inlineLabel()
						->label('spvp')
						->content(function ($record) {
							$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
							$tahun = $record->periodetahun;
							$fileName = $record->userDocs?->spvp;
					
							if ($fileName) {
								// Jika fileName sudah mengandung 'uploads/', gunakan langsung sebagai URL
								if (str_starts_with($fileName, 'uploads/')) {
									$fileUrl ="/{$fileName}";
								} else {
									// Jika tidak, anggap file ada di dalam folder uploads/{npwp}/{tahun}/
									$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
								}
					
								return new HtmlString('<a class="font-bold" href="' . $fileUrl . '" target="_blank">Terlampir</a>');
							}
					
							return 'Tidak ada';
						}),
					Actions::make([
						Action::make('unggahVerifikasi')
							->label('Unggah Pengajuan')
							->modalHeading('Unggah Pengajuan Verifikasi')
							->visible(fn () => Auth::user()->hasRole('importir'))
							->icon('icon-cloud-upload')
							->iconButton()
							->button()
							->color('info')
							->fillForm(fn (Commitment2024 $record): array => [
								'spvt' => $record->userDocs?->spvt,
								'spvp' => $record->userDocs?->spvp,
							])
							->form([
								FileUpload::make('spvt')
									->openable()
									->maxSize(2048)
									->inlineLabel()
									->columnSpan(1)
									->downloadable()
									->deletable()
									->label('Tanam')
									->visibility('public')
									->panelAspectRatio('5:1')
									->imagePreviewHeight('50')
									->fetchFileInformation(true)
									->helperText('Maksimal 2MB, format PDF')
									->disk('public')
									->directory(function ($record) {
										$tahun = $record->periodetahun;
										$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
										$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
										return "uploads/{$cleanNpwp}/{$tahun}";
									})
									->rules([
										'file',
										'mimetypes:application/pdf',
										'mimes:pdf'
									])
									->validationMessages([
										'mimetypes' => 'Hanya file PDF yang diperbolehkan',
										'mimes' => 'Ekstensi file harus .pdf',
									])
									->getUploadedFileNameForStorageUsing(
										function (TemporaryUploadedFile $file, $get, $record): string {
											$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

											return 'spvt_'. $cleanNoIjin .'_'.uniqid(). '.' . $file->getClientOriginalExtension();
										}
									),
								FileUpload::make('spvp')
									->openable()
									->maxSize(2048)
									->inlineLabel()
									->columnSpan(1)
									->downloadable()
									->deletable()
									->label('Produksi')
									->visibility('public')
									->panelAspectRatio('5:1')
									->imagePreviewHeight('50')
									->fetchFileInformation(true)
									->helperText('Maksimal 2MB, format PDF')
									->disk('public')
									->directory(function ($record) {
										$tahun = $record->periodetahun;
										$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
										$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
										return "uploads/{$cleanNpwp}/{$tahun}";
									})
									->rules([
										'file',
										'mimetypes:application/pdf',
										'mimes:pdf'
									])
									->validationMessages([
										'mimetypes' => 'Hanya file PDF yang diperbolehkan',
										'mimes' => 'Ekstensi file harus .pdf',
									])
									->getUploadedFileNameForStorageUsing(
										function (TemporaryUploadedFile $file, $get, $record): string {
											$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

											return 'spvp_'. $cleanNoIjin .'_'.uniqid(). '.' . $file->getClientOriginalExtension();
										}
									),
							])
							->action(function (array $data, Commitment2024 $record): void {
								$record->userDocs()->updateOrCreate(
									['commitment_id' => $record->id], // Kondisi pencarian
									[
										'spvt' => $data['spvt'] ?? null,
										'spvp' => $data['spvp'] ?? null,
										'npwp' => $record->npwp,
									]
								);
							}),
					])->alignEnd()
				]),

            Section::make('Data Kemitraan')
                ->aside()
                ->description('Mitra Kelompok Tani beserta data Perjanjian Kerjasama')
                ->schema([
                    TableRepeater::make('pks')
                        ->hiddenLabel()
                        ->addable(false)
                        ->deletable(false)
						->relationship('pks')
                        ->headers([
                            Header::make('Poktan'),
                            Header::make('No. PKS'),
                            Header::make('Status Verifikasi'),
                            Header::make(''),
                        ])
                        ->schema([
                            Hidden::make('id'),
                            Placeholder::make('poktan_id')
								->hiddenLabel()
                                ->content(fn ($record) => $record->masterpoktan->nama_kelompok),
							Placeholder::make('no_perjanjian')
								->hiddenLabel()
								->content(fn ($record) => $record->no_perjanjian),
                            Placeholder::make('status')
								->hiddenLabel()
								->extraAttributes(['class'=>'text-center'])
								->content(fn ($get) => view('components.status-badge-verifikasi', ['status' => $get('status')])),
							Actions::make([
								Action::make('PKS')
									->hiddenLabel()
									->visible(fn ()=> Auth::user()->hasRole('importir'))
									->icon('icon-journal-bookmark-fill')
									->iconButton()
									->button()
									->color('info')
									->url(function ($record) {
										$statusVerifikasi = $record->status;
										if (is_null($statusVerifikasi) || $statusVerifikasi == 'Tidak Sesuai') { 
											return route('filament.panel2024.resources.pks2024s.edit', $record->id);
										}
										
										return route('filament.panel2024.resources.pks2024s.view', $record->id);
									}),
							])->alignCenter()
                        ])
                ]),

            Section::make('Kebutuhan Pupuk dan Mulsa')
                ->aside()
                ->description('Rencana kebutuhan pupuk dan mulsa untuk memenuhi pelaksanaan komitmen tanam-produksi')
                ->schema([
                    Placeholder::make('organik')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->pupuk_organik,0,',','.').' kg'),
                    Placeholder::make('dolomit')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->dolomit,0,',','.').' kg'),
                    Placeholder::make('npk')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->npk,0,',','.').' kg'),
                    Placeholder::make('za')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->za,0,',','.').' kg'),
                    Placeholder::make('mulsa')
                        ->inlineLabel()
                        ->content(fn ($record) => number_format($record->mulsa,0,',','.').' roll'),
                ]),
                
            Section::make('Kebutuhan Benih')
                ->aside()
                ->hidden()
                ->description('Rencana kebutuhan benih untuk memenuhi pelaksanaan komitmen tanam-produksi')
                ->schema([
                    Placeholder::make('kebutuhan')
                        ->inlineLabel()
                        ->content(fn ($record) => $record->kebutuhan_benih),
                    Placeholder::make('mandiri')
                        ->inlineLabel()
                        ->content(fn ($record) => $record->mandiri),
                    Placeholder::make('penangkar')
                        ->inlineLabel()
                        ->content(fn ($record) => $record->beli_penangkar),
                ]),

            Section::make('Penangkar')
                ->aside()
                ->hidden()
                ->description('Daftar penangkar benih')
                ->schema([
                    TableRepeater::make('penangkar')
                        ->hiddenLabel()
                        ->addable(false)
                        ->reorderable(false)
                        ->relationship('penangkar_riph')
                        ->headers([
                            Header::make('Penangkar'),
                            Header::make('Pimpinan'),
                            Header::make('Varietas'),
                        ])
                        ->schema([
                            TextInput::make('nama_penangkar')
                                ->hiddenLabel(),
                                TextInput::make('nama_pimpinan')
                                ->hiddenLabel(),
                                TextInput::make('varietas')
                                ->hiddenLabel(),
                        ])
                ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                //
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
			]);
            // ->bulkActions([
            //     Tables\Actions\BulkActionGroup::make([
            //         Tables\Actions\DeleteBulkAction::make(),
            //     ]),
            // ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCommitment2024s::route('/'),
            'create' => Pages\CreateCommitment2024::route('/create'),
            'edit' => Pages\EditCommitment2024::route('/{record}/edit'),
            'sync' => Pages\UnduhRiph::route('/sync'),
            'view' => Pages\ViewCommitment2024::route('/{record}'),
        ];
    }

	// public static function mutateFormDataBeforeSave(array $data, $record): array
	// {
	// 	dd($data, $record);

	// 	return $data;
	// }
}
