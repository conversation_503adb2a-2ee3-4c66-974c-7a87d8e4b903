<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\AjuVerifSkl2024;
use App\Models\Commitment2024;
use App\Models\Commitment2025;
use App\Models\Completed;
use App\Models\Pengajuanskl2025;
use App\Models\SklRekomendasi2024;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ValidateQRController extends Controller
{
	public function verify(Request $request)
	{
		// Log awal verifikasi
		Log::info('Memulai verifikasi QR Code', [
			'url' => $request->fullUrl(),
			'ip' => $request->ip(),
			'user_agent' => $request->userAgent()
		]);

		$mask = $request->query('mask');
		$mock = $request->query('mock');

		// Log parameter yang diterima
		Log::info('Parameter QR Code', [
			'mask' => $mask,
			'mock' => $mock ? substr($mock, 0, 8) . '...' . substr($mock, -8) : null // Hanya tampilkan sebagian hash untuk keamanan
		]);

		if (!$mask || !$mock) {
			Log::warning('Parameter tidak lengkap', [
				'mask_exists' => !empty($mask),
				'mock_exists' => !empty($mock)
			]);
			return response()->json(['error' => 'Invalid Parameter! (Err: 01).'], 400);
		}

		// Validasi panjang karakter
		if (strlen($mock) !== 64) {
			Log::warning('Panjang mock tidak valid', [
				'expected' => 64,
				'actual' => strlen($mock)
			]);
			return response()->json(['error' => 'Invalid Parameter! (Err: 02).'], 400);
		}

		if (strlen($mask) != 24) {
			Log::warning('Panjang mask tidak valid', [
				'expected' => 24,
				'actual' => strlen($mask)
			]);
			return response()->json(['error' => 'Invalid Parameter! (Err: 03).'], 400);
		}

		$shortHash = substr($mask, -8);
		$reversedNoIjin = substr($mask, 0, -8);
		$inverseNoIjin = strrev($reversedNoIjin);

		// Log proses ekstraksi data
		Log::info('Ekstraksi data dari mask', [
			'shortHash' => $shortHash,
			'reversedNoIjin' => $reversedNoIjin,
			'inverseNoIjin' => $inverseNoIjin
		]);

		$noIjin = substr($inverseNoIjin, 0, 4) . '/' .
			substr($inverseNoIjin, 4, 2) . '.' .
			substr($inverseNoIjin, 6, 3) . '/' .
			substr($inverseNoIjin, 9, 1) . '/' .
			substr($inverseNoIjin, 10, 2) . '/' .
			substr($inverseNoIjin, 12, 4);

		// Log no_ijin yang direkonstruksi
		Log::info('No IJIN direkonstruksi', [
			'no_ijin' => $noIjin,
			'parts' => [
				'part1' => substr($inverseNoIjin, 0, 4),
				'part2' => substr($inverseNoIjin, 4, 2),
				'part3' => substr($inverseNoIjin, 6, 3),
				'part4' => substr($inverseNoIjin, 9, 1),
				'part5' => substr($inverseNoIjin, 10, 2),
				'part6' => substr($inverseNoIjin, 12, 4)
			]
		]);

		/*
			1. cari data di Completed. jika tidak ada,
			2. cari di Commitment2024. jika ada, 'data ditemukan dan belum lunas'. jika tidak ada,
			3. cari di Commitment2025. jika ada, 'data ditemukan dan belum lunas'. jika tidak ada,
			4. return 404 'data tidak ditemukan'
		*/

		// 1. Cari data di Completed
		$record = Completed::where('no_ijin', $noIjin)->first();

		Log::info('Pencarian di Completed', [
			'no_ijin' => $noIjin,
			'found' => !empty($record),
			'record_id' => $record->id ?? null
		]);

		if (!$record) {
			// 2. Cari di Commitment2024
			$record2024 = Commitment2024::where('no_ijin', $noIjin)->first();

			Log::info('Pencarian di Commitment2024', [
				'no_ijin' => $noIjin,
				'found' => !empty($record2024),
				'record_id' => $record2024->id ?? null
			]);

			if ($record2024) {
				Log::info('Data ditemukan di Commitment2024 - belum lunas', [
					'no_ijin' => $noIjin,
					'commitment_id' => $record2024->id
				]);

				return response()->json([
					'error' => 'Data ditemukan dan belum lunas',
					'status' => 'Belum Lunas',
					'message' => 'Data komitmen ditemukan tetapi belum memiliki SKL'
				], 200);
			}

			// 3. Cari di Commitment2025
			$record2025 = Commitment2025::where('no_ijin', $noIjin)->first();

			Log::info('Pencarian di Commitment2025', [
				'no_ijin' => $noIjin,
				'found' => !empty($record2025),
				'record_id' => $record2025->id ?? null
			]);

			if ($record2025) {
				Log::info('Data ditemukan di Commitment2025 - belum lunas', [
					'no_ijin' => $noIjin,
					'commitment_id' => $record2025->id
				]);

				return response()->json([
					'error' => 'Data ditemukan dan belum lunas',
					'status' => 'Belum Lunas',
					'message' => 'Data komitmen ditemukan tetapi belum memiliki SKL'
				], 200);
			}

			// 4. Jika tidak ditemukan di mana pun
			// Coba cari dengan LIKE query untuk debugging
			$similarRecords = Completed::where('no_ijin', 'LIKE', '%' . substr($noIjin, 5, 10) . '%')->get();

			Log::warning('Data tidak ditemukan di semua tabel', [
				'no_ijin' => $noIjin,
				'similar_records_count' => $similarRecords->count(),
				'similar_records' => $similarRecords->take(5)->pluck('no_ijin')->toArray()
			]);

			return response()->json(['error' => 'Data tidak ditemukan'], 404);
		}

		// Log record yang ditemukan
		Log::info('Record ditemukan', [
			'id' => $record->id,
			'no_ijin_db' => $record->no_ijin,
			'npwp' => $record->npwp,
			'periodetahun' => $record->periodetahun
		]);

		$npwp = preg_replace('/[^A-Za-z0-9]/', '', $record->npwp);
		$secretKey = config('app.qr_secret');

		// Log data untuk hash
		Log::info('Data untuk hash', [
			'sanitized_npwp' => $npwp,
			'secret_key_exists' => !empty($secretKey),
			'data' => "{$reversedNoIjin}|{$npwp}"
		]);

		// Generate ulang hash
		$data = "{$reversedNoIjin}|{$npwp}";
		$calculatedHash = hash_hmac('sha256', $data, $secretKey);
		$calculatedShortHash = substr($calculatedHash, 0, 8);

		// Log hasil hash
		Log::info('Hasil hash', [
			'calculated_short_hash' => $calculatedShortHash,
			'received_short_hash' => $shortHash,
			'hash_match' => $mock === $calculatedHash,
			'short_hash_match' => $shortHash === $calculatedShortHash
		]);

		// Validasi hash
		if ($mock !== $calculatedHash || $shortHash !== $calculatedShortHash) {
			Log::warning('Validasi hash gagal', [
				'calculated_hash_prefix' => substr($calculatedHash, 0, 8),
				'received_hash_prefix' => substr($mock, 0, 8),
				'calculated_short_hash' => $calculatedShortHash,
				'received_short_hash' => $shortHash
			]);
			return response()->json(['error' => 'Invalid Parameter! (Err: 04)'], 403);
		}

		try {
			// Log periode tahun
			Log::info('Menentukan periode tahun', [
				'periodetahun' => $record->periodetahun,
				'is_2025_or_later' => $record->periodetahun >= 2025
			]);

			if($record->periodetahun >= 2025){
				$commitment = $record->commitment2025;
				$dataSkl = Pengajuanskl2025::where('no_ijin', $noIjin)->first();

				// Log data SKL 2025
				Log::info('Data SKL 2025', [
					'commitment_exists' => !empty($commitment),
					'dataSkl_exists' => !empty($dataSkl),
					'commitment_id' => $commitment->id ?? null,
					'dataSkl_id' => $dataSkl->id ?? null
				]);
			}else{
				$commitment = $record->commitment2024;
				$dataSkl = SklRekomendasi2024::where('no_ijin', $noIjin)->first();

				// Log data SKL 2024
				Log::info('Data SKL 2024', [
					'commitment_exists' => !empty($commitment),
					'dataSkl_exists' => !empty($dataSkl),
					'commitment_id' => $commitment->id ?? null,
					'dataSkl_id' => $dataSkl->id ?? null
				]);
			}

			// Log data yang akan dikembalikan
			Log::info('Data valid yang akan dikembalikan', [
				'company' => $record->datauser->company_name ?? 'tidak tersedia',
				'npwp' => $record->npwp ?? 'tidak tersedia',
				'no_skl' => $record->no_skl ?? 'tidak tersedia',
				'no_ijin' => $record->no_ijin ?? 'tidak tersedia',
				'wajib_tanam' => $commitment->luas_wajib_tanam ?? 0,
				'wajib_produksi' => $commitment->volume_produksi ?? 0,
				'realisasi_tanam' => $record->luas_tanam ?? 0,
				'realisasi_produksi' => $record->volume ?? 0
			]);

			// Return data valid
			return response()->json([
				'status' => 'Valid',
				'company' => $record->datauser->company_name,
				'npwp' => $record->npwp,
				'no_skl' => $record->no_skl,
				'tgl_terbit' => $record->published_date,
				'no_ijin' => $record->no_ijin,
				'approved_by' => $dataSkl->approvedBy->dataadmin->nama,
				'nip' => $dataSkl->approvedBy->dataadmin->nip,
				'wajib_tanam' => $commitment->luas_wajib_tanam,
				'wajib_produksi' => $commitment->volume_produksi,
				'realisasi_tanam' => $record->luas_tanam,
				'realisasi_produksi' => $record->volume,
				'url' => $record->url,
			]);
		} catch (\Exception $e) {
			// Log error
			Log::error('Error saat memproses data valid', [
				'error' => $e->getMessage(),
				'trace' => $e->getTraceAsString(),
				'no_ijin' => $noIjin,
				'record_id' => $record->id ?? null
			]);

			// Return error
			return response()->json([
				'error' => 'Terjadi kesalahan saat memproses data. Silakan hubungi administrator.',
				'message' => $e->getMessage()
			], 500);
		}
	}

}
