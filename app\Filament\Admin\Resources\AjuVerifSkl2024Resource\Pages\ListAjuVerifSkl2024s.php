<?php

namespace App\Filament\Admin\Resources\AjuVerifSkl2024Resource\Pages;

use App\Filament\Admin\Resources\AjuVerifSkl2024Resource;
use App\Models\AjuVerifSkl2024;
use App\Models\Completed;
use App\Models\User;
use Filament\Actions;
use Filament\Actions\StaticAction;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\SelectColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\HtmlString;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class ListAjuVerifSkl2024s extends ListRecords
{
    protected static string $resource = AjuVerifSkl2024Resource::class;
	public function getTitle(): string | Htmlable
    {
        return 'Daftar Pengajuan Penerbitan SKL';
    }
	public function getHeading(): string
	{
		return 'Daftar Pengajuan Penerbitan SKL';
	}
	public function getSubheading(): ?string
    {
        return 'untuk RIPH Periode sebelum Tahun 2025';
    }

	protected function getHeaderActions(): array
	{
		return [
			// Actions\CreateAction::make(),
		];
	}
	
	public function getRecordTitleAttribute()
	{
		return 'Pengajuan #'.$this->id;
	}

	// protected function getTableQuery(): Builder
	// {
	// 	return static::getModel()::query()
	// 		->whereNotExists(function ($subQuery) {
	// 			$subQuery->select(DB::raw(1))
	// 				->from('completeds')
	// 				->whereColumn('completeds.no_ijin', 'avskls.no_ijin')
	// 				->whereNotNull('completeds.url')
	// 				->where('completeds.url', '!=', '');
	// 		})
	// 		->when(Auth::user()->hasAnyRole(['direktur']), function ($query) {
	// 			$query->where('status', '2');
	// 		})
	// 		->orderBy('status', 'asc');
	// }
	
	public function table(Table $table): Table
	{
		return $table
			->modifyQueryUsing(function (Builder $query) {
				return $query
					->whereDoesntHave('completed', function ($q) {
						$q->whereNotNull('url')
						->where('url', '!=', '');
					})
					->when(Auth::user()->hasAnyRole(['direktur']), function ($query) {
						$query->where('status', '2');
					})
					->orderBy('status', 'asc');
			})
			->recordTitleAttribute('no_ijin')
			->emptyStateHeading('Belum ada pengajuan Status Lunas')
			->columns([
				TextColumn::make('datauser.company_name')
					->label('Perusahaan')
					->hidden(fn () => Auth::user()->hasAnyRole(['importir']))
					->sortable()
					->searchable(),
				TextColumn::make('no_ijin')
					->label('No. RIPH')
					->sortable()
					->searchable(),
				TextColumn::make('created_at')
					->label('Tanggal dibuat')
					->date()
					->sortable(),
				TextColumn::make('status')
					->badge()
					->sortable()
					->searchable()
					->badge()
					->color(fn ($record) => match ($record->status) {
						'1' => 'warning',   // Diajukan
						'2' => 'info',      // Direkomendasikan
						'3' => 'primary',   // Antrian TTD
						'4' => $record->completed && $record->completed->url ? 'success' : 'gray', // Proses Penerbitan vs. Diterbitkan
						'5' => 'danger',   // Perbaikan
						default => 'gray' // Status tidak valid atau belum diajukan
					})
					->formatStateUsing(function ($record) {
						return match ($record->status) {
							'1' => 'Diajukan',
							'2' => 'Direkomendasikan',
							'3' => 'Antrian TTD',
							'4' => $record->completed && $record->completed->url ? 'Terbit' : 'Proses Penerbitan',
							'5' => 'Perbaikan',
							default => 'Status Tidak Dikenal'
						};
					}),
			])->filters([
				//
			])
			->actions([
				Action::make('viewVerification')
					->icon('heroicon-o-clipboard-document-check')
					->color('warning')
					->hiddenLabel()
					->visible(Auth::user()->hasAnyRole(['importir']))
					->tooltip('Lihat Data')
					->url(function ($record) {
						return route('filament.admin.resources.aju-verif-skl2024s.view-verification', ['record' => $record]);
					})
					->openUrlInNewTab(),
				Action::make('finalVerification')
					->icon('heroicon-o-clipboard-document-check')
					->color('warning')
					->hiddenLabel()
					->visible(Auth::user()->hasAnyRole(['admin','Super Admin']))
					->tooltip('Lihat Data')
					->url(function ($record) {
						if ($record->status === '1') {
							return route('filament.admin.resources.aju-verif-skl2024s.final-verification', ['record' => $record]);
						}
						return route('filament.admin.resources.aju-verif-skl2024s.view-verification', ['record' => $record]);
					})
					->openUrlInNewTab(false),

				Action::make('roll-back')
					->iconButton()
					->icon('heroicon-o-bookmark-slash')
					->requiresConfirmation()
					->tooltip('Batalkan hasil verifikasi SKL')
					->visible(fn ($record) =>
						in_array((string) $record->status, ['2', '5']) &&
						Auth::user()->hasAnyRole(['admin', 'Super Admin'])
					)
					->fillForm(fn (AjuVerifSkl2024 $record): array => [
						'pengajuan_id' => $record->id,
						'request_by' => Auth::id(),
					])
					->form([
						Textarea::make('reason')
							->label('Alasan Pembatalan')
							->required()
							->autosize()
							->placeholder('Berikan alasan pembatalan submission sebelumnya')
							->columnSpanFull()
					])
					->action(function (array $data, AjuVerifSkl2024 $record): void {
						DB::transaction(function () use ($data, $record) {
							// Hapus data SKL jika ada
							$record->sklRekomendasi()?->delete();

							// Update data pengajuan
							$record->update([
								'status' => '1',
								'note' => null,
								'check_by' => null,
								'verif_at' => null,
							]);

							// Simpan log rollback
							\App\Models\RollbackLog::create([
								'log_type' => 'Cancel SKL Submission',
								'model_type' => get_class($record),
								'model_id' => $record->id,
								'request_by' => Auth::id(),
								'data' => $data['reason'],
								'metadata' => $data,
							]);
						});

						// ✅ Verifikasi integritas setelah rollback
						$record->refresh();

						abort_if($record->sklRekomendasi()->exists(), 400, 'SKL masih terhubung');
						abort_if((string) $record->status !== '1', 400, 'Status belum kembali ke 1');
						abort_if(!is_null($record->note), 400, 'Catatan masih ada');
						abort_if(!is_null($record->check_by), 400, 'Verifikator masih terisi');
						abort_if(!is_null($record->verif_at), 400, 'Tanggal verifikasi masih ada');

						$log = \App\Models\RollbackLog::where([
							'log_type' => 'Cancel SKL Submission',
							'model_type' => get_class($record),
							'model_id' => $record->id,
						])->latest()->first();

						abort_if(!$log, 400, 'Log rollback tidak ditemukan');

						Notification::make()
							->title('Rollback berhasil & terverifikasi')
							->success()
							->send();
					}),
				
				Action::make('doRecomend')
					->icon('icon-bookmark-plus')
					->hidden()
					->color('danger')
					->iconButton()
					->requiresConfirmation()
					// ->visible(fn ($record) =>$record->status === '1' && Auth::user()->hasAnyRole(['admin','Super Admin']))
					->tooltip('Penetapan Hasil Verifikasi')
					->modal()
					->modalWidth(MaxWidth::Large)
					->modalHeading('Penetapan Hasil Verifikasi')
					->modalDescription(function ($record) {
						return 'Anda akan menetapkan hasil verifikasi pengajuan penerbitan SKL untuk RIPH No.: '. $record->no_ijin .'. Lanjutkan?';
					})
					->modalSubmitActionLabel('Simpan')
					->fillForm(fn (AjuVerifSkl2024 $record): array => [
						'pengajuan_id' => $record->id,
						'no_skl' => $record->sklrekomendasi?->no_skl,
						'npwp' => $record->sklrekomendasi?->npwp,
						'no_ijin' => $record->sklrekomendasi?->no_ijin,
						'submit_by' => $record->sklrekomendasi?->submit_by,
						'published_date' => $record->sklrekomendasi?->published_date,
						'approved_by' => $record->sklrekomendasi?->approved_by,
						'note' => $record->note,
					])
					->form([
						Select::make('status')
							->label('Rekomendasi')
							->required()
							->options([
								'2' => 'Rekomendasikan ke Pimpinan',
								'5' => 'Kembalikan ke Importir untuk Perbaikan'
							])
							->required()
							->reactive(),
						
							Group::make()
								->visible(fn ($get) => $get('status') === '2')
								->columns(1)
								->schema([
									TextInput::make('no_skl')
										->label('No. SKL')
										->inlineLabel()
										->required()
										->live(onBlur: true)
										->helperText(fn (callable $get) => 
											$get('duplicate_warning') 
												? new HtmlString('<span class="text-danger-500">' . $get('duplicate_warning') . '</span>')
												: null
										)
										->afterStateUpdated(function (callable $set, $state) {
											$isDuplicate = \App\Models\SklRekomendasi2024::where('no_skl', $state)->first();
											$set('duplicate_warning', $isDuplicate ? 'Nomor SKL sudah digunakan' : '');
											
											if ($isDuplicate) {
												$set('no_skl', null);
												Notification::make()
													->title('Error')
													->danger()
													->body('Nomor SKL sudah digunakan. Silakan gunakan nomor lain.')
													->send();
											}
										}),
									DatePicker::make('published_date')
										->label('Tanggal Terbit')->inlineLabel()
										->required()
										->helperText('Tanggal akan tercetak di SKL'),
									Select::make('approved_by')
										->label('Pejabat')->inlineLabel()
										->required()
										->helperText('Pejabat penandatangan SKL')
										->options(
											User::whereHas('roles', function ($query) {
													$query->where('name', 'direktur');
												})
												->whereDoesntHave('roles', function ($query) {
													$query->where('name', 'Super Admin');
												})
												->where('status', 'aktif')
												->get()
												->pluck('name', 'id')
										)
										->searchable()
										->preload(),
								]),
						Textarea::make('note')
							->label('Catatan')
							->required()
							->autosize()
							->placeholder(fn (callable $get) => $get('status') === '2' 
								? 'Berikan alasan Anda merekomendasikan kepada pimpinan' 
								: 'Berikan alasan pengembalian dan petunjuk perbaikan untuk pelaku usaha')
							->columnSpanFull()
					])
					->action(function (array $data, AjuVerifSkl2024 $record): void {
						if ($data['status'] === '2') {
							$record->update([
								'status' => $data['status'],
								'note' => $data['note'],
								'check_by' => Auth::user()->id,
								'verif_at' => today(),
							]);
							
							$record->sklrekomendasi()->updateOrCreate(
								['no_ijin' => $record->no_ijin],
								[
									'pengajuan_id' => $record->id,
									'no_skl' => $data['no_skl'] ?? null,
									'npwp' => $record->npwp ?? null,
									'submit_by' => Auth::user()->id ?? null,
									'published_date' => $data['published_date'] ?? null,
									'approved_by' => $data['approved_by'] ?? $record->sklrekomendasi?->approved_by ?? null, // Gunakan data yang ada atau yang baru
								]
							);
							Notification::make()
								->title('Penerbitan SKL Direkomendasikan')
								->body('Penerbitan SKL telah direkomendasikan ke Pimpinan.')
								->success()
								->send();
						}else{
							$record->update([
								'status' => $data['status'],
								'note' => $data['note'],
								'check_by' => Auth::user()->id,
								'verif_at' => today(),
							]);
							Notification::make()
								->title('Pengajuan Dikembalikan untuk Perbaikan')
								->body('Pengajuan telah dikembalikan ke Importir untuk perbaikan.')
								->success()
								->send();
						}
					}),

				Action::make('approval')
					->icon('icon-bookmark-check-fill')
					->color('danger')
					->hiddenLabel()
					->requiresConfirmation()
					->visible(fn ($record) =>$record->status === '2' && Auth::user()->hasAnyRole(['direktur']))
					->tooltip('Lihat Rekomendasi')
					->modal()
					->modalWidth(MaxWidth::Large)
					->modalHeading('Rekomendasi SKL')
					->modalDescription('Lihat Data Rekomendasi Penerbitan SKL')
					->modalSubmitAction(fn (StaticAction $action) => $action->label('Lihat Data'))
					->action(function ($record) {
						return redirect()->route('filament.admin.resources.aju-verif-skl2024s.rekomendasi', ['record' => $record->id]);
					}),
				Action::make('draftSkl')
					->icon('heroicon-s-printer')
					->color('danger')
					->hiddenLabel()
					->visible(fn ($record) =>$record->status === '4' && Auth::user()->hasAnyRole(['admin', 'Super Admin']))
					->tooltip('Cetak Draft')
					->url(fn ($record) => '/'.$record->sklrekomendasi?->skl_auto)
    				->openUrlInNewTab(),
				Action::make('uploadSkl')
					->icon('icon-cloud-upload')
					->color('danger')
					->hiddenLabel()
					->visible(fn ($record) =>$record->status === '4' && Auth::user()->hasAnyRole(['Super Admin', 'admin']))
					->tooltip('Unggah SKL')
					->requiresConfirmation()
					->modal()
					->modalWidth(MaxWidth::Large)
					->modalIcon('icon-cloud-upload')
					->modalHeading('Unggah SKL')
					->modalDescription('Pastikan Draft SKL telah ditandatangani oleh Pimpinan!')
					->modalSubmitAction(fn (StaticAction $action) => $action->label('Terbitkan'))
					->fillForm(fn (AjuVerifSkl2024 $record): array => [
						'skl_upload' => $record->skl_upload,
					])
					->form([
						FileUpload::make('skl_upload')
							->openable()
							->required()
							->maxSize(2048)
							->columnSpanFull()
							->deletable()
							->downloadable()
							->required()
							->hiddenLabel()
							->visibility('public')
							->fetchFileInformation(true)
							->helperText(fn () => new HtmlString('<span class="text-danger-500">Berkas SKL yang telah ditandatangan oleh Pimpinan. Maksimal 2MB, format PDF</span>'))
							->disk('public')
							->directory(function ($record) {
								$tahun = $record->commitment->periodetahun;
								$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
								return "uploads/{$cleanNpwp}/{$tahun}";
							})
							->rules([
								'file',
								'mimetypes:application/pdf',
								'mimes:pdf',
								'max:2048',
							])
							->validationMessages([
								'mimetypes' => 'Hanya file PDF yang diperbolehkan',
								'mimes' => 'Ekstensi file harus .pdf',
							])
							->getUploadedFileNameForStorageUsing(
								function (TemporaryUploadedFile $file, callable $get, $record): string {
									$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);
									return 'skl_'. $cleanNoIjin . '_' . time() . '.' . $file->getClientOriginalExtension();
								}
							),
					])
					->action(function (array $data, $record): void {
						$record->sklrekomendasi->update([
							'skl_upload' => $data['skl_upload'] ?? null,
						]);
						$fullUrl = url(Storage::url($record->sklrekomendasi->skl_upload));
						Completed::updateOrCreate(
							[
								'no_ijin' => $record->commitment->no_ijin,
							],
							[
								'no_skl' => $record->sklrekomendasi->no_skl,
								'periodetahun' => $record->commitment->periodetahun,
								'npwp' => $record->commitment->npwp,
								'published_date' => $record->sklrekomendasi->published_date,
								'luas_tanam' => $record->commitment->datarealisasi->sum('luas_lahan'),
								'volume' => $record->commitment->datarealisasi->sum('volume'),
								'status' => 'Lunas',
								'skl_upload' => $record->sklrekomendasi->skl_upload,
								'url' => $fullUrl,
							]
						);
						
						Notification::make()
							->title('Surat Keterangan Lunas berhasil Diterbitkan')
							->success()
							->send();
					})
			]);
	}
}
