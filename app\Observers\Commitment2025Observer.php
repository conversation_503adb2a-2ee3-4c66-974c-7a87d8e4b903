<?php

namespace App\Observers;

use App\Models\Commitment2025;
use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class Commitment2025Observer
{
    /**
     * Handle the Commitment2025 "created" event.
     */
    public function created(Commitment2025 $commitment2025): void
    {
		$pusat = User::whereHas('roles', function ($query) {
			$query->whereIn('name', ['Super Admin', 'admin']);
		})->get();

		$registrar = Auth::user();
		$noIjin = $commitment2025->no_ijin;
		$periode = $commitment2025->periodetahun;
		$company = $commitment2025->user->datauser->company_name;
		$cleanNoIjin = str_replace(['/', '-','.'], '', $noIjin);
		$linkUrl = route('panel.2025.report.efilling', $cleanNoIjin);

		Notification::make()
			->title('Komitmen Baru Terdaftar')
			->body("<span class='text-info-500 font-bold'>{$company}</span> telah mendaftarkan Komitmen No. PPRK: <span class='text-info-500 font-bold'>{$noIjin}</span>, untuk periode {$periode}.")
			->sendToDatabase($pusat);

		Notification::make()
			->title('Komitmen Baru Terdaftar')
			->body("<p class='mb-3'>Selamat, <span class='text-info-500 font-bold'>{$company}</span>. Komitmen Anda dengan No. PPRK: <span class='text-info-500 font-bold'>{$noIjin}</span>, untuk periode {$periode} telah masuk ke dalam database kami.</p>
			<p class='mb-3 mt-5'>Selanjutnya, untuk langkah pertama silahkan memilih lokasi tanam di tautan ini:</p>
			<p class='mb-3 mt-5'><a class='text-primary-500 font-bold' href='{$linkUrl}' rel='nofollow noreferer'>Pilih Lokasi Tanam</a></p>")
			->sendToDatabase($registrar);
    }

    /**
     * Handle the Commitment2025 "updated" event.
     */
    public function updated(Commitment2025 $commitment2025): void
    {
    }

    /**
     * Handle the Commitment2025 "deleted" event.
     */
    public function deleted(Commitment2025 $commitment2025): void
    {
        //
    }

    /**
     * Handle the Commitment2025 "restored" event.
     */
    public function restored(Commitment2025 $commitment2025): void
    {
        //
    }

    /**
     * Handle the Commitment2025 "force deleted" event.
     */
    public function forceDeleted(Commitment2025 $commitment2025): void
    {
        //
    }
}
