<?php

namespace App\Filament\Admin\Resources\MasterSpatialResource\Widgets;

use App\Models\MasterSpatial;
use Filament\Widgets\ChartWidget;

class SpatialLineChartWidgets extends ChartWidget
{
	protected static ?string $heading = 'Grafik Sebaran';
	// protected int | string | array $columnSpan = 'full';
	// protected static ?string $maxHeight = '200px';

	protected function getData(): array
	{
		$chartData = MasterSpatial::query()
			->where('is_active', 1)
			->join('data_kabupatens', 'data_kabupatens.kabupaten_id', '=', 't2025_master_spatials.kabupaten_id')
			->selectRaw('data_kabupatens.nama_kab as nama_kabupaten, SUM(t2025_master_spatials.luas_lahan) as total_luas')
			->groupBy('data_kabupatens.nama_kab')
			->orderBy('data_kabupatens.nama_kab', 'asc')
			->pluck('total_luas', 'nama_kabupaten')
			->toArray();

		$baseColor = [6, 0, 122]; // Warna dasar (RGB)
		$opacityStart = 0.2;
		$opacityEnd = 1.0;
		$steps = 20;
		$backgroundColors = [];
		
		for ($i = 0; $i < $steps; $i++) {
			$opacity = $opacityStart + (($opacityEnd - $opacityStart) / ($steps - 1)) * $i;
			$backgroundColors[] = "rgba({$baseColor[0]}, {$baseColor[1]}, {$baseColor[2]}, {$opacity})";
		}
			

		return [
			'datasets' => [
				[
					'label' => 'Total Luas Lahan (m2)',
					'data' => array_values($chartData),
					// 'borderColor' => '#4F46E5',
					// 'backgroundColor' => array_slice($backgroundColors, 0, count($chartData)),
					'backgroundColor' => [
						'rgba(220, 20, 60, 0.8)',   // Merah tua
						'rgba(250, 128, 114, 0.8)', // Salmon
						'rgba(255, 99, 132, 0.8)',  // Merah muda
						'rgba(210, 105, 30, 0.8)',  // Coklat tua
						'rgba(255, 140, 0, 0.8)',   // Oranye tua
						'rgba(255, 159, 64, 0.8)',  // Oranye
						'rgba(255, 206, 86, 0.8)',  // Kuning
						'rgba(255, 215, 0, 0.8)',   // Emas
						'rgba(148, 0, 211, 0.8)',   // Ungu tua
						'rgba(153, 102, 255, 0.8)', // Ungu
						'rgba(83, 102, 255, 0.8)',  // Biru tua
						'rgba(176, 196, 222, 0.8)', // Biru keabuan
						'rgba(70, 130, 180, 0.8)',  // Biru baja
						'rgba(54, 162, 235, 0.8)',  // Biru
						'rgba(0, 191, 255, 0.8)',   // Biru langit
						'rgba(75, 192, 192, 0.8)',  // Hijau tosca
						'rgba(60, 179, 113, 0.8)',  // Hijau sedang
						'rgba(34, 139, 34, 0.8)',   // Hijau gelap
						'rgba(47, 79, 79, 0.8)',    // Abu-abu tua
						'rgba(199, 199, 199, 0.8)', // Abu-abu terang
					],
					'fill' => true,
					'tension' => 0.3,
				],
			],
			'labels' => array_keys($chartData),
		];
	}

	protected function getType(): string
	{
		return 'polarArea';
	}

	public function getDescription(): ?string
	{
		return 'Sebaran lahan per kabupaten dalam satuan luas m².';
	}

	protected function getOptions(): array
	{
		return [
			'scales' => [
				'y' => [
					'ticks' => [
						'display' => false, // Menyembunyikan label sumbu Y
					],
				],
				'x' => [
					'ticks' => [
						'display' => false, // Menyembunyikan label sumbu X
					],
				],
			],
			'plugins' => [
				'legend' => [
					'display' => false,
				],
			],
		];
	}
}
