<?php

namespace App\Observers;

use App\Models\Pks2025;
use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class Pks2025Observer
{
	/**
	 * Handle the Pks2025 "created" event.
	 */
	public function created(Pks2025 $pks2025): void
	{
		//
	}

	/**
	 * Handle the Pks2025 "updated" event.
	 */
	public function updated(Pks2025 $pks2025): void
	{
		if ($pks2025->wasChanged('status_dinas') && $pks2025->status_dinas === '0') {
			$registrar = Auth::user();
			Notification::make()
				->title('Dokumen PKS')
				->body("Dokumen PKS No: {$pks2025->no_perjanjian}, berhasil dikirimkan ke Dinas Kabupaten terkait. Silahkan tunggu kabar selanjutnya.")
				->sendToDatabase($registrar);

			$dinas = User::whereHas('dataadmin', function ($query) use ($pks2025) {
				$query->where('kabupaten_id', $pks2025->kabupaten_id);
			})->first();

			$pusat = User::whereHas('roles', function ($query) {
				$query->whereIn('name', ['Super Admin', 'admin']);
			})->get();

			$verifDinasUrl = route('filament.panel2025.resources.pks2025s.verifdinas', $pks2025->id);

			if($dinas){
				Notification::make()
					->title('Pengajuan PKS Baru')
					->body("{$registrar->datauser->company_name} telah mengajukan pemeriksaan PKS No: {$pks2025->no_perjanjian}. <a href='{$verifDinasUrl}' class='text-info-500'>Buka Tautan PKS</a>.")
					->sendToDatabase($dinas);

				Notification::make()
					->title('Pengajuan PKS Baru')
					->body("{$registrar->datauser->company_name} telah mengajukan pemeriksaan PKS No: {$pks2025->no_perjanjian} ke Dinas {$dinas->dataadmin->nama}.")
					->sendToDatabase($pusat);
			}else{
				Notification::make()
					->title('Akun Dinas Belum Tersedia')
					->body("{$registrar->datauser->company_name} telah mengajukan pemeriksaan PKS No: {$pks2025->no_perjanjian} ke Dinas terkait di kabupaten {$pks2025->kabupaten->nama_kab}. Namun belum tersedia akun untuk Dinas tersebut.<a href='{$verifDinasUrl}' class='text-info-500'>Buka Tautan PKS</a>.")
					->sendToDatabase($pusat);
			}
		}

		if ($pks2025->wasChanged('status_dinas') && $pks2025->status_dinas === '1') {
			$registrar = $pks2025->user;
			$dinas = Auth::user();
			$pksUrl = route('filament.panel2025.resources.pks2025s.edit', $pks2025->id);

			Notification::make()
				->title("<span class='text-danger-500'>Perbaikan PKS</span>")
				->body("Dokumen PKS No: <span class='font-bold'>{$pks2025->no_perjanjian}</span>, telah selesai diperiksa oleh <span class='font-bold'>{$dinas->dataadmin->nama}</span>. Anda diharuskan melakukan <span class='font-bold text-danger-500'>PERBAIKAN</span> atas data dan berkas PKS terkait. <p><a class='font-bold text-primary-500' href='{$pksUrl}'>Klik untuk memperbaiki</a></p>")
				->sendToDatabase($registrar);

			$pusat = User::whereHas('roles', function ($query) {
				$query->whereIn('name', ['Super Admin', 'admin']);
			})->get();

			Notification::make()
				->title('Pemeriksaan PKS')
				->body("Terima kasih Anda telah selesai memeriksa PKS No: <span class='font-bold'>{$pks2025->no_perjanjian}</span>.")
				->sendToDatabase($dinas);

			Notification::make()
				->title('Pengajuan PKS Baru')
				->body("PKS No: {$pks2025->no_perjanjian} telah selesai diperiksa oleh {$dinas->dataadmin->nama}.")
				->sendToDatabase($pusat);
		}

		if ($pks2025->wasChanged('status_dinas') && $pks2025->status_dinas === '2') {
			$registrar = $pks2025->user;
			$dinas = Auth::user();
			Notification::make()
				->title('Hasil Pemeriksaan PKS')
				->body("Dokumen PKS No: <span class='font-bold'>{$pks2025->no_perjanjian}</span>, telah selesai diperiksa oleh <span class='font-bold'>{$dinas->dataadmin->nama}</span>. Anda dapat melanjutkan melakukan pelaporan realisasi komitmen untuk lokasi-lokasi terkait.")
				->sendToDatabase($registrar);

			$pusat = User::whereHas('roles', function ($query) {
				$query->whereIn('name', ['Super Admin', 'admin']);
			})->get();

			$verifDinasUrl = route('filament.panel2025.resources.pks2025s.verifdinas', $pks2025->id);

			Notification::make()
				->title('Pemeriksaan PKS')
				->body("Terima kasih Anda telah selesai memeriksa PKS No: <span class='font-bold'>{$pks2025->no_perjanjian}</span>.")
				->sendToDatabase($dinas);

			Notification::make()
				->title('Pengajuan PKS Baru')
				->body("PKS No: {$pks2025->no_perjanjian} telah selesai diperiksa oleh {$dinas->dataadmin->nama}.")
				->sendToDatabase($pusat);
		}
	}

	/**
	 * Handle the Pks2025 "deleted" event.
	 */
	public function deleted(Pks2025 $pks2025): void
	{
		//
	}

	/**
	 * Handle the Pks2025 "restored" event.
	 */
	public function restored(Pks2025 $pks2025): void
	{
		//
	}

	/**
	 * Handle the Pks2025 "force deleted" event.
	 */
	public function forceDeleted(Pks2025 $pks2025): void
	{
		//
	}
}
