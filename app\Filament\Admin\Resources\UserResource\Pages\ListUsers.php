<?php

namespace App\Filament\Admin\Resources\UserResource\Pages;

use App\Filament\Admin\Resources\UserResource;
use App\Models\DataAdministrator;
use App\Models\User;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Builder;

class ListUsers extends ListRecords
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Pengguna Baru')->icon('icon-person-add'),
        ];
    }

	protected function getTableQuery(): Builder
	{
		// Tidak perlu memfilter Super Admin karena sudah ada tab khusus
		return User::query()
			->select(['id', 'name', 'username', 'email', 'status'])
			->with(['roles:id,name'])
			->with(['dataadmin:id,nama']);
	}

	public function getDefaultActiveTab(): string
	{
		return 'Importir';
	}

	
	protected function paginateTableQuery(Builder $query): Paginator
	{
		return $query->simplePaginate(($this->getTableRecordsPerPage() === 'all') ? $query->count() : $this->getTableRecordsPerPage());
	}

	public function getTabs(): array
    {
        // Hitung jumlah pengguna untuk setiap kategori
        $countSuperAdmin = User::whereHas('roles', function ($q) { $q->where('name', 'Super Admin'); })->where('status', 'Aktif')->count();
        $countAdmin = User::whereHas('roles', function ($q) { $q->where('name', 'admin'); })->where('status', 'Aktif')->count();
        $countDirektur = User::whereHas('roles', function ($q) { $q->where('name', 'direktur'); })->where('status', 'Aktif')->count();
        $countVerifikator = User::whereHas('roles', function ($q) { $q->where('name', 'verifikator'); })->where('status', 'Aktif')->count();
        $countDinas = User::whereHas('roles', function ($q) { $q->where('name', 'dinas'); })->where('status', 'Aktif')->count();
        $countImportir = User::whereHas('roles', function ($q) { $q->where('name', 'importir'); })->where('status', 'Aktif')->count();
        $countSpatial = User::whereHas('roles', function ($q) { $q->where('name', 'spatial'); })->where('status', 'Aktif')->count();
        $countBaru = User::where('status', 'Baru')->count();

        return [
            'Registrar Baru' => Tab::make()
                ->badge($countBaru > 0 ? $countBaru : null)
                ->badgeColor($countBaru > 0 ? 'danger' : null)
                ->modifyQueryUsing(function (Builder $query){
                    $query->where('status', 'Baru');
                }),
            // 'Super Admin' => Tab::make()
            //     ->badge($countSuperAdmin > 0 ? $countSuperAdmin : null)
            //     ->badgeColor('info')
            //     ->modifyQueryUsing(function (Builder $query){
            //         $query->where('status', 'Aktif')->whereHas('roles', function ($q) {
            //             $q->where('name', 'Super Admin');
            //         });
            //     }),
            'Administrator' => Tab::make()
                ->badge($countAdmin > 0 ? $countAdmin : null)
                ->badgeColor('info')
                ->modifyQueryUsing(function (Builder $query){
                    $query->where('status', 'Aktif')->whereHas('roles', function ($q) {
                        $q->where('name', 'admin');
                    });
                }),
            'Direktur' => Tab::make()
                ->badge($countDirektur > 0 ? $countDirektur : null)
                ->badgeColor('info')
                ->modifyQueryUsing(function (Builder $query){
                    $query->where('status', 'Aktif')->whereHas('roles', function ($q) {
                        $q->where('name', 'direktur');
                    });
                }),
            'Verifikator' => Tab::make()
                ->badge($countVerifikator > 0 ? $countVerifikator : null)
                ->badgeColor('info')
                ->modifyQueryUsing(function (Builder $query){
                    $query->where('status', 'Aktif')->whereHas('roles', function ($q) {
                        $q->where('name', 'verifikator');
                    });
                }),
            'Tim Spasial' => Tab::make()
                ->badge($countSpatial > 0 ? $countSpatial : null)
                ->badgeColor('info')
                ->modifyQueryUsing(function (Builder $query){
                    $query->where('status', 'Aktif')->whereHas('roles', function ($q) {
                        $q->where('name', 'spatial');
                    });
                }),
            'Dinas' => Tab::make()
                ->badge($countDinas > 0 ? $countDinas : null)
                ->badgeColor('info')
                ->modifyQueryUsing(function (Builder $query){
                    $query->where('status', 'Aktif')->whereHas('roles', function ($q) {
                        $q->where('name', 'dinas');
                    });
                }),
            'Importir' => Tab::make()
                ->badge($countImportir > 0 ? $countImportir : null)
                ->badgeColor('info')
                ->modifyQueryUsing(function (Builder $query){
                    $query->where('status', 'Aktif')->whereHas('roles', function ($q) {
                        $q->where('name', 'importir');
                    });
                }),
        ];
    }
}
