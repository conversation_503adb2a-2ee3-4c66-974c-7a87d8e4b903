<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\SklRekomendasi2024;
use App\Models\User;

class SklRekomendasi2024Policy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any SklRekomendasi2024');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, SklRekomendasi2024 $sklrekomendasi2024): bool
    {
        return $user->checkPermissionTo('view SklRekomendasi2024');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create SklRekomendasi2024');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, SklRekomendasi2024 $sklrekomendasi2024): bool
    {
        return $user->checkPermissionTo('update SklRekomendasi2024');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, SklRekomendasi2024 $sklrekomendasi2024): bool
    {
        return $user->checkPermissionTo('delete SklRekomendasi2024');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any SklRekomendasi2024');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, SklRekomendasi2024 $sklrekomendasi2024): bool
    {
        return $user->checkPermissionTo('restore SklRekomendasi2024');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any SklRekomendasi2024');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, SklRekomendasi2024 $sklrekomendasi2024): bool
    {
        return $user->checkPermissionTo('replicate SklRekomendasi2024');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder SklRekomendasi2024');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, SklRekomendasi2024 $sklrekomendasi2024): bool
    {
        return $user->checkPermissionTo('force-delete SklRekomendasi2024');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any SklRekomendasi2024');
    }
}
