<?php

namespace App\Filament\Admin\Pages;

use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;
use League\CommonMark\Environment\Environment;
use League\CommonMark\Extension\GithubFlavoredMarkdownExtension;
use League\CommonMark\Extension\CommonMark\CommonMarkCoreExtension;
use League\CommonMark\MarkdownConverter;

class Documentation extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationLabel = 'Dokumentasi Teknis';
    protected static ?string $title = 'Dokumentasi Teknis';
    protected static ?string $navigationGroup = 'Documentations';
    protected static ?int $navigationSort = 1;

    public static function shouldRegisterNavigation(): bool
    {
        return Auth::user()?->hasAnyRole(['admin', 'Super Admin']);
    }

	public static function canAccess(): bool
    {
        return Auth::user()?->hasAnyRole(['admin', 'Super Admin']);
    }

    protected static string $view = 'filament.admin.pages.documentation';

    public function getViewData(): array
    {
        $markdownPath = base_path('documentations/technical-documentation.md');

        if (!file_exists($markdownPath)) {
            return [
                'documentationHtml' => '<div class="text-red-500">Documentation file not found. Please make sure the file exists at: ' . $markdownPath . '</div>',
            ];
        }

        $markdownContent = file_get_contents($markdownPath);

        if (empty($markdownContent)) {
            return [
                'documentationHtml' => '<div class="text-red-500">Documentation file is empty.</div>',
            ];
        }

        // Create a new environment with GFM extension
        $environment = new Environment([
            'html_input' => 'strip',
            'allow_unsafe_links' => false,
        ]);

        // Add the CommonMark core extension and GFM extension
        $environment->addExtension(new CommonMarkCoreExtension());
        $environment->addExtension(new GithubFlavoredMarkdownExtension());

        // Create a new converter using the configured environment
        $converter = new MarkdownConverter($environment);

        // Convert the markdown to HTML
        $result = $converter->convert($markdownContent);
        $html = $result->getContent();

        return [
            'documentationHtml' => $html,
        ];
    }
}
