<?php

namespace App\Filament\Panel2025\Resources\VerifberkasResource\Pages;

use App\Filament\Panel2025\Resources\VerifberkasResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;

class Berkas extends EditRecord
{
    protected static string $resource = VerifberkasResource::class;
	public static string | Alignment $formActionsAlignment = Alignment::Right;

	protected static ?string $title = 'Verifikasi Berkas';
    public function getHeading(): string
	{
        return 'Verifikasi Berkas';
	}

    public function getSubheading(): ?string
    {
		$kindLabels = [
			'spvt' => 'Surat Pengajuan Verifikasi (Tanam)',
			'spvp' => 'Surat Pengajuan Verifikasi (Produksi)',
			'spskl' => 'Surat Pengajuan Penerbitan SKL',
			'sptjmt' => 'Surat Pernyataan Tanggung Jawab Mutlak (Periode Tanam)',
			'sptjmp' => 'Surat Pernyataan Tanggung Jawab Mutlak (Periode Produksi)',
			'rta' => 'Form Realisasi Tanam',
			'rpo' => 'Form Realisasi Produksi',
			'spht' => 'Statistik Pertanian Hortikultura (Periode Tanam)',
			'sphb' => 'Statistik Pertanian Hortikultura (Periode Produksi)',
			'spdst' => 'Surat Pengantar Dinas Telah Selesai Tanam',
			'spdsp' => 'Surat Pengantar Dinas Telah Selesai Produksi',
			'logbook' => 'Logbook (Tanam/Produksi)',
			'la' => 'Laporan Akhir',
		];

		$kind = $this->record?->kind;

        return $kind ? ($kindLabels[$kind] ?? $kind) : null;

    }
}
