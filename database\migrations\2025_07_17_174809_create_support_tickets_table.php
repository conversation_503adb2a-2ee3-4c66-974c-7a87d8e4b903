<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('support_tickets', function (Blueprint $table) {
			$table->id();

            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->foreignId('staff_id')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('department_id')->constrained('support_departements')->cascadeOnDelete();

            $table->string('subject');
            $table->text('message');
            $table->string('attachment')->nullable();
            $table->string('related_service')->nullable(); //ini akan kompleks, diambil dari no_ijin di 2 table milik user yang memiliki relasi ke 2 table tersebut (relationship hasMany: commitment, oldCommitment)

            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->enum('status', ['open', 'in_progress', 'resolved', 'closed'])->default('open');

            $table->enum('last_replied_by', ['user', 'staff'])->nullable();
            $table->timestamp('last_activity_at')->nullable();
            $table->timestamp('closed_at')->nullable();

            $table->timestamps();
            $table->softDeletes();

			$table->index(['user_id', 'status']);
			$table->index(['staff_id', 'status']);
			$table->index('department_id');
			$table->index('last_activity_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('support_tickets');
    }
};
