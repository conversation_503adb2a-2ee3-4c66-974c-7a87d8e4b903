<?php

namespace App\Filament\Admin\Pages;

use Filament\Pages\Page;
use Illuminate\Support\Facades\Storage;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;

class KmlFailureReportsPage extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-exclamation-circle';

    protected static ?string $navigationLabel = 'Laporan Kegagalan KML';

    protected static ?string $navigationGroup = 'Data Induk';

    protected static ?string $title = 'Laporan Kegagalan Unggah KML';

    protected static string $view = 'filament.admin.pages.kml-failure-reports-page';

    protected static ?string $slug = 'kml-failure-reports';

	public static function canAccess(): bool
    {
        return Auth::user()?->hasAnyRole(['admin', 'Super Admin', 'spatial']);
    }

	protected static bool $shouldRegisterNavigation = false;

    public $selectedReport = null;
    public $reportFiles = [];
    public $reportData = [];
    public $reportMetadata = null;
    public $search = '';
    public $perPage = 10;
    public $currentPage = 1;

    public function mount()
    {
        $this->loadReportFiles();
    }

    protected function loadReportFiles()
    {
        $directory = 'uploads/kml/not_processed';

        if (!Storage::disk('public')->exists($directory)) {
            $this->reportFiles = [];
            return;
        }

        $files = Storage::disk('public')->files($directory);

        // Filter hanya file JSON
        $jsonFiles = array_filter($files, function ($file) {
            return pathinfo($file, PATHINFO_EXTENSION) === 'json';
        });

        // Urutkan berdasarkan waktu modifikasi (terbaru dulu)
        usort($jsonFiles, function ($a, $b) {
            return Storage::disk('public')->lastModified($b) - Storage::disk('public')->lastModified($a);
        });

        $this->reportFiles = array_map(function ($file) {
            $filename = basename($file);
            $timestamp = Storage::disk('public')->lastModified($file);
            $size = Storage::disk('public')->size($file);

            return [
                'path' => $file,
                'filename' => $filename,
                'timestamp' => date('Y-m-d H:i:s', $timestamp),
                'size' => $this->formatBytes($size),
            ];
        }, $jsonFiles);
    }

    protected function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= (1 << (10 * $pow));

        return round($bytes, $precision) . ' ' . $units[$pow];
    }

    /**
     * Memuat laporan berdasarkan path yang dipilih
     *
     * @param string $reportPath Path file laporan
     * @return void
     */
    public function loadReport($reportPath)
    {
        try {
            if (!Storage::disk('public')->exists($reportPath)) {
                Notification::make()
                    ->title('File tidak ditemukan')
                    ->body('File laporan yang dipilih tidak ditemukan.')
                    ->danger()
                    ->send();

                $this->reportData = [];
                $this->reportMetadata = null;
                return;
            }

            $jsonContent = Storage::disk('public')->get($reportPath);
            $data = json_decode($jsonContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Notification::make()
                    ->title('Format JSON tidak valid')
                    ->body('File laporan tidak berisi JSON yang valid: ' . json_last_error_msg())
                    ->danger()
                    ->send();

                $this->reportData = [];
                $this->reportMetadata = null;
                return;
            }

            // Ekstrak metadata dan data kegagalan
            $this->reportMetadata = $data['metadata'] ?? null;
            $this->reportData = $data['failures'] ?? [];

            // Pastikan setiap item memiliki properti yang diperlukan
            foreach ($this->reportData as $key => $item) {
                // Pastikan properti yang diperlukan ada
                $this->reportData[$key]['fileName'] = $item['fileName'] ?? 'Unknown';
                $this->reportData[$key]['fileSize'] = $item['fileSize'] ?? 0;
                $this->reportData[$key]['failureReason'] = $item['failureReason'] ?? 'Unknown reason';
                $this->reportData[$key]['failureType'] = $item['failureType'] ?? 'unknown';
                $this->reportData[$key]['failureWhen'] = $item['failureWhen'] ?? 'upload';
                $this->reportData[$key]['timestamp'] = $item['timestamp'] ?? now()->toIso8601String();
            }

            Notification::make()
                ->title('Laporan dimuat')
                ->body('Laporan kegagalan berhasil dimuat.')
                ->success()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error')
                ->body('Terjadi kesalahan saat memuat laporan: ' . $e->getMessage())
                ->danger()
                ->send();

            $this->reportData = [];
            $this->reportMetadata = null;
        }
    }

    /**
     * Memuat laporan berdasarkan pilihan dropdown
     *
     * @return void
     */
    public function loadSelectedReport()
    {
        if (!empty($this->selectedReport)) {
            $this->loadReport($this->selectedReport);
        }
    }

    /**
     * Reset pilihan laporan
     *
     * @return void
     */
    public function resetSelection()
    {
        $this->selectedReport = null;
        $this->reportData = [];
        $this->reportMetadata = null;

        Notification::make()
            ->title('Pilihan direset')
            ->body('Pilihan laporan telah direset.')
            ->info()
            ->send();
    }

    /**
     * Mendapatkan data yang sudah difilter berdasarkan pencarian
     *
     * @return array
     */
    public function getFilteredData()
    {
        if (empty($this->reportData)) {
            return [];
        }

        $search = strtolower($this->search);

        if (empty($search)) {
            return $this->reportData;
        }

        return array_filter($this->reportData, function ($item) use ($search) {
            return
                str_contains(strtolower($item['fileName'] ?? ''), $search) ||
                str_contains(strtolower($item['failureReason'] ?? ''), $search) ||
                str_contains(strtolower($item['failureType'] ?? ''), $search) ||
                str_contains(strtolower($item['failureWhen'] ?? ''), $search);
        });
    }

    /**
     * Mendapatkan data yang sudah dipaginasi
     *
     * @return array
     */
    public function getPaginatedData()
    {
        $filteredData = $this->getFilteredData();
        $totalItems = count($filteredData);
        $totalPages = max(1, ceil($totalItems / $this->perPage));

        // Pastikan halaman saat ini valid
        $this->currentPage = min(max(1, $this->currentPage), $totalPages);

        $offset = ($this->currentPage - 1) * $this->perPage;

        return [
            'data' => array_slice($filteredData, $offset, $this->perPage),
            'total' => $totalItems,
            'per_page' => $this->perPage,
            'current_page' => $this->currentPage,
            'last_page' => $totalPages,
        ];
    }

    /**
     * Pindah ke halaman tertentu
     *
     * @param int $page
     * @return void
     */
    public function goToPage($page)
    {
        $this->currentPage = $page;
    }

    /**
     * Reset pencarian
     *
     * @return void
     */
    public function resetSearch()
    {
        $this->search = '';
        $this->currentPage = 1;
    }

    /**
     * Mengekspor data ke CSV
     *
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function exportToCSV()
    {
        if (empty($this->reportData)) {
            Notification::make()
                ->title('Tidak ada data')
                ->body('Tidak ada data yang dapat diekspor.')
                ->warning()
                ->send();

            return;
        }

        $filename = 'kml_failures_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0',
        ];

        $callback = function() {
            $file = fopen('php://output', 'w');

            // Header
            fputcsv($file, ['Nama File', 'Ukuran File', 'Tipe Kegagalan', 'Tahap Kegagalan', 'Alasan Kegagalan', 'Waktu']);

            // Data
            foreach ($this->reportData as $row) {
                fputcsv($file, [
                    $row['fileName'] ?? 'Unknown',
                    isset($row['fileSize']) ? $this->formatBytes($row['fileSize']) : '0 B',
                    $row['failureType'] ?? 'unknown',
                    $row['failureWhen'] ?? 'unknown',
                    $row['failureReason'] ?? 'Unknown reason',
                    isset($row['timestamp']) ? \Carbon\Carbon::parse($row['timestamp'])->format('Y-m-d H:i:s') : '-',
                ]);
            }

            fclose($file);
        };

        Notification::make()
            ->title('Ekspor berhasil')
            ->body('Data berhasil diekspor ke CSV.')
            ->success()
            ->send();

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Mengekspor data ke Excel
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportToExcel()
    {
        if (empty($this->reportData)) {
            Notification::make()
                ->title('Tidak ada data')
                ->body('Tidak ada data yang dapat diekspor.')
                ->warning()
                ->send();

            return;
        }

        $filename = 'kml_failures_' . date('Y-m-d_H-i-s') . '.xlsx';

        // Buat file Excel menggunakan library PhpSpreadsheet yang sudah terintegrasi dengan Laravel
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set header
        $sheet->setCellValue('A1', 'Nama File');
        $sheet->setCellValue('B1', 'Ukuran File');
        $sheet->setCellValue('C1', 'Tipe Kegagalan');
        $sheet->setCellValue('D1', 'Tahap Kegagalan');
        $sheet->setCellValue('E1', 'Alasan Kegagalan');
        $sheet->setCellValue('F1', 'Waktu');

        // Style header
        $headerStyle = [
            'font' => [
                'bold' => true,
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'E0E0E0',
                ],
            ],
        ];
        $sheet->getStyle('A1:F1')->applyFromArray($headerStyle);

        // Isi data
        $row = 2;
        foreach ($this->reportData as $data) {
            $sheet->setCellValue('A' . $row, $data['fileName'] ?? 'Unknown');
            $sheet->setCellValue('B' . $row, isset($data['fileSize']) ? $this->formatBytes($data['fileSize']) : '0 B');
            $sheet->setCellValue('C' . $row, ucfirst($data['failureType'] ?? 'unknown'));
            $sheet->setCellValue('D' . $row, ucfirst($data['failureWhen'] ?? 'unknown'));
            $sheet->setCellValue('E' . $row, $data['failureReason'] ?? 'Unknown reason');
            $sheet->setCellValue('F' . $row, isset($data['timestamp']) ? \Carbon\Carbon::parse($data['timestamp'])->format('Y-m-d H:i:s') : '-');
            $row++;
        }

        // Auto size kolom
        foreach (range('A', 'F') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        // Buat file Excel
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_');
        $writer->save($tempFile);

        Notification::make()
            ->title('Ekspor berhasil')
            ->body('Data berhasil diekspor ke Excel.')
            ->success()
            ->send();

        return response()->download($tempFile, $filename, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ])->deleteFileAfterSend(true);
    }

    /**
     * Mengunduh file JSON
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadJsonFile()
    {
        if (!$this->selectedReport) {
            Notification::make()
                ->title('Tidak ada file yang dipilih')
                ->body('Pilih file terlebih dahulu.')
                ->warning()
                ->send();

            return;
        }

        if (!Storage::disk('public')->exists($this->selectedReport)) {
            Notification::make()
                ->title('File tidak ditemukan')
                ->body('File yang dipilih tidak ditemukan.')
                ->danger()
                ->send();

            return;
        }

        $filename = basename($this->selectedReport);
        $filePath = Storage::disk('public')->path($this->selectedReport);

        Notification::make()
            ->title('Unduh berhasil')
            ->body('File JSON berhasil diunduh.')
            ->success()
            ->send();

        return response()->download($filePath, $filename);
    }

    /**
     * Konfirmasi hapus file JSON
     *
     * @return void
     */
    public function confirmDeleteJsonFile()
    {
        if (!$this->selectedReport) {
            Notification::make()
                ->title('Tidak ada file yang dipilih')
                ->body('Pilih file terlebih dahulu.')
                ->warning()
                ->send();

            return;
        }

        $this->dispatch('open-delete-modal');
    }

    /**
     * Hapus file JSON
     *
     * @return void
     */
    public function deleteJsonFile()
    {
        if (!$this->selectedReport) {
            Notification::make()
                ->title('Tidak ada file yang dipilih')
                ->body('Pilih file terlebih dahulu.')
                ->warning()
                ->send();

            return;
        }

        if (!Storage::disk('public')->exists($this->selectedReport)) {
            Notification::make()
                ->title('File tidak ditemukan')
                ->body('File yang dipilih tidak ditemukan.')
                ->danger()
                ->send();

            return;
        }

        // Hapus file
        Storage::disk('public')->delete($this->selectedReport);

        // Reset pilihan
        $this->selectedReport = null;
        $this->reportData = [];
        $this->reportMetadata = null;

        // Muat ulang daftar file
        $this->loadReportFiles();

        Notification::make()
            ->title('Hapus berhasil')
            ->body('File JSON berhasil dihapus.')
            ->success()
            ->send();
    }

    protected function getHeaderActions(): array
    {
        return [];
    }
}
