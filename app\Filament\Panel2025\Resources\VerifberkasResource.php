<?php

namespace App\Filament\Panel2025\Resources;

use App\Filament\Panel2025\Resources\VerifberkasResource\Pages;
use App\Filament\Panel2025\Resources\VerifberkasResource\Pages\Berkas;
use App\Filament\Panel2025\Resources\VerifberkasResource\Pages\CreateVerifberkas;
use App\Filament\Panel2025\Resources\VerifberkasResource\Pages\EditVerifberkas;
use App\Filament\Panel2025\Resources\VerifberkasResource\Pages\ListVerifberkas;
use App\Filament\Panel2025\Resources\VerifberkasResource\Pages\VerififikasiBerkas;
use App\Filament\Panel2025\Resources\VerifberkasResource\RelationManagers;
use App\Models\Completed;
use App\Models\PengajuanVerifikasi;
use App\Models\User;
use App\Models\Userfile;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\SelectColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class VerifberkasResource extends Resource
{
    protected static ?string $model = Userfile::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
				Hidden::make('verif_by')
					->default(fn () => Auth::user()->id),
				Hidden::make('verif_at')
					->default(today()),
				Section::make('Pemeriksaan Berkas')
					->aside()
					->schema([
						Placeholder::make('berkas')
							->label('Tautan berkas')
							->inlineLabel()
							->extraAttributes(['class' => 'font-bold text-info-500'])
							->content(fn ($record) => new HtmlString(
								'<a href="/'. $record->file_url .'" rel="nofollow noreferer" target="_blank">Lihat Berkas</a>'
							)),
						Hidden::make('verif_by')
							->default(Auth::user()->id)
							->formatStateUsing(fn ()=>Auth::User()->id),
						Placeholder::make('verifikator')
							->label('Pemeriksa')
							->inlineLabel()
							->extraAttributes(['class' => 'font-bold text-info-500'])
							->content(fn ($record) => $record->verifikator?->name),
						Placeholder::make('verif_at')
							->label('Tanggal Periksa')
							->inlineLabel()
							->content(fn ($record) => $record->verif_at 
								? Carbon::parse($record->verif_at)->translatedFormat('d F Y') 
								: '-'),
						Select::make('status')
							->inlineLabel()
							->options([
								'Sesuai' => 'Sesuai',
								'Tidak Sesuai' => 'Tidak Sesuai',
							]),
					])
            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
		$kindLabels = [
			'spvt' => 'Surat Pengajuan Verifikasi (Tanam)',
			'spvp' => 'Surat Pengajuan Verifikasi (Produksi)',
			'spskl' => 'Surat Pengajuan Penerbitan SKL',
			'sptjmt' => 'Surat Pernyataan Tanggung Jawab Mutlak (Periode Tanam)',
			'sptjmp' => 'Surat Pernyataan Tanggung Jawab Mutlak (Periode Produksi)',
			'rta' => 'Form Realisasi Tanam',
			'rpo' => 'Form Realisasi Produksi',
			'spht' => 'Statistik Pertanian Hortikultura (Periode Tanam)',
			'sphb' => 'Statistik Pertanian Hortikultura (Periode Produksi)',
			'spdst' => 'Surat Pengantar Dinas Telah Selesai Tanam',
			'spdsp' => 'Surat Pengantar Dinas Telah Selesai Produksi',
			'logbook' => 'Logbook (Tanam/Produksi)',
			'la' => 'Laporan Akhir',
		];
        return $table
            ->columns([
                TextColumn::make('kind')
					->label('Berkas')
					->formatStateUsing(fn ($state) => $kindLabels[$state] ?? $state),
				TextColumn::make('file_url')
					->label('Tautan')
					->formatStateUsing(fn ($state) => new HtmlString(
						'<a href="/'. $state .'" rel="nofollow noreferer" target="_blank">Lihat Berkas</a>'
					)),
				TextColumn::make('status')
					->badge()
					->color(fn ($record) => $record->status === 'Sesuai' ? 'success' : 'danger')
            ])
            ->filters([
                //
            ])
            ->actions([
                // EditAction::make()
				// 	->modalHeading(fn ($record) => 'Verifikasi Berkas ' . ($kindLabels[$record->kind] ?? $record->kind))
				// 	->modalWidth('md'),
            ])
            ->bulkActions([
                // BulkActionGroup::make([
                //     DeleteBulkAction::make(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListVerifberkas::route('/'),
            'verifikasi' => VerififikasiBerkas::route('/{kind}/{pengajuan}/verifikasiberkas'),
            // 'create' => CreateVerifberkas::route('/create'),
            // 'edit' => EditVerifberkas::route('/{record}/edit'),
            'berkas' => Berkas::route('/{record}/berkas'),
        ];
    }

	public static function shouldRegisterNavigation(): bool
	{
		return false;
	}
}
