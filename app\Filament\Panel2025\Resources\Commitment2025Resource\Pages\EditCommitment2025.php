<?php

namespace App\Filament\Panel2025\Resources\Commitment2025Resource\Pages;

use App\Filament\Panel2025\Resources\Commitment2025Resource;
use App\Livewire\GoogleMap;
use App\Models\MasterKabupaten;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Actions;
use Filament\Forms\Components\{Group, Hidden, Livewire, Placeholder,Section, TextInput};
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;

class EditCommitment2025 extends EditRecord
{
    protected static string $resource = Commitment2025Resource::class;
    // protected static string $view = 'realisasi.view-map';
    public function getHeading(): string
	{
        return 'Ubah Data PPRK';
	}
    
    protected static ?string $title = 'Pembaruan Data PPRK';
    public static string | Alignment $formActionsAlignment = Alignment::Right;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            // Actions\DeleteAction::make(),
        ];
    }
    
}
