<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;

class CheckDeadlines extends Command
{
    protected $signature = 'app:check-deadlines';
    protected $description = 'Check deadline_at @t2025_pks table and send notification when needed';

    public function handle()
    {
        $now = Carbon::now();

        // <PERSON><PERSON> notifikasi mingguan setiap hari Senin jam 8 pagi
        if ($now->isMonday() && $now->hour === 8) {
            dispatch(new \App\Jobs\SendWeeklyNotifications());
        }
        dispatch(new \App\Jobs\SendDailyWarnings());

        // Expire data yang melewati deadline
        dispatch(new \App\Jobs\ExpireOverdueRecords());

        $this->info('Cek deadline.');
    }
}
