<?php

namespace App\Filament\Panel2024\Resources\AjuVerifProduksi2024Resource\Pages;

use App\Filament\Panel2024\Resources\AjuVerifProduksi2024Resource;
use App\Models\Pks2024;
use App\Models\UserDocs2024;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
use Filament\Forms\Components\{Actions, Fieldset, FileUpload, Grid, Group,Placeholder, Radio, Repeater, Section, Select, Tabs, Textarea, TextInput};
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Form;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\HtmlString;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class VerificationReport extends ViewRecord
{
    protected static string $resource = AjuVerifProduksi2024Resource::class;
	protected static ?string $title = 'Verification Report';
    public function getHeading(): string
	{
        return 'Laporan Hasil Verifikasi Produksi';
	}

    public function getSubheading(): ?string
    {
        $noIjin = $this->record ? $this->record->no_ijin : '##';
        return 'RIPH No: ' . $noIjin;
    }

	public function form(Form $form): Form
	{
		return $form
			->schema([
				Tabs::make('Tabs')
					->tabs([
						Tab::make('Data Pengajuan')
							->schema([
								Section::make('Data Pengajuan')
									->aside()
									->description('Data Rekomendasi Import Produk Hortikultura')
									->schema([
										Placeholder::make('No. RIPH')
											->inlineLabel()
											->label('No. RIPH')
											->content(fn($record) => $record->no_ijin),
										Placeholder::make('NPWP')
											->inlineLabel()
											->label('NPWP')
											->content(fn($record) => $record->npwp),
										Placeholder::make('Pelaku Usaha')
											->inlineLabel()
											->content(fn($record) => $record->datauser->company_name),
										Placeholder::make('Tanggal Pengajuan')
											->inlineLabel()
											->label('Tanggal Pengajuan')
											->content(fn($record): string => $record->created_at->format('d M Y')),
									]),

								Section::make('Komitmen dan Realisasi')
									->aside()
									->description('Ringkasan data komitmen dan realisasi')
									->columns(3)
									->schema([
										TextInput::make('komitmenTanam')
											->disabled()
											->inlineLabel()
											->columnSpan(2)
											->formatStateUsing(fn ($record) => $record->commitment->luas_wajib_tanam . ' ha'),
										TextInput::make('komitmenProduksi')
											->disabled()
											->inlineLabel()
											->columnSpan(2)
											->columnStart(1)
											->formatStateUsing(fn ($record) => $record->commitment->volume_produksi . ' ton'),
										TextInput::make('countSpatial')
											->disabled()
											->label('Jumlah Lahan')
											->inlineLabel()
											->columnSpan(2)
											->columnStart(1)
											->suffixIcon(function ($record) {
												if($record->commitment->lokasi->count() > $record->commitment->datarealisasi->count()){
													return 'heroicon-s-exclamation-circle';
												}
												return 'heroicon-s-check-circle';
											})
											->suffixIconColor(function ($record) {
												if($record->commitment->lokasi->count() > $record->commitment->datarealisasi->count()){
													return 'warning';
												}
												return 'info';
											})
											->formatStateUsing(fn ($record) => $record->commitment->datarealisasi->count() . ' titik')
											->helperText(function ($record) {
												$lokasiCount = $record->commitment?->lokasi?->count() ?? 0;
												$realisasiCount = $record->commitment?->datarealisasi?->count() ?? 0;

												if ($lokasiCount > 0 && $realisasiCount < (0.6 * $lokasiCount)) {
													return 'Jumlah realisasi kurang dari 60%';
												}

												return null;
											}),
										TextInput::make('realisasiTanam')
											->disabled()
											->inlineLabel()
											->columnSpan(2)
											->columnStart(1)
											->suffixIcon(function ($record) {
												if($record->commitment->luas_wajib_tanam > $record->commitment->datarealisasi->sum('luas_lahan')){
													return 'heroicon-s-x-circle';
												}
												return 'heroicon-s-check-circle';
											})
											->suffixIconColor(function ($record) {
												if($record->commitment->luas_wajib_tanam > $record->commitment->datarealisasi->sum('luas_lahan')){
													return 'danger';
												}
												return 'success';
											})
											->formatStateUsing(fn ($record) => $record->commitment->datarealisasi->sum('luas_lahan') . ' ha'),
										TextInput::make('realisasiProduksi')
											->disabled()
											->inlineLabel()
											->columnSpan(2)
											->columnStart(1)
											->suffixIcon(function ($record) {
												if($record->commitment->volume_produksi > $record->commitment->datarealisasi->sum('volume')){
													return 'heroicon-s-x-circle';
												}
												return 'heroicon-s-check-circle';
											})
											->suffixIconColor(function ($record) {
												if($record->commitment->volume_produksi > $record->commitment->datarealisasi->sum('volume')){
													return 'danger';
												}
												return 'success';
											})
											->formatStateUsing(fn ($record) => $record->commitment->datarealisasi->sum('volume') . ' ton'),
									])
							]),

						Tab::make('Berkas Kelengkapan')
							// ->badge(function ($record) {
							// 	$countRealisasi = Userfile::where('no_ijin', $record->no_ijin)->whereNull('status')->count();
							// 	return $countRealisasi;
							// })
							->badgeColor('warning')
							->schema([
								Section::make('Berkas-berkas Tanam')
									->aside()
									->description('Berkas-berkas Pengajuan Verifikasi Tahap Tanam')
									->schema([
										Group::make()
											->relationship('userDocs')
											->columns(2)
											->schema([
												Select::make('spvtcheck')
													->label('Surat Pengajuan Verifikasi')
													->columnSpan(1)
													->options([
														'sesuai' => 'Ada/Sesuai',
														'perbaiki' => 'Tidak ada/Perbaiki',
													])
													->helperText(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
														$tahun = $record->commitment->periodetahun;
														$fileName = $record->spvt;

														if ($fileName) {
															if (str_starts_with($fileName, 'uploads/')) {
																$fileUrl ="/{$fileName}";
															} else {
																$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
															}

															return new HtmlString('<a class="text-primary-600" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
														}

														return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
													}),

												Select::make('sptjmtanamcheck')
													->label('SPTJM Tanam')
													->columnSpan(1)
													->options([
														'sesuai' => 'Ada/Sesuai',
														'perbaiki' => 'Tidak ada/Perbaiki',
													])
													->helperText(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
														$tahun = $record->commitment->periodetahun;

														$fileName = $record->sptjmtanam;

														if ($fileName) {
															if (str_starts_with($fileName, 'uploads/')) {
																$fileUrl ="/{$fileName}";
															} else {
																$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
															}

															return new HtmlString('<a class="text-primary-600" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
														}

														return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
													}),

												Select::make('rtacheck')
													->label('Realisasi/Rencana Tanam')
													->columnSpan(1)
													->options([
														'sesuai' => 'Ada/Sesuai',
														'perbaiki' => 'Tidak ada/Perbaiki',
													])
													->helperText(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
														$tahun = $record->commitment->periodetahun;

														$fileName = $record->rtacheck;

														if ($fileName) {
															if (str_starts_with($fileName, 'uploads/')) {
																$fileUrl ="/{$fileName}";
															} else {
																$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
															}

															return new HtmlString('<a class="text-primary-600" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
														}

														return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
													}),

												Select::make('sphtanamcheck')
													->label('SPH Tanam')
													->columnSpan(1)
													->options([
														'sesuai' => 'Ada/Sesuai',
														'perbaiki' => 'Tidak ada/Perbaiki',
													])
													->helperText(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
														$tahun = $record->commitment->periodetahun;

														$fileName = $record->sphtanamcheck;

														if ($fileName) {
															if (str_starts_with($fileName, 'uploads/')) {
																$fileUrl ="/{$fileName}";
															} else {
																$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
															}

															return new HtmlString('<a class="text-primary-600" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
														}

														return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
													}),

												Select::make('logbooktanamcheck')
													->label('Logbook Tanam')
													->columnSpan(1)
													->options([
														'sesuai' => 'Ada/Sesuai',
														'perbaiki' => 'Tidak ada/Perbaiki',
													])
													->helperText(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
														$tahun = $record->commitment->periodetahun;

														$fileName = $record->logbooktanamcheck;

														if ($fileName) {
															if (str_starts_with($fileName, 'uploads/')) {
																$fileUrl ="/{$fileName}";
															} else {
																$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
															}

															return new HtmlString('<a class="text-primary-600" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
														}

														return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
													}),
											]),
									]),
								Section::make('Berkas-berkas Produksi')
									->aside()
									->description('Pemeriksaan Berkas-berkas Pengajuan Verifikasi Tahap Produksi')
									->schema([
										Group::make()
											->relationship('userDocs')
											->columns(2)
											->schema([
												Select::make('spvpcheck')
													->label('Surat Pengajuan Verifikasi')
													->columnSpan(1)
													->options([
														'sesuai' => 'Ada/Sesuai',
														'perbaiki' => 'Tidak ada/Perbaiki',
													])
													->helperText(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
														$tahun = $record->commitment->periodetahun;

														$fileName = $record->spvp;

														if ($fileName) {
															if (str_starts_with($fileName, 'uploads/')) {
																$fileUrl ="/{$fileName}";
															} else {
																$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
															}

															return new HtmlString('<a class="text-primary-600" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
														}

														return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
													}),

												Select::make('sptjmproduksicheck')
													->label('SPTJM Produksi')
													->columnSpan(1)
													->options([
														'sesuai' => 'Ada/Sesuai',
														'perbaiki' => 'Tidak ada/Perbaiki',
													])
													->helperText(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
														$tahun = $record->commitment->periodetahun;

														$fileName = $record->sptjmproduksi;

														if ($fileName) {
															if (str_starts_with($fileName, 'uploads/')) {
																$fileUrl ="/{$fileName}";
															} else {
																$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
															}

															return new HtmlString('<a class="text-primary-600" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
														}

														return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
													}),

												Select::make('rpocheck')
													->label('Realisasi/Rencana Produksi')
													->columnSpan(1)
													->options([
														'sesuai' => 'Ada/Sesuai',
														'perbaiki' => 'Tidak ada/Perbaiki',
													])
													->helperText(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
														$tahun = $record->commitment->periodetahun;

														$fileName = $record->rpo;

														if ($fileName) {
															if (str_starts_with($fileName, 'uploads/')) {
																$fileUrl ="/{$fileName}";
															} else {
																$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
															}

															return new HtmlString('<a class="text-primary-600" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
														}

														return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
													}),

												Select::make('sphproduksicheck')
													->label('SPH Produksi')
													->columnSpan(1)
													->options([
														'sesuai' => 'Ada/Sesuai',
														'perbaiki' => 'Tidak ada/Perbaiki',
													])
													->helperText(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
														$tahun = $record->commitment->periodetahun;

														$fileName = $record->sphproduksi;

														if ($fileName) {
															if (str_starts_with($fileName, 'uploads/')) {
																$fileUrl ="/{$fileName}";
															} else {
																$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
															}

															return new HtmlString('<a class="text-primary-600" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
														}

														return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
													}),

												Select::make('logbookproduksicheck')
													->label('Logbook')
													->columnSpan(1)
													->options([
														'sesuai' => 'Ada/Sesuai',
														'perbaiki' => 'Tidak ada/Perbaiki',
													])
													->helperText(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
														$tahun = $record->commitment->periodetahun;

														$fileName = $record->logbookproduksi;

														if ($fileName) {
															if (str_starts_with($fileName, 'uploads/')) {
																$fileUrl ="/{$fileName}";
															} else {
																$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
															}

															return new HtmlString('<a class="text-primary-600" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
														}

														return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
													}),

												Select::make('formLacheck')
													->label('Laporan Akhir')
													->columnSpan(1)
													->options([
														'sesuai' => 'Ada/Sesuai',
														'perbaiki' => 'Tidak ada/Perbaiki',
													])
													->helperText(function ($record) {
														$cleanNpwp = str_replace(['.', ',', '/', '-'], '', $record->npwp);
														$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
														$tahun = $record->commitment->periodetahun;

														$fileName = $record->formLa;

														if ($fileName) {
															if (str_starts_with($fileName, 'uploads/')) {
																$fileUrl ="/{$fileName}";
															} else {
																$fileUrl = Storage::url("uploads/{$cleanNpwp}/{$tahun}/{$fileName}");
															}

															return new HtmlString('<a class="text-primary-600" href="' . $fileUrl . '" target="_blank">Lihat berkas</a>');
														}

														return new HtmlString('<span class="text-danger-600">Tidak ada berkas</span>');
													}),
											]),
									]),
							]),

						Tab::make('Ringkasan Kemitraan')
							// ->badge(function ($record) {
							// 	$countRealisasi = Pks2025::where('no_ijin', $record->no_ijin)->whereNull('status')->count();
							// 	return $countRealisasi;
							// })
							->badgeColor('warning')
							->schema([
								Section::make('Ringkasan Kemitraam')
									->aside()
									->columns(3)
									->description('Data ringkasan kemitraan')
									->schema([
										TextInput::make('jumlahAnggota')
											->disabled()
											->inlineLabel()
											->columnSpan(2)
											->columnStart(1)
											->formatStateUsing(fn ($record) => $record->commitment->lokasi->count() . ' orang'),
										// TextInput::make('realisasiKelompok')
										// 	->disabled()
										// 	->inlineLabel()
										// 	->columnSpan(2)
										// 	->columnStart(1)
										// 	->formatStateUsing(fn ($record) => $record->commitment->lokasi()->distinct('poktan_id')->count() . ' kelompok'),
										TextInput::make('realisasiPks')
											->disabled()
											->inlineLabel()
											->columnSpan(2)
											->columnStart(1)
											->suffixIcon(function ($record) {
												if($record->commitment->lokasi()->distinct('poktan_id')->count() > $record->commitment->pks->count('berkas_pks')){
													return 'heroicon-s-x-circle';
												}
												return 'heroicon-s-check-circle';
											})
											->suffixIconColor(function ($record) {
												if($record->commitment->lokasi()->distinct('poktan_id')->count() > $record->commitment->pks->count('berkas_pks')){
													return 'warning';
												}
												return 'info';
											})
											->formatStateUsing(fn ($record) => $record->commitment->pks->count('berkas_pks') . ' berkas'),

									]),
								Section::make('Perjanjian Kemitraan')
									->aside()
									->description('Pemeriksaan Berkas dan Data Perjanjian antara Pelaku Usaha dengan Kelompok Tani Mitra.')
									->schema([
										TableRepeater::make('berkaspks')
											->addable(false)
											->deletable(false)
											->hiddenLabel()
											->relationship('pks')
											->streamlined()
											->headers([
												Header::make('Kelompok Tani'),
												Header::make('No. Perjanjian'),
												Header::make('Tautan'),
												Header::make('Status'),
												Header::make('Rinci'),
											])
											->schema([
												Placeholder::make('poktan')
													->hiddenLabel()
													->content(fn($record) => $record->masterpoktan->nama_kelompok),
												Placeholder::make('no_Perjanjian')
													->hiddenLabel()
													->content(fn($record) => $record->no_perjanjian),
												Placeholder::make('Tautan')
													->hiddenLabel()
													->extraAttributes(['class' => 'text-info-500'])
													->content(fn($record) => new HtmlString(
														'<a class="text-info-500 font-bold" href="/' . e($record->berkas_pks) . '" target="_blank" rel="noopener noreferrer">Buka File</a>'
													)),
												Placeholder::make('statuspks')
													->hiddenLabel()
													->content(fn($get) => view('components.status-badge-verifikasi', ['status' => $get('status')])),

												Actions::make([
													Action::make('detail')
														->hiddenLabel()
														->iconButton()
														->color('warning')
														->fillForm(fn (Pks2024 $record): array => [
															'status' => $record->status,
															'note' => $record->note,
														])
														->form([
															Grid::make([
																'default' => 1,
																'sm' => 1,
																'lg' => 2,
															])->schema([
																Group::make([
																	Placeholder::make('poktan')
																		->inlineLabel()
																		->content(fn($record) => $record->masterpoktan->nama_kelompok),
																	Placeholder::make('no_Perjanjian')
																		->inlineLabel()
																		->content(fn($record) => $record->no_perjanjian),
																	Placeholder::make('masa_berlaku')
																		->inlineLabel()
																		->content(
																			fn($record) => ($record->tgl_perjanjian_start ?
																				Carbon::parse($record->tgl_perjanjian_start)->format('d M Y') : '-') .
																				' s.d ' .
																				($record->tgl_perjanjian_end ?
																					Carbon::parse($record->tgl_perjanjian_end)->format('d M Y') : '-')
																		),
																	Placeholder::make('memberCount')
																		->inlineLabel()
																		->label('Anggota')
																		->content(fn($record) => $record->datarealisasi->count() .' orang'),
																	Placeholder::make('luas_rencana')
																		->inlineLabel()
																		->label('Luas Rencana')
																		->content(fn($record) => number_format($record->datarealisasi->sum('luas_lahan'), 2, ',', '.') . ' ha'),
																	Placeholder::make('varietas')
																		->inlineLabel()
																		->label('Rencana Varietas')
																		->content(fn($record) => $record->varietas?->nama_varietas ?? '-')

																]),
																Fieldset::make('Kesimpulan Pemeriksaan')
																	->columnSpan(1)
																	->columns(1)
																	->schema([
																		Textarea::make('note')
																			->label('Catatan Pemeriksaan')
																			->disabled(),
																		Select::make('status')
																			->inlineLabel()
																			->label('Kesimpulan')
																			->disabled()
																			->options([
																				'sesuai' => 'Sesuai',
																				'perbaikan' => 'Tidak Sesuai/Perbaikan',
																			]),
																	])
															]),
														])
														->icon('icon-layout-text-window-reverse')
														->modal()
														->modalCancelAction(fn($action) => $action->label('Tutup'))
														->modalHeading(fn($record) => 'Detail Perjanjian ' . ($record->no_perjanjian ?? $record->nama_poktan)),

													Action::make('viewReport')
														->hiddenLabel()
														->tooltip('Lihat Daftar Realisasi')
														->iconButton()
														->icon('heroicon-o-map')
														->url(fn ($record) => route(
															'panel.2024.realisasiReportView',
															['pksId' => Crypt::encryptString($record->id, config('app.qr_secret'))] // Enkripsi dengan key dari config
														)), //encrypt 256 menggunakan key dari config/app 'qr_secret' => env('QR_SECRET', 'B1804bio')
												])->alignCenter(),
												// Placeholder::make('detail')
												//     ->hiddenLabel()
												//     ->extraAttributes(['class'=>'text-center'])
												//     ->content(fn ($record) => $record->no_perjanjian),
											])
									]),
							]),
						Tab::make('Ringkasan Pemeriksaan')
							->schema([
								Section::make('Ringkasan Pemeriksaan')
									->aside()
									->description('Ringkasan hasil pemeriksaan serta penetapan hasil.')
									->schema([
										Fieldset::make('Hasil Pemeriksaan Akhir')
											->visible(fn () => Auth::user()->hasAnyRole(['admin','direktur','Super Admin','verifikator']))
											->schema([
												Radio::make('status')
													->label('Status Verifikasi')
													->options([
														'4' => 'Selesai',
														'3' => 'Perbaikan',
													])
													->descriptions([
														'4' => 'Pemeriksaan telah selesai dilaksanakan. Data dinyatakan Sesuai',
														'3' => 'Pemeriksaan telah selesai dilaksanakan, dengan catatan perbaikan yang harus dilaksanakan oleh Pelaku Usaha',
													]),
												Group::make([

													Select::make('metode')
														->label('Metode Verifikasi')
														->required()
														->options([
															'Dokumen' => 'Dokumen',
															'Wawancara' => 'Wawancara',
														]),
													Textarea::make('note')
														->label('Catatan')
														->placeholder('kesimpulan hasil pemeriksaan')
														->autosize(),
												]),
												FileUpload::make('baproduksi')
													->openable()
													->required()
													->maxSize(2048)
													->downloadable()
													->deletable()
													->disk('public')
													->visibility('public')
													->panelAspectRatio('4:1')
													->imagePreviewHeight('100')
													->fetchFileInformation(true)
													->label('Unggah Berita Acara')
													->helperText('Berita Acara Hasil Pemeriksaan. Maksimal 2MB, format PDF')
													->directory(function ($record) {
														$tahun = $record->commitment->periodetahun;
														$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
														return "uploads/{$cleanNpwp}/{$tahun}";
													})
													->rules([
														'file',
														'mimetypes:application/pdf',
														'mimes:pdf'
													])
													->validationMessages([
														'mimetypes' => 'Hanya file PDF yang diperbolehkan',
														'mimes' => 'Ekstensi file harus .pdf',
													])
													->getUploadedFileNameForStorageUsing(
														function (TemporaryUploadedFile $file, $get, $record): string {
															$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
															$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

															// Format nama file: [ID]_[NPWP]_[NOIJIN].[ext]
															return 'bap_' . $record->id . '_' . $cleanNpwp . '_' . $cleanNoIjin . '_'. uniqid() . '.' . $file->getClientOriginalExtension();
														}
													),

												FileUpload::make('ndhprp')
													->openable()
													->required()
													->maxSize(2048)
													->downloadable()
													->deletable()
													->disk('public')
													->visibility('public')
													->panelAspectRatio('4:1')
													->imagePreviewHeight('100')
													->fetchFileInformation(true)
													->label('Unggah Nota Dinas')
													->helperText('Nota Dinas Hasil Pemeriksaan. Maksimal 2MB, format PDF')
													->directory(function ($record) {
														$tahun = $record->commitment->periodetahun;
														$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
														return "uploads/{$cleanNpwp}/{$tahun}";
													})
													->rules([
														'file',
														'mimetypes:application/pdf',
														'mimes:pdf'
													])
													->validationMessages([
														'mimetypes' => 'Hanya file PDF yang diperbolehkan',
														'mimes' => 'Ekstensi file harus .pdf',
													])
													->getUploadedFileNameForStorageUsing(
														function (TemporaryUploadedFile $file, $get, $record): string {
															$cleanNpwp = str_replace(['.', ',', '-', '/', ' '], '', $record->npwp);
															$cleanNoIjin = str_replace(['.', ',', '-', '/', ' '], '', $record->no_ijin);

															// Format nama file: [ID]_[NPWP]_[NOIJIN].[ext]
															return 'ndhp_' . $record->id . '_' . $cleanNpwp . '_' . $cleanNoIjin . '_'. uniqid() . '.' . $file->getClientOriginalExtension();
														}
													),

												]),
										Fieldset::make('Hasil Pemeriksaan Akhir')
											->visible(fn () => Auth::user()->hasRole('importir'))
											->columns(2)
											->schema([
												Radio::make('status')
													->label('Status Verifikasi')
													->columnSpan(2)
													->options([
														'4' => 'Selesai',
														'3' => 'Perbaikan',
													])
													->descriptions([
														'4' => 'Pemeriksaan telah selesai dilaksanakan. Data dinyatakan Sesuai',
														'3' => 'Pemeriksaan telah selesai dilaksanakan, dengan catatan perbaikan yang harus dilaksanakan oleh Pelaku Usaha',
													]),
												Textarea::make('note')
													->label('Catatan Akhir')
													->columnSpan(2)
													->placeholder('kesimpulan hasil pemeriksaan')
													->autosize(),

												FileUpload::make('baproduksi')
													->openable()
													->hiddenLabel()
													->downloadable()
													->panelAspectRatio('4:1')
													->imagePreviewHeight('100')
													->fetchFileInformation(true)
													->helperText('Berita Acara Hasil Pemeriksaan'),

												FileUpload::make('ndhprp')
													->openable()
													->hiddenLabel()
													->downloadable()
													->panelAspectRatio('4:1')
													->imagePreviewHeight('100')
													->fetchFileInformation(true)
													->helperText('Nota Dinas Hasil Pemeriksaan'),

											])
									]),
							])
					])->contained(false)
			])->columns(1);
	}
}
