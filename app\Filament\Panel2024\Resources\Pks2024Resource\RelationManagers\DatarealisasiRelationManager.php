<?php

namespace App\Filament\Panel2024\Resources\Pks2024Resource\RelationManagers;

use App\Models\DataRealisasi2024;
use App\Models\MasterPoktan2024;
use App\Models\Pks2024;
use App\Tables\Columns\SpatialColumn;
use Filament\Forms;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Support\Enums\Alignment;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Actions\Action as ActionsAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;

class DatarealisasiRelationManager extends RelationManager
{
    protected static string $relationship = 'datarealisasi';
	protected static ?string $title = 'Daftar Realisasi';
    public function form(Form $form): Form
    {
        return $form
            ->schema([
				Group::make()
					->columnSpanFull()
					->columns(1)
					->schema([
						TextInput::make('nama_lokasi')
							->inlineLabel()
							->required()
							->maxLength(20),

						Select::make('lokasi_id')
							->label('Anggota')
							->required()
							->inlineLabel()
							->searchable()
							->options(function ($state) {
								$ownerRecord = $this->getOwnerRecord();

								// Get locations with available land
								$lokasis = $ownerRecord->lokasi()
									->with('masteranggota')
									->withSum('datarealisasi as total_luas_tanam', 'luas_lahan')
									->havingRaw('ROUND(luas_lahan, 2) > ROUND(IFNULL(total_luas_tanam, 0), 2)')
									->get();

								// If editing (state contains a value), ensure the current location is included
								if ($state) {
									$currentLokasi = $ownerRecord->lokasi()
										->with('masteranggota')
										->where('id', $state)
										->first();

									if ($currentLokasi && !$lokasis->contains('id', $currentLokasi->id)) {
										$lokasis->push($currentLokasi);
									}
								}

								return $lokasis->mapWithKeys(fn ($lokasi) => [
									$lokasi->id => $lokasi->masteranggota?->nama_petani ?? 'Unknown'
								])->toArray();
							})->reactive()
							->afterStateUpdated(function ($set, $state) {
								$ownerRecord = $this->getOwnerRecord();
								$lokasi = $ownerRecord->lokasi()->where('id', $state)->first();
								if ($lokasi) {
									$set('anggota_id', $lokasi->anggota_id);
								}
							}),

						Hidden::make('anggota_id'),

						Hidden::make('npwp_company')
							->default(fn () => $this->getOwnerRecord()->npwp),
						Hidden::make('no_ijin')
							->default(fn () => $this->getOwnerRecord()->no_ijin),
						Hidden::make('poktan_id')
							->default(fn () => $this->getOwnerRecord()->poktan_id),
						Hidden::make('pks_id')
							->default(fn () => $this->getOwnerRecord()->id),
					]),
            ]);
    }

    public function table(Table $table): Table
    {
		$isImportir = Auth::user()->hasRole('importir');
        return $table
            ->recordTitleAttribute('pks_id')
            ->columns([
				TextColumn::make('masterkelompok.nama_kelompok')
					->label('Poktan')->sortable()->searchable(),
                TextColumn::make('masteranggota.nama_petani')
					->label('Nama Anggota')->sortable()->searchable(),
				TextColumn::make('luas_kira')
					->label('Luas Peta')
					->numeric()
					->suffix(' ha')->sortable(),
				TextColumn::make('luas_lahan')
					->numeric()
					->label('Luas Tanam')
					->suffix(' ha')->sortable(),
				TextColumn::make('volume')
					->numeric()
					->suffix(' ton')->sortable(),
				SpatialColumn::make('Data Peta')->sortable(),
            ])
            ->filters([
				SelectFilter::make('poktan_id')
				->label('Poktan')
				->options(function () {
					$pksId = $this->getOwnerRecord()->id;
					$poktanIds = Pks2024::whereNotNull('poktan_id')
						->where('id', $pksId)
						->pluck('poktan_id')
						->unique()
						->toArray();
					return MasterPoktan2024::whereIn('id', $poktanIds)
						->whereNotNull('nama_kelompok')
						->where('nama_kelompok', '!=', '')
						->pluck('nama_kelompok', 'id')
						->filter()
						->toArray();
				})

            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->label('Tambah Realisasi Tanam')->icon('heroicon-o-squares-plus')->modal()->modalHeading('Realisasi Tanam'),
            ])
            ->actions([
                EditAction::make()
					->iconButton()
					->modal()
					->modalWidth(MaxWidth::Medium)
					->modalHeading(fn ($record) => 'Data Lokasi dan Realisasi '.$record->masteranggota->nama_petani),

				DeleteAction::make()
					->hidden(function () use ($isImportir) {
						// Jika bukan importir, sembunyikan tombol delete
						if (!$isImportir) {
							return true;
						}

						// Ambil record pemilik (PKS)
						$pks = $this->getOwnerRecord();

						// Ambil nomor RIPH
						$noIjin = $pks->no_ijin;

						// Cek apakah RIPH memiliki SKL
						// Metode 1: Cek di tabel Completed
						$hasCompleted = \App\Models\Completed::where('no_ijin', $noIjin)->exists();

						// Metode 2: Cek di tabel AjuVerifSkl2024 dengan status 4 (Selesai)
						$hasSkl = \App\Models\AjuVerifSkl2024::where('no_ijin', $noIjin)
							->where('status', '4')
							->exists();

						// Jika RIPH memiliki SKL, sembunyikan tombol delete
						return $hasCompleted || $hasSkl;
					})
					->iconButton()
					->modalHeading('Hapus Data Realisasi')
					->modalDescription('Apakah Anda yakin ingin menghapus data realisasi ini?')
					->modalSubmitActionLabel('Ya, Hapus'),

				ActionsAction::make('lokasiTanam')
					->iconButton()
					->icon('icon-geo-alt')
					->color(fn ($record) => $record->latitude && $record->longitude && $record->polygon ? 'success' : 'danger')
					->tooltip('Peta Lokasi Tanam')
					->url(function ($record) {
						$cleanNoIjin = str_replace(['.', ',', '/', '-'],'', trim($record->no_ijin));
						return route('panel.2024.draw.drawMap', ['noRiph' => $cleanNoIjin, 'realisasi' => $record->id]);
					}),

				ActionsAction::make('realisasiTanam')
					->iconButton()
					->icon('icon-seedling-line')
					->color(fn ($record) => $record->luas_lahan && $record->mulai_tanam && $record->akhir_tanam ? 'success' : 'danger')
					->tooltip('Data Realisasi Tanam')
					->modal()
					->modalWidth(MaxWidth::Medium)
					->modalHeading(fn ($record) => 'Realisasi Tanam di '.$record->nama_lokasi)
					->fillForm(fn (Model $record): array => [
						'luas_lahan' => $record->luas_lahan,
						'mulai_tanam' => $record->mulai_tanam,
						'akhir_tanam' => $record->akhir_tanam,
					])
					->form([
						TextInput::make('luas_lahan')
							->inlineLabel()
							->label('Luas Tanam')
							->suffix(' ha')
							->step(0.01)
							->columnSpanFull()
							->required()
							->inputMode('decimal')
							->numeric(),
						DatePicker::make('mulai_tanam')
							->inlineLabel()
							->required(),
						DatePicker::make('akhir_tanam')
							->inlineLabel()
							->required(),
						Actions::make([
							Action::make('spatial')
								->label('Lokasi Tanam')
								->hidden()
								->visible(fn ()=> Auth::user()->hasRole('importir'))
								->icon('icon-geo-alt')
								->iconButton()
								->button()
								->color(function ($record) {
									if($record->latitude && $record->longitude && $record->polygon){
										return 'success';
									}
									return 'danger';
								})
								->url(function ($record) {
									$cleanNoIjin = str_replace(['.', ',', '/', '-'], '', $record->no_ijin);
									return route('panel.2024.draw.drawMap', ['noRiph' => $cleanNoIjin, 'realisasi' => $record->id]);
								}),
						])->alignEnd()
					])
					->action(function (array $data, Model $record): void {
						$record->update(
							[
								'luas_lahan' => $data['luas_lahan'] ?? null,
								'mulai_tanam' => $data['mulai_tanam'] ?? null,
								'akhir_tanam' => $data['akhir_tanam'] ?? null,
							]
						);
					}),

				ActionsAction::make('realisasiPanen')
					->iconButton()
					->icon('icon-shopping-basket-2-line')
					->color(fn ($record) => $record->volume && $record->mulai_panen && $record->akhir_panen ? 'success' : 'danger')
					->tooltip('Data Realisasi Panen')
					->modal()
					->modalWidth(MaxWidth::Medium)
					->modalHeading(fn ($record) => 'Realisasi Panen di '.$record->nama_lokasi)
					->fillForm(fn (Model $record): array => [
						'volume' => $record->volume,
						'mulai_panen' => $record->mulai_panen,
						'akhir_panen' => $record->akhir_panen,
					])
					->form([
						TextInput::make('volume')
							->inlineLabel()
							->label('Volume')
							->suffix(' ton')
							->step(0,1)
							->columnSpanFull()
							->required()
							->numeric(),
						DatePicker::make('mulai_panen')
							->inlineLabel()
							->required(),
						DatePicker::make('akhir_panen')
							->inlineLabel()
							->required(),
					])
					->action(function (array $data, Model $record): void {
						$record->update(
							[
								'volume' => $data['volume'] ?? null,
								'mulai_panen' => $data['mulai_panen'] ?? null,
								'akhir_panen' => $data['akhir_panen'] ?? null,
							]
						);
					}),
				ActionsAction::make('uploadImageTanam')
					->iconButton()
					->icon('icon-file-image')
					->color(fn ($record) => $record->fototanam()->exists() ? 'success' : 'danger')
					->tooltip('Unggah foto tanam')
					->url(fn ($record) =>
						route('filament.panel2024.resources.data-realisasi2024s.fototanam', $record->id)
					),
				ActionsAction::make('uploadImageProduksi')
					->iconButton()
					->icon('icon-file-image')
					->color(fn ($record) => $record->fotoproduksi()->exists() ? 'success' : 'danger')
					->tooltip('Unggah foto produksi')
					->url(fn ($record) =>
						route('filament.panel2024.resources.data-realisasi2024s.fotoproduksi', $record->id)
					)
                // ViewAction::make()->iconButton()->modalHeading(fn ($record) => 'Data Lokasi dan Realisasi '.$record->masteranggota->nama_petani),
                // Tables\Actions\DeleteAction::make(),
			]);
            // ->bulkActions([
            //     // Tables\Actions\BulkActionGroup::make([
            //     //     Tables\Actions\DeleteBulkAction::make(),
            //     // ]),
            // ]);
    }

	// protected function paginateTableQuery(Builder $query): Paginator
	// {
	// 	return $query->simplePaginate(($this->getTableRecordsPerPage() === 'all') ? $query->count() : $this->getTableRecordsPerPage());
	// }
}
