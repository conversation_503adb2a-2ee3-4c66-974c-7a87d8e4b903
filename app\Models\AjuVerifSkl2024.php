<?php

namespace App\Models;

use App\Observers\AjuVerifSklObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

#[ObservedBy([AjuVerifSklObserver::class])]
class AjuVerifSkl2024 extends Model
{
	use LogsActivity;
	public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
			->logFillable('*')
			->logOnlyDirty();
    }
	public $table = 'avskls';
	protected $primaryKey = 'id'; // Pastikan ada
    public $incrementing = true;
    protected $keyType = 'int';

	protected $fillable = [
		'npwp',
		'commitment_id',
		'no_ijin',
		'no_pengajuan',
		'status',
		'note',


		//file upload
		'baskls', //berita acara hasil pemeriksaan realisasi produksi
		'ndhpskl', //nota dinas hasil pemeriksaan realisasi tanam

		'check_by',
		'verif_at',
		'metode',
	];

	protected static function booted()
	{
		static::addGlobalScope('npwp', function (Builder $builder) {
			if (Auth::check()) {
				$user = Auth::user();

				if ($user->hasAnyRole(['admin', 'Super Admin','direktur'])) {
				}
				else {
					$builder->where('npwp', $user->npwp);
				}
			}
		});
	}

	public function commitment()
	{
		return $this->belongsTo(Commitment2024::class, 'no_ijin', 'no_ijin');
	}

	public function sklrekomendasi()
	{
		return $this->hasOne(SklRekomendasi2024::class, 'no_ijin', 'no_ijin');
	}

	public function completed()
	{
		return $this->hasOne(Completed::class, 'no_ijin', 'no_ijin');
	}

	public function datauser()
	{
		return $this->belongsTo(DataUser::class, 'npwp', 'npwp_company');
	}

	public function dataadmin()
	{
		return $this->belongsTo(DataAdministrator::class, 'check_by', 'user_id');
	}
	

	public function userDocs()
	{
		return $this->hasOne(UserDocs2024::class, 'no_ijin', 'no_ijin');
	}

	public function pks()
	{
		return $this->hasMany(Pks2024::class, 'no_ijin', 'no_ijin');
	}
}
