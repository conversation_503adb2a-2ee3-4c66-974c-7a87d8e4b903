<?php

namespace App\Filament\Panel2025\Resources\CommitmentRegionResource\Pages;

use App\Filament\Panel2025\Resources\CommitmentRegionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewCommitmentRegion extends ViewRecord
{
    protected static string $resource = CommitmentRegionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\EditAction::make(),
        ];
    }

    public function getHeading(): string
	{
        $noIjin = $this->record ? $this->record->no_ijin : '##';
        return 'PPRK No: ' . $noIjin;
	}

    public function getSubheading(): ?string
    {
        $kabupaten = $this->record->kabupaten->nama_kab;
        return 'untuk Kabupaten: '.$kabupaten;
    }
}
